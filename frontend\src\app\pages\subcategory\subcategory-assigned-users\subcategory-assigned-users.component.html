<div class="site-customer-main-container">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
      <div class="row">
        <div class="col-12 col-sm-6  mt-2">
          <label>Show <select (change)="onChangeShowEntries($event.target.value)" name="assined-users-length"
              aria-controls="assined-users-show-entries" class="form-select show-entries form-select-sm">
              <option value="10" selected="selected">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select> Rows</label>
        </div>
        <div class="col-12 col-sm-6 d-flex justify-content-end mt-2">
          <div class="custom-input-group">
            <input class="form-control search-form-control" placeholder="" appDelayedInput
              (delayedInput)="search($event)">
            <i class="bi bi-search pe-3"></i>
          </div>
        </div>
        <div *ngIf="records.length > 0" class="custom-action-button text-right">
          <button [disabled]="userSelected ? false : true" (click)="userSelected ? unassignedUser() : null" type="button"
            class="btn btn-primary mb-3 action-button" title="Unassigned Staff" *ngIf="type === 'USER'">Unassigned Staff
          </button>
          <button [disabled]="userSelected ? false : true" (click)="userSelected ? unassignedFarm() : null" type="button"
            class="btn btn-primary mb-3 action-button" title="Unassigned Staff" *ngIf="type === 'FARM'">Unassigned Site
          </button>
        </div>
      </div>
      <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
        <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
          <thead>
            <tr>
              <th width="100px" style="padding-bottom: 11px">
                <div *ngIf="records.length > 0" class="form-check publish-training-check-cls">
                  <input (change)="selectUnselectAll($event)" name="selectAll" class="form-check-input selectAll"
                    type="checkbox" value="" id="flexCheckDefault">
                  <label class="form-check-label" for="flexCheckDefault">
                    Select All
                  </label>
                </div>
              </th>
              <th *ngIf="type === 'USER'">{{"USERS.Name" | translate}}</th>
              <th *ngIf="type === 'USER'">{{"USERS.MobileNo" | translate}}</th>
              <th *ngIf="type === 'USER'">{{"USERS.Active" | translate}}</th>
              <th *ngIf="type === 'FARM'">{{"Farm.name" | translate}}</th>
              <th *ngIf="type === 'FARM'">{{"Farm.propertyNo" | translate}}</th>
            </tr>
          </thead>
          <tbody>
            <tr class="records-cls" *ngFor="let record of records;">
              <td>
                <div class="form-check form-check-inline publish-training-check-cls">
                  <input name="subCategoryId" (change)="selectUnselectCheck()" class="form-check-input" type="checkbox"
                    id="inlineCheckbox1" [value]="record.id">
                </div>
              </td>
              <td class="text-capitalize" *ngIf="type === 'USER'"><a class="text-decoration-underline"
                  [routerLink]="['/dashboard/user-details/'+record?.userId]">{{record?.userDetail?.fullName}}</a>
              </td>
              <td class="text-lowercase" *ngIf="type === 'USER'">{{record?.userDetail?.phoneNumber}}</td>
              <td *ngIf="type === 'USER'">
                <span class="label label-info">{{record?.userDetail?.isActive ? 'Yes' : 'No'}}</span>
              </td>
              <td class="text-capitalize" *ngIf="type === 'FARM'"><a class="text-decoration-underline"
                [routerLink]="['/dashboard/farm/edit/'+record?.farmId]">{{record?.farmDetail?.name}}</a>
            </td>
            <td class="text-lowercase" *ngIf="type === 'FARM'">{{record?.farmDetail?.propertyNo}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  