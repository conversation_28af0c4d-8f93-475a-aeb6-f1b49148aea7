import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { ManageContentManager } from '../manage-content.manager';

import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
import { Content } from 'src/app/models/content';
declare const $: any;

@Component({
	selector: 'app-manage-content-edit',
	templateUrl: './manage-content-edit.component.html',
	styleUrls: ['./manage-content-edit.component.scss']
})

export class ManageContentEditComponent extends BaseEditComponent implements OnInit {
	public content: Content;
	public languages: Language[];
  readonly MY_CONSTANT = Constant;
	constructor(protected route: ActivatedRoute, protected manageContentService: ManageContentManager,
		protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
		protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService
		, private languageManager: LanguageManager
		, public commonUtil: CommonUtil) {
		super(manageContentService, commonService, toastService, loadingService, route, router, translateService);
	}

	ngOnInit() {
		this.content = new Content();
		this.content.isActive = true;
		this.setRecord(this.content);


		this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
		this.init();
	}

	onFetchCompleted() {
		this.content = Content.fromResponse(this.record);
		this.setRecord(this.content);
	}

	onSaveSuccess(message: any) {
		this.toastService.success(message);
		this.navigate('/dashboard/manage-content');
	}


	checkConditionToReload(records: BaseModel[], selectedRecord: any) {
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}

	async save(form: any) {
		this.onClickValidation = !form.valid;
		if (!form.valid) {
			return;
		}

		// if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
		// 	return;
		// }
		try {
			this.loadingService.show();
			const method = this.request.isNewRecord ? 'save' : 'update';
			const response: RestResponse = await this.manager[method](this.content);
			this.loadingService.hide();
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.onSaveSuccess(response.message);
		} catch (error) {
			this.loadingService.hide();
			this.toastService.error(error.message);
		}
	}
}
