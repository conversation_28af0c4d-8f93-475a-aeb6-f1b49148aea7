<div *ngIf="!currentVideoData" data-aos="fade-up" data-aos-duration="1000" class="staff-assigned-training container">
  <div class="text-center">
    <button (click)="openFilterTrainingModal()" type="button" class="btn filter-buttton me-2 bg-dark text-light btn-lg">
      <img src="/assets/images/icons/menu/filter.svg" class="moment-icon me-2 img-fluid" alt="">FILTER
    </button>
    <button (click)="changeTrainingData('INPROGRESS')"
      [ngClass]="{'bg-secondary staff-active-training-button': trainingTabs == 'INPROGRESS'}" type="button"
      class="btn staff-training-buttons btn-lg">ASSIGNED TRAINING</button>
    <button (click)="changeTrainingData('APPROVED')"
      [ngClass]="{'bg-secondary staff-active-training-button': trainingTabs == 'APPROVED'}" type="button"
      class="btn staff-training-buttons training-buttons btn-lg">APPROVED TRAINING</button>
    <button (click)="changeTrainingData('REJECTED')"
      [ngClass]="{'bg-secondary staff-active-training-button': trainingTabs == 'REJECTED'}" type="button"
      class="btn staff-training-buttons training-buttons btn-lg">REJECTED TRAINING</button>
  </div>
  <div *ngIf="noTrainingData" class="staff-assigned-content p-5 text-center">{{trainingTabs == 'INPROGRESS' ? 'No
    Assigned Trainings Found' : trainingTabs == 'APPROVED' ? 'No Approved Trainings Found' : trainingTabs == 'REJECTED'
    && 'No Rejected Trainings Found'}} </div>
  <div class="loadMore" scrollTracker (scrollingFinished)="onScrollingFinished()">
    <div *ngFor="let training of trainingData" class="staff-assigned-content">
      <div class="row">
        <div class="col-8">
          <h4 class="fw-bold content-msg">{{training.trainingIdDetail?.videoTitle}}</h4>
          <p class="text-secondary">{{moment(training.createdOn).format('LL')}}</p>
        </div>
        <div class="col-4 text-end">
          <img class="px-2"
            [src]="training.status == 'REJECTED' ? '/assets/images/close-logo.png' : training.status == 'INPROGRESS' ? '/assets/images/icons/menu/refresh.svg' : (training.status == 'APPROVED' || training.status == 'COMPLETED') && '/assets/images/icons/menu/confirmation.svg'">
          <p class="f-s-10">{{training.status}}</p>
        </div>
        <div class="col-6">
          <h6>Program</h6>
          <span class="fw-bold content-msg"
            *ngFor="let category of training.trainingCategoryMappingList; let last = last;">{{category.categoryName +
            (last ? '' : ', ')}}</span>
        </div>
        <div class="col-6 margin-bottom-15">
          <h6>Course</h6>
          <span class="fw-bold content-msg"
            *ngFor="let subCategory of training.trainingSubCategoryMappingList; let last = last;">{{subCategory.subCategoryName
            + (last ? '' : ', ')}}</span>
        </div>
        <div class="col-12">
          <div class="card">
            <div class="d-flex justify-content-between">
              <button class="video-icon mb-3 btn bg-secondary"><img src="/assets/images/icons/menu/v_camera.svg"
                  alt=""></button>
              <p class="card-content ms-2">{{training.trainingIdDetail?.description}}</p>

            </div>
            <span (click)="readMoreDescription(training)" class="text-white"
              *ngIf="training.trainingIdDetail.description && training.trainingIdDetail.description.length > 63">Read
              More</span>
            <button (click)="watchVideo(training)" class="watch-video-button btn">WATCH VIDEO</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="loadMore" class="mt-4">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>
<app-reel-view-videos acceptMediaType="video/*" (previousPage)="backToTrainingList($event)"
  fileTypeMessage="Only Mkv, Mp4 files are allowed" allowedExtension="mkv,mp4" VideosType="assigned-training"
  *ngIf="currentVideoData" [currentVideoData]="currentVideoData" [trainingData]="trainingData"></app-reel-view-videos>
<div class="modal fade" id="filterStaffTrainingModal" tabindex="-1" aria-labelledby="filterStaffTrainingModalLabel"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="filterStaffTrainingModalLabel">Filter Training</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div *ngIf="filterStaffTrainingModal && filterStaffTrainingModal._isShown" class="modal-body">
        <form #trainingFilterForm="ngForm" novalidate="novalidate">
          <mat-form-field class="example-form-field w-100">
            <mat-label>Search Video Title...</mat-label>
            <input [(ngModel)]="filterParam.SearchCommonTitle" name="searchCommonTitle" matInput type="text">
            <button mat-button *ngIf="filterParam.SearchCommonTitle" matSuffix mat-icon-button aria-label="Clear"
              (click)="filterParam.SearchCommonTitle=''">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <mat-form-field class="example-form-field w-100">
            <mat-label>Search Program...</mat-label>
            <input [(ngModel)]="filterParam.categorySearch" name="searchCategory" matInput type="text">
            <button mat-button *ngIf="filterParam.categorySearch" matSuffix mat-icon-button aria-label="Clear"
              (click)="filterParam.categorySearch=''">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <mat-form-field class="example-form-field w-100">
            <mat-label>Search Course...</mat-label>
            <input [(ngModel)]="filterParam.subCategorySearch" name="searchSubCategory" matInput type="text">
            <button mat-button *ngIf="filterParam.subCategorySearch" matSuffix mat-icon-button aria-label="Clear"
              (click)="filterParam.subCategorySearch=''">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
            [fromDateInput]="fromDate" [toDateInput]="toDate"></app-date-range-filter>
          <div class="modal-footer">
            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
            <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
              class="btn btn-primary">Filter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="trainingDescriptionModal" aria-hidden="true" aria-labelledby="trainingDescriptionModal"
  tabindex="-1">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content" *ngIf="trainingDescriptionModal && trainingDescriptionModal._isShown">
      <div class="modal-header">
        <h5 class="modal-title" id="trainingDescriptionModalLabel">{{recordData?.name}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        {{recordData?.description}}
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>