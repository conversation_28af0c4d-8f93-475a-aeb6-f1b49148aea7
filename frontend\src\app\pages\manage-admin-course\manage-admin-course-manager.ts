import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Course } from 'src/app/models/course';
import { ManageAdminCourseService } from './manage-admin-course-service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ManageAdminCourseManager extends BaseManager {

    constructor(protected manageAdminCourseService: ManageAdminCourseService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageAdminCourseService, loadingService, toastService);
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageAdminCourseService.fetchAll(filterParam);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onFetchAllSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }

    updatePublish(data: Course): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageAdminCourseService.updatePublish(data);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onSaveSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }

    getPublishUser(filterParam?: FilterParam): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageAdminCourseService.getisPublish(filterParam);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onSaveSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }

    getsCourseDetails(id: string): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageAdminCourseService.getsCourseDetails(id).toPromise();
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onFetchSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }
}
