import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class SubCategoryEditBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const subcategoryId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard", active: false
            },
            {
                title: "Manage Courses", link: "/dashboard/sub-category", active: false
            },
            {
                title: "Course", link: "/dashboard/sub-category/edit/" + subcategoryId, active: true
            }
        ])



    }
}
