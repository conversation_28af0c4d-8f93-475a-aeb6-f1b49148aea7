import { Injectable } from "@angular/core";
import { BaseManager } from "src/app/config/base.manager";
import { LoadingService } from "src/app/services/loading.service";
import { ToastService } from "src/app/shared/toast.service";
import { SupplierChainManagerService } from "./supplier-chain-manager.service";

@Injectable({
    providedIn: 'root'
})
export class SupplierChainManager extends BaseManager {

    constructor(private supplierChainManagerService: SupplierChainManagerService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(supplierChainManagerService, loadingService, toastService);
    }
}