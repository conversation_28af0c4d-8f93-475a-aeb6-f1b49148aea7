<div class="site-customer-main-container">
  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-6  mt-2">
        <label>Show <select (change)="onChangeShowEntries($event.target.value)" name="assined-users-length"
            aria-controls="assined-users-show-entries" class="form-select show-entries form-select-sm">
            <option value="10" selected="selected">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select> Rows</label>
      </div>
      <div class="col-12 col-sm-6 d-flex justify-content-end mt-2">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="" appDelayedInput
            (delayedInput)="search($event)">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div *ngIf="records.length > 0" class="custom-action-button text-right">
        <button [disabled]="farmSelected ? false : true" (click)="farmSelected ? unassignedFarm() : null" type="button"
          class="btn btn-primary mb-3 action-button" title="Unassigned Staff">Unassigned Site
        </button>
      </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th width="100px" style="padding-bottom: 11px">
              <div *ngIf="records.length > 0" class="form-check publish-training-check-cls">
                <input (change)="selectUnselectAll($event)" name="selectAll" class="form-check-input selectAll"
                  type="checkbox" value="" id="flexCheckDefault">
                <label class="form-check-label" for="flexCheckDefault">
                  Select All
                </label>
              </div>
            </th>
            <th>{{"USERS.Name" | translate}}</th>
            <th>{{"USERS.MobileNo" | translate}}</th>
            <th width="180">{{"USERS.Sites" | translate}}</th>
            <th width="180">{{"USERS.TrainingStatus" | translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr class="records-cls" *ngFor="let record of records;">
            <td>
              <div class="form-check form-check-inline publish-training-check-cls">
                <input name="trainingId" (change)="selectUnselectCheck()" class="form-check-input" type="checkbox"
                  id="inlineCheckbox1" [value]="record.id">
              </div>
            </td>
            <td class="text-capitalize"><a class="text-decoration-underline"
                [routerLink]="['/dashboard/user-details/'+record?.userId]">
                {{record?.userIdDetail?.fullName}}</a>
            </td>
            <td class="text-lowercase">{{record?.userIdDetail?.phoneNumber}}</td>
            <td class="text-capitalize"><a class="text-decoration-underline"
                [routerLink]="['/dashboard/farm/edit/'+record?.farmIdDetail?.id]">
                {{record?.farmIdDetail?.name}}</a>
            </td>
            <td>
              <!-- {{record?.userIdDetail?.trainingStatus}} -->
              <div *ngIf="record?.userIdDetail?.trainingStatus; else noStatus" class="status-button"
                [ngClass]="{'status-button-progress-color': record?.userIdDetail?.trainingStatus == MY_CONSTANT.TRAINING_STATUS.INPROGRESS,
                 'status-button-completed-color': record?.userIdDetail?.trainingStatus == MY_CONSTANT.TRAINING_STATUS.COMPLETED}">
                <img
                  [src]="record?.userIdDetail?.trainingStatus == 'INPROGRESS' ? '/assets/images/icons/menu/refresh.svg' : '/assets/images/icons/menu/confirmation.svg'"
                  class="px-2" alt="">{{record?.userIdDetail?.trainingStatus && record?.userIdDetail?.trainingStatus ==
                MY_CONSTANT.TRAINING_STATUS.INPROGRESS ? 'IN PROGRESS' : MY_CONSTANT.TRAINING_STATUS.COMPLETED}}
              </div>
              <ng-template #noStatus>-</ng-template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>