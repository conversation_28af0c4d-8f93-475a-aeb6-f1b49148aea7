import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../config/base.service';
import { RestResponse } from 'src/app/shared/auth.model';

@Injectable({
    providedIn: 'root'
})
export class FarmAdminService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/account/farmadmin', '/api/account/farmadmins');
    }

    register(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/account/farmadmin/register', data);
    }

}

