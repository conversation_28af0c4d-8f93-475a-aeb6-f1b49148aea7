import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewChildren } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import AOS from 'aos';
import { ToastService } from 'src/app/shared/toast.service';
import { Course } from 'src/app/models/course';
import { RestResponse } from 'src/app/shared/auth.model';
import { FilterParam } from 'src/app/models/filterparam';
import * as moment from 'moment';
import { ManageProgramUserService } from './manage-program-user.service';
import { ManageProgramUserManager } from './manage-program-user.manager';
import { __extends } from 'tslib';
import { UserEnrolledListComponent } from '../user-enrolled-list/user-enrolled-list.component';
import { UserInvitedListingComponent } from '../user-invited-listing/user-invited-listing.component';
import { UserPendingListingComponent } from '../user-pending-listing/user-pending-listing.component';
import { ManageCourseUserService } from '../program-users/manage-course-users-service';
import { FarmService } from '../farm/farm.service';
import { Farm } from 'src/app/models/farm';
import { filter } from 'rxjs/operators';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-program-user',
  templateUrl: './manage-program-user.component.html',
  styleUrls: ['./manage-program-user.component.scss']
})
export class ManageProgramUserComponent extends BaseListServerSideComponent implements OnInit {
  userDetailsTabs: string = "enrolled";
  @ViewChild(UserEnrolledListComponent) userEnrolledListComponent: UserEnrolledListComponent;
  @ViewChild(UserInvitedListingComponent) userInvitedListingComponent: UserInvitedListingComponent;
  @ViewChild(UserPendingListingComponent) userPendingListingComponent: UserPendingListingComponent;
  @ViewChild('userInviteForm') userInviteForm: NgForm;

  currentActiveTab: string;
  userDetails: boolean = true;
  filterParam: FilterParam;
  searchText: string;
  filterTrainingModal: any;
  selectedCourseUserId: string;
  selectedCourseUser: any;
  courses: Course;
  selectedCourseId: any;
  moment: any = moment;
  publishCourseList: any[];
  inviteUserCourseModal: any;
  insertedData: { username: string, email: string, id }[] = [];
  onClickValidation = false;
  errorMessage: string = '';
  isSelfInvite: boolean = true; // Default to self-invite
  selectedCourseProgramUserId: string;
  programCourseList: any[];
  public isFarmInvite: boolean = true;
  public farms: Farm[] = [];
  public selectedFarmIds: string[] = [];
  public username: string = '';
  public email: string = '';
  public farmValidation: boolean = false;
  public individualValidation: boolean = false;

  constructor(private route: ActivatedRoute, public authService: AuthService, protected router: Router, private zone: NgZone, protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected manageProgramUserService: ManageProgramUserService, protected manageProgramUserManager: ManageProgramUserManager, private manageCourseUserService: ManageCourseUserService,
    protected commonService: CommonService, public commonUtil: CommonUtil, private farmService: FarmService) {
    super(manageProgramUserManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.filterParam = new FilterParam();
    this.route.queryParams.subscribe(params => {
      this.currentActiveTab = params.tab ?? 'enrolled';
    });
    this.programCourseList = new Array();
    this.publishCourseList = new Array<any>();
    this.userDetailsTabs = this.currentActiveTab ? this.currentActiveTab : "enrolled";
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.selectedCourseProgramUserId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    if (this.userDetailsTabs === "enrolled") {
      this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    }
    // switch(this.currentActiveTab)
    this.filterChildRecords();
  }

  onClickTrainingFilter(valid) {
    this.filterTrainingModal.hide();
    this.filterChildRecords();
    // this.onCancel();
  }

  // filterChildRecords() {
  //   this.userEnrolledListComponent.refreshRecord();
  // }

  //radio button using in  invite user modal
  selectInviteUser(isSelf: boolean): void {
    this.isSelfInvite = isSelf;
    this.fetchPublishRecords(isSelf);
  }

  selectInviteUserCourse(event: any): void {
    if (event) {
      // this.selectedInviteUserCourse = event.title;
      this.selectedCourseUser = event.title;
      this.selectedCourseUserId = event ? event.id : null;
      this.fetchFarms();
    }
  }

  selectInviteProgramUserCourse(event: any): void {
    if (event) {
      this.selectedCourseProgramUserId = event ? event.id : null;
    }
  }

  filterChildRecords() {
    switch (this.userDetailsTabs) {
      case 'enrolled':
        // this.userEnrolledListComponent.fetchEnrolledCourseRecords(this.filterParam);
        this.userEnrolledListComponent.refreshRecord();
        break;
      case 'invited':
        this.userInvitedListingComponent.refreshRecord();
        // this.userInvitedListingComponent.fetchInvitedCourseRecords(this.filterParam);
        break;
      case 'pending':
        this.userPendingListingComponent.refreshRecord();
        // this.userPendingListingComponent.fetchPendingCourseRecords(this.filterParam);
        break;
    }
  }

  //filter
  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('filterTrainingModal');
      if (modalElement) {
        this.filterTrainingModal = new bootstrap.Modal(modalElement);
      }
    }, 0);
    setTimeout(() => {
      this.inviteUserCourseModal = new bootstrap.Modal(
        document.getElementById('inviteUserCourseModal')
      );
    }, 0);
  }

  resetFilter() {
    delete this.filterParam.course;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    this.selectedCourseUserId = null;
    this.filterTrainingModal.hide();
    this.filterChildRecords();
    // this.onCancel();
  }



  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  // onCancel() {
  //   this.request.loadEditPage = false;
  //   if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
  //     this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
  //       dtInstance.destroy();
  //     });
  //   }
  //   this.init();
  // }


  openFilterTrainingModal() {
    // if (this.searchTraining) {
    //   this.searchTraining = "";
    //   delete this.filterParam.searchCommonTitle;
    //   this.onCancel();
    // }
    this.fetchCoursesForFilters();
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  async fetchCoursesForFilters() {
    try {
      var course = new Course();
      course.id = this.selectedCourseId;
      this.loadingService.show();
      const response: RestResponse = await this.manageProgramUserService.getCourseFilter(null);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // open profile page
  openUserProfilePage() {
    this.router.navigate(['/dashboard/program-admin/profile-setting']);
  }

  openInvitedTab() {
    this.router.navigate(['/dashboard/program-admin/user/invited']);
  }

  onClickUserDetailsTab(name: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          queryParams: { tab: name },
          queryParamsHandling: 'merge'
        });
    });
    this.userDetailsTabs = name;
    this.filterParam = new FilterParam();
  }

  // invite tab

  // invited user popup tab
  closeInviteModal() {
    this.inviteUserCourseModal.hide();
    this.courses = new Course();
  }

  // Invited User
  openInviteCourseUserModal() {
    AOS.init({ disable: true });
    this.inviteUserCourseModal.show();
    // reset template-driven form and component state
    this.userInviteForm?.resetForm();
    this.courses = new Course();
    this.selectedCourseUserId = null;
    this.selectedCourseProgramUserId = null;
    this.insertedData = [];
    this.selectedFarmIds = [];
    this.errorMessage = '';
    this.onClickValidation = false;
    this.farmValidation = false;
    this.individualValidation = false;
    this.isFarmInvite = true;
    this.isSelfInvite = true;
    this.fetchPublishRecords(true);
    if (this.authService.getRoles().includes('ROLE_ADMIN')) {
      this.fetchProgramActiveUserRecords();
    }
  }

  //program active user course
  async fetchProgramActiveUserRecords(filterParam?: FilterParam) {
    try {
      var course = new Course();
      course.id = this.selectedCourseProgramUserId;
      this.loadingService.show();
      const response: RestResponse = await this.manageProgramUserService.getProgramCourse(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.programCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // fatch course list
  async fetchPublishRecords(isSelf: boolean) {
    try {
      var course = new Course();
      course.id = this.selectedCourseId;
      this.loadingService.show();

      let input: any = {
        isSelf: isSelf
      }
      const response: RestResponse = await this.manageProgramUserService.getAvailablePublishedCourses(input);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  insertData(form: any, id: any) {
    this.individualValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$/;
    if (!emailPattern.test(this.courses.email)) {
      this.errorMessage = 'Invalid email format.';
      return;
    }
    const isDuplicate = this.insertedData.some(data => data.email === this.courses.email);
    if (isDuplicate) {
      this.errorMessage = 'This email is already entered.';
    } else {
      this.insertedData.push({ username: this.courses.username, email: this.courses.email, id: this.courses.id });
      this.errorMessage = '';  // Clear error message if the data is successfully inserted
      this.courses = new Course();
    }
  }

  removeData(index: number) {
    this.insertedData.splice(index, 1);
    this.errorMessage = '';  // Clear error message
  }

  async onClickInviteUserCourse(form: any) {
    this.onClickValidation = true;
    this.farmValidation = false;
    this.individualValidation = false;

    // Ensure course is selected before proceeding
    if (!form?.controls?.selectedCourseUser?.valid) {
      return;
    }

    if (this.isFarmInvite) {
      if (!this.selectedFarmIds || this.selectedFarmIds.length === 0) {
        this.farmValidation = true;
        return;
      }
      this.courses.courseEnrolled = this.selectedFarmIds.map(id => ({ farmId: id }));
    } else {
      const shouldValidateForm = this.insertedData.length === 0;

      if (shouldValidateForm && !form.valid) {
        this.individualValidation = true;
        return;
      }

      if (this.insertedData.length === 0) {
        this.toastService.error('Please add at least one user.');
        return;
      }

      // no error — set form data
      this.courses.courseEnrolled = this.insertedData;
    }

    // Passed all validation
    this.onClickValidation = false;

    try {
      this.loadingService.show();
      this.courses.id = this.selectedCourseUserId;

      if (this.authService.getRoles().includes('ROLE_ADMIN')) {
        this.courses.programAdmin = this.isSelfInvite
          ? this.authService.getUser().id
          : this.selectedCourseProgramUserId;
      }

      const response: RestResponse = (this.authService.getRoles().includes('ROLE_ADMIN'))
        ? await this.manageCourseUserService.sendAdminCourseInvite(this.courses)
        : await this.manageCourseUserService.sendCourseInvite(this.courses);

      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }

      // Reset form
      this.insertedData = [];
      this.courses = new Course();
      this.inviteUserCourseModal.hide();
      this.onSaveSuccess(response.message);
      this.fetchCourseUsersData();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }



  async fetchCourseUsersData() {
    const resp: RestResponse = await this.manageProgramUserService.getCourseUserRecords(this.filterParam);
    this.loadingService.hide();
    if (!resp.status) {
      return;
    }
    this.records = resp.data;
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    if (this.authService.isProgramAdmin()) {
      this.router.navigate(['/dashboard/program-admin/users']);
    } else if (this.authService.isAdmin()) {
      this.router.navigate(['/dashboard/seekers/enrolled']);
    }
  }

  async fetchFarms() {
    const filterParam = new FilterParam();
    filterParam.relationId = this.selectedCourseUserId;
    filterParam.relationTable = "COURSE";
    console.log(filterParam, "filterParam");
    const farmsResponse = await this.farmService.fetchAllAvailableFarms(filterParam);
    this.farms = farmsResponse.data;
    this.farms.forEach(farm => {
      farm.displayLabel = farm.farmCode?.trim() + ' - ' + farm.name;
    });
  }


}
