import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';

@Injectable({
    providedIn: 'root'
})
export class NotificationService extends BaseService {
    constructor(public http: HttpClient) {
        super(http, '/api/notification', '/api/notifications');
    }

    fetchUnreadNotification(): Observable<RestResponse> {
        return this.getRecord('/api/notification/unread');
    }

    seenNotification(id: string): Promise<RestResponse> {
        return this.updateRecord(`/api/notification/status/${id}`, null);
    }

    seenAllNotification(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/seen/all/notifications', data);
    }

    removeNotifications(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/delete/notifications', data);
    }

}

