import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { UsersService } from 'src/app/services/users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Users } from '../../models/users';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { CommonUtil } from '../../shared/common.util';
import { ToastService } from '../../shared/toast.service';
import { UsersManager } from './users.manager';
import { FarmService } from '../farm/farm.service';
import { Farm } from 'src/app/models/farm';
import AOS from 'aos';
import { ActivatedRoute } from '@angular/router';
import { constants } from 'buffer';
import { Constant } from 'src/app/config/constants';


declare const $: any;
let bootstrap: any;

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  farms: Farm[];
  farmsWithNoCompany: any[] = [];
  filterModal: any;
  onClickValidation: boolean = false;
  statusOptions = Constant.STATUS_OPTIONS;
  
  constructor(protected usersManager: UsersManager, protected toastService: ToastService, public authService: AuthService,
    protected loadingService: LoadingService, protected commonService: CommonService, protected router: Router,
    public commonUtil: CommonUtil, private userService: UsersService, private farmService: FarmService, private route: ActivatedRoute) {
    super(usersManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.records = [] as Users[];
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.fetchFarms();
    this.init();
    this.route.queryParams.subscribe(params => {
      const farmId = params['farmId'];
      if (farmId) {
        this.filterParam.farm = farmId;
        this.applyFilter(true);
      }
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.filterModal = new (window as any).bootstrap.Modal(
        document.getElementById('filterModal')
      );
    }, 0);
  }

  openfilterModal() {
    AOS.init({ disable: true });
    this.filterModal.show();
  }
  
  resetFilter() {
    delete this.filterParam.farm;
    this.filterModal.hide();
    this.refreshRecord();
  }

  applyFilter(valid) {
    this.filterModal.hide();
    if (this.filterParam.farm === 'no-company') {
      // Show only users without company
      this.records = this.records.filter((record : any) => !record.userFarmDetail || record.userFarmDetail.length === 0);
    } else {
      this.refreshRecord();
    }
  }

  fetchFarms() {
    this.farmService.fetchAvailableFarms(this.filterParam).then((response) => {
      this.farms = response.data;
      this.farmsWithNoCompany = [
        { id: 'no-company', name: 'No Company' },
        ...this.farms
      ];
    });
  }

  onItemSelection(record: Users) {
    this.onAssociatedValueSelected(record);
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }
  removeSuccess() {
    this.onCancel();
  }

  activateDeactiveUserConfirmation(data: any, recordData: any) {
    const confirmationMessage = data.currentTarget.checked
      ? 'Are you sure you want to activate this user account?'
      : 'Are you sure you want to deactivate this user account?';

    this.commonService.confirmation(
      confirmationMessage,
      this.activateDeactiveCallback.bind(this),
      { id: recordData.id, isActive: data.currentTarget.checked },
      null,
      null,
      this.cancelActivateDeactiveCallback.bind(this)
    );
  }


  cancelActivateDeactiveCallback() {
    this.onCancel();
  }

  async activateDeactiveCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userService.activateDeactivateUser(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  onNewRecord() {
    this.router.navigate(['/dashboard/user/edit/0'])
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/user/edit/' + id])
  }

  ngOnDestroy() {
    this.clean();
  }

  remove(id) {
    this.commonService.confirmation('Are you sure you want to delete this user account?', this.removeCallback.bind(this), id);
  }

  
}
