import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ProgramUserService extends BaseService {
    constructor(public http: HttpClient) {
        super(http, '/api/account/admin/program', '/api/account/admin/programs');
    }

    //for content transfer
    saveContentTransfer(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/admin/contenttransfer', data);
    }

}

