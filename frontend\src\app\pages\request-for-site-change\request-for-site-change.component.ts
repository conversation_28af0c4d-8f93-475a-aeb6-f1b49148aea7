import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { SiteRequestChange } from 'src/app/models/siterequestchange';
import { LoadingService } from 'src/app/services/loading.service';
import { RequestSiteChangeService } from 'src/app/services/request-site-change.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { RequestForSiteChangeManager } from './request-for-site-change.manager';
import AOS from 'aos';

declare var bootstrap: any;

@Component({
  selector: 'app-request-for-site-change',
  templateUrl: './request-for-site-change.component.html',
  styleUrls: ['./request-for-site-change.component.scss']
})
export class RequestForSiteChangeComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  userDetails: boolean = true;
  isUserDetailsModal: boolean = true;
  userDetailsModal: any;
  recordData: any;
  constructor(protected requestForSiteChangeManager: RequestForSiteChangeManager, private requestSiteChangeService: RequestSiteChangeService, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, protected router: Router,
    public commonUtil: CommonUtil) {
    super(requestForSiteChangeManager, commonService, toastService, loadingService, router);
  }

  ngOnDestroy() {
    this.clean();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.userDetailsModal = new bootstrap.Modal(
        document.getElementById('userDetailsModal')
      );
    }, 0)
  }

  userDetailsModalClose(event: any){
    if(event){
      this.userDetailsModal.hide();
    }
  }

  openUserDetailsModal(record: any){
    AOS.init({ disable: true });
    this.recordData = record;
    this.userDetailsModal.show();
  }

  updateStatus(record: any){
    const data = {
      id: record.id,
      isCompleted: true
    }
    this.commonService.confirmation('Would you like to change status of pending to completed?', this.updateStatusCallback.bind(this), data, null, null);
  }

  async updateStatusCallback(data: any){
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.requestSiteChangeService.updateStatusOfRequestSiteChange(data);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  ngOnInit(){
    this.records = new Array<SiteRequestChange>();
    this.init();
    this.requestSiteChangeMarkSeenUpdate();
  }

  async requestSiteChangeMarkSeenUpdate(){
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.requestSiteChangeService.requestSiteChangeMarkSeenUpdate(null);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.requestSiteChangeService.$setCountRead.next(null)
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  removeSuccess() {
    this.onCancel();
  }

  search($event) {
		const value = ($event.target as HTMLInputElement).value;
		this.filterParam.searchText = (value && value != '') ? value.trim() : null;
		this.refreshRecord();
	}

}
