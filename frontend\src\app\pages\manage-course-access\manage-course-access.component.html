<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">
    <div class="user-details-section">
        <div class="row">
            <div class="col-12 col-lg-7 ps-0">
                <!-- <div class="user-details-section">
                    <ul class="nav nav-pills">
                        <li class="nav-item bg-secondary user-details-btn width-180px"
                            [ngClass]="{'bg-secondary': userDetailsTabs == 'enrolled'}"
                            *ngIf="!authService.isSuperAdmin() && !authService.isProgramAdmin()"
                            (click)="onClickUserDetailsTab('enrolled')">
                            <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'enrolled'}"
                                aria-current="page">Enrolled</a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': userDetailsTabs == 'invited'}"
                            *ngIf="!authService.isSuperAdmin() && !authService.isProgramAdmin()"
                            (click)="onClickUserDetailsTab('invited')">
                            <a class="btn nav-link"
                                [ngClass]="{' active bg-secondary': userDetailsTabs == 'invited'}">Invited
                            </a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': userDetailsTabs == 'pending'}"
                            (click)="onClickUserDetailsTab('pending')"
                            *ngIf="!authService.isSuperAdmin() && !authService.isProgramAdmin()">
                            <a class="btn nav-link"
                                [ngClass]="{' active bg-secondary': userDetailsTabs == 'pending'}">Pending
                            </a>
                        </li>
                    </ul>
                </div> -->
            </div>
            <div class="col-12 col-lg-5 pe-0 d-flex align-items-center justify-content-end">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" appDelayedInput (delayedInput)="search($event)"
                        [(ngModel)]="filterParam.searchText" [delayTime]="1000">
                    <i class="bi bi-search pe-3"></i>
                </div>
                <!-- <button type="button" (click)="openFilterTrainingModal()"
                    class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px pe-0">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button> -->
            </div>
        </div>
    </div>
    <!-- Manage User Tabs -->
    <!-- <app-user-enrolled-list *ngIf="userDetailsTabs == 'enrolled'" [filterParam]="filterParam"></app-user-enrolled-list>
    <app-user-invited-listing *ngIf="userDetailsTabs == 'invited'"
        [filterParam]="filterParam"></app-user-invited-listing> -->
    <app-user-pending-listing></app-user-pending-listing>

    <!-- filter training listing -->
    <!-- <div class="modal fade" id="filterTrainingModal" tabindex="-1" aria-labelledby="filterTrainingModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterTrainingModalLabel">Filter Manage Users</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div *ngIf="filterTrainingModal && filterTrainingModal._isShown" class="modal-body">
                    <form #trainingFilterForm="ngForm" novalidate="novalidate">
                        <div class="form-floating">
                            <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                                [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                    name="selectedCourseUser" clearable="false" [items]="publishCourseList"
                                    bindLabel="title" bindValue="id" (change)="selectInviteUserCourse($event)"
                                    class="custom-multiselect form-control padding-bottom-8"
                                    [(ngModel)]="filterParam.course" #selectedCourseUser="ngModel" [searchable]="false">
                                </ng-select>
                            </div>
                            <label for="selectedCourseUser">
                                {{"Course.chooseCourse" | translate}}
                            </label>
                        </div>
                        <app-date-range-filter (fromDateOutput)="fromDateOutput($event)"
                            (toDateOutput)="toDateOutput($event)" [fromDateInput]="filterParam.startDate"
                            [toDateInput]="filterParam.endDate">
                        </app-date-range-filter>
                        <div class="modal-footer">
                            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">
                                Reset
                            </button>
                            <div>
                                <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
                                    class="btn btn-primary ms-2">
                                    Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div> -->

</div>