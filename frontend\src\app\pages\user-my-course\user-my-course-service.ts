import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UserMyCourseService extends BaseService {
  strUserId: string;

  constructor(public http: HttpClient) {
    super(http, '', '');
  }

  getUserId(id: string) {
    this.strUserId = id;
  }


  // GET COURSES BY USER ID
  // fetch(id: string): Observable<RestResponse> {
  //   return this.getRecord('/api/courses/userid/' + id);
  // }
  fetchAll(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/courses/userid', filterParam);
  }

  // GET COURSES BY USER ID View Trainings
  getViewTraining(courseid: string, userId: string): Observable<RestResponse> {
    return this.getRecord(`/api/trainings/course/${courseid}/user/${userId}`);
  }

  remove(id: string): Promise<RestResponse> {
    return this.removeRecord('/api/course/user/' + id);
  }
  // fetchRecordsForUsers(id: string): Observable<RestResponse> {
  //   return this.getRecord('/api/courses/userid/'+id);
  // }

  approveOrRejectTraining(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/training/approve/reject', data);
  }

  getisPublish(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/ispublish/courses', filterParam);
  }

  getProgramCourse(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/account/active/program/admins', filterParam);
  }

  getCourseUserRecords(data: any): Promise<RestResponse> {
    return this.getRecords('/api/courses', data);
  }

}

