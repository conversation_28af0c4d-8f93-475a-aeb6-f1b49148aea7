import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, ResolveEnd, Router, RouterStateSnapshot } from '@angular/router';
import { AuthService } from './auth.services';
import { Observable } from 'rxjs';
import { LocalStorageService } from 'angular-2-local-storage';
import { Constant } from '../config/constants';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router, private localStorageService: LocalStorageService) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    const roles = route.data.roles as Array<string>;
    const user = this.localStorageService.get('user') as any;

    // Redirect FARM_ADMIN to dashboard if popup flag is true
    if (
      user &&
      user.roles &&
      user.roles.indexOf(Constant.ROLES.FARM_ADMIN) !== -1 &&
      user.isFirstLogin &&
      state.url !== '/dashboard'
    ) {
      this.router.navigate(['dashboard']);
      return false;
    }

    return this.authService.isAuthorizedUser(roles).then((response: any) => {
      
      if (roles.indexOf('ROLE_ANONYMOUS') !== -1) {
        if (response.hasAccess && user.roles.indexOf('ROLE_ADMIN') > -1) {
          this.router.navigate(['dashboard']);
          return false;
        }
        if (response.hasAccess && user.roles.indexOf('ROLE_SUPER_ADMIN') > -1) {
          // console.log('ROLE_SUPER_ADMIN')
          this.router.navigate(['dashboard']);
          return false;
        }
        if (response.hasAccess && user.roles.indexOf('ROLE_PROGRAM_ADMINISTRATOR') > -1) {
          this.router.navigate(['dashboard/program-admin']);
          return false;
        }
        if (response.hasAccess && user.roles.indexOf('ROLE_STAFF') > -1) {
          this.router.navigate(['staff-assigned-training']);
          return false;
        }
        this.unsetForgotPasswordUserNameOrRedirectTo404();
        return true;
      }
      if (!response.hasAccess) {
        this.router.navigate(['login'], { queryParams: { redirectTo: state.url } });
        return false;
      }
      if (!response.hasRoleAccess) {
        console.log("hello");
        
        this.router.navigate(['403']);
        return false;
      }
      return true;
    });

  }

  unsetForgotPasswordUserNameOrRedirectTo404() {
    if (window.location.pathname == '/account/recover') {
      const userName = this.localStorageService.get('forgotPasswordUserName') as any
      if (!userName) {
        this.router.navigate(['404']);
        return false
      }
    }
    this.router.events.subscribe((routerData) => {
      if (routerData instanceof ResolveEnd) {
        if (routerData.url != '/account/recover') {
          this.localStorageService.remove('forgotPasswordUserName');
        }
      }
    })
  }
}
