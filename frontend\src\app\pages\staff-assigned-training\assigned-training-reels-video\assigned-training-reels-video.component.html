<div class="staff-assigned-reel-training container videos-reel-cls">
  <swiper *ngIf="isTrainingData" [height]="browserHeight" [noSwiping]="true" (init)="init($event)"
    (slideChange)="onChangeSlide($event)" (reachBeginning)="reachBeginning()" (reachEnd)="reachEnd()"
    [allowSlideNext]="allowNextSlide" [initialSlide]="currentIntialSlide" [allowSlidePrev]="allowSlidePrev"
    direction="vertical" #swiperVirtualRef [slidesPerView]="1" class="mySwiper" id="mySwiperID">
    <ng-template swiperSlide *ngFor="let training of trainingData; index as i">
      <div class="video-wrapper">
        <div class="video-container w-100" id="video-container"
          (click)="!currentVideoWaiting && !videoMuted  ? playVideoFromPlayIcon(training.trainingId): null">
          <div *ngIf="currentVideoWaiting == training.trainingId"
            class="d-flex align-items-center justify-content-center h-100">
            <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
            <div class="spinner-border text-light" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
          <div class="play-button-wrapper">
            <div title="Play video" class="play-gif circle-play-b-cls" [id]="'circle-play-b'+training.trainingId">
              <!-- SVG Play Button -->
              <svg *ngIf="currentPlayVideo == training.trainingId" xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 80 80">
                <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
              </svg>
            </div>
          </div>
          <div *ngIf="videoMuted && currentActiveVideo == training.trainingId"
            class="unmute-button-reel-video position-absolute" (click)="unmuteVideo(training.trainingId)">
            <div class="mute-button-container">
              <img src="/assets/images/icons/menu/volume-slash-light.svg" />
            </div>
          </div>
          <div class="video-inner-content-container">
            <div class="video-title-reel">
              <p class="text-white">{{training?.trainingIdDetail?.videoTitle}}</p>
            </div>
            <div class="video-credit-upload-icon">
              <div class="video-credit-container d-flex align-items-center">
                <img src="/assets/images/icons/menu/video-credit-icon.svg" alt="">
                <p class="mx-1 m-0 text-white">{{training?.trainingIdDetail?.videoCredit}}</p>
              </div>
              <div class="upload-video-container">
                <label class="fw-bold" [for]="'file-input'+training.trainingId"
                  [ngStyle]="{'padding-top': (training.status && training.status != 'INPROGRESS') ? '7px' : '' }">
                  <img
                    *ngIf="(training.status == undefined || training.status == 'INPROGRESS') && !trainingIds.includes(training.trainingId)"
                    src="/assets/images/icons/menu/upload-icon-white.svg" class="img-fluid" alt="" />
                  <i *ngIf="(training.status && training.status != 'INPROGRESS')"
                    class="bi bi-check-circle px-2 text-white"></i>
                  <div *ngIf="trainingIds.includes(training.trainingId)"
                    class="d-flex align-items-center justify-content-center h-100">
                    <div class="spinner-border text-light" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <!-- <span class="progress-ratio" id="progressRatio"></span> -->
                  </div>
                </label>
                <p class="text-white">{{trainingIds.includes(training.trainingId) ? 'UPLOADING' :
                  !trainingIds.includes(training.trainingId) && (training.status == undefined || training.status ==
                  'INPROGRESS') ? 'UPLOAD VIDEO' : 'VIDEO
                  UPLOADED' }}</p>
                <input
                  *ngIf="(training.status == undefined || training.status == 'INPROGRESS') && !trainingIds.includes(training.trainingId)"
                  (change)="fileChangeEvent(training.trainingId,$event)" [name]="training.trainingId+'Video'"
                  [id]="'file-input'+training.trainingId" type="file" accept="video/*" />
              </div>
            </div>
          </div>
          <video autoplay playsinline [ngStyle]="{'display': !currentVideoWaiting ? 'block' : 'none'}"
            *ngIf="currentActiveVideo == training.trainingId" [id]="'videoId'+training.trainingId" nodownload
            oncontextmenu="return false">
            <source [src]="training?.trainingIdDetail?.videoUrl">
          </video>
        </div>
      </div>
    </ng-template>
  </swiper>
</div>

<div class="modal fade" id="assignTeam" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
  aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="close-icon" (click)="closeModal()">
          <i class="bi bi-x"></i>
        </div>
        <div class="inner-container">
          <div class="img-container">
            <svg id="employees" xmlns="http://www.w3.org/2000/svg" width="100" height="100"
              viewBox="0 0 24.882 24.797">
              <path id="Path_53270" data-name="Path 53270"
                d="M98.54,8.449a.727.727,0,0,0,.539-.238l3.007-3.3L106.274,8a.729.729,0,0,0,.952-.074l4.483-4.546V6.317a.729.729,0,1,0,1.458,0V1.6a.729.729,0,0,0-.729-.729h-4.417a.729.729,0,1,0,0,1.458H110.7l-4.064,4.121-4.209-3.11a.729.729,0,0,0-.972.1L98,7.23a.729.729,0,0,0,.539,1.22Z"
                transform="translate(-93.057 -0.875)" fill="#fff" />
              <path id="Path_53271" data-name="Path 53271"
                d="M21.165,217.286a3.984,3.984,0,0,0-4.7-6.3,4.588,4.588,0,0,0-8.157-.05,3.983,3.983,0,0,0-4.595,6.353A5.265,5.265,0,0,0,0,222.312v.138a.729.729,0,0,0,.729.729H24.153a.729.729,0,0,0,.729-.729v-.138A5.265,5.265,0,0,0,21.165,217.286ZM18.292,212a2.528,2.528,0,0,1,0,5.057h-.558a5.222,5.222,0,0,0-2.067-.8,4.582,4.582,0,0,0,1.252-3.852A2.5,2.5,0,0,1,18.292,212Zm-5.915-2.07a3.131,3.131,0,1,1-3.131,3.131A3.135,3.135,0,0,1,12.377,209.929ZM6.59,212a2.5,2.5,0,0,1,1.256.333,4.583,4.583,0,0,0,1.256,3.94,5.222,5.222,0,0,0-1.973.785H6.59a2.528,2.528,0,0,1,0-5.057Zm-1.334,6.515h.4a5.224,5.224,0,0,0-.9,2.933v.274H1.5A3.8,3.8,0,0,1,5.256,218.514Zm.959,2.933a3.792,3.792,0,0,1,1.492-3.014.731.731,0,0,0,.18-.131,3.775,3.775,0,0,1,2.124-.651H11.7v4.071H6.215v-.274Zm12.433.274H13.161V217.65h1.691a3.8,3.8,0,0,1,3.8,3.8Zm1.458,0v-.274a5.224,5.224,0,0,0-.9-2.933h.417a3.8,3.8,0,0,1,3.752,3.207Z"
                transform="translate(0 -198.382)" fill="#fff" />
            </svg>
          </div>
          <div class="heading">Not Assigned Any Team</div>
          <div class="content">You are not assigned to any team yet, please contact administration.</div>
          <button class="btn btn-primary close-btn" (click)="closeModal()">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>