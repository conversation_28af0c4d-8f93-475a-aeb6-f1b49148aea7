import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { Constant } from 'src/app/config/constants';
import { StaffAssignedTrainingService } from 'src/app/services/staff-assigned-training.service';
import { StaffMomentsService } from 'src/app/services/staff-moments.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-reel-view-videos',
  templateUrl: './reel-view-videos.component.html',
  styleUrls: ['./reel-view-videos.component.scss']
})
export class ReelViewVideosComponent implements OnInit {
  @Input() currentVideoData: any;
  @Input() VideosType: string;
  @Input() fileTypeMessage: string;
  @Input() allowedExtension: string;
  @Input() trainingData: any;
  @Input() acceptMediaType: string;
  @Output() previousPage = new EventEmitter<any>();
  currentIntialSlide: number;
  public currentActiveVideo: string = null;
  public currentPlayVideo: string = null;
  allowNextSlide: boolean = true;
  allowSlidePrev: boolean = true;
  updateStatusData: any = { isDeleted: false, isActive: true }
  trainingIds: any = [];
  public currentVideoWaiting: string = null
  private fileData: any;
  trainingId: string;

  public browserHeight: any = null;
  constructor(private toastService: ToastService, private staffAssignedTrainingService: StaffAssignedTrainingService, private staffMomentService: StaffMomentsService,
    private commonService: CommonService) { }

  ngOnInit() {
    this.browserHeight = window.innerWidth <= 600 ? window.innerHeight : 590;
    this.trainingData.map((training, index, row) => {
      training.uploader = training.status == this.checkTypeOfVideos(this.VideosType) ? this.initializeUploaderTraining(null, this.allowedExtension, null, null, this.toastService, this.fileTypeMessage, null, training) : null;
      if (training.id == this.currentVideoData.id) {
        this.currentIntialSlide = index;
        if (index == 0) {
          this.allowNextSlide = true;
          this.allowSlidePrev = false;
        }
        if (index + 1 === row.length) {
          this.allowNextSlide = false;
          this.allowSlidePrev = true;
        }
      }
    })
  }

  checkTypeOfVideos(type: string) {
    if (type == 'moment') {
      return Constant.MOMENT_STATUS.OPEN
    }
    if (type == "assigned-training") {
      return Constant.TRAINING_STATUS.INPROGRESS
    }
  }

  resizeDiv() {
    if (window.innerWidth <= 600) {
      let h = window.innerHeight;

      this.browserHeight = h;
    } else {
      this.browserHeight = 590;
    }
  }

  ngOnDestroy() {
  }


  init(event: any) {
    window.addEventListener('resize', () => this.resizeDiv());
    if (event.activeIndex == 0) {
      this.onChangeSlide(event);
    }
  }

  removeEndedVideoEventListener(videoId: any) {
    videoId.removeEventListener("ended", (event) => {
    });
  }

  addEndedVideoEventListener(videoId: any, id: string) {
    videoId.addEventListener("ended", (event) => {
      this.currentPlayVideo = id
    });
  }

  loadVideo(url) {
    return fetch(url)
      .then(resp => resp.blob())
      .then(blob => URL.createObjectURL(blob));
  }

  onChangeSlide(event: any) {
    this.trainingData.map((training, index, row) => {
      if (index === event.activeIndex) {
        this.currentActiveVideo = training.id;
        this.currentPlayVideo = null;
        this.currentVideoWaiting = training.id;
        setTimeout(() => {
          var videoId = document.getElementById("videoId" + training.id) as HTMLMediaElement | null;
          videoId.addEventListener('canplaythrough', (event) => {
            this.currentVideoWaiting = null;
            this.addEndedVideoEventListener(videoId, training.id)
          });
        }, 0)
      }
    })
    if (event.activeIndex > 0 && event.activeIndex + 1 < this.trainingData.length) {
      this.allowNextSlide = true;
      this.allowSlidePrev = true;
    }
  }

  backToPreviousPage(training: any) {
    this.previousPage.emit(training);
  }

  reachEnd() {
    this.allowNextSlide = false;
    this.allowSlidePrev = true;
    this.toastService.info('End of training videos', null, null, 500, 500);
  }

  reachBeginning() {
    this.allowNextSlide = true;
    this.allowSlidePrev = false;
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string, training: any) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService, training);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService, training);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccess(file, files, training);
        }, 200);
      }
    };

    uploader.onErrorItem = (fileItem, response) => {
      this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== training.id)
      toastService.error('Something error occurred please try again later');
    }
    return uploader;
  }

  fileValidationErrorTraining(data: string, toastService: any, training: any) {
    this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== training.id)
    toastService.error(data);
  }

  async onUploadSuccess(file: any, files: any, trainingData: any) {

    try {
      const response = await this.getStatusUpdateData(trainingData, file)
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.updateDataBasedOnType(trainingData)
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }

    this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== trainingData.id)
  }

  updateDataBasedOnType(trainingData: any) {
    this.trainingData.map(training => {
      if (trainingData.id == training.id) {
        if (this.VideosType == 'moment' && trainingData.isRecurring === false) {
          training.status = Constant.MOMENT_STATUS.WAITING_FOR_APPROVAL;
        }
        if (this.VideosType == 'assigned-training') {
          training.status = Constant.TRAINING_STATUS.COMPLETED;
        }
      }
      return training
    })
  }

  async getStatusUpdateData(trainingData: any, file: any) {
    if (this.VideosType == 'moment') {
      const data = {
        id: trainingData.id,
        userVideo: file.path,
        ...this.updateStatusData
      }
      const response: RestResponse = await this.staffMomentService.updateStatusOfMoment(data)
      return response
    }

    if (this.VideosType == 'assigned-training') {
      const data = {
        id: trainingData.id,
        userVideoUrl: file.path,
        userId: trainingData.userId,
        trainingId: trainingData.trainingId,
        ...this.updateStatusData
      }
      const response: RestResponse = await this.staffAssignedTrainingService.updateStatusOfTraining(data);
      return response
    }
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  uploadVideo(trainingId: string, event) {
    const file = event.target.files[0];
    if (trainingId) {
      this.trainingId = trainingId;
      this.trainingIds.push(trainingId);
    }
    if (event.target.files[0].type != "video/mp4") {
      this.commonService.convertVideoFormat(file).then(res => {
        // console.log(res, 'res');
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    }
    else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  playVideoFromPlayIcon(id: string) {
    var videoId = document.getElementById("videoId" + id) as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        this.currentPlayVideo = null;
        this.addEndedVideoEventListener(videoId, id)
      } else {
        this.currentPlayVideo = id;
        videoId.pause();
        this.removeEndedVideoEventListener(videoId)
      }
    }
  }

  async onFileProcessingCompleted(files: any) {
    let index = this.trainingData.findIndex(x => x.id == this.trainingId);
    this.trainingData[index].uploader.addToQueue(files);
    this.trainingData[index].uploader.uploadAll();
  }

}
