<div data-aos="fade-up" data-aos-duration="1000" class="staff-assigned-sites container">
  <div class="staff-assigned-sites-content mt-3 loadMore" scrollTracker (scrollingFinished)="onScrollingFinished()">
    <div *ngFor="let record of records" class="row">
      <div class="col-12 mb-3">
        <div [ngStyle]="{'background': !record?.farmIdDetail.farmingUrl ? '#F2F2F2' : ''}"
          class="staff-assigned-sites-logo">
          <img [ngStyle]="{'object-fit': !record?.farmIdDetail.farmingUrl ? 'contain' : ''}"
            [src]="record?.farmIdDetail?.farmingUrl ? record?.farmIdDetail?.farmingUrl : '/assets/images/farm-no-image.jpg' " />
        </div>
      </div>
      <div class="col-12 mb-3">
        <h1 class="text-start">{{record?.farmIdDetail?.name}}</h1>
      </div>
      <div class="col-6 mb-3">
        <div class="assigned-sites-location d-flex">
          <img src="/assets/images/icons/menu/loc-route.svg" class="me-3 mx-3 img-fluid" alt="">
          <p class="staff-loc pt-3"><span class="opacity-50">Location</span><br>{{record?.farmIdDetail?.country}}</p>
        </div>
      </div>
      <div class="col-6 mb-3">
        <div class="p-2">
          <h6 class="opacity-50">Property Number</h6>
          <p class="fw-bold lh-1">{{record?.farmIdDetail?.propertyNo}}</p>
        </div>
      </div>
      <div class="col-12 mb-3">
        <button [routerLink]="['/site-view-staff/'+record.farmId]" [queryParams]="{ n: record?.farmIdDetail?.name }"
          queryParamsHandling="merge" class="btn btn-lg bg-secondary view-staff">VIEW
          STAFF</button>
      </div>
      <div class="col-12 mb-3">
        <button [routerLink]="['/staff-moments/'+record.farmId]" [queryParams]="{ n: record?.farmIdDetail?.name }"
          queryParamsHandling="merge" class="btn btn-lg border border-dark text-dark view-staff">SHOW MOMENTS</button>
      </div>
    </div>
  </div>
  <div *ngIf="records && !records?.length" class="text-center bg-white p-4">No Assigned Sites Found</div>
  <div *ngIf="loadMore" class="mt-4">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>
