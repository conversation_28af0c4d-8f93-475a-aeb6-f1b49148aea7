import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { BaseService } from '../../config/base.service';
import { Observable } from 'rxjs';
import { RestResponse } from 'src/app/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class ManageProgramUserService extends BaseService {
  constructor(public http: HttpClient) {
    super(http, '', '');
  }

  getCourseFilter(data: any): Promise<RestResponse> {
    return this.getRecords('/api/ispublish/courses', data);
  }

  getisPublish(data: any): Promise<RestResponse> {
    return this.getRecords('/api/ispublish/courses', data);
  }

  sendCourseInvite(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/course/invite/users', data);
  }

  getCourseUserRecords(data: any): Promise<RestResponse> {
    return this.getRecords('/api/courses', data);
  }

  getProgramCourse(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/account/active/program/admins', filterParam);
  }

  getAvailablePublishedCourses(data: any): Promise<RestResponse> {
    return this.getRecords('/api/available/ispublish/courses', data);
  }

}

