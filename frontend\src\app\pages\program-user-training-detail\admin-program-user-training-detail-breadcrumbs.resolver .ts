import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AdminProgramUserTrainingDetailBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const strProgramAdmin: string | null = route.paramMap.get("id");

    return of([
      {
        title: "Dashboard", link: "/dashboard", active: false
      },
      {
        title: "Manage Course", link: "/dashboard/program-admin/courses", active: false
      },
      // {
      //   title: "Training Detail", link: "/dashboard/program-detail/" + strProgramAdmin, active: true
      // }

    ])
  }



}
