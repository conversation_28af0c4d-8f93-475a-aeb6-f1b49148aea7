import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { AuthService } from 'src/app/shared/auth.services';

@Injectable({
    providedIn: 'root'
})
export class UserDetailsBreadcrumbs implements Resolve<any> {
    constructor(private authService: AuthService) {}

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const userId: any | null = route.paramMap.get("id");
        const user = this.authService.getUser();
        const breadcrumbs = [];
        
        // Only show Dashboard link if user is not ROLE_FARM_ADMIN
        if (user && user.roles && !user.roles.includes('ROLE_FARM_ADMIN')) {
            breadcrumbs.push({
                title: "Dashboard", 
                link: "/dashboard", 
                active: false
            });
        }
        
        breadcrumbs.push({
            title: "Manage Seekers", 
            link: "/dashboard/users", 
            active: false
        });
        
        breadcrumbs.push({
            title: "Seeker Detail", 
            link: "/dashboard/user-details/" + userId, 
            active: true
        });

        return of(breadcrumbs);
    }
}
