import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Training } from './training';
import { Farm } from './farm';
export class FarmAssignTraining extends BaseModel {
	
				tenantId: number;
			  	slug: string;
			  	trainingIdDetail: Training;
			  	trainingId: string;
			  	farmIdDetail: Farm;
			  	farmId: string;
			  	assignedDate: Date;
			  	assignedDateCalendar: any;
			  	allUserAssigned: boolean;
	
    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
			this.allUserAssigned=false;
    }
    
   static fromResponse(data: any) : FarmAssignTraining {
		const obj = new FarmAssignTraining();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
	  	obj.trainingIdDetail = data.trainingIdDetail;
	  	obj.trainingId = data.trainingId;
	  	obj.farmIdDetail = data.farmIdDetail;
	  	obj.farmId = data.farmId;
		obj.assignedDate = data.assignedDate;
		obj.assignedDateCalendar = { date: moment(data.assignedDate).format('DD/MM/YYYY') };
		obj.allUserAssigned = data.allUserAssigned;
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
				this.assignedDate = this.convertCalToDate(this.assignedDateCalendar);
        return this;
    }
}
