import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageAdminTrainingService } from './manage-admin-training.service';

@Injectable({
    providedIn: 'root'
})
export class ManageAdminTrainingManager extends BaseManager {

    constructor(protected manageAdminTrainingService: ManageAdminTrainingService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageAdminTrainingService, loadingService, toastService);
    }
}
