<div class="error-message" *ngIf="!field1 && field && field.invalid && onClickValidation && !customErrorMessage">
    <div *ngIf="field?.errors?.required">
        <div *ngIf="type && type==='FILE_UPLOAD'">
            {{"COMMON.REQUIRED_FILE_VALIDATION_MESSAGE" | translate}}
        </div>
        <div *ngIf="!type || type!=='FILE_UPLOAD'">
            {{"COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate}}
        </div>
    </div>
    <div *ngIf="field?.errors?.email">
        {{"COMMON.REQUIRED_EMAIL_VALIDATION_MESSAGE" | translate}}
    </div>
    <div *ngIf="field?.errors?.pattern">
        {{"COMMON.REQUIRED_PATTERN_VALIDATION_MESSAGE" | translate}}
    </div>
    <div *ngIf="field?.errors?.mobile">
        {{"COMMON.REQUIRED_MOBILE_VALIDATION_MESSAGE" | translate}}
    </div>
    <div *ngIf="field?.errors?.mask">
        {{"COMMON.REQUIRED_MASK_VALIDATION_MESSAGE" | translate}} {{field?.errors?.mask.requiredMask}}
    </div>
    <div *ngIf="field?.errors?.incorrect">
        {{"COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate}}
    </div>
    <div *ngIf="field?.errors?.year">
        {{"COMMON.YEAR_INPUT_VALIDATION_MESSAGE" | translate}}
    </div>
    <div *ngIf="field?.errors?.month">
        {{"COMMON.MONTH_INPUT_VALIDATION_MESSAGE" | translate}}
    </div>
    <div *ngIf="field?.errors?.['minlength'] || field?.errors?.customMin">
        {{"COMMON.REQUIRED_INPUT_MIN_VALIDATION_MESSAGE" | translate}} {{field?.errors?.minlength?.requiredLength}}
    </div>
    <div *ngIf="field?.errors?.['maxlength'] || field?.errors?.customMax">
        {{"COMMON.REQUIRED_INPUT_MAX_VALIDATION_MESSAGE" | translate}} {{field?.errors?.max?.max}}
    </div>
    <div *ngIf="field?.errors?.exists">
        Farm code already exists
    </div>
</div>
<div class="error-message"
    *ngIf="onClickValidation && field && field.valid && comparableField && field.value !== comparableField.value">
    Password does not match
</div>
<div class="error-message" *ngIf="onClickValidation && (field && field.invalid) && (field1 && field1.invalid)">
    {{optionalCustomErrorMessage}}
</div>
<div class="error-message" *ngIf="customErrorMessage !=null && onClickValidation">
    {{customErrorMessage}}
</div>
<div class="error-message"
    *ngIf=" onClickValidation && (field?.name == 'email' || field?.name == 'phone') && !field?.value && (field1 && (!field1.value || field1.invalid))">
    Email or Phone Number required
</div>
<!-- <div class="error-message" *ngIf="onClickValidation && field?.invalid">

    <div *ngIf="field && field.name == 'email' &&  field.errors?.pattern">
        {{"COMMON.REQUIRED_EMAIL_VALIDATION_MESSAGE" | translate}}
    </div>
    <div
        *ngIf="field && field.name == 'phone' &&  (field.errors?.maxlength || field.errors?.minlength || field.errors?.pattern)">
        {{"COMMON.REQUIRED_MOBILE_VALIDATION_MESSAGE" | translate}}
    </div>
</div> -->
<!-- Custom password validation errors -->
<div *ngIf="field?.errors?.twoCriteria">
    Password must contain at least two of the following: numbers, lowercase letters, uppercase letters, or special
    characters.
</div>
<div *ngIf="field?.errors?.firstLetterCapital">
    The first letter of the password must be capitalized.
</div>