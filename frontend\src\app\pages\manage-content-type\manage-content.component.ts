import { Component, OnD<PERSON>roy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { ManageContentManager } from './manage-content.manager';
import { Category } from '../../models/category';
import { Router } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { UsersService } from 'src/app/services/users.service';
import AOS from 'aos';
import { ManageContentService } from './manage-content.service';
import { Content } from 'src/app/models/content';


declare const $: any;
declare var bootstrap: any;

@Component({
	selector: 'app-manage-content',
	templateUrl: './manage-content.component.html',
	styleUrls: ['./manage-content.component.scss']
})
export class ManageContentComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {

	selectedFarmsClone: any = [];
	assignProgramModal: any;
	farmIds: any = [];
	userIds: any = [];
	onClickValidation: boolean;

	constructor(protected manageContentManager: ManageContentManager, protected toastService: ToastService,
		protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
		protected router: Router, public commonUtil: CommonUtil, private usersService: UsersService, private manageContentService: ManageContentService) {
		super(manageContentManager, commonService, toastService, loadingService, router);
	}

	ngOnInit() {
		this.request.loadEditPage = false;
		this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
		this.records = new Array<Content>();
		this.init();
	}

	ngAfterViewInit() {
	}

	onItemSelection(record: any) {
		this.onAssociatedValueSelected(record);
	}

	onCancel() {
		this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
	}

	onNewRecord() {
		if (!this.isPlusButton) {
			if (this.filterParam) {
				this.router.navigate(['/dashboard/manage-content/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
			} else {
				this.router.navigate(['/dashboard/manage-content/edit/0']);
			}
			return;
		}
		this.request.loadEditPage = true;
	}

	removeSuccess() {
		this.onCancel();
	}


	ngOnDestroy() {
		this.clean();
	}

	loadDetailPage(recordId) {
		this.selectedId = recordId;
		setTimeout(() => {
			$('#manageContentDetailPage').appendTo('body').modal('show');
			$('#manageContentDetailPage').on('hidden.bs.modal', () => {
				setTimeout(() => {
					this.selectedId = undefined;
				});
			});
		}, 500);
	}

	search($event) {
		const value = ($event.target as HTMLInputElement).value;
		this.filterParam.searchText = (value && value != '') ? value.trim() : null;
		this.refreshRecord();
	}

	editRecord(id: any) {
		this.router.navigate(['/dashboard/manage-content/edit/' + id])
	}

	removeNullDataFromObject(obj: any) {
		for (var propName in obj) {
			if (obj[propName] === null || obj[propName] === undefined) {
				delete obj[propName];
			}
		}
		return obj
	}
}
