import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Users } from './users';
import { Training } from './training';
import { Farm } from './farm';
import { TrainingCategoryMapping } from './trainingcategorymapping';
import { TrainingSubCategoryMapping } from './trainingsubcategorymapping';
export class UserApprovedAndRejectedTraining extends BaseModel {

				tenantId: number;
			  	slug: string;
			  	userIdDetail: Users;
			  	userId: string;
			  	trainingIdDetail: Training;
			  	trainingId: string;
			  	status: string;
			  	userVideoUrl: string;
			  	assignedDate: Date;
			  	assignedDateCalendar: any;
			  	farmIdDetail: Farm;
			  	farmId: string;
				trainingCategoryMappingList: TrainingCategoryMapping[];
				trainingSubCategoryMappingList: TrainingSubCategoryMapping[];

    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
    }

   static fromResponse(data: any) : UserApprovedAndRejectedTraining {
		const obj = new UserApprovedAndRejectedTraining();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
	  	obj.userIdDetail = data.userIdDetail;
	  	obj.userId = data.userId;
	  	obj.trainingIdDetail = data.trainingIdDetail;
	  	obj.trainingId = data.trainingId;
		obj.status = data.status;
		obj.userVideoUrl = data.userVideoUrl;
		obj.assignedDate = data.assignedDate;
		obj.assignedDateCalendar = { date: moment(data.assignedDate).format('DD/MM/YYYY') };
	  	obj.farmIdDetail = data.farmIdDetail;
	  	obj.farmId = data.farmId;
		obj.trainingCategoryMappingList = data.trainingCategoryMappingList;
		obj.trainingSubCategoryMappingList = data.trainingSubCategoryMappingList;
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
				this.status = this.trimMe(this.status);
				this.userVideoUrl = this.trimMe(this.userVideoUrl);
				this.assignedDate = this.convertCalToDate(this.assignedDateCalendar);
        return this;
    }
}
