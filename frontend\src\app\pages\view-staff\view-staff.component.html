<div class="view-staff container loadMore" scrollTracker (scrollingFinished)="onScrollingFinished()">
  <div *ngFor="let staff of records" class="staff-farm-assign mb-2">
    <div class="row mt-1">
      <div class="col-8 p-0">
        <div class="row">
          <div class="col-12 text-left">
            <h6 class="mt-3 fw-bold">{{staff?.userIdDetail?.fullName}}</h6>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-6 text-left pe-0">
            <h6 class="opacity-50">Location</h6>
            <h6 class="fw-bold lh-1">{{staff?.userIdDetail?.location}}</h6>
          </div>
          <div class="col-6 text-left">
            <h6 class="opacity-50">Phone No</h6>
            <p class="fw-bold lh-1">{{staff?.userIdDetail?.phoneNumber}}</p>
          </div>
        </div>
      </div>
      <div class="col-4 d-flex align-items-center justify-content-center">
        <div class="profile-image-container">
          <img
            [src]="staff?.userIdDetail?.profileImageUrl ? staff?.userIdDetail?.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="loadMore" class="mt-4">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>

