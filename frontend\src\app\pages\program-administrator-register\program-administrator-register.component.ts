import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { LanguageManager } from '../language/language.manager';
import { CommonUtil } from 'src/app/shared/common.util';
import { RestResponse } from 'src/app/shared/auth.model';
import { AccountService } from 'src/app/services/account.service';
import { UsersService } from 'src/app/services/users.service';
import { Users } from 'src/app/models/users';
import { Content } from '@angular/compiler/src/render3/r3_ast';

@Component({
  selector: 'app-program-administrator-register',
  templateUrl: './program-administrator-register.component.html',
  styleUrls: ['./program-administrator-register.component.scss']
})
export class ProgramAdministratorRegisterComponent implements OnInit {
  onClickValidation: boolean = false;
  user: Users;
  languages: any = [];
  dropdownSettings = {};
  selectedFarms: any = [];
  comparableField: any;
  buttonName: string = "CREATE ACCOUNT";
  selectedCurrentType: string;
  passwordFieldType: any;
  confirmPasswordFieldType: any;
  userId:any;
  contentTypes: any[] = [
    { id: 1, title: 'integrax' },
    { id: 2, title: 'bc' },
    { id: 3, title: 'anotherOption' }
  ];
  filteredContentTypes: any[];

  constructor(private usersService: UsersService, private loadingService: LoadingService, private toastService: ToastService, private accountService: AccountService,
    private languageManager: LanguageManager, private router: Router, private titleService: Title) { }

  async ngOnInit() {
    this.user = new Users();
    this.userId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.passwordFieldType = "password";
    this.confirmPasswordFieldType = "password";
    this.titleService.setTitle("Create new account");
    // this.languages = await this.languageManager.fetchAllData(null);
    this.setDropdownSettings();
    this.filteredContentTypes = this.contentTypes.filter(type =>
      ['integrax', 'bc'].includes(type.title)
    )
    // await this.fetchUserDetailsRecord(this.userId);
  }

  hasError(event: any) {
  }
  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }
  selectContentType(event: any) {
    this.selectedCurrentType = event.title;
  }

  telInputObject(event: any) {
    event.setCountry('sg');
  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  async save(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return;
    }

    if (this.comparableField && this.comparableField !== this.user.password) {
      this.onClickValidation = true;
      return false
    }
    this.buttonName = "PLEASE WAIT..."
    const { isActive, isDeleted, ...rest } = this.user

    this.loadingService.show();
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.accountService.programAdminRegister({ ...rest });

      this.loadingService.hide();
      if (!response.status) {
        this.buttonName = "CREATE ACCOUNT"
        this.toastService.error(response.message);
        return;
      }
      this.router.navigate(['/verify-otp'], { queryParams: { uc: response.data.uniqueCode, isProgramAdmin: true } })
      this.toastService.success(response.message);
    } catch (e) {
      this.buttonName = "CREATE ACCOUNT"
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  async fetchFarmOnCompanyCode(companyCode) {
    if (CommonUtil.isNullOrUndefined(companyCode) || companyCode == null || companyCode == "") {
      return;
    }
    // this.fetchFarms(companyCode);
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  eyeConfirmPassword() {
    if (this.confirmPasswordFieldType == "password") {
      this.confirmPasswordFieldType = "text";
    } else {
      this.confirmPasswordFieldType = "password";
    }
  }

  async fetchUserDetailsRecord(userId) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.accountService.getUserProfileDetail(userId).toPromise();
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.user = response.data;

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }


}
