import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ManageCourseService extends BaseService {
  strUserId: string;
  strProgramAdmin: string;

  constructor(public http: HttpClient) {
    super(http, '/api/course', '/api/courses');
  }

  getProgramUserId(id: string) {
    this.strProgramAdmin = id;
  }

  getUserId(id: string) {
    this.strUserId = id;
  }

  saveCoursePart(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/coursepart', data);
  }
  updateCoursePart(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/coursepart', data);
  }

  updatePublish(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/course', data);
  }

  getisPublish(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/ispublish/courses', filterParam);
  }

  getProgramCourse(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/account/active/program/admins', filterParam);
  }

  changeOrder(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/reorder/coursepart/trainings', data);
  }

  getsCourseDetails(id: string): Observable<RestResponse> {
    return this.getRecord('/api/course/' + id);
  }

  getCourseUserRecords(data: any): Promise<RestResponse> {
    return this.getRecords('/api/courses', data);
  }

  // GET COURSES BY USER ID
  getCourseUserById(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/courses/getByUserId' + this.strUserId, filterParam);
  }

  //author data 
  fetchLearningSeriesAuthorBased(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/author/learningseries', filterParam);
  }

  getTrainingFilterData(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/filter/data', null);
  }

  fetchAvailableCourses(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/available/farm/courses', filterParam);
  }

  getAvailablePublishedCourses(filterparam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/available/ispublish/courses', filterparam);
  }

}

