<div class="loading-link-cls" *ngIf="!isUniqueCode">
  <img src="/assets/images/loading-image.gif" /><span class="ms-2">{{validLinkMessage}}</span>
</div>
<div *ngIf="isUniqueCode" class="site-login-page">
  <div class="row g-0">
    <div class="col-12 col-lg-5 col-xl-3 col-xxl-3 offset-lg-1 offset-xl-1 offset-xxl-2 login-left-side-section">
      <div class="login-form-section">
        <div class="margin-bottom-40">
          <img src="/assets/images/logo.svg" class="img-fluid" width="200">
        </div>
        <h2>Set New Password</h2>
        <!-- <p>There are many variations of passages of the majority</p> -->
        <div class="clearfix"></div>
        <form class="login-form" #recoverForm="ngForm" novalidate="novalidate">
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input class="form-control" type="password" placeholder="New Password" name="password" #password="ngModel"
                [ngClass]="{'is-invalid':!password.valid && onClickValidation}" [(ngModel)]="data.password"
                required="required" autofocus>
              <label for="useUserName"> {{"New Password" | translate}}</label>
            </div>
            <app-validation-message [field]="password" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input class="form-control" type="password" placeholder="Confirm Password" name="confirmPassword"
                [ngClass]="{'is-invalid':(!confirmPassword.valid || password.value != confirmPassword.value) && onClickValidation}"
                #confirmPassword="ngModel" [(ngModel)]="data.confirmPassword" required="required">
              <label for="useUserName"> {{"CONFIRM PASSWORD" | translate}}</label>
            </div>
            <app-validation-message [field]="confirmPassword" [comparableField]="password"
              [onClickValidation]="onClickValidation"></app-validation-message>
          </div>
          <div class="clearfix"></div>
          <div class="col-12 margin-bottom-40">
            <button [disabled]="changePasswordButtonDisabled" class="btn btn-secondary site-button large-button full-width uppercase-text"
              (click)="resetPassword(recoverForm.form.valid)">
              {{"RECOVER PASSWORD" | translate}}
            </button>
          </div>
        </form>
      </div>
    </div>
    <div class="col-12 col-lg-7 col-xl-8 col-xxl-7 forgot-right-side-section">
    </div>
  </div>
</div>

