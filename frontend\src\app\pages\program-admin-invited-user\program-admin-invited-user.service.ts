import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminInvitedService extends BaseService {
    userId: string;

    constructor(public http: HttpClient) {
        super(http, '/api/account/invited/program/admin', '/api/account/invited/program/admins');
    }

    getEnrolledgCourse(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/account/invited/program/admins', filterParam);
    }

    removeUserAccess(id: string): Promise<RestResponse> {
        return this.removeRecord('/api/account/admin/program/' + id)
    }

    // resendInviteData(id: string): Promise<RestResponse> {
    //     return this.updateRecord(' /api/account/admin/user/' + '$id' / resend / confirmation / email, null);
    // }
    resendInviteData(id: string): Observable<RestResponse> {
        return this.getRecord(`/api/account/admin/user/${id}/resend/confirmation/email`);
    }

}

