export class VideoTraining {
  id: string;
  title: string;
  description: string;
  contentTypeDetail: ContentTypeDetail;
  learningSeriesDetail: LearningSeriesDetail;
  url: string;
  time: number;
  publishDate: Date;
  thumbnailImageUrl: string;
  isViewed: number;
  userDetails: UserDetails;
  totalCount: number;

  constructor(data: any) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.contentTypeDetail = new ContentTypeDetail(data.contentTypeDetail);
    this.learningSeriesDetail = new LearningSeriesDetail(data.learningSeriesDetail);
    this.url = data.url;
    this.time = data.time;
    this.publishDate = new Date(data.publishDate);
    this.thumbnailImageUrl = data.thumbnailImageUrl;
    this.isViewed = data.isViewed;
    this.userDetails = new UserDetails(data.userDetails);
    this.totalCount = data.totalCount;
  }
}

export class ContentTypeDetail {
  id: string;
  title: string;
  totalCount: number;

  constructor(data: any) {
    this.id = data.id;
    this.title = data.title;
    this.totalCount = data.totalCount;
  }
}

export class LearningSeriesDetail {
  id: string;
  title: string;
  description: string;
  totalCount: number;

  constructor(data: any) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.totalCount = data.totalCount;
  }
}

export class UserDetails {
  programName: string;
  authorName: string;
  programProfileImage: string;
  id: string;

  constructor(data: any) {
    this.programName = data?.programName;
    this.authorName = data?.authorName;
    this.programProfileImage = data?.programProfileImage;
    this.id = data?.id;
  }
}
