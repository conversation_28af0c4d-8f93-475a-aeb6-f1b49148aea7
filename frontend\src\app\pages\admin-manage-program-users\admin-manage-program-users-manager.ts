import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { AdminManageProgramUserService } from './admin-manage-program-users-service';

@Injectable({
    providedIn: 'root'
})
export class AdminManageProgramUserManager extends BaseManager {

    constructor(protected adminManageProgramUserService: AdminManageProgramUserService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(adminManageProgramUserService, loadingService, toastService);
    }
}
