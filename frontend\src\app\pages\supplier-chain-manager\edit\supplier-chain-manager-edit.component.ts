import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { BaseEditComponent } from "src/app/config/base.edit.component";
import { Users } from "src/app/models/users";
import { LoadingService } from "src/app/services/loading.service";
import { RestResponse } from "src/app/shared/auth.model";
import { AuthService } from "src/app/shared/auth.services";
import { CommonService } from "src/app/shared/common.service";
import { CommonUtil } from "src/app/shared/common.util";
import { ToastService } from "src/app/shared/toast.service";
import { LanguageManager } from "../../language/language.manager";
import { SupplierChainManager } from "../supplier-chain-manager.manager";

@Component({
    selector: 'app-supplier-chain-manager-edit',
    templateUrl: './supplier-chain-manager-edit.component.html',
    styleUrls: ['./supplier-chain-manager-edit.component.scss']
})

export class SupplierChainManagerEditComponent extends BaseEditComponent implements OnInit {
    supplier: Users;
    languages: any = [];
    profileImageLoader: boolean = false;
    uploader: any;

    constructor(protected route: ActivatedRoute, protected supplierChainManager: SupplierChainManager, protected toastService: ToastService,
        protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
        protected translateService: TranslateService, public commonUtil: CommonUtil, private languageManager: LanguageManager) {
        super(supplierChainManager, commonService, toastService, loadingService, route, router, translateService);
    }

    ngOnInit() {
        this.supplier = new Users();
        this.supplier.isActive = true;
        this.setRecord(this.supplier);
        this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
        this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
        this.init();
    }

    onFetchCompleted() {
        this.supplier = Users.fromResponse(this.record);
        this.setRecord(this.supplier);
    }

    async fetchAssociatedData() {
        this.languages = await this.languageManager.fetchAllData(null);
    }

    telInputObject(event: any) {
        if (this.request.recordId == 0) {
            event.setCountry('sg');
            return
        }
        if (this.supplier.countryCode && this.supplier.phoneNumber) {
            event.setNumber('+' + this.supplier.countryCode + this.supplier.phoneNumber);
            return
        } else {
            event.setCountry('sg')
        }

    }

    onCountryChange(event) {
        this.supplier.countryCode = event.dialCode;
        this.supplier.countryCode = "+" + this.supplier.countryCode;
    }

    getNumber(event: any) {
    }

    hasError(event: any) {
    }

    uploadProfilePhoto(event: any) {
        if (event && event.target.files.length > 0) {
            this.profileImageLoader = true;
        }
    }

    fileValidationError(data: string, toastService: any) {
        this.profileImageLoader = false;
        toastService.error(data);
    }

    onUploadSuccess(data: any) {
        this.supplier.profileImageUrl = data.path;
        this.profileImageLoader = false;
    }

    onSaveSuccess(data: any) {
        this.navigate('/dashboard/teams');
    }

    async save(form: any) {
        this.onClickValidation = !form.valid;
        if (!form.valid) {
            return;
        }

        if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
            return;
        }
        let language = this.languages.filter(x=> x.name=="English");
        if(language.length > 0) {
            this.supplier.languageId = language[0].id;
        }
        try {
            this.loadingService.show();
            const method = this.request.isNewRecord ? 'save' : 'update';
            const response: RestResponse = await this.supplierChainManager[method](this.supplier);
            this.loadingService.hide();
            if (!response.status) {
                this.toastService.error(response.message);
                return;
            }
            this.onSaveSuccess(response.data);
        } catch (error) {
            this.loadingService.hide();
            this.toastService.error(error.message);
        }
    }

    omitSpecialChar(event) {
        var k;
        k = event.charCode;  //         k = event.keyCode;  (Both can be used)
        return ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32 || (k >= 48 && k <= 57));
    }

}