<div class="register-user-page">
  <div class="row px-0">
    <div class="register-user-header bg-image">
      <div class="col-12 offset-md-1 offset-lg-2 col-lg-8 px-2">
        <h1 class="register-user-heading">Create new account</h1>
        <p class="register-user-sub-heading">Login to continue</p>
      </div>
      <!-- <div class="col-6 col-lg-6">
                <div class="register-logo">
                    <img src="/assets/images/register-logo.png">
                </div>
            </div> -->
    </div>
  </div>
  <form autocomplete="off" #registerForm="ngForm" novalidate="novalidate" class="offset-lg-3 col-lg-6">
    <div class="row register-user-form">
      <div class="col-6 col-lg-6 mb-4">
        <div class="form-floating">
          <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text" name="firstName"
            #firstName="ngModel" [(ngModel)]="user.firstName" required="required" placeholder="First Name"
            [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
          <label for="floatingInput">{{"USERS.FirstName" | translate}}</label>
        </div>
        <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>
      <div class="col-6 col-lg-6 mb-4">
        <div class="form-floating">
          <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text" name="lastname"
            #lastName="ngModel" [(ngModel)]="user.lastName" required="required" placeholder="Last Name"
            [ngClass]="{'is-invalid':!lastName.valid && onClickValidation}">
          <label for="floatingInput">Last Name</label>
        </div>
        <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>
      <div class="col-6 col-lg-6 mb-4">
        <div class="form-floating">
          <input autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
            (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
            (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="user.phoneNumber"
            [ngClass]="{'is-invalid':(phone.invalid || !phone.value) && onClickValidation && (email.invalid || !email.value)}" 
            [required]="!email.value || !email.valid"
             placeholder="Phone Number"
             value=""
            minlength="7" maxlength="12" pattern="^[0-9]+$" (countryChange)="onCountryChange($event)">
          <!-- <label for="floatingInput" class="mobile_number_label">Mobile Number</label> -->
        </div>
        <app-validation-message [field]="phone" [field1]="email" [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>
      <div class="col-6 col-lg-6 mb-4">
        <div class="form-floating">
          <input autocomplete="off" class="form-control" type="email" name="email" 
            (hasError)="hasError($event)" 
             #email="ngModel" [(ngModel)]="user.email"
            [ngClass]="{'is-invalid':(email.invalid || !email.value) && onClickValidation  && (phone.invalid || !phone.value)}" 
            placeholder="Email Address"
            [required]="!phone.value || !phone.valid"
            value=""
             pattern="^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$" >
             <label for="floatingInput">Email Address</label>

        </div>
        <app-validation-message [field]="email" [field1]="phone" [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>
      <div class="col-12 col-lg-12 mb-4">
        <div class="form-floating">
          <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text" name="location"
            #location="ngModel" [(ngModel)]="user.location" required="required" placeholder="Location"
            [ngClass]="{'is-invalid':!location.valid && onClickValidation}">
          <label for="floatingInput">{{"USERS.Location" | translate}}</label>
        </div>
        <app-validation-message [field]="location" [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>

    

      <div class="col-12 mb-4">
        <div class="form-floating">
          <input type="password" class="form-control" name="password" id="floatingPassword" placeholder="Password"
            #password="ngModel" [(ngModel)]="user.password" required="required" autocomplete="new-password"
            [ngClass]="{'is-invalid':!password.valid && onClickValidation}">
          <label for="floatingPassword">Password</label>
          <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-eye"
                    viewBox="0 0 16 16" (click)="eyePassword()" *ngIf="data.password && data.password.length>0">
                    <path
                        d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
                    <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
                </svg> -->
        </div>
        <app-validation-message [field]="password" [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>
      <div class="col-12 mb-4">
        <div class="form-floating">
          <div class="form-floating">
            <input [(ngModel)]="comparableField" #confirmPassword="ngModel"
              [ngClass]="{'is-invalid':(!confirmPassword.valid || password.value != confirmPassword.value) && onClickValidation}"
              required="required" name="confirmPassword" type="password" class="form-control"
              id="floatingConfirmPassword" placeholder="Password">
            <label for="floatingConfirmPassword">Confirm Password</label>
          </div>
        </div>
        <app-validation-message [field]="confirmPassword" [comparableField]="password"
          [onClickValidation]="onClickValidation">
        </app-validation-message>
      </div>

      
      
      <div class="col-12 col-lg-12 mb-4">
        <button [disabled]="buttonName == 'PLEASE WAIT...' ? true : false" (click)="save(registerForm.form.valid)"
          class="btn bg-dark w-100 fs-6 p-3 btn-lg text-light">{{buttonName}}</button>
      </div>
    </div>
  </form>
</div>