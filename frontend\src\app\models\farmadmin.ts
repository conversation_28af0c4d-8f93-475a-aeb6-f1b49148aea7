import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { TranslateService } from '@ngx-translate/core';
import { UserFarmDetail } from './userfarmdetail';

export class FarmAdmin extends BaseModel {
	phoneNumber: string;
	user: string;
	email: string | null;
	fullName: string;
	firstName: string;
	lastName: string;
	countryCode: string;
	dialCode: string
	farmIds?: string[];
	userFarmDetail: Partial<UserFarmDetail>[] = [];
	activeFarmAdminCount: number;
	inActiveFarmAdminCount: number;
	invitedFarmAdminCount: number;

	constructor() {
		super();
		this.userFarmDetail = new Array<UserFarmDetail>();
	}

	isValidAccountSettingRequest(form: any) {
		if (this.isNullOrUndefinedAndEmpty(this.firstName) || this.firstName.trim() === '') {
			form.controls.firstName.setErrors({ invalid: true });
			return false;
		}
		if (this.isNullOrUndefinedAndEmpty(this.lastName) || this.lastName.trim() === '') {
			form.controls.lastName.setErrors({ invalid: true });
			return false;
		}
		if (this.isNullOrUndefinedAndEmpty(this.phoneNumber) || this.phoneNumber.trim() === '') {
			form.controls.phoneNumber.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	forRequest() {
		this.phoneNumber = this.trimMe(this.phoneNumber);
		this.email = this.trimMe(this.email);
		this.fullName = this.trimMe(this.fullName);
		this.firstName = this.trimMe(this.firstName);
		this.lastName = this.trimMe(this.lastName);
		return this;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.firstName)) {
			form.controls.name.setErrors({ invalid: true });
			return false;
		}

		if (this.isNullOrUndefinedAndEmpty(this.lastName)) {
			form.controls.name.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	static fromResponse(data: any): FarmAdmin {
		const farmAdmin = new FarmAdmin();
		farmAdmin.id = data.id;
		farmAdmin.phoneNumber = data.phoneNumber;
		farmAdmin.user = data.user;
		farmAdmin.email = data.email;
		farmAdmin.fullName = data.fullName;
		farmAdmin.firstName = data.firstName;
		farmAdmin.lastName = data.lastName;
		farmAdmin.isActive = data.isActive;
		farmAdmin.isDeleted = data.isDeleted;
		farmAdmin.userFarmDetail = data.userFarmDetail;
		farmAdmin.activeFarmAdminCount = data.activeFarmAdminCount;
		farmAdmin.inActiveFarmAdminCount = data.inActiveFarmAdminCount;
		farmAdmin.invitedFarmAdminCount = data.invitedFarmAdminCount;

		return farmAdmin;
	}
}
