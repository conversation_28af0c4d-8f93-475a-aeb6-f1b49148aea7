<div data-aos="fade-up" data-aos-duration="1000" class="admin-dasboard container-fluid"
    *ngIf="!authService.hasRole(['ROLE_PROGRAM_ADMINISTRATOR'])">
    <div class="row d-flex">
        <div class="col-12 col-sm-4 col-lg-5 col-xl-4 col-xxl-2 col-md-4 d-flex-basis">
            <a (click)="navigateToTrainingWithPublishedFilter()" style="cursor: pointer;">
                <div class="card bg-color mt-4">
                    <div class="card-logo bg-light">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/teaching.svg" alt=""
                            class="img-fluid">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/cross.svg" alt=""
                            class="img-fluid cross-icon">
                    </div>
                    <p class="pt-4">Total Published Training</p>
                    <h6 class="numeric">{{dashboardData?.totalPublishedTraining}}</h6>
                    <a>MORE INFO<i class="bi bi-arrow-right-square mx-2 "></i></a>
                </div>
            </a>
        </div>
        <!-- <div class="col-12 col-lg-5 col-xl-4 col-xxl-2 col-md-4 d-flex-basis" *ngIf="authService.isAdmin()">
            <a routerLink="/dashboard/moment">
                <div class="card bg-color mt-4">
                    <div class="card-logo bg-light">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/calendar.svg" alt=""
                            class="img-fluid">
                    </div>
                    <p class="pt-4">Moments Created</p>
                    <h6 class="numeric">{{dashboardData?.momentClosedCount}}/{{dashboardData?.momentCount}}</h6>
                    <a href="">MORE INFO<i class="bi bi-arrow-right-square mx-2 "></i></a>
                </div>
            </a>
        </div> -->
        <!-- <div class="col-12 col-lg-5 col-xl-4 col-xxl-2 col-md-4 d-flex-basis" *ngIf="authService.isSuperAdmin()">
            <a routerLink="/dashboard/teams">
                <div class="card bg-color mt-4">
                    <div class="card-logo bg-light">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/employees.svg" alt=""
                            class="img-fluid">
                    </div>
                    <p class="pt-4">Total Teams</p>
                    <h6 class="numeric">{{dashboardData?.adminCount}}</h6>
                    <a>MORE INFO<i class="bi bi-arrow-right-square mx-2 "></i></a>
                </div>
            </a>
        </div> -->
        <div class="col-12 col-lg-5 col-xl-4 col-xxl-2 col-md-4 d-flex-basis"
            *ngIf="!authService.hasRole(['ROLE_PROGRAM_ADMINISTRATOR'])">
            <a routerLink="/dashboard/users">
                <div class="card bg-color mt-4">
                    <div class="card-logo bg-light">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/employees.svg" alt=""
                            class="img-fluid">
                    </div>
                    <p class="pt-4">{{authService.isAdmin() || authService.isSuperAdmin() || authService.isFarmAdmin() ? 'Total Seekers' : 'Unassigned Staff'}}</p>
                    <h6 class="numeric">{{dashboardData?.totalUsers}}</h6>
                    <a>MORE INFO<i class="bi bi-arrow-right-square mx-2 "></i></a>
                </div>
            </a>
        </div>
        <!-- <div class="col-12 col-lg-5 col-xl-4 col-xxl-2 col-md-4 d-flex-basis" *ngIf="authService.isSuperAdmin()">
            <a routerLink="/dashboard/training-feed">
                <div class="card bg-color mt-4">
                    <div class="card-logo bg-light">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/newspaper.svg" alt=""
                            class="img-fluid">

                    </div>
                    <p class="pt-4">Training Feed</p>
                    <h6 class="numeric">{{dashboardData?.trainingFeedCount}}</h6>
                    <a>MORE INFO<i class="bi bi-arrow-right-square mx-2 "></i></a>
                </div>
            </a>
        </div> -->
        <!-- <div class="col-12 col-lg-5 col-xl-4 col-xxl-2 col-md-4 d-flex-basis" *ngIf="authService.isAdmin()">
            <a routerLink="/dashboard/request-site-change">
                <div class="card bg-color mt-4">
                    <div class="card-logo bg-light">
                        <img _ngcontent-nwg-c73="" src="/assets/images/icons/menu/manage-site-icon.svg" alt=""
                            class="img-fluid">
                    </div>
                    <p class="pt-4">Request Site Change</p>
                    <h6 class="numeric">{{dashboardData?.requestSiteChangePendingCount}}</h6>
                    <a>MORE INFO<i class="bi bi-arrow-right-square mx-2 "></i></a>
                </div>
            </a>
        </div> -->
    </div>
    <div class="row d-flex" *ngIf="!authService.hasRole(['ROLE_PROGRAM_ADMINISTRATOR'])">
        <div class="col-12 col-lg-12 col-xl-12 col-xxl-6 col-md-12 mt-5">
            <app-most-watch-trainings></app-most-watch-trainings>
        </div>
        <div class="col-12 col-lg-12 col-xl-12 col-xxl-6 col-md-12 mt-5">
            <app-most-watched-news-feed></app-most-watched-news-feed>
        </div>
        <div class="col-12 col-lg-12 col-xl-12 col-xxl-6 col-md-12 mt-5">
            <app-most-completed-trainings></app-most-completed-trainings>
        </div>
    </div>
</div>

<div class="custom-modal-backdrop" *ngIf="showFarmAdminPasswordPopup">
    <div class="custom-modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Set New Password</h5>
            </div>
            <div class="modal-body">
                <!-- <form (ngSubmit)="onPasswordChangeSubmit()" #passwordForm="ngForm">
                    <div class="col-12 col-md-12 mb-4">
                        <div class="form-floating">
                            <input class="form-control" [type]="newPasswordFieldType" name="newPassword"
                                #newPassword="ngModel" [(ngModel)]="farmAdminNewPassword" required minlength="6"
                                pattern="^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*?&]{6,}$" placeholder="New Password"
                                [ngClass]="{'is-invalid': !newPassword.valid && onClickValidation || (newPassword.errors?.pattern && onClickValidation)}">
                            <label for="newPassword">{{'COMMON.NEW_PASSWORD' | translate}}</label>
                            <i (click)="toggleNewPasswordVisibility()" *ngIf="newPasswordFieldType == 'text'"
                                class="bi bi-eye"></i>
                            <i (click)="toggleNewPasswordVisibility()" class="bi bi-eye-slash"
                                *ngIf="newPasswordFieldType == 'password'"></i>
                        </div>
                        <app-validation-message [field]="newPassword"
                            [onClickValidation]="onClickValidation"></app-validation-message>
                        <div *ngIf="newPassword.errors?.pattern && onClickValidation" class="text-danger">
                            Password must be at least 6 characters long and contain at least one letter and one number.
                        </div>
                    </div>

                    <div class="col-12 col-md-12">
                        <div class="form-floating">
                            <input class="form-control" [type]="confirmPasswordFieldType" name="confirmPassword"
                                #confirmPassword="ngModel" [(ngModel)]="farmAdminConfirmPassword" required="required"
                                placeholder="{{'COMMON.CONFIRM_PASSWORD' | translate}}"
                                [ngClass]="{'is-invalid':!confirmPassword.valid && onClickValidation || (passwordsDoNotMatch && onClickValidation)}" minlength="6">
                            <label for="confirmPassword">{{'COMMON.CONFIRM_PASSWORD' | translate}}</label>
                            <i (click)="toggleConfirmPasswordVisibility()" *ngIf="confirmPasswordFieldType == 'text'"
                                class="bi bi-eye"></i>
                            <i (click)="toggleConfirmPasswordVisibility()" class="bi bi-eye-slash"
                                *ngIf="confirmPasswordFieldType == 'password'"></i>
                        </div>
                        <app-validation-message [field]="confirmPassword"
                            [onClickValidation]="onClickValidation"></app-validation-message>
                        <div *ngIf="passwordsDoNotMatch && onClickValidation" class="text-danger">
                            Passwords do not match.
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary" [disabled]="passwordLoading">Change
                            Password</button>
                    </div>
                </form> -->

                <form class="login-form" #recoverForm="ngForm" novalidate="novalidate">
                    <div class="col-12 margin-bottom-30">
                        <div class="form-floating">
                            <input class="form-control" [type]="newPasswordFieldType" placeholder="New Password" name="password"
                                #password="ngModel" [ngClass]="{'is-invalid':(!password.valid || password.errors?.pattern) && onClickValidation}"
                                [(ngModel)]="farmAdminNewPassword" required autofocus
                                pattern="^(?=.*[A-Za-z])(?=.*\d).{6,}$">
                            <label for="useUserName"> {{"New Password" | translate}}</label>
                            <i (click)="toggleNewPasswordVisibility()" *ngIf="newPasswordFieldType == 'text'"
                                class="bi bi-eye"></i>
                            <i (click)="toggleNewPasswordVisibility()" class="bi bi-eye-slash me-2"
                                *ngIf="newPasswordFieldType == 'password'"></i>
                        </div>
                        <ng-container *ngIf="onClickValidation">
                            <div *ngIf="password.errors?.pattern" class="text-danger">
                                Password must be at least 6 characters long and contain at least one letter and one number.
                            </div>
                            <div *ngIf="!password.valid && !password.errors?.pattern && (password.touched || password.dirty)" class="text-danger">
                                Please provide a valid value.
                            </div>
                        </ng-container>
                    </div>
                    <div class="col-12 margin-bottom-30">
                        <div class="form-floating">
                            <input class="form-control" [type]="confirmPasswordFieldType" placeholder="Confirm Password"
                                name="confirmPassword"
                                [ngClass]="{'is-invalid':(!confirmPassword.valid || password.value != confirmPassword.value) && onClickValidation}"
                                #confirmPassword="ngModel" [(ngModel)]="farmAdminConfirmPassword" required>
                            <label for="useUserName"> {{"CONFIRM PASSWORD" | translate}}</label>
                            <i (click)="toggleConfirmPasswordVisibility()" *ngIf="confirmPasswordFieldType == 'text'"
                                class="bi bi-eye"></i>
                            <i (click)="toggleConfirmPasswordVisibility()" class="bi bi-eye-slash me-2"
                                *ngIf="confirmPasswordFieldType == 'password'"></i>
                        </div>
                        <app-validation-message [field]="confirmPassword" [comparableField]="password"
                            [onClickValidation]="onClickValidation"></app-validation-message>
                    </div>
                    <div class="clearfix"></div>
                    <div class="col-12 margin-bottom-40">
                        <button [disabled]="changePasswordButtonDisabled"
                            class="btn btn-secondary site-button large-button full-width uppercase-text"
                            (click)="onPasswordChangeSubmit(recoverForm.form.valid)">
                            {{"RECOVER PASSWORD" | translate}}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>