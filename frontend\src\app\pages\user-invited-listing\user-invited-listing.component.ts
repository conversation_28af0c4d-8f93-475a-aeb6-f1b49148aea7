import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { Course } from 'src/app/models/course';
import AOS from 'aos';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadingService } from 'src/app/services/loading.service';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { ManageCourseUserService } from '../program-users/manage-course-users-service';
import { ManageCourseService } from '../manage-course/manage-course-service';
import { UserInvitedService } from './user-invited.service';
import { UserInvitedManager } from './user-invited.manager';
import { ManageUserCourseDetail } from 'src/app/models/manageusercoursedetail';
import { RestResponse } from 'src/app/shared/auth.model';
import { FilterParam } from 'src/app/models/filterparam';
import * as moment from 'moment';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-user-invited-listing',
  templateUrl: './user-invited-listing.component.html',
  styleUrls: ['./user-invited-listing.component.scss']
})
export class UserInvitedListingComponent extends BaseListServerSideComponent implements OnInit {
  moment: any = moment;
  publishCourseList: any[];
  courseUser: ManageUserCourseDetail;
  errorMessage: string = '';
  userDetailsTabs: string = "profile";
  records: ManageUserCourseDetail[] = [];
  searchInvitedUser: any;

  @Input() filterParam: FilterParam;

  constructor(protected userInvitedManager: UserInvitedManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil,
    private userInvitedService: UserInvitedService,
    protected route: ActivatedRoute) {
    super(userInvitedManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.courseUser = new ManageUserCourseDetail();
    this.records = new Array<ManageUserCourseDetail>();
    // this.fetchInvitedCourseRecords();
    this.init();
  }

  clearErrorMessage() {
    this.errorMessage = '';
  }
  // reload data 
  // reloadData(data: any): Promise<RestResponse> {
  //   const promise = new Promise<RestResponse>(async (resolve, reject) => {
  //     try {
  //       const response: RestResponse = await this.userInvitedManager.update(data);
  //       if (!response.status) {
  //         resolve(response);
  //         return;
  //       }
  //       response.data = this.(response.data);
  //       resolve(response);
  //     } catch (error) {
  //       this.onFailure(error);
  //       reject(error);
  //     }
  //   });
  //   return promise;
  // }

  async reloadData(id: string) {
    try {
      const response: RestResponse = await this.userInvitedService.resendInviteData(id)
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  // open profile page
  openUserProfilePage() {
    this.router.navigate(['/dashboard/program-admin/profile-setting']);
  }

  async fetchInvitedCourseRecords() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userInvitedService.getInvitedCourse(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
      this.onFetchCompleted();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }
  onFetchCompleted() {
    this.hasDataLoad = true;
  }

  // removeSuccess() {
  //   this.refreshRecord();
  // }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/user/invited/' + id]);
  }

  removeNullDataFromObject(obj: any) {
    for (var propName in obj) {
      if (obj[propName] === null || obj[propName] === undefined) {
        delete obj[propName];
      }
    }
    return obj
  }
  removeSuccess() {
    this.onCancel();
  }

}
