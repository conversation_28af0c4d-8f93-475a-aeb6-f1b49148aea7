.staff-assigned-reel-training {
  text-align: center;
  max-width: 350px !important;
  overflow: hidden;
  height: auto;
}

.progress-ratio {
  position: absolute;
  font-size: 13px;
  color: #fff;
  line-height: 45px;
}

.spinner-border {
  --bs-spinner-width: 2.5rem !important;
  --bs-spinner-height: 2.5rem !important;
}

.upload-video-container {
  label {
    width: 50px;
    height: 50px;
  }
}

#assignTeam {
  .modal-dialog {
    max-width: 390px !important;

    .modal-content {
      border-radius: 0px;
      height: 370px;
      border: none !important;

      .modal-body {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #398BF7;
        padding: 0;

        .close-icon {
          top: 5px;
          right: 5px;
          background: #3f4660b5;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          position: absolute;

          i {
            color: #fff;
            font-size: 20px;
          }
        }

        .inner-container {
          width: 390px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .heading {
            font-size: 24px;
            color: #fff;
            margin-top: 15px;
          }

          .content {
            margin-top: 15px;
            width: 250px;
            text-align: center;
            color: #fff;
            font-size: 16px;
          }

          .close-btn {
            margin-top: 15px;
            background-color: #fff;
            border-color: #fff;
            color: #000;
            width: 110px;
            border-radius: 4px !important;
          }
        }
      }
    }
  }


}

@media (max-width: 600px) {
  .videos-reel-cls {
    position: fixed;
    top: 73px;
    max-width: 100% !important;
    bottom: 0;
    right: 0;
    left: 0;
    padding: 0 !important;
  }
}