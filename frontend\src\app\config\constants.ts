export const Constant = {
  CATEGORY_G<PERSON><PERSON>CODE_OPTIONS: [
    {
      id: '',
      name: 'Category.GROUPCODE_'
    },
  ],
  OS_VERSION: {
    Windows_OS: "Windows OS",
    Mac_OS: "Macintosh",
    Linux_OS: "Linux OS",
    Android_OS: "Android OS",
    IOS: "IOS"
  },
  MEDIA_TYPE: {
    VIDEO: "VIDEO",
    IMAGE: "IMAGE"
  },
  TRAINING_STATUS: {
    INPROGRESS: "INPROGRESS",
    COMPLETED: "COMPLETED",
    WAITING_FOR_APPROVAL: "WAITING_FOR_APPROVAL"
  },
  TRAINING_ACCESSIBILITY: {
    PUBLIC: "PUBLIC",
    RESTRICTED: "RESTRICTED",
  },
  LEARTNING_SERIES_ACCESSIBILITY: {
    PUBLIC: "PUBLIC",
    RESTRICTED: "RESTRICTED",
  },
  TRAINING_REQUIRED: {
    UPLOAD_REQUIRED: "UPLOADING REQUIRED",
    WATCH_REQUIRED: "WATCHING REQUIRED",
  },
  BROWSER_DETECT: {
    chrome: "chrome",
    firefox: "firefox",
    safari: "safari",
    opera: "opera",
    edge: "edge",
  },
  MOMENT_STATUS: {
    OPEN: "OPEN",
    CLOSED: "CLOSED",
    WAITING_FOR_APPROVAL: "WAITING_FOR_APPROVAL",
    ACTION_TAKEN: "ACTION_TAKEN"
  },
  languages: {
    English: "English",
    Bahasa: "Bhasha",
    Vietnamese: "Vietnamese"
  },
  MY_DATE_PICKER: {
    DATE_TYPE: 'dd/mm/yyyy'
  },
  VIEW_USER_MAPPING: {
    COMMON_ACCESS: {
      UserPage: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AccountSettingPage: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      }
    },
    STAFFASSIGNEDTRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_STAFF'],
        ENABLED_FOR_ROLE: ['ROLE_STAFF'],
      }
    },
    FARM_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      }
    },
    FARM_ADMIN_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      }
    },
    LANGUAGE_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      }
    },
    CATEGORY_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AssignButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      ShowAssignUsersButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      }
    },
    SUBCATEGORY_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN'],
      },
      AssignButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      ShowAssignUsersButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      }
    },
    USERFARM_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      }
    },
    MANAGETRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
    },
    TRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AssignButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      ShowAssignUsersButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      RestrictedUsers: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      RejectedLogs: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      }
    },
    USERASSIGNTRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      }
    },
    FARMASSIGNTRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      }
    },
    MOMENT_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      }
    },
    USERTRAININGSTATUS_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      }
    },
    SEEKER_ENROLLMENT_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      }
    },
    TRAININGFEED_ACCESS: {
      Page: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
      EditButton: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
      DeleteButton: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
    },
    USERSCONTACTLIST_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
    },
    PROGRAMADMINLIST_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
    },
    REQUESTSITECHANGE_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
    },
    STAFFPROFILE_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_STAFF'],
        ENABLED_FOR_ROLE: ['ROLE_STAFF'],
      },
    },
    DASHBOARD_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_PROGRAM_ADMINISTRATOR'],
      },
    },
    SUPPLIER_ACCESS: {
      Page: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
      AddButton: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
      EditButton: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
      DeleteButton: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      },
      DetailButton: {
        SHOW_TO_ROLE: [],
        ENABLED_FOR_ROLE: [],
      }
    },
    STAFF_ACCESS: {
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      }
    },
    ADMIN_MANAGE_PROGRAM_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
      },
    },
    MANAGE_COURSE_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
    },
    MANAGE_COURSE_ADMIN_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
    },
    MANAGE_COURSE_TRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
    },
    MANAGE_ADMIN_COURSE_TRAINING_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
    },
    MANAGE_COURSE_TRAINING_LIBRARY_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR'],
      },
    },
    MANAGE_USER_ACCESS: {
      Page: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      EditButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DeleteButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      DetailButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      AddTrainingButton: {
        SHOW_TO_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
      },
      FilterButton: {
        SHOW_TO_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
        ENABLED_FOR_ROLE: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
      },
      COURSETRAINING_STATUS: {
        COMPLETED: "COMPLETED",
        WAITING_FOR_APPROVAL: "WAITING_FOR_APPROVAL"
      },
    },
  },

  CONTENT_TRANSFER_REQUIRED: {
    EXISTING_PROGRAM_ADMIN: "EXISTING PROGRAM ADMIN",
    EXTERNAL_PROGRAM_ADMIN: "EXTERNAL PROGRAM ADMIN",
  },
  INVITE_USER_REQUIRED: {
    SELF_INVITE_USER: "SELF INVITE USER",
    OTHER_INVITE_USER: "OTHER INVITE USER",
  },

  MOMENT_OPTIONS: {
    RECURRING_OPTIONS: [
      { value: true, label: 'Recurring' },
      { value: false, label: 'Non-recurring' }
    ],
    TYPE_OPTIONS: [
      { value: 'COURSE', label: 'Course' },
      { value: 'TRAINING', label: 'Training' }
    ]
  },

  NOTIFICATION_TYPE: {
    NEW_STAFF: "TYPE_NEW_STAFF",
    NEW_MOMENT: "TYPE_NEW_MOMENT",
    UPDATE_MOMENT: "TYPE_UPDATE_MOMENT",
    USER_ENQUIRY: "TYPE_NEW_USER_ENQUIRY",
    REQUEST_SITE_CHANGE: "TYPE_REQUEST_SITE_CHANGE",
    UPDATED_REQUEST_SITE_CHANGE: "TYPE_SITE_CHANGE_REQUEST_UPDATED",
    NEW_TRAINING_ASSIGNED: "TYPE_NEW_TRAINING_ASSIGNED",
    TRAINING_APPROVED: "TYPE_TRAINING_APPROVED",
    TRAINING_REJECTED: "TYPE_TRAINING_REJECTED",
    TRAINING_COMPLETED: "TYPE_TRAINING_COMPLETED",
    NEW_PROGRAM_ASSIGNED: "TYPE_NEW_PROGRAM_ASSIGNED",
    NEW_COURSE_ASSIGNED: "TYPE_NEW_COURSE_ASSIGNED",
    MESSAGE_MOMENT_VIDEO_UPLOADED: "TYPE_MESSAGE_MOMENT_VIDEO_UPLOADED",
    TYPE_NEW_USER: "TYPE_NEW_USER",
    TYPE_PROGRAM_ADMINISTRATOR_INVITE_ACCEPT: "TYPE_PROGRAM_ADMINISTRATOR_INVITE_ACCEPT",
    TYPE_NEW_PROGRAM_ADMINISTRATOR: "TYPE_NEW_PROGRAM_ADMINISTRATOR",
    TYPE_UPLOADING_REQUIRED_TRAINING_UPLOADED: "TYPE_UPLOADING_REQUIRED_TRAINING_UPLOADED",
    TYPE_COURSE_COMPLETION: "TYPE_COURSE_COMPLETION",
    TYPE_USER_SEND_COURSE_REQUEST: "TYPE_USER_SEND_COURSE_REQUEST"
  },
  ROLES: {
    SUPER_ADMIN: "ROLE_SUPER_ADMIN",
    ADMIN: "ROLE_ADMIN",
    STAFF: "ROLE_STAFF",
    ROLE_PROGRAM_ADMINISTRATOR: "ROLE_PROGRAM_ADMINISTRATOR",
    FARM_ADMIN: "ROLE_FARM_ADMIN",
  },

  ROLES_LIST: [
    { id: 'ROLE_SUPER_ADMIN', name: 'Super Admin' },
    { id: 'ROLE_ADMIN', name: 'Admin' },
    { id: 'ROLE_STAFF', name: 'Staff' },
    { id: 'ROLE_PROGRAM_ADMINISTRATOR', name: 'Program Administrator' },
    { id: 'ROLE_FARM_ADMIN', name: 'Company Admin' }
  ],

  STATUS_OPTIONS: [
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' }
  ]
}

