import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CourseTrainingDetailBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const trainingId: string | null = route.paramMap.get("id");
    const courseId: string | null = route.paramMap.get("courseId");

    return of([
      {
        title: "Dashboard", link: "/dashboard/program-admin", active: false
      },
      {
        title: "Manage Course", link: "/dashboard/program-admin/courses", active: false
      },
      // {
      //   title: "Course Training Detail", link: "/dashboard/program-admin/course/training/detail/" + trainingId, active: true
      // }
    ])
  }


}
