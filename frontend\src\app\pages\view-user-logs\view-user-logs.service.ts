import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ViewUserLogsService extends BaseService {
    trainingId: string;

    constructor(public http: HttpClient) {
        super(http, '', '');
    }

    getUserId(id: string) {
        this.trainingId = id;
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/users-by-training', filterParam);
    }

    // getcourseTitle(id: string): Observable<RestResponse> {
    //     return this.getRecord('/api/course/title/' + id);
    // }

    approveOrRejectTraining(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/training/approve/reject', data);
    }

}

