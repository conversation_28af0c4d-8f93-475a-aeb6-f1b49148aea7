.swiper-container {
  background: black;
}
.swiper-slide {
  background: black !important;
}
.video-title-reel {
  padding-left: 15px;
  padding-right: 15px;
  text-align: left;
  p {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.video-credit-upload-icon {
  display: flex;
  margin-top: -15px;
  justify-content: space-between;
}

.back-button-reel-video {
  top: 10px;
  left: 10px;
  z-index: 2;
  img {
    width: 35px;
  }
}

.upload-video-container {
  padding-right: 15px;

  label {
    background: #057dfd;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    img {
      width: 25px;
      margin-top: 13px;
    }
    i {
      font-size: 23px;
    }
  }

  input {
    display: none;
  }
  p {
    font-size: 11px;
    padding-top: 6px;
  }
}

.video-credit-container {
  padding-left: 15px;
  p {
    font-size: 13px;
  }
}

.video-inner-content-container {
  position: absolute;
  z-index: 2;
  bottom: 0;
  width: 100%;
}

@media (min-width: 601px) {
  .swiper-container {
    height: 590px !important;
  }
}
.video-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100% !important;
  .video-container {
    max-width: 100%;
    border-radius: 4px;
    margin: 0 auto;
    height: 100% !important;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    video {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.play-button-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  pointer-events: none;

  .circle-play-b-cls {
    cursor: pointer;
    pointer-events: auto;
    z-index: 1;

    svg {
      width: 55px;
      fill: #fff;
      stroke: #fff;
      cursor: pointer;
      background-color: rgba(black, 0.2);
      border-radius: 50%;
      opacity: 0.9;
    }
  }
}
