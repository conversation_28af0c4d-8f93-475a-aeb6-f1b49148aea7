.register-user-header {
  background-image: url(/assets/images/register-user.png);
  background-repeat: no-repeat;
  height: 200px;
  background-position: top 25px right;
  background-size: 150px 140px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.register-user-form {
  padding-left: 8px !important;
  padding-right: 8px !important;
  .mobile_number_label {
    padding-left: 50px !important;
  }
  .iti {
    display: block !important;
    input {
      height: 61px;
    }
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.register-user-heading {
  font-size: 22px;
  font-weight: 700;
  margin-top: 10px;
}

.register-user-sub-heading {
  font-size: 16px;
  color: #263941;
}

// Medium devices (tablets, 768px and up)
@media (min-width: 768px) {
  .register-user-header {
    background-size: 195px 165px;
    background-position: top 50px right;
  }

  .register-user-heading {
    font-size: 55px;
    font-weight: 700;
    margin-top: 80px;
  }

  .register-user-sub-heading {
    font-size: 26px;
    color: #263941;
  }

  .register-user-form {
    padding-top: 100px;
    padding-left: 50px !important;
    padding-right: 50px !important;
  }
}

// Large devices (desktops, 992px and up)
@media (min-width: 992px) {
  .register-user-form {
    padding: 100px;
  }
}

// X-Large devices (large desktops, 1200px and up)
@media (min-width: 1200px) {
}

// XX-Large devices (larger desktops, 1400px and up)
@media (min-width: 1400px) {
}
