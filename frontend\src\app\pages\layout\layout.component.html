<div class="wrapper">
  <nav id="sidebar" class="collapse d-lg-block sidebar collapse bg-white">
    <div class="sidebar-logo-header">
      <!-- <a [routerLink]="[authService.isAdmin() ? '/dashboard' : '/staff-reel-trainings']"><img alt="Logo"
          src="/assets/images/logo.svg" class="img-fluid" /></a> -->
      <a href="/"><img alt="Logo" src="/assets/images/logo.svg" class="img-fluid" /></a>
    </div>
    <div class="user-info-section-container">
      <div class="user-info-section">
        <div class="user-info-body w-100">
          <div class="user-info-body-container w-100">
            <div class="user-info-body-inner-container w-100">
              <div class="d-flex flex-nowrap justify-content-between">
                <div>
                  <div class="user-name-text">{{authService.getUser()?.fullName}}

                  </div>
                  <p class="user-type-text">{{ roleName }}</p>
                </div>
                <a (click)="checkUserLogin()"><img src="/assets/images/icons/menu/setting-logo.svg" alt=""></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ul class="components accordion-menu" id="accordionSidenav">
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}"
        *ngIf="authService.isAccessible('SUPPLIER','Page')">
        <a [routerLink]="['/dashboard/teams']">
          <img src="/assets/images/icons/menu/employees-icon.svg"> <span>Team</span>
        </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}"
        *ngIf="authService.isAccessible('STAFFASSIGNEDTRAINING','Page')">
        <a [routerLink]="['/staff-assigned-training']">
          <img src="/assets/images/icons/menu/manage-traning-icon.svg"> <span>Assigned Training</span>
        </a>
      </li>
      <!-- program admin -->
      <!-- Manage Course Section -->
      <li routerLinkActive="active" *ngIf="authService.isAccessible('DASHBOARD','Page')"
        [routerLinkActiveOptions]="{exact: authService.isAccessible('DASHBOARD','Page') ? true : false}">
        <a [routerLink]="[
          authService.isProgramAdmin() ? '/dashboard/program-admin' :
          authService.isAccessible('DASHBOARD','Page') ? '/dashboard' :
          '/staff-reel-trainings']" (click)="collapseAllSections()">
          <img class="learning-logo me-2"
            [src]="authService.isAccessible('DASHBOARD','Page') ? '/assets/images/icons/menu/dashboard-icon.svg' : '/assets/images/icons/menu/traning-feed-icon.svg'">
          <span>{{authService.isAccessible('DASHBOARD','Page') ? 'Dashboard' : 'Training Feed'}}</span>
        </a>
      </li>

      <li *ngIf="!authService.isFarmAdmin() && (authService.isAccessible('ADMIN_MANAGE_PROGRAM', 'Page') || authService.isAccessible('FARM_ADMIN','Page') || authService.isAccessible('COMMON','UserPage') || authService.isProgramAdmin())"
        [class.parent-active]="
    activeSection === 'manageUserParent' ||
    activeSection === 'programAdmins' ||
    activeSection === 'companyAdmins' ||
    activeSection === 'manageSeekers' ||
    activeSection === 'manageProgramUser'">
        <a class="d-flex align-items-center parent" data-bs-toggle="collapse"
          (click)="setActiveSection('manageUserParent')" href="#collapseUser" role="button" aria-expanded="false"
          aria-controls="collapseUser">
          <img src="/assets/images/icons/menu/employees-icon.svg" alt="manage" class="learning-logo me-2 parent">
          <span>Manage User</span>
          <i id="courseIcon" class="bi bi-chevron-right ms-auto parent"></i>
        </a>
        <ul id="collapseUser" class="collapse list-unstyled" data-bs-parent="#accordionSidenav">
          <li class="sub-li" [class.active]="activeSection === 'programAdmins'" *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM', 'Page')">
            <a class="sub-a" (click)="setActiveSection('programAdmins')"
              [routerLink]="['/dashboard/program-admin-list']">
              <span>Program Admin</span>
            </a>
          </li>

          <li class="sub-li" [class.active]="activeSection === 'companyAdmins'"
            *ngIf="authService.isAccessible('FARM_ADMIN','Page')">
            <a class="sub-a" [routerLink]="['/dashboard/farm/admins']" (click)="setActiveSection('companyAdmins')">
              <span>{{'FarmAdmin.objName' | translate}}</span>
            </a>
          </li>

          <li class="sub-li" [class.active]="activeSection === 'manageSeekers'"
            *ngIf="authService.isAccessible('COMMON','UserPage')">
            <a class="sub-a" [routerLink]="['/dashboard/users']" (click)="setActiveSection('manageSeekers')">
              <span>Seeker</span>
            </a>
          </li>

          <li class="sub-li" [class.active]="activeSection === 'manageProgramUser'"
            *ngIf="authService.isProgramAdmin()">
            <a class="sub-a" [routerLink]="['/dashboard/program-admin/users']"
              (click)="setActiveSection('manageProgramUser'); collapseManageTraining()">
              <span>Seeker</span>
            </a>
          </li>
        </ul>
      </li>

      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isFarmAdmin() && authService.isAccessible('COMMON','UserPage')">
        <a class="d-flex align-items-center parent" [routerLink]="['/dashboard/users']" (click)="collapseAllSections()">
          <img src="/assets/images/icons/menu/employees-icon.svg" class="me-2"> <span>Manage Seekers</span>
        </a>
      </li>

      <li *ngIf="!authService.isFarmAdmin() && (authService.isAccessible('FARM','Page') || !authService.isProgramAdmin())"
        [class.parent-active]="activeSection === 'manageCompanyParent' || activeSection === 'manageFarm' ||
      activeSection === 'manageMoments'">
        <a class="d-flex align-items-center parent" data-bs-toggle="collapse"
          (click)="setActiveSection('manageCompanyParent')" href="#collapseCompany" role="button" aria-expanded="false"
          aria-controls="collapseCompany">
          <img src="/assets/images/icons/menu/farm-admin.svg" alt="manage" class="learning-logo me-2 parent">
          <span>Manage Company</span>
          <i id="courseIcon" class="bi bi-chevron-right ms-auto parent"></i>
        </a>
        <ul id="collapseCompany" class="collapse list-unstyled" data-bs-parent="#accordionSidenav">
          <li class="sub-li" [class.active]="activeSection === 'manageFarm'"
            *ngIf="authService.isAccessible('FARM','Page')">
            <a class="sub-a" [routerLink]="['/dashboard/farms']" (click)="setActiveSection('manageFarm')">
              <span>Company</span>
            </a>
          </li>

          <li class="sub-li" [class.active]="activeSection === 'manageMoments'" *ngIf="!authService.isProgramAdmin()">
            <a class="sub-a"
              [routerLink]="[authService.isFarmAdmin() || authService.isSuperAdmin() ? '/dashboard/moments' : '/staff-moments']"
              (click)="setActiveSection('manageMoments')">
              <span>Moment</span>
            </a>
          </li>
        </ul>
      </li>

      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isFarmAdmin()">
        <a class="d-flex align-items-center parent"
          [routerLink]="[authService.isFarmAdmin() || authService.isSuperAdmin() ? '/dashboard/moments' : '/staff-moments']"
          (click)="collapseAllSections()">
          <img src="/assets/images/icons/menu/calendar-icon.svg" class="me-2"> <span>Moments</span>
        </a>
      </li>

      <li
        *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','Page')"
        [class.parent-active]="activeSection === 'manageTraining' || activeSection === 'learningSeries' || activeSection === 'training'">
        <a class="d-flex align-items-center parent" data-bs-toggle="collapse"
          (click)="setActiveSection('manageTraining')" href="#collapseTraining" role="button" aria-expanded="false"
          aria-controls="collapseTraining">
          <img src="/assets/images/icons/menu/manage-traning-icon.svg" class="learning-logo me-2 parent">
          <span>Manage Trainings</span>
          <i id="trainingIcon" class="bi ms-auto"
            [ngClass]="{
              'bi-chevron-down': activeSection === 'manageTraining',
              'bi-chevron-right': activeSection !== 'manageTraining'
            }"></i>
        </a>
        <ul id="collapseTraining" class="collapse list-unstyled" data-bs-parent="#accordionSidenav">
          <li class="sub-li" [class.active]="activeSection === 'learningSeries'">
            <a class="sub-a" [routerLink]="['/dashboard/program-admin/manage-course-learning-series']"
              (click)="setActiveSection('learningSeries')">
              <span>Learning Series</span>
            </a>
          </li>
          <li class="sub-li" [class.active]="activeSection === 'training'">
            <a class="sub-a" [routerLink]="['/dashboard/program-admin/trainings']"
              (click)="setActiveSection('training')">
              <span>Training</span>
            </a>
          </li>
        </ul>
      </li>

      <li
        *ngIf="authService.isAccessible('MANAGE_COURSE','Page') && (authService.isAccessible('MANAGE_COURSE','Page'))"
        [class.parent-active]="activeSection === 'manageCourse' || activeSection === 'course' || activeSection === 'manageSeekerEnrollment'">
        <a class="d-flex align-items-center parent" data-bs-toggle="collapse" (click)="setActiveSection('manageCourse')"
          href="#collapseCourse" role="button" aria-expanded="false" aria-controls="collapseCourse">
          <img src="/assets/images/icons/menu/course-ims.svg" alt="manage" class="learning-logo me-2 parent">
          <span>Manage Courses</span>
          <i id="courseIcon" class="bi bi-chevron-right ms-auto parent"></i>
        </a>
        <ul id="collapseCourse" class="collapse list-unstyled" data-bs-parent="#accordionSidenav">
          <li class="sub-li" [class.active]="activeSection === 'course'" [routerLinkActiveOptions]="{exact: true}">
            <a class="sub-a" [routerLink]="['/dashboard/program-admin/courses']" (click)="setActiveSection('course')">
              <span>Course</span>
            </a>
          </li>

          <li class="sub-li" [class.active]="activeSection === 'manageSeekerEnrollment'"
            [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('SEEKER_ENROLLMENT', 'Page')">
            <a class="sub-a" [routerLink]="['/dashboard/seekers/enrolled']"
              (click)="setActiveSection('manageSeekerEnrollment')">
              <span>Seekers Enrollment</span>
            </a>
          </li>
        </ul>
      </li>
      <!-- Manage user program -->

      <!-- Training Library Section -->
      <li routerLinkActive="active" [class.parent-active]="activeSection === 'manageTrainingLibrary'"
        *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING_LIBRARY', 'Page') && (authService.isAccessible('MANAGE_COURSE_TRAINING_LIBRARY', 'Page'))">
        <a class="d-flex align-items-center parent" [routerLink]="['/dashboard/program-admin/training/library']"
          (click)="setActiveSection('manageTrainingLibrary'); collapseManageTraining()">
          <img src="/assets/images/icons/menu/books-medical.svg" class="me-2">
          <span>Training Library</span>
        </a>
      </li>
      <!-- Manage Admin Training Section -->
      <li
        [class.parent-active]="activeSection === 'manageTrainingAdmin' || activeSection === 'contentType' || activeSection === 'learningSeries' || activeSection === 'training'"
        *ngIf="authService.isAccessible('MANAGETRAINING','Page')">
        <a class="d-flex align-items-center parent" data-bs-toggle="collapse"
          (click)="setActiveSection('manageTraining')" href="#collapseTrainingAdmin" role="button" aria-expanded="false"
          aria-controls="collapseTrainingAdmin">
          <img src="/assets/images/icons/menu/manage-traning-icon.svg" class="learning-logo me-2 parent">
          <span>Manage Training</span>
          <i id="trainingIcon" class="bi bi-chevron-right ms-auto"
            [ngClass]="{'bi-chevron-down': activeSection === 'manageTraining', 'bi-chevron-right': activeSection !== 'manageTraining'}"></i>
        </a>
        <ul id="collapseTrainingAdmin" class="collapse list-unstyled" data-bs-parent="#accordionSidenav">
          <li class="sub-li" [class.active]="activeSection === 'contentType'">
            <a class="sub-a" [routerLink]="['/dashboard/manage-content']" (click)="setActiveSection('contentType')">
              <span>Content Type</span>
            </a>
          </li>
          <li class="sub-li" [class.active]="activeSection === 'learningSeries'">
            <a class="sub-a" [routerLink]="['/dashboard/manage-learning-series']"
              (click)="setActiveSection('learningSeries')">
              <span>Learning Series</span>
            </a>
          </li>
          <li class="sub-li" [class.active]="activeSection === 'training'">
            <a class="sub-a" [routerLink]="['/dashboard/training']" (click)="setActiveSection('training')">
              <span>Training</span>
            </a>
          </li>
        </ul>
      </li>

      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}"
        *ngIf="!authService.isProgramAdmin() && !authService.isFarmAdmin()">
        <a class="d-flex align-items-center parent" [routerLink]="['/dashboard/news-feed']"
          (click)="collapseAllSections()">
          <img src="/assets/images/icons/menu/newspaper.svg" class="me-2"> <span>Manage News Feed</span>
        </a>
      </li>
      <li *ngIf="authService.isAccessible('STAFFPROFILE','Page')" routerLinkActive="active"
        [routerLinkActiveOptions]="{exact: true}">
        <a [routerLink]="['/staff-profile']">
          <img src="/assets/images/icons/menu/staff-profile-icon.svg"> <span>Profile</span>
        </a>
      </li>
      <!-- <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="!authService.isSuperAdmin()">
        <a [routerLink]="[authService.isAdmin() ? '/dashboard/farm' : '/staff-assigned-sites']">
          <img src="/assets/images/icons/menu/manage-site-icon.svg"> <span>{{authService.isAdmin() ? 'Manage Sites' :
            'Assigned Sites'}}</span>
        </a>
      </li> -->
      <li *ngIf="authService.isAccessible('TRAININGFEED','Page')" routerLinkActive="active"
        [routerLinkActiveOptions]="{exact: true}">
        <a [routerLink]="['/dashboard/training-feed']">
          <img src="/assets/images/icons/menu/traning-feed-icon.svg"> <span>Training Feed</span>
        </a>
      </li>
      <li *ngIf="authService.isAccessible('USERSCONTACTLIST','Page')" routerLinkActive="active"
        [routerLinkActiveOptions]="{exact: true}">
        <a class="d-flex align-items-center parent" [routerLink]="['/dashboard/users-enquiry']"
          (click)="collapseAllSections()">
          <img src="/assets/images/icons/menu/user-enquiry.svg" style="width: 30px; margin-right: 3px;"> <span>Manage
            User
            Enquiry</span>
        </a>
      </li>

    </ul>
    <div class="logout-item">
      <a (click)="authService.logout()">
        <div class="logout-icon-container">
          <button class="logout-button"><i class="bi bi-box-arrow-left text-light fs-4"></i></button>
        </div>
        <span class="logout-button-content">{{"DASHBOARD.LOGOUT" | translate}}</span>
      </a>
    </div>
  </nav>
  <div id="content" [ngClass]="{'reel-height': router.url.indexOf('staff-reel-trainings') !== -1}">
    <div
      [ngClass]="{'reel-header': router.url.indexOf('staff-reel-trainings') !== -1, 'd-block': !authService.isAdmin() && !authService.isSuperAdmin(), 'd-flex align-items-center': authService.isAdmin() }"
      class="page-info-top-bar desktop">
      <div class="d-flex" [ngClass]="{'reelMobileClass': router.url.indexOf('staff-reel-trainings') !== -1}">
        <div class="d-flex position-relative">
          <i class="bi bi-list menu-toggle-icon sidebarCollapse"></i>
        </div>
        <!-- margin-left-33n -->
        <div class="reel-logo-header d-none page-name-container"
          [ngClass]="{'text-center w-100 page-name-staff-cls':!authService.isAdmin() && !authService.isSuperAdmin()}">
          <!-- <a [routerLink]="[authService.isAdmin() ? '/dashboard' : '/staff-reel-trainings']"><img alt="Logo"
              src="/assets/images/logo.svg" class="img-fluid" /></a> -->
          <a><img alt="Logo" src="/assets/images/logo.svg" class="img-fluid" /></a>
        </div>
        <div [ngClass]="{'text-center w-100 margin-left-33n page-name-staff-cls':!authService.isAdmin() && !authService.isSuperAdmin(),
        'hide-title-cls': router.url.indexOf('staff-reel-trainings') !== -1 }" class="page-name-container">
          <h3 class="page-heading-name" style="text-align: start; margin: 0px 40px;">
            {{this.routeDataService.getData(this.router.url) ?
            this.routeDataService.getData(this.router.url): pageTitle}}</h3>
          <ol class="breadcrumb padding-left-42">
            <li class="breadcrumb-item " *ngFor="let breadcrumb of breadcrumbs"
              [ngClass]="{'active':breadcrumb.active}">
              <a [routerLink]="[breadcrumb.link]">{{getFormKeyword(breadcrumb)}} {{breadcrumb.title}}</a>
            </li>
          </ol>
        </div>

      </div>
      <div class="icons" [ngClass]="{'staff-notification-icon': !authService.isAdmin()}">

        <div class="notification">
          <a class="custom-dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" id="customDropdownToggle"
            (click)="openNotificationPopup()">
            <div class="notBtn">
              <div class="number">
                <span class="count-number">{{showUnReadNotificationCount}}</span>
              </div>
              <i class="bi-bell bell-icon"></i>
            </div>
          </a>
          <div class="box dropdown-menu">
            <div class="display" *ngIf="showNotificationPopup">
              <div class="heading-notification">
                <div class="pull-left">
                  <span class="notificaction-heading">You have {{showUnReadNotificationCount}} new
                    notifications.</span>
                </div>
                <div class="pull-right">
                  <span class="all-notification" tabindex="0" (click)="showAllNotifications()">Show All</span>
                </div>
              </div>
              <div class="notification-cont search-results" infinite-scroll [infiniteScrollDistance]="1"
                [infiniteScrollThrottle]="300" [scrollWindow]="false" (scrolled)="onScrollDown($event)"
                [infiniteScrollDisabled]="(notifications.length !=0 && notifications.length == notifications[0].totalCount)">
                <div class="sec new">
                  <div class="notification-read" *ngFor="let notification of notifications;let index=index"
                    (click)="viewNotification(notification)">
                    <a>
                      <div class="media-icon">
                        <i class="bi-envelope-fill"></i>
                      </div>
                      <div class="message" [innerHTML]="notification.messageHtml"></div>
                    </a>
                    <div *ngIf="!notification.isSeen" class="unread-message-icon"></div>
                    <div class="txt sub">{{notification.createdOn | timeAgo}}</div>
                  </div>
                </div>
                <div *ngIf="notificationLoader" class="text-center">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="clearfix"></div>
    <router-outlet>
    </router-outlet>

  </div>
</div>
<ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="ball-clip-rotate" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>
<app-file-cropper></app-file-cropper>
<app-image-popup-model></app-image-popup-model>