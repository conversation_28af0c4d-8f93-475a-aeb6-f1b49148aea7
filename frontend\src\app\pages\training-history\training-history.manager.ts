import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersService } from 'src/app/services/users.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { TrainingHistoryService } from 'src/app/services/training-history.service';
@Injectable({
    providedIn: 'root'
})
export class TrainingHistoryManager extends BaseManager {

    constructor(protected trainingHistoryService: TrainingHistoryService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(trainingHistoryService, loadingService, toastService);
    }
}
