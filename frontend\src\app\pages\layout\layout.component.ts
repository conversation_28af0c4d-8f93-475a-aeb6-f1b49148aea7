import { <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml, Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { interval, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { RequestSiteChangeService } from 'src/app/services/request-site-change.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from '../../shared/auth.services';
import { CommonUtil } from '../../shared/common.util';
import { NotificationService } from '../notification/notification.service';
import { Notification } from 'src/app/models/notification';
import { FilterParam } from 'src/app/models/filterparam';
import { CommonService } from 'src/app/shared/common.service';
import { How<PERSON>, Howler } from 'howler';
import { RouteDataService } from 'src/app/shared/title.service';
import AOS from 'aos';
import { Course } from 'src/app/models/course';
import { LoadingService } from 'src/app/services/loading.service';
import { Constant } from 'src/app/config/constants';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit, AfterViewInit {
  requestCount: any = 0;
  isShowSubMenu: boolean[];
  pageTitle: string;
  breadcrumbs: Array<any>;
  actions: Array<any>;
  setIntervalId: any;
  setCountRead: Subscription;
  routeEvents: any;
  roleName: string;
  notificationLoader: boolean;
  notifications: Notification[];
  callNotificationData: boolean;
  showUnReadNotificationCount: number;
  offset: number;
  notificationInterval: any;
  infiniteScrollDisabled: false;
  showNotificationPopup: boolean;
  unReadNotificationCount: number;
  sanitizedNotifications: SafeHtml[];
  currentActive: string;
  activeSection: string = '';
  inviteUserCourseModal: any;
  insertedData: { username: string, email: string, id }[] = [];
  selectedCourseUserId: string;
  selectedCourseId: any;
  selectedCourseUser: any;
  courses: Course;
  publishCourseList: any[];
  onClickValidation = false;
  errorMessage: string = '';
  filterParam: FilterParam;
  records: Course[];

  constructor(private translate: TranslateService,
    public commonUtil: CommonUtil,
    public authService: AuthService,
    public router: Router,
    private toastService: ToastService,
    private requestSiteChangeService: RequestSiteChangeService,
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private notificationService: NotificationService,
    private sanitized: DomSanitizer,
    public commonService: CommonService,
    protected loadingService: LoadingService,
    public routeDataService: RouteDataService,
  ) {
    this.isShowSubMenu = new Array<boolean>();
    translate.setDefaultLang('en');
    this.routeEvents = this.router.events.pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(({ urlAfterRedirects }: NavigationEnd) => {
        this.processMetaData();
        this.getRequestSiteCountEveryMenuClick();
        if (this.mobileWidthCheck()) {
          this.closedSidebarMenu();
        }
      });
    if (this.authService.getRoles().includes('ROLE_ADMIN')) {
      this.setCountRead = this.requestSiteChangeService.$setCountRead.subscribe($event => {
        this.getRequestSiteCount()
      });
    }

  }

  getRequestSiteCountEveryMenuClick() {
    if (this.authService.getRoles().includes('ROLE_ADMIN')) {
      if (this.router.url.indexOf('request-site-change') === -1) {
        this.getRequestSiteCount()
      }
    }
  }

  mobileWidthCheck() {
    if (window.innerWidth < 992) {
      return true
    }
  }

  closedSidebarMenu() {
    $("body").css("overflow", "auto");
    $('#content').removeClass('active');
    $('#sidebar').removeClass('final-active');
    $('.collapse.in').removeClass('in');
  }

  processMetaData() {
    this.breadcrumbs = new Array<any>();
    const route = this.getChild(this.activatedRoute);
    route.data.subscribe(data => {
      this.pageTitle = data.title
      switch (data.title) {
        case 'Setting':
          const mergedParams = this.mergeRouteParams(this.router);
          this.pageTitle = mergedParams.id != '0' ? 'Author Profile' : this.pageTitle;
          // will access the resource using async pipe later

          break;
      }
      this.breadcrumbs = data.breadcrumbs;
      this.actions = data.actions ? data.actions : new Array<any>();
      this.titleService.setTitle(data.title)
    });
  }


  mergeRouteParams(router: Router): { [key: string]: string } {
    let params = {};
    let route = router.routerState.snapshot.root;
    do {
      params = { ...params, ...route.params };
      route = route.firstChild;
    } while (route);

    return params;
  }

  getChild(activatedRoute: ActivatedRoute) {
    if (activatedRoute.firstChild) {
      return this.getChild(activatedRoute.firstChild);
    } else {
      return activatedRoute;
    }
  }

  ngOnInit() {
    if (this.authService.getRoles().includes('ROLE_ADMIN')) {
      this.setIntervalId = setInterval(() => this.getRequestSiteCount(), 300000)
    }
    const userRole = this.authService.getUser()?.roles?.[0];
    const foundRole = Constant.ROLES_LIST.find(r => r.id === userRole);
    this.roleName = foundRole ? foundRole.name : 'User';
    this.notificationLoader = false;
    this.callNotificationData = false;
    this.notifications = new Array<Notification>();
    this.records = new Array<Course>();
    this.publishCourseList = new Array();
    this.offset = 1;
    this.infiniteScrollDisabled = false;
    this.showNotificationPopup = false;
    this.unReadNotificationCount = 0;
    // this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.fetchUnReadNotifications();

    //to access the layout component in routerservice
    this.routeDataService.setLayoutComponent(this);

    //track route to update in routedata service
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        const currentRoute = this.router.url; // Get the current route

        // Call the RouteDataService function with the current route
        this.routeDataService.updateBreadcrumbsBasedOnRoute(currentRoute);
      });
  }

  // get the breadcrumbsfrom routeDataService
  getBreadcrumb() {
    // Merge current breadcrumbs with new ones from the service
    const combinedBreadcrumbs = [...this.breadcrumbs, ...this.routeDataService.getBreadcrumbs()];

    // Remove duplicates based on the unique 'id' property
    this.breadcrumbs = combinedBreadcrumbs.filter(
      (breadcrumb, index, self) =>
        index === self.findIndex((b) => b.id === breadcrumb.id && b.link === breadcrumb.link))
      ;
  }


  async getRequestSiteCount() {
    const data = {
      isSeen: 0
    }
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.requestSiteChangeService.getRequestSiteChangesCount(data);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if (response.data) {
        this.requestCount = response.data;
      } else {
        this.requestCount = 0;
      }
    } catch (e) {
      this.toastService.error(e.message);
    }
  }


  checkUserLogin() {
    let roleName = this.authService.getUser()?.roles?.[0];
    if (roleName == 'ROLE_STAFF') {
      this.router.navigate(['/staff-profile']);
      return;
    }
    else if (roleName == 'ROLE_PROGRAM_ADMINISTRATOR') {
      this.router.navigate(['/dashboard/program-admin/profile-setting/' + this.authService.getUser()?.id]);
      return;
    }
    this.router.navigate(['/dashboard/admin-setting']);
  }

  ngOnDestroy() {
    clearInterval(this.setIntervalId);
    if (this.notificationInterval) {
      this.notificationInterval.unsubscribe();
    }
    this.routeEvents.unsubscribe();
    if (this.setCountRead) {
      this.setCountRead.unsubscribe();
    }
  }

  ngAfterViewInit() {
    // Hamburger Icon Toggle for Sidebar
    $('.sidebarCollapse').on('click', () => {
      $('#content').toggleClass('active');
      $('#sidebar').toggleClass('final-active');
      if ($('#sidebar').hasClass('collapse')) {
        $('#sidebar').removeClass('collapse');
      }
    });

    // Manage Collapse Sections and Chevron Icons
    const collapsibles = document.querySelectorAll('.collapse');

    collapsibles.forEach(collapse => {
      const previousSibling = collapse.previousElementSibling;

      collapse.addEventListener('show.bs.collapse', () => {
        if (previousSibling) {
          const icon = previousSibling.querySelector('i');
          if (icon) {
            icon.classList.remove('bi-chevron-right');
            icon.classList.add('bi-chevron-up');
          }
        }
      });

      collapse.addEventListener('hide.bs.collapse', () => {
        if (previousSibling) {
          const icon = previousSibling.querySelector('i');
          if (icon) {
            icon.classList.remove('bi-chevron-up');
            icon.classList.add('bi-chevron-right');
          }
        }
      });
    });

    setTimeout(() => {
      const modalElement = document?.getElementById('inviteUserCourseModal');
      if (modalElement) {
        this.inviteUserCourseModal = new bootstrap.Modal(modalElement);
      }
    }, 0);

  }

  collapseAllSections() {
    const sections = document.querySelectorAll('.collapse');
    sections.forEach(section => {
      if (section.classList.contains('show')) {
        section.classList.remove('show');

        // Find the icon within the previous sibling (the header of the section)
        const previousSibling = section.previousElementSibling;
        if (previousSibling) {
          const icon = previousSibling.querySelector('i');
          if (icon) {
            icon.classList.remove('bi-chevron-up');
            icon.classList.add('bi-chevron-right');
          }
        }
      }
    });
    this.activeSection = ''; // Reset the active section if necessary
  }

  stopPropagation(event: MouseEvent): void {
    event.stopPropagation();
  }

  setActiveSection(section: string) {
    this.activeSection = section;
  }

  toggleIcon(iconId: string) {
    const icon = document.getElementById(iconId);
    if (icon.classList.contains('bi-chevron-right')) {
      icon.classList.remove('bi-chevron-right');
      icon.classList.add('bi-chevron-up');
    } else {
      icon.classList.remove('bi-chevron-up');
      icon.classList.add('bi-chevron-right');
    }
  }

  checkMenuIsOpened() {
    if (this.mobileWidthCheck()) {
      if ($('#sidebar').hasClass('final-active')) {
        $("body").css("overflow", "hidden");
      } else {
        $("body").css("overflow", "auto");
      }
    }
  }

  showSubMenu(type) {
    this.isShowSubMenu[type] = !this.isShowSubMenu[type];
  }

  getUserDisplayName() {
    if (!this.authService.getUser()) {
      return "";
    }
    return this.authService.getUser().fullName.charAt(0);
  }

  logout() {
    this.authService.logout();
  }

  getFormKeyword(breadcrumb) {
    if (!breadcrumb.active) {
      return "";
    }
    return this.router.url.endsWith("/edit/0") ? "Add New" : this.router.url.includes("/edit/") ? "Edit" : "";
  }

  openLink(link) {
    if (link.link) {
      this.router.navigate([link.link]);
      return;
    }
  }

  openNotificationPopup() {
    if ($("#customDropdownToggle").hasClass('show')) {
      this.infiniteScrollDisabled = false;
      this.fetchNotificationData(1, null);
    }
  }

  fetchUnReadNotifications() {
    if (!this.callNotificationData) {
      this.getUnReadCountForNotification();
    }
    if (this.authService.getUser() != null) {
      this.notificationInterval = interval(30000).subscribe(x => {
        this.getUnReadCountForNotification();
      });
    }
  }

  onScrollDown(event) {
    this.notificationLoader = true;
    setTimeout(() => {
      this.fetchNotificationData(1, 'scrolling');
    }, 300);
  }

  async fetchNotificationData(offset, value) {
    try {
      let param = new FilterParam();
      param.next = 10;
      if (value == 'scrolling') {
        this.offset += offset;
        param.offset = this.offset;
      }
      else {
        param.offset = offset;
      }
      const resp: RestResponse = await this.notificationService.fetchAll(param);
      if (value == 'scrolling') {
        this.notificationLoader = false;
      }
      if (!resp.status) {
        this.toastService.error(resp.message);
        return;
      }
      if (value == 'scrolling') {
        this.notifications = this.notifications.concat(resp.data);
      }
      else {
        this.notifications = resp.data;
      }
      this.showNotificationPopup = true;

      if (this.notifications && this.notifications.length > 0) {
        this.notifications.forEach((notification) => {
          notification.message = notification.message.replace("</span>  ", " </span>&nbsp;");
          notification.messageHtml = this.sanitized.bypassSecurityTrustHtml(notification.message);
        });
      }
    }
    catch (e) {

    }
  }

  async getUnReadCountForNotification() {
    try {
      const resp: RestResponse = await this.notificationService.fetchUnreadNotification().toPromise();
      if (!resp.status) {
        this.toastService.error(resp.message);
        return;
      }
      this.callNotificationData = true;
      this.showUnReadNotificationCount = resp.data;
      if (resp.data > this.unReadNotificationCount) {
        this.unReadNotificationCount = resp.data;
        var sound = new Howl({
          src: ['/assets/notification-bell/notication-bell.wav']
        });
        sound.play();
      }
    }
    catch (error) {
      if (error.currentTarget.status == 0 && error.currentTarget.responseText) {
        this.toastService.error(error.currentTarget.responseText);
      }
    }
  }

  async viewNotification(notification: Notification) {
    await this.commonService.viewNotification(notification);
    this.getUnReadCountForNotification();
  }

  showAllNotifications() {
    const url = this.authService.isProgramAdmin()
      ? '/dashboard/program-admin/notifications'
      : this.authService.isAdmin()
        ? '/dashboard/notifications'
        : '/staff-notifications';
    // Navigate to the selected URL
    this.router.navigate([url]);

  }

  collapseManageTraining() {
    const collapseTraining = document.getElementById('collapseTraining');
    if (collapseTraining) {
      // Collapse the "Manage Training" section
      collapseTraining.classList.remove('show');

      // Update the icon to bi-chevron-right
      this.toggleIcon('trainingIcon');

      // Update the aria-expanded attribute to false
      const manageTrainingLink = document.querySelector('[href="#collapseTraining"]');
      if (manageTrainingLink) {
        manageTrainingLink.setAttribute('aria-expanded', 'false');
      }
    }
  }

}
