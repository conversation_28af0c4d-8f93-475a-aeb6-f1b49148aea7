import { Component, EventE<PERSON>ter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Content } from 'src/app/models/content';
import { LearningSeries } from 'src/app/models/learningseries';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageContentManager } from '../manage-content-type/manage-content.manager';
import { ManageLearningSeriesManager } from '../manage-learning-series/manage-learning-series.manager';
import { Training } from 'src/app/models/training';
import { ManageAdminTrainingManager } from './manage-admin-training.manager';
import { ManageAdminTrainingService } from './manage-admin-training.service';
import AOS from 'aos';
import { Users } from 'src/app/models/users';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { FilterParam } from 'src/app/models/filterparam';
import { saveAs } from 'file-saver';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Constant } from 'src/app/config/constants';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-admin-training',
  templateUrl: './manage-admin-training.component.html',
  styleUrls: ['./manage-admin-training.component.scss']
})
export class ManageAdminTrainingComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  moment: any = moment;
  dropdownSettings = {};
  dropdownSettingsUsers = {};
  searchTraining: any;
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  trainings: CourseTrainings[];
  contentTypes: Content[];
  learningSeries: CourseLearningSeries[];
  trainingFilterData: RestResponse;
  videos: any[] = [];
  isViewAllTableVisible: boolean = false;
  filterTrainingModal: any;
  records: CourseTrainings[];
  username: any;
  strProgramAdmin: string;
  isTrainingCsvExport: boolean = false
  readonly MY_CONSTANT = Constant;
  loadingVideo: boolean;


  constructor(protected manageAdminTrainingManager: ManageAdminTrainingManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, private route: ActivatedRoute, public commonUtil: CommonUtil, private manageAdminTrainingService: ManageAdminTrainingService, private manageContentManager: ManageContentManager, private managelearningSeriesManager: ManageLearningSeriesManager, private http: HttpClient) {
    super(manageAdminTrainingManager, commonService, toastService, loadingService, router);
  }

  async ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    this.setDropdownSettings();
    this.setDropdownSettingsUsers();
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseTrainings>();
    this.contentTypes = new Array<Content>();
    this.filterParam.sortBy = true;
    this.learningSeries = new Array<CourseLearningSeries>();
    this.strProgramAdmin = this.route.snapshot.paramMap.get('id');
    this.manageAdminTrainingService.getUserId(this.strProgramAdmin);
    this.filterParam.strProgramAdmin = this.strProgramAdmin;
    this.init();
    // this.fetchAllTrainings()
    // this.fetchAssociatedData();
  }

  async fetchRecords(param, callBack) {
    try {
      this.hasDataLoad = false;
      this.setParam(param);
      this.loadingService.show();
      const response: RestResponse = await this.manageAdminTrainingManager.fetchAll(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
      this.onFetchCompleted();
      callBack({ recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length, recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length, data: [] });
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/training/edit/' + id])
  }

  onClickTrainingFilter(valid) {
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  updateStatus(data: any, recordData: any) {
    this.commonService.confirmation('Would you like to change the status of Training?', this.updateStatusCallback.bind(this), { id: recordData.id, isPublish: data.currentTarget.checked }, null, null, this.cancelUpdateStatusCallback.bind(this));
  }

  //Accssibility update status
  getTooltipText(accessibility: string): string {
    return accessibility === 'Public' ? 'Public' : 'Private';
  }

  updateStatusAccssibility(record: any, index) {
    this.commonService.confirmation('Would you like to change the Accessibility Mode?', this.updateAccessibilityCallback.bind(this), { id: record.id, accessibility: record.accessibility }, null, null, this.cancelUpdateStatusCallback.bind(this));
  }

  onCancelAccessibility(record, index) {
    this.records[index] = record;
  }

  updateStatusAccssibilityMethod(record: any, index) {
    if (record.accessibility === "Public") {
      this.records[index].accessibility = "Private";
    }
    else {
      this.records[index].accessibility = "Public";
    }
    const data = {
      id: record.id,
      accessibility: record.accessibility
    };
    this.updateAccessibilityCallback(data);
  }

  cancelUpdateStatusCallback() {
    this.onCancel();
  }

  async updateAccessibilityCallback(data) {
    data.accessibility = data.accessibility === "Public" ? "Private" : "Public";
    try {
      this.loadingService.show();
      console.log(data)
      const response: RestResponse = await this.manageAdminTrainingManager.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  //Accssibility update status end

  async updateStatusCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageAdminTrainingManager.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  openTrainingDetailPage(record: any) {
    this.router.navigate(['/dashboard/training/program/admin/detail/' + record.id]);
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  setDropdownSettingsUsers() {
    this.dropdownSettingsUsers = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  removeSuccess() {
    this.onCancel();
  }

  async fetchAllTrainings() {
    try {
      const filterParam: FilterParam = new FilterParam();
      filterParam.sortBy = true;
      this.loadingService.show();
      const response: RestResponse = await this.manageAdminTrainingService.fetchAll(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.videos = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  exportTrainingHistroyCsv() {
    this.isTrainingCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], this.username + '_My Training Report_' + moment().format('DD-MM-YYYY') + '.xlsx', { type: 'application/vnd.ms.excel' });

        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isTrainingCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isTrainingCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isTrainingCsvExport = false;
      return;
    }
  }

  download(): Observable<Blob> {
    const strProgramAdmin = this.route.snapshot.paramMap.get('id');
    this.filterParam.strProgramAdmin = strProgramAdmin;
    return this.http.post(environment.BaseApiUrl + '/api/trainings/program/admin/' + '/export',
      this.filterParam, { responseType: 'blob' }).pipe(catchError(this.parseErrorBlob));
  }


  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isTrainingCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }


}
