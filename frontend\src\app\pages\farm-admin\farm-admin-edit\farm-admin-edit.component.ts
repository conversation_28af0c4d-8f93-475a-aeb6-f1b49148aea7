import { Component, OnInit, ViewChild } from '@angular/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FarmAdmin } from 'src/app/models/farmadmin';
import { FarmAdminService } from '../farm-admin.service';
import { FarmAdminManager } from '../farm-admin.manager';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonUtil } from 'src/app/shared/common.util';
import { ActivatedRoute, Router } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { CommonService } from 'src/app/shared/common.service';
import { TranslateService } from '@ngx-translate/core';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { FarmService } from '../../farm/farm.service';
import { Farm } from 'src/app/models/farm';
import { NgModel } from '@angular/forms';

@Component({
  selector: 'app-farm-admin-edit',
  templateUrl: './farm-admin-edit.component.html',
  styleUrls: ['./farm-admin-edit.component.scss']
})
export class FarmAdminEditComponent extends BaseEditComponent implements OnInit {

  farmAdmin: FarmAdmin;
  farms: Farm[];
  @ViewChild('phone') phoneField: NgModel;

  constructor(
    protected route: ActivatedRoute,
    protected farmAdminManager: FarmAdminManager,
    protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected router: Router,
    protected commonService: CommonService,
    public authService: AuthService,
    protected translateService: TranslateService,
    public commonUtil: CommonUtil,
    protected farmService: FarmService,
    protected farmAdminService: FarmAdminService
  ) {
    super(farmAdminManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.farmAdmin = new FarmAdmin();
    this.farms = new Array<Farm>();
    this.farmAdmin.countryCode = '+91';
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.init();
  }

  onFetchCompleted() {
    this.farmAdmin = FarmAdmin.fromResponse(this.record);
    this.farmAdmin.farmIds = (this.farmAdmin.userFarmDetail || [])
      .filter(fd => !fd.isDeleted)
      .map(fd => fd.farmId);
    this.setRecord(this.farmAdmin);
  }

  async fetchAssociatedData() {
    // need to move to if check as we need farms detail for super admin
    this.filterParam = new FilterParam();
    const farms: RestResponse = await this.farmService.fetchAvailableFarms(this.filterParam);
    this.farms = farms.data;
    this.farms.forEach(farm => {
      farm.displayLabel = farm.farmCode.trim() + ' - ' + farm.name;
    });
  }


  async save(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    const payload = { ...this.farmAdmin.forRequest() };
    // If email is an empty string, set it to null before sending
    if (payload.email === "") {
      payload.email = null;
    }
    if ('farmIds' in payload) {
      delete payload.farmIds;
    }

    const method = this.request.isNewRecord ? 'register' : 'update';
    try {
      const response: RestResponse = await this.farmAdminService[method](payload);

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }

      this.onSaveSuccess(response.message);

    } catch (err: any) {
      this.toastService.error(err.message ?? 'Something went wrong');

    } finally {
      this.loadingService.hide();
    }
  }

  onFarmsChange(selectedFarmIds: string[]) {
    // Get previous farm IDs from userFarmDetails (not deleted)
    const prevFarmDetails = this.farmAdmin.userFarmDetail || [];
    const prevFarmIds = prevFarmDetails.filter(fd => !fd.isDeleted).map(fd => fd.farmId);

    // Farms removed by user
    const removedFarmIds = prevFarmIds.filter(id => !selectedFarmIds.includes(id));
    // Farms newly added by user
    const addedFarmIds = selectedFarmIds.filter(id => !prevFarmIds.includes(id));

    // Mark removed farms as isDeleted: true
    const updatedFarmDetails = prevFarmDetails.map(fd => {
      if (removedFarmIds.includes(fd.farmId)) {
        return { ...fd, isDeleted: true };
      }
      return fd;
    });

    // Add new farms
    addedFarmIds.forEach(id => {
      updatedFarmDetails.push({ farmId: id });
    });

    this.farmAdmin.userFarmDetail = updatedFarmDetails;
  }

  telInputObject(event: any) {
    // Use countryCode from farmAdmin if available (e.g., '+91')
    if (this.farmAdmin.countryCode) {
      const iso2 = this.getCountryIso2FromDialCode(this.farmAdmin.countryCode, event);
      event.setCountry(iso2);
      return;
    }
    // Fallback: try to extract from phoneNumber if it starts with +
    if (this.farmAdmin.phoneNumber && this.farmAdmin.phoneNumber.startsWith('+')) {
      const match = this.farmAdmin.phoneNumber.match(/^(\+\d{1,4})/);
      if (match) {
        const iso2 = this.getCountryIso2FromDialCode(match[1], event);
        event.setCountry(iso2);
        return;
      }
    }
    // Default country if not set
    event.setCountry('in');
  }

  getNumber(event: any) {
  }

  getCountryIso2FromDialCode(dialCode: string, itiInstance: any): string {
    // Use intl-tel-input's country data to map dial code to ISO2
    if (itiInstance && itiInstance.countries) {
      const dial = dialCode.replace('+', '');
      const country = itiInstance.countries.find((c: any) => c.dialCode === dial);
      if (country) return country.iso2;
    }
    return 'in';
  }

  onCountryChange(event: any) {
    console.log(event, "event");
    // event.dialCode contains the country dial code (e.g., "91" for India)
    this.farmAdmin.countryCode = event.dialCode ? `+${event.dialCode}` : '';
  }

  onSaveSuccess(data: any): void {
    this.toastService.success(data);
    this.router.navigate(['/dashboard/farm/admins']);
  }

  hasError(hasError: boolean) {
    if (this.phoneField && this.phoneField.control) {
      if (!hasError) {
        this.phoneField.control.setErrors({ mobile: true });
      } else {
        const errors = this.phoneField.control.errors;
        if (errors) {
          delete errors.mobile;
          if (Object.keys(errors).length === 0) {
            this.phoneField.control.setErrors(null);
          } else {
            this.phoneField.control.setErrors(errors);
          }
        }
      }
    }
  }

}
