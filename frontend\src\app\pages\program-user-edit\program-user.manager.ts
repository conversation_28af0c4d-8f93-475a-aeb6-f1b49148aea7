import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramUserService } from './program-user.service';

@Injectable({
    providedIn: 'root'
})
export class ProgramUserManager extends BaseManager {

    constructor(protected programUserService: ProgramUserService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(programUserService, loadingService, toastService);
    }
}
