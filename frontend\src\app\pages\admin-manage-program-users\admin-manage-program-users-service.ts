import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class AdminManageProgramUserService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/admin/course/invite/user', '/api/admin/course/invite/users');
    }

    sendCourseInvite(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/admin/course/invite/users', data);
    }
}

