import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminActiveService extends BaseService {
    strProgramAdmin: string;
    getUserId(id: string) {
        this.strProgramAdmin = id;
    }

    constructor(public http: HttpClient) {
        super(http, '/api/account/active/program/admin', '/api/account/active/program/admins');
    }
    getActiveUser(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/account/active/program/admins', filterParam);
    }

    removeUserAccess(id: string): Promise<RestResponse> {
        return this.removeRecord('/api/account/admin/program/' + id)
    }

    activateDeactivateUser(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/account/user/activate-deactivate', data);
    }
}

