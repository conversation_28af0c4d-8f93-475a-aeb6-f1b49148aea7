<div class="site-customer-main-container training-detail" id="userEditComponent">
  <div class="dashboard-content-container training-detail-main-cls container">
    <div *ngIf="newsfeed?.id" class="row">
      <div class="col-12 col-lg-6">
        <div class="text-start">
          <div class="d-flex">
            <h2>{{newsfeed?.title}}</h2>
          </div>
          <hr class="mt-4 opacity-50">
          <div class="row d-flex text-start mt-4 justify-content-between">
            <div class="col-12 col-lg-6">
              <h6 class="mt-5 semi-bold">Status</h6>
              <p>{{newsfeed?.isPublish == true ? "Publish" : "Draft"}}
              </p>
            </div>
            <div class="col-12 col-lg-6 pe-0 ps-5">
              <h6 class="mt-5 semi-bold">News Feed Type</h6>
              <p>{{newsfeed?.type}}
              </p>
            </div>
          </div>
          <div *ngIf="newsfeed.type == 'TRAINING'">
            <hr class="mt-5">
            <div class="row d-flex text-start mt-4 justify-content-between">
              <div class="col-12 col-lg-6">
                <h6 class="semi-bold">Content Type</h6>
                <div>{{newsfeed?.contentTypeDetail?.title}}</div>
              </div>
              <div class="col-12 col-lg-6 pe-0 ps-5">
                <h6 class="semi-bold">Learning Series</h6>
                <div class="text-start">{{newsfeed?.learningSeriesDetail?.title}}</div>
              </div>
            </div>
          </div>
          <hr class="mt-5">
          <div class="row d-flex text-start mt-4 justify-content-between">
            <div class="col-12 col-lg-6">
              <h6 class="semi-bold">Accessibility</h6>
              <img
                [title]="newsfeed.isNewsFeedPublic == false ? 'Private' : newsfeed.isNewsFeedPublic == true && 'Public'"
                [src]="newsfeed.isNewsFeedPublic == false ? '/assets/images/icons/menu/private-red.svg' : newsfeed.isNewsFeedPublic == true && '/assets/images/icons/menu/users-green.svg'"
                class="width-27px mb-3" />
            </div>
            <div *ngIf="newsfeed.type == 'TRAINING'" class="col-12 col-lg-6 pe-0 ps-5">
              <!-- <h6 class="semi-bold">{{'Training.TrainingRequired' | translate}}</h6> -->
              <h6><span *ngIf="newsfeed.uploadRequired" class="badge integrax-tags integrax-font">Uploading
                  Required</span></h6>
              <h6><span *ngIf="newsfeed.watchRequired" class="badge integrax-tags integrax-font">Watching
                  Required</span></h6>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="newsfeed.mediaType == 'VIDEO'" class="col-12 col-lg-6">
        <div class="video-wrapper mt-5">
          <div class="video-container" id="video-container">
            <div class="play-button-wrapper">
              <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
                id="circle-play-b">
                <!-- SVG Play Button -->
                <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                  <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                </svg>
              </div>
            </div>
            <video playsinline [src]="newsfeed.url" class="mw-100" id="video_id" controlslist="nodownload">
              Your browser does not support HTML video.
            </video>
          </div>
        </div>
      </div>
      <div *ngIf="newsfeed.mediaType == 'IMAGE'" class="moment-image-container mb-4 col-12 col-lg-6 ">
        <img [src]="newsfeed.url" />
      </div>
      <div class="col-12 col-lg-12">
        <hr class="mt-5">
        <h6 class="mt-5 semi-bold pe-0 ps-2">Description</h6>
        <p  class="pe-0 ps-2" [innerHtml]="newsfeed?.description">
        </p>
      </div>
    </div>
  </div>
</div>