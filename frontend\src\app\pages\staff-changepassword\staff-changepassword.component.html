<div class="staff-changepassword container">
  <div class="staff-changepassword-form">
    <form #changePasswordForm="ngForm" novalidate="novalidate">
      <div class="row">
        <div class="col-12 mb-4 d-flex justify-content-center">
          <img width="150" height="130" src="/assets/images/icons\menu\Layer.svg" class="img-fluid change-password-logo"
            alt="">
        </div>
        <div class="col-12 mb-4">
          <div class="form-floating position-relative">
            <input [type]="showOldPassword ? 'text' : 'password'" name="oldpassword" class="form-control rounded-3" style="padding-right:2.5rem;height:56px;"
              id="floatingPassword" placeholder="Old Password" #oldPassword="ngModel"
              [ngClass]="{'is-invalid':!oldPassword.valid && onClickValidation}" [(ngModel)]="data.oldPassword"
              required="required" autofocus>
            <label for="floatingPassword">Old Password</label>
            <button type="button" tabindex="-1" class="btn btn-link p-0 m-0 border-0 position-absolute password-eye-btn"
              (click)="showOldPassword = !showOldPassword">
              <i class="bi" [ngClass]="showOldPassword ? 'bi-eye-slash' : 'bi-eye'" style="font-size:1.3rem;"></i>
            </button>
          </div>
          <app-validation-message [field]="oldPassword" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>
        <div class="col-12 mb-4">
          <div class="form-floating position-relative">
            <input [type]="showNewPassword ? 'text' : 'password'" name="newpassword" class="form-control rounded-3" style="padding-right:2.5rem;height:56px;"
              id="floatingPassword1" placeholder="New Password" #newPassword="ngModel"
              [ngClass]="{'is-invalid': (onClickValidation && (!newPassword.valid || newPassword.errors?.pattern))}"
              [(ngModel)]="data.password" pattern="^(?=.*[!@#$%^&*])(?=.*[0-9])(?=.*[A-Z]).{8,}$" required="required" />
            <label for="floatingPassword1">New Password</label>
            <button type="button" tabindex="-1" class="btn btn-link p-0 m-0 border-0 position-absolute password-eye-btn"
              (click)="showNewPassword = !showNewPassword">
              <i class="bi" [ngClass]="showNewPassword ? 'bi-eye-slash' : 'bi-eye'" style="font-size:1.3rem;"></i>
            </button>
            <app-validation-message [field]="newPassword"
              [onClickValidation]="onClickValidation"></app-validation-message>
            <small *ngIf="onClickValidation && newPassword.errors?.pattern" class="text-danger">
              Password must include at least one special character, one number, one uppercase letter, and be at least 8
              characters long.
            </small>
          </div>
        </div>
        <div class="col-12 mb-4">
          <div class="form-floating position-relative">
            <input [type]="showConfirmPassword ? 'text' : 'password'" name="confirmpassword" class="form-control rounded-3" style="padding-right:2.5rem;height:56px;"
              id="floatingPassword2" placeholder="Confirm Password" #confirmPasswordRef="ngModel"
              [ngClass]="{'is-invalid':(!confirmPasswordRef.valid || newPassword.value != confirmPasswordRef.value) && onClickValidation}"
              [(ngModel)]="confirmPassword" required="required">
            <label for="floatingPassword2">Confirm Password</label>
            <button type="button" tabindex="-1" class="btn btn-link p-0 m-0 border-0 position-absolute password-eye-btn"
              (click)="showConfirmPassword = !showConfirmPassword">
              <i class="bi" [ngClass]="showConfirmPassword ? 'bi-eye-slash' : 'bi-eye'" style="font-size:1.3rem;"></i>
            </button>
            <app-validation-message [field]="confirmPasswordRef" [comparableField]="newPassword"
              [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
        </div>
        <div class="col-12 ">
          <button [disabled]="changePasswordButtonDisabled" (click)="changePassword(changePasswordForm.form.valid)"
            class="btn btn-lg bg-dark change-password">CHANGE PASSWORD</button>
        </div>
      </div>
    </form>
  </div>
  <!-- Removed misplaced duplicate eye icon button at the bottom -->