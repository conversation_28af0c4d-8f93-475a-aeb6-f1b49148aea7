import { Component, OnInit } from '@angular/core';
import {BaseDetailComponent} from '../../../config/base.detail.component';
import { UserAssignTraining } from '../../../models/userassigntraining';
import { ActivatedRoute, Router } from '@angular/router';
import { UserAssignTrainingManager } from '../userassigntraining.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';

@Component({
  selector: 'app-userassigntraining-detail',
  templateUrl: './userassigntraining-detail.component.html',
  styleUrls: ['./userassigntraining-detail.component.scss']
})
export class UserAssignTrainingDetailComponent extends BaseDetailComponent implements OnInit {

	  constructor(protected route: ActivatedRoute, protected userAssignTrainingManager: UserAssignTrainingManager, protected toastService: ToastService,
	    		  protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService, 
	    		  protected translateService: TranslateService,public commonUtil: CommonUtil ) {
	    	super(userAssignTrainingManager, commonService, toastService, loadingService, route, router, translateService);
	    	
	  }

	  ngOnInit() {
	  	this.record = new UserAssignTraining();    
	    this.isDetailPage =  true;
	    this.init();
	  }
  
	  onFetchCompleted() { 
	    super.onFetchCompleted();
	    this.filterParam.relationTable = "UserAssignTraining";
	    this.filterParam.relationId = this.record.id;
	  }
	  
}
