import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { NewsFeedClickLogs } from 'src/app/models/newsfeedclicklogs';
import { NewsFeedClickLogsManager } from './news-feed-click-logs.manager';
import { NewsFeedClickLogsService } from './news-feed-click-logs.service';

@Component({
  selector: 'app-news-feed-click-logs',
  templateUrl: './news-feed-click-logs.component.html',
  styleUrls: ['./news-feed-click-logs.component.scss']
})
export class NewsFeedClickLogsComponent extends BaseListServerSideComponent implements OnInit, OnDestroy{
  @Input() getnewsFeedVideoProgressData: any | undefined;
  filterParam: FilterParam | undefined;
  constructor(protected newsFeedClickLogsManager: NewsFeedClickLogsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService,
    protected router: Router, public commonUtil: CommonUtil , protected service : NewsFeedClickLogsService) {
    super(newsFeedClickLogsManager, commonService, toastService, loadingService, router);
  }
  
  ngOnInit(){
    this.filterParam.trainingId = this.getnewsFeedVideoProgressData.id;
    this.init();
    this.records = new Array<NewsFeedClickLogs>();
  }
  
  ngOnDestroy(): void {
  }

}
