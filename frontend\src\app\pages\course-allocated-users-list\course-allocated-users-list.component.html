<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
    <div [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-6 text-start">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" appDelayedInput (delayedInput)="search($event)"
                        [delayTime]="1000">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div class="col-6 d-flex align-items-center justify-content-end">
                <!-- <button *ngIf="authService.isAccessible('MANAGE_COURSE','AddButton')" type="button"
                    class="btn add-button btn-lg font-15px invite-user-btn d-flex align-items-center justify-content-center">
                    <img src="/assets/icons/invite_user.svg" height="28" width="28" class="me-2" alt="">Invite Now
                </button> -->
                <button (click)="openInviteCourseUserModal()" type="button"
                    class="btn add-button add-button-training bg-dark text-light btn-lg font-15px height-51px">
                    <img src="/assets/images/icons/menu/invite.svg" class="me-2 width-22px img-fluid" alt=""
                        style="vertical-align: sub;">Invite Now
                </button>
            </div>
            <div class="table-responsive server-side-table allocated-users-list"
                [ngClass]="{'has-records': records.length >0 }">
                <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" style="width: 100%;">
                    <thead>
                        <th width="100">Seeker's Name</th>
                        <th width="50">Email</th>
                        <th width="50">Last Updated Date</th>
                        <th width="50">Progress Status</th>
                    </thead>
                    <tbody>
                        <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                            <!-- <td style="text-align: center;">
                                <img src="/assets/icons/user_profile_icon.svg" height="38" width="38" class="me-2"
                                    alt="">
                            </td> -->
                            <!-- <td width="100" class="pe-0">
                                <h5 class="code ms-2" title="View User Profile"
                                    style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                    [routerLink]="['/dashboard/program-admin/user-details/' +record.user]">
                                    {{record?.username}}
                                </h5>
                            </td> -->

                            <td class="pe-0">
                                <h5 class="code ms-2" title="View Profile"
                                    style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                    [routerLink]="authService.getRoles().includes('ROLE_ADMIN') 
                                                                          ?['/dashboard/program-detail/' + record?.user,] 
                                                                          : ['/dashboard/user-details/'+record?.user]"
                                    [queryParams]="authService.getRoles().includes('ROLE_ADMIN') ? { tab: 'profile', 'username': record.username} : ''">
                                    {{record?.username}}
                                </h5>
                            </td>


                            <td>
                                {{record?.email}}
                            </td>

                            <td>
                                <h5 class="my-0" style="font-size: 18px;font-weight: 500;white-space: nowrap;">
                                    {{record?.createdOn | date: 'dd-MM-yyyy'}}
                                </h5>
                            </td>
                            <!-- <td width="50">
                                <h5 class="my-0" style="font-size:24px; font-weight: 600; white-space: nowrap;">
                                    {{record.courseDetail?.code}}
                                </h5>
                            </td> -->
                            <td>
                                <!-- Percentage Not Complete -->
                                <div class="circular-progress" *ngIf="record.courseDetail?.percentage !== '100%'">
                                    <span class="title-per timer">{{record.courseDetail?.percentage}}</span>
                                    <div class="progress-bar"
                                        [ngStyle]="{'background': 'conic-gradient(#555 0% '+record.courseDetail?.percentage +', #f0f0f0 '+record.courseDetail?.percentage +')'}">
                                    </div>
                                </div>
                                <!-- Percentage Complete -->
                                <div *ngIf="record.courseDetail?.percentage === '100%'">
                                    <img src="/assets/images/icons/menu/batch.svg" class="me-2 img-fluid" alt=""
                                        style="vertical-align: sub;">
                                </div>
                            </td>
                            <!-- <td width="120" class="text-right">
                                <div class="d-flex justify-content-end">
                                    <button
                                        class="btn manage-filter-buttton text-light btn-lg font-15px filter-button-cls revoke-access-btn"
                                        (click)="remove(record.id)">
                                        Revoke Access
                                    </button>
                                    <div class="vertical-line">
                                    </div>
                                </div>
                            </td> -->
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- modal invite course user -->
    <div class="modal fade" id="inviteUserAllocatedCourseModal" tabindex="-1"
        aria-labelledby="inviteUserAllocatedCourseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0" style="padding-top: 30px;
                   margin: auto 15px;">
                    <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div *ngIf="inviteUserAllocatedCourseModal && inviteUserAllocatedCourseModal._isShown"
                    class="modal-body" style="padding: 10px 50px;">
                    <div class="modal-content-inside">
                        <h5 class="modal-title fw-bold" id="inviteUserAllocatedCourseModalLabel">Invite User for Course
                        </h5>
                        <p class="modal-heading pt-1" id="inviteUserAllocatedCourseModalLabel">
                            Please make sure you fill in all the fields before you click on the Send Invite button
                        </p>
                    </div>
                    <form #userInviteForm="ngForm" novalidate="novalidate">
                        <div class="form-floating mb-4">
                          <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                              [ngClass]="{'is-invalid':!chooseCourse.valid && onClickValidation}">
                            <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="chooseCourse"
                                [items]="publishCourseList" bindLabel="title" bindValue="id"
                                (change)="selectInviteUserCourse($event)" required="required"
                                class="custom-multiselect form-control padding-bottom-8"
                                [(ngModel)]="selectedCourseId" #chooseCourse="ngModel" [searchable]="false">
                            </ng-select>
                          </div>
                          <label for="chooseCourse">
                              {{"Course.chooseCourse" | translate}}
                          </label>
                        </div>
                        <div class="mb-3">
                          <label>
                            <input type="radio" name="inviteType" [(ngModel)]="isFarmInvite" [value]="true">
                            Company
                          </label>
                          <label class="ms-3">
                            <input type="radio" name="inviteType" [(ngModel)]="isFarmInvite" [value]="false">
                            Individual
                          </label>
                        </div>

                        <ng-container *ngIf="!isFarmInvite">
                          <div class="form-floating mb-4">
                            <input maxlength="20" [ngClass]="{'is-invalid':!username.valid && individualValidation}"
                              class="form-control b-r-8" type="text" name="username" #username="ngModel" required
                              [(ngModel)]="courses.username" placeholder="{{'Course.name' | translate}}">
                            <label for="floatingInput">{{"Course.name" | translate}}</label>
                          </div>
                          <div class="form-floating mb-2">
                            <input [ngClass]="{'is-invalid':!email.valid && individualValidation}"
                              class="form-control b-r-8" type="email" name="email" #email="ngModel" required
                              [(ngModel)]="courses.email" placeholder="{{'Course.email' | translate}}"
                              pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                            <label for="floatingInput">{{"Course.email" | translate}}</label>
                          </div>
                          <div *ngIf="errorMessage" style="color: red;">
                            {{ errorMessage }}
                          </div>
                          <div class="text-end mb-2 mt-2">
                            <button class="btn" (click)="insertData(userInviteForm, courses.id)"
                              style="border: 1px solid #000; border-style: dashed; font-size: 20px;" title="Add username or email">
                              <i class="bi bi-plus-lg" style="color: #000;"></i>
                            </button>
                          </div>
                          <div class="mb-2" *ngFor="let insert of insertedData; let i = index">
                            <div class="d-flex justify-content-between align-middle"
                              style="border: 1px solid #1681FF; border-radius: 10px; padding: 10px;">
                              <h5 class="text-secondary mt-1">
                                {{insert.username}} ( {{insert.email}} )
                              </h5>
                              <div class="mt-1" (click)="removeData(i)">
                                <img src="/assets/images/icons/menu/remove-user.svg" class="me-2 img-fluid text-end" alt="">
                              </div>
                            </div>
                          </div>
                        </ng-container>
                        <ng-container *ngIf="isFarmInvite">
                          <div class="col-12 mb-4">
                            <div class="form-floating">
                              <div class="form-control select-width ng-select-main-container"
                                [ngClass]="{'is-invalid':!farmIds?.valid && farmValidation}">
                                <ng-select [items]="farms" bindLabel="displayLabel" bindValue="id" name="farmIds" #farmIds="ngModel"
                                  [required]="isFarmInvite" [(ngModel)]="selectedFarmIds" placeholder="Select Companies"
                                  [multiple]="true">
                                </ng-select>
                              </div>
                              <label for="farmIds">Companies</label>
                              <app-validation-message [field]="farmIds" [onClickValidation]="farmValidation"></app-validation-message>
                            </div>
                          </div>
                        </ng-container>
                        <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
                          <button (click)="onClickInviteUserCourse(userInviteForm.form)" type="button"
                            class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                            INVITE
                          </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal invite course user end -->

</div>