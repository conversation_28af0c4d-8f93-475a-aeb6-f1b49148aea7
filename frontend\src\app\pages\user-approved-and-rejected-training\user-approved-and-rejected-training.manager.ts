import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { UserApprovedRejectedService } from './user-approved-and-rejected-training.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class UserApprovedAndRejectedTrainingManager extends BaseManager {

    constructor(private userApprovedRejectedService: UserApprovedRejectedService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userApprovedRejectedService, loadingService, toastService);
    }
}
