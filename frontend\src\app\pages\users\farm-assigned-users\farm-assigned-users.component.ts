import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Users } from 'src/app/models/users';
import { AssignedUsersService } from 'src/app/services/assigned-users.service';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { AssignedUsersManager } from '../assigned-users.manager';

declare const $: any;
@Component({
  selector: 'app-farm-assigned-users',
  templateUrl: './farm-assigned-users.component.html',
  styleUrls: ['./farm-assigned-users.component.scss']
})
export class FarmAssignedUsersComponent extends BaseListServerSideComponent implements OnInit {
  userSelected: boolean = false
  constructor(private route: ActivatedRoute, protected assignedUsersManager: AssignedUsersManager, protected commonService: CommonService, protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private assignedUsersService: AssignedUsersService) {
    super(assignedUsersManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.records = [] as Users[];
    const farmId = this.route.snapshot.paramMap.get('id');
    this.filterParam.farmId = farmId;
    this.init();
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.userSelected = false;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    this.userSelected = false;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  selectUnselectAll(event: any) {
    if (event.currentTarget.checked) {
      this.userSelected = true;
      $('tbody').find('input[type="checkbox"]').prop('checked', true);
    } else {
      this.userSelected = false;
      $('tbody').find('input[type="checkbox"]').prop('checked', false);
    }

  }

  selectUnselectCheck() {
    let rowsLength = $('tbody').find('.records-cls').length;
    let checkedCheckboxLength = $('tbody').find('input[type="checkbox"]:checked').length;
    if (rowsLength == checkedCheckboxLength)
      $(".selectAll").prop('checked', true)
    else
      $(".selectAll").prop('checked', false)

    if (checkedCheckboxLength > 0)
      this.userSelected = true;
    else
      this.userSelected = false;
  }

  unassignedUser() {
    let userFarmIds = []
    $('input[name="userFarmId"]:checked').each(function () {
      userFarmIds.push(this.value);
    });
    this.commonService.confirmation(userFarmIds.length == 1 ? "Would you like to unassigned staff?" : "Would you like to unassigned multiple staff?",
    this.unAssignedUserCallback.bind(this),userFarmIds);
  }

  async unAssignedUserCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.assignedUsersService.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
