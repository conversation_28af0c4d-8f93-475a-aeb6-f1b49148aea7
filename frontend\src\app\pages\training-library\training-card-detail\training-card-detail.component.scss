.training-detail {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    max-width: 1200px; /* Maximum width to center content */
    margin: 0 auto;
  }

  &-video {
    width: 100%;
    max-width: 960px; /* Larger width for a big video container */
    height: auto; /* Maintain aspect ratio */
    max-height: 540px; /* Max height for responsiveness */
    border-radius: 8px; /* Rounded corners */
    margin-bottom: 16px;
    background-color: black; /* Background color while loading */
  }

  &-content {
    width: 100%;
    max-width: 960px; /* Align with video width */
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px; /* Spacing between info and card */

    .training-detail-info {
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

      h2 {
        font-size: 24px;
        color: #333;
        margin-bottom: 8px;
      }

      p {
        font-size: 16px;
        color: #666;
        margin: 0;
      }
    }

    .training-detail-card {
      background-color: #ffffff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

      .training-detail-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .training-detail-card-author {
          display: flex;
          align-items: center;

          .training-detail-card-author-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
          }

          .training-detail-card-author-name {
            font-weight: bold;
            color: #333;
          }
        }

        .training-detail-card-date {
          font-size: 12px;
          color: #888;
        }
      }

      .training-detail-card-title {
        font-size: 24px;
        color: #222;
        margin: 10px 0;
      }

      .training-detail-card-divider {
        border: 0;
        height: 1px;
        background-color: #ddd;
        margin: 15px 0;
      }

      .training-detail-card-footer {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .training-detail-card-series-title {
          font-size: 18px;
          color: #555;
        }

        .training-detail-card-description {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}
