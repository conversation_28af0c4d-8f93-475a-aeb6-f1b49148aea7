<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
  <div class="dashboard-content-container">
    <div class="container admin-setting mt-5">
      <form *ngIf="user.id" #recordForm="ngForm" novalidate="novalidate">
        <div class="row">
          <div class="text-start">
            <h4 class="fw-bold">Settings</h4>
            <p class="user-edit-msg">Please Make sure fill all the filed before click on save button</p>
          </div>
          <div class="text-start mt-2 mb-2" *ngIf="!authService.isFarmAdmin()">
            <div class="profile-image-container position-relative"
              [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
              <img *ngIf="!profileImageLoader"
                [src]="user.profileImageUrl ? user.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
              <div *ngIf="profileImageLoader" class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <div *ngIf="!profileImageLoader"
                class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
                <label for="file-input">
                  <i class="bi bi-pencil-fill"></i>
                  <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect [uploader]="uploader"
                    id="file-input" type="file" accept="image/png, image/jpg, image/jpeg" />
                </label>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 mb-4">
            <div class="form-floating">
              <input class="form-control rounded" type="text" name="firstName" #firstName="ngModel"
                [(ngModel)]="user.firstName" required="required" placeholder="First Name"
                [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
              <label for="floatingInput">First Name</label>
            </div>
          </div>
          <div class="col-12 col-md-6 mb-4">
            <div class="form-floating">
              <input class="form-control rounded" name="lastname" #lastname="ngModel" [(ngModel)]="user.lastName"
                required="required" placeholder="Last Name"
                [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}">
              <label for="floatingInput">Last Name</label>
            </div>
          </div>
          <div class="col-12 col-md-6 mb-4">
            <div class="form-floating">
              <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control" type="email"
                name="email" #email="ngModel" [(ngModel)]="user.email" required="required" placeholder="Email"
                pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
              <label for="floatingInput">{{"USERS.Email" | translate}}</label>
            </div>
          </div>
          <div class="col-12 col-md-6 mb-4">
            <div class="form-floating">
              <input autocomplete="off" class="form-control phone_number" type="text" name="phone" ng2TelInput
                (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="user.phoneNumber"
                [ngClass]="{'is-invalid':!phone.valid && onClickValidation}" required="required"
                placeholder="Phone Number" minLength="7" maxlength="12" pattern="^[0-9]*$"
                (keypress)="onlyAllowNumbers($event)" (countryChange)="onCountryChange($event)">
            </div>
          </div>
          <div class="col-12 col-md-12 mb-4" *ngIf="authService.isFarmAdmin()">
            <div class="form-floating">
              <div class="form-control select-width ng-select-main-container">
                <ng-select
                  [items]="farms"
                  bindLabel="farmName"
                  bindValue="id"
                  [(ngModel)]="selectedFarms"
                  name="selectedFarms"
                  placeholder="Select Company"
                  [multiple]="true" [disabled]="true"
                  (ngModelChange)="onFarmsChange($event)">
                </ng-select>
              </div>
              <label for="selectedFarmIds">Company</label>
            </div>
          </div>
          <div class="admin-setting-button d-flex justify-content-end">
            <button class="setting-button border border-dark mx-2 btn rounded-3"
              [routerLink]="['/dashboard/admin-change-password']">Change password</button>
            <button (click)="save(recordForm.form.valid)"
              class="setting-button bg-secondary btn text-light rounded-3">Save</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>