import { Injectable } from '@angular/core';
import { HttpServiceRequests } from './http.service';
import { IResourceWithId, RestResponse } from './auth.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseService } from '../config/base.service';
import { FilterParam } from '../models/filterparam';

@Injectable({
  providedIn: 'root'
})
export class GumletService extends BaseService {

  fetchGumletStatus(filterParam: FilterParam): Observable<RestResponse> {
    return this.getRecord('/api/gumlet/' + filterParam.gumletId + '/status');
  }
}
