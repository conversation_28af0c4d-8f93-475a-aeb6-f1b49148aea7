import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AccountService } from 'src/app/services/account.service';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';

@Component({
  selector: 'app-staff-changepassword',
  templateUrl: './staff-changepassword.component.html',
  styleUrls: ['./staff-changepassword.component.scss']
})
export class StaffChangepasswordComponent implements OnInit {
  confirmPassword: any;
  data: any = {}
  onClickValidation: boolean = false;
  changePasswordButtonDisabled: boolean = false;
  showOldPassword: boolean = false;
  showNewPassword: boolean = false;
  showConfirmPassword: boolean = false;
  constructor(private accountService: AccountService, private toastService: ToastService, private router: Router, private authService: AuthService) { }

  ngOnInit(){
  }

  changePassword(bol) {
    this.onClickValidation = !bol;
    if (!bol) {
      return false;
    }
    if (this.data.password !== this.confirmPassword) {
      this.onClickValidation = true;
      return;
    }
    this.changePasswordButtonDisabled = true;
    this.accountService.staffOrAdminChangePassword(this.data)
      .then((data) => {
        if (!data.status) {
          this.changePasswordButtonDisabled = false;
          this.toastService.error(data.message);
          return;
        }
        this.authService.getRoles().includes('ROLE_STAFF') ? this.router.navigate(['/staff-profile']) :  this.router.navigate(['dashboard/admin-setting'])
        this.toastService.success(data.message);
      }, (error) => {
        this.changePasswordButtonDisabled = false;
        this.toastService.error(error.message);
      });
  }

}
