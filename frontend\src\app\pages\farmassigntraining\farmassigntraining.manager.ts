import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { FarmAssignTrainingService } from './farmassigntraining.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class FarmAssignTrainingManager extends BaseManager {

    constructor(private farmAssignTrainingService: FarmAssignTrainingService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(farmAssignTrainingService, loadingService, toastService);
    }
}
