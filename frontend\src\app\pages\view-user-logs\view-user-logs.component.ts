import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Constant } from 'src/app/config/constants';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { ToastService } from 'src/app/shared/toast.service';
import { ViewUserLogsService } from './view-user-logs.service';
import { ViewUserLogsManager } from './view-user-logs.manager';
import AOS from 'aos';
import { Course } from 'src/app/models/course';
import { RouteDataService } from 'src/app/shared/title.service';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-view-user-logs',
  templateUrl: './view-user-logs.component.html',
  styleUrls: ['./view-user-logs.component.scss']
})
export class ViewUserLogsComponent extends BaseListServerSideComponent implements OnInit {
  readonly MY_CONSTANT = Constant;
  trainingId: string;
  loadingVideo: boolean;
  programUserLogsTrainingVideoModal: any;
  moment: any = moment;
  searchTraining: any;
  isTrainingCsvExport: boolean = false
  userLogsFilterTrainingModal: any;
  username: any;
  fromDate: string;
  toDate: string;
  trainingFilterData: RestResponse;
  courses: Course;

  constructor(private route: ActivatedRoute, protected viewUserLogsManager: ViewUserLogsManager, protected commonService: CommonService,
    protected toastService: ToastService, protected loadingService: LoadingService,
    protected router: Router, private viewUserLogsService: ViewUserLogsService,
    private commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private authService: AuthService, private http: HttpClient, public routeDataService: RouteDataService) {
    super(viewUserLogsManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    this.records = new Array<Course>();
    this.courses = new Course();
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.request.loadEditPage = false;
    this.trainingId = this.route.snapshot.paramMap.get('id');
    this.viewUserLogsService.getUserId(this.trainingId);
    this.filterParam.trainingId = this.trainingId;
    this.init();
    // this.setCourseTitle();
  }

  // setCourseTitle(): void {
  //   const trainingId: string | null = this.route.snapshot.paramMap.get("id");
  //   if (trainingId) {
  //     this.viewUserLogsService.getcourseTitle(trainingId).subscribe(response => {
  //       const title = response?.data?.title || 'Manage Courses';
  //       this.routeDataService.setData(this.router.url, title);
  //     });
  //   }
  // }

  ngAfterViewInit() {
    setTimeout(() => {
      this.userLogsFilterTrainingModal = new bootstrap.Modal(
        document.getElementById('userLogsFilterTrainingModal')
      );
    }, 0);
    setTimeout(() => {
      this.programUserLogsTrainingVideoModal = new bootstrap.Modal(
        document.getElementById('programUserLogsTrainingVideoModal')
      );
    }, 0);
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  resetFilter() {
    delete this.filterParam.title;
    delete this.filterParam.startDate
    delete this.filterParam.endDate;
    delete this.filterParam.searchText;
    delete this.filterParam.status;
    this.userLogsFilterTrainingModal.hide();
    this.onCancel();
  }

  approveOrRejectTraining(id: string, status: string) {
    let data = {
      id: id,
      userId: this.trainingId,
      status: status
    }
    const statusText = status == "APPROVED" ? 'approve' : 'reject';
    const confirmatiomMessage = 'Would you like to ' + statusText + ' this training?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectTrainingCallback.bind(this), data);
  }

  async approveOrRejectTrainingCallback(data: boolean) {
    try {
      const response: RestResponse = await this.viewUserLogsService.approveOrRejectTraining(data)
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

  watchVideo(record: any) {
    this.loadingVideo = true
    this.programUserLogsTrainingVideoModal.show();
    AOS.init({ disable: true });
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.videoUrl);
      this.loadingVideo = false;
    }, 0)
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  //filter 
  openUserLogsFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      delete this.filterParam.searchCommonTitle;
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.userLogsFilterTrainingModal.show();
  }



}
