.vertical-line {
  border-left: 1px solid #000;
  height: 34px;
  margin: 10px 15px;
  width: 1px;
}

table.dataTable tbody th,
table.dataTable tbody td {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.site-customer-main-container .add-button {
  max-width: 155px;
}

//
.btn-close-dark {
  color: #000;
  border: none;
  opacity: 1;
}

//
.b-r-8 {
  border-radius: 8px !important;
}

.table> :not(caption)>*>* {
  padding: 0.5rem 10px;
  background-color: var(--bs-table-bg);
  border: 0px;
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}

.code {
  font-size: 24px;
  font-weight: 600;
  margin-top: 8px;
  white-space: nowrap;
  cursor: pointer;

}

.code:hover {
  color: #1681ff;
}

.modal-footer>* {
  margin: 0 !important;
}

// 

.custom-action-icon {
  border: 0;
  background-color: #fff;
}

.courseRate {
  border: 1px solid #000;
  padding: 10px;
  width: 100px;
  text-align: center;
}

.modal-footer>* {
  margin: 0 !important;
}

.title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  display: inline-block;
}