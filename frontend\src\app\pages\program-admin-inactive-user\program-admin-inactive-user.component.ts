import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminInactiveService } from './program-admin-inactive-user.service';
import { ManageUserCourseDetail } from 'src/app/models/manageusercoursedetail';
import { RestResponse } from 'src/app/shared/auth.model';
import { ProgramAdminInactiveManager } from './program-admin-inactive-user.manager';

@Component({
  selector: 'app-program-admin-inactive-user',
  templateUrl: './program-admin-inactive-user.component.html',
  styleUrls: ['./program-admin-inactive-user.component.scss']
})
export class ProgramAdminInactiveUserComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  searchEnrolled: any;

  @Input() filterParam: FilterParam;

  constructor(protected programAdminInactiveManager: ProgramAdminInactiveManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil,
    private programAdminInactiveService: ProgramAdminInactiveService, protected route: ActivatedRoute) {
    super(programAdminInactiveManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<ManageUserCourseDetail>();
    this.fetchEnrolledCourseRecords();
    this.init();
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  ngOnDestroy() {
    this.clean();
  }

  removeSuccess() {
    this.onCancel();
  }

  async fetchEnrolledCourseRecords(filterParam?: FilterParam) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.programAdminInactiveService.getEnrolledgCourse(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/user/edit/' + id])
  }

  removeUserRecord(id: string) {
    this.commonService.confirmation('Would you like to delete?', this.DeleteUserCallback.bind(this), id, null, null, null);
  }

  async DeleteUserCallback(id: string) {
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.programAdminInactiveService.removeUserAccess(id);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  activateDeactiveUserConfirmation(data: any, recordData: any) {
    const confirmationMessage = data.currentTarget.checked
      ? 'Are you sure you want to activate this user account?'
      : 'Are you sure you want to deactivate this user account?';

    this.commonService.confirmation(
      confirmationMessage,
      this.activateDeactiveCallback.bind(this),
      { id: recordData.id, isActive: data.currentTarget.checked },
      null,
      null,
      this.cancelActivateDeactiveCallback.bind(this)
    );
  }

  cancelActivateDeactiveCallback() {
    this.onCancel();
  }

  async activateDeactiveCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.programAdminInactiveService.activateDeactivateUser(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
