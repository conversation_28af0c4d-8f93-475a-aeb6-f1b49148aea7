import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Language } from './language';
import { Content } from './content';
export class CourseLearningSeries extends BaseModel {

	title: string;
	description: string;

	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
	}

	static fromResponse(data: any): CourseLearningSeries {
		const obj = new CourseLearningSeries();
		obj.id = data.id;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.title = data.title;
		obj.description = data.description;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.title)) {
			form.controls.title.setErrors({ invalid: true });
			return false;
		}
		if (this.isNullOrUndefinedAndEmpty(this.description)) {
			form.controls.description.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	forRequest() {
		this.title = this.trimMe(this.title);
		this.description = this.trimMe(this.description);
		return this;
	}
}
