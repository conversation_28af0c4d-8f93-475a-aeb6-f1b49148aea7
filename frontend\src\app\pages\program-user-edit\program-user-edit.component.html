<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container"
    style="padding: 20px 0px;">
    <div class="dashboard-content-container p-0">
        <div class="container program-admin-setting ">
            <form #recordForm="ngForm" novalidate="novalidate">
                <div class="row px-0">
                    <div>
                        <h4 class="fw-bold">{{!userDetails ? request.recordId == 0 ? "Add New User" : "Edit User" :
                            "Manage Profile"}}
                        </h4>
                        <p class="user-edit-msg">Please make sure you fill all the fields before you click on
                            {{request.recordId ==
                            0 ? 'save' : 'update'}} button</p>
                    </div>
                    <div class="col-12 col-md-12 mb-3">
                        <div class="profile-image-container position-relative"
                            [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
                            <img *ngIf="!profileImageLoader"
                                [src]="user.profileImageUrl ? user.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
                            <div *ngIf="profileImageLoader" class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div *ngIf="!profileImageLoader"
                                class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
                                <label for="file-input">
                                    <i class="bi bi-pencil-fill"></i>
                                    <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect
                                        [uploader]="uploader" id="file-input" type="file"
                                        accept="image/png, image/jpg, image/jpeg" />
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input class="form-control rounded" type="text" name="firstName" #firstName="ngModel"
                                [(ngModel)]="user.firstName" required="required" placeholder="First Name"
                                [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
                            <label for="floatingInput">First Name</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input class="form-control rounded" name="lastname" #lastname="ngModel"
                                [(ngModel)]="user.lastName" required="required" placeholder="Last Name"
                                [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}">
                            <label for="floatingInput">Last Name</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input class="form-control rounded" name="programName" #programName="ngModel"
                                [(ngModel)]="user.programName" required="required" placeholder="Program Name"
                                [ngClass]="{'is-invalid':!programName.valid && onClickValidation}">
                            <label for="floatingInput">Program Name</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control"
                                type="email" name="email" [readonly]="user.id" #email="ngModel" [(ngModel)]="user.email"
                                required="required" placeholder="Email"
                                pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                            <label for="floatingInput">{{"USERS.Email" | translate}}</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 mb-4">
                        <div class="form-floating">
                            <input autocomplete="off" class="form-control phone_number" type="tel" name="phone"
                                ng2TelInput (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                                (ng2TelOutput)="getNumber($event)" #phone="ngModel" pattern="^[0-9]*$"
                                [(ngModel)]="user.phoneNumber"
                                [ngClass]="{'is-invalid':!phone.valid && onClickValidation}"
                                required="required"
                                placeholder="Phone Number" minLength="7" maxlength="12"
                                (countryChange)="onCountryChange($event)" (keypress)="onlyAllowNumbers($event)">
                        </div>
                    </div>
                   
                    <div class="col-12 col-md-12">
                        <div class="form-floating form-floating-textarea mb-4 w-100">
                            <textarea maxlength="250" [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
                                class="form-control form-description" name="description" #description="ngModel"
                                [(ngModel)]="user.about" required="required" placeholder="Description"
                                id="floatingTextarea2"></textarea>
                            <label for="floatingTextarea2">About</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                    </div>
                    <!-- Program Profile Image Upload -->
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-control text-center border-2 p-3 border-dark" *ngIf="!user.programProfileImage"
                            [ngClass]="{'is-invalid': !user.programImageUrl && onClickValidation}">
                            <label class="upload-img-button cursor-pointer"
                                [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingProgramImage}">
                                <img src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">
                                <span *ngIf="!uploadingProgramImage">
                                    {{ 'UPLOAD PROGRAM PROFILE IMAGE' | translate }}
                                </span>
                                <span *ngIf="uploadingProgramImage">{{ fileUploadingMessage }}</span>
                                <div *ngIf="uploadingProgramImage" class="spinner-border ms-2" role="status"
                                    style="width: 1.7rem; height: 1.7rem">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <input *ngIf="!uploadingProgramImage" name="program-image" class="d-none"
                                    (change)="uploadProgramImageUrl($event)" id="program-file-input" type="file"
                                    accept="image/png, image/jpg, image/jpeg" />
                            </label>
                        </div>
                        <div *ngIf="user.programProfileImage"
                            class="moment-image-container mb-4 max-width-none position-relative">
                            <img [src]="user.programProfileImage" />
                            <div class="position-absolute delete-video-container"
                                (click)="removeProgramImage(user.programProfileImage)">
                                <i class="bi bi-x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-2"
                        [ngClass]="user.id ? 'justify-content-between' : 'justify-content-end'">
                        <span class="cursor-pointer" *ngIf="user.id" (click)="openContentTransferModal()">
                            <img class="me-1" width="25" src="/assets/images/icons/menu/content-transfer.svg" alt="">
                            Content
                            Transfer
                        </span>
                        <div class="admin-setting-button d-flex">
                            <button (click)="save(recordForm.form)"
                                class="setting-button bg-secondary btn text-light rounded-3">{{user.id ? 'Update' :
                                'Save'}}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- content transfer modal -->
    <div class="modal fade" id="adminContentTransferModal" tabindex="-1"
        aria-labelledby="adminContentTransferModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0" style="padding-top: 30px;
                      margin: auto 15px;">
                    <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                        (click)="closeContentTransferModal()"></button>
                </div>
                <div *ngIf="adminContentTransferModal && adminContentTransferModal._isShown" class="modal-body"
                    style="padding: 0px 40px;">
                    <div class="modal-content-inside">
                        <h5 class="modal-title fw-bold" id="adminContentTransferModalLabel">Content Transfer</h5>
                        <p class="modal-heading pt-1" id="adminContentTransferModalLabel">
                            Please make sure you fill in all the fields before you click on the Send button
                        </p>
                    </div>
                    <form #contentTransferForm="ngForm" novalidate="novalidate">
                        <div class="row px-0">
                            <div class="px-0">
                                <div class="col-12 col-md-12 mb-4">
                                    <div class="form-floating">
                                        <input class="form-control rounded" type="text" name="contentFirstName"
                                            #contentFirstName="ngModel" [(ngModel)]="transferUser.firstName"
                                            required="required" placeholder="First Name"
                                            [ngClass]="{'is-invalid':!contentFirstName.valid && onClickValidation}">
                                        <label for="floatingInput">First Name</label>
                                    </div>
                                </div>
                                <div class="col-12 col-md-12 mb-4">
                                    <div class="form-floating">
                                        <input class="form-control rounded" name="contentLastName"
                                            #contentLastName="ngModel" [(ngModel)]="transferUser.lastName"
                                            required="required" placeholder="Last Name"
                                            [ngClass]="{'is-invalid':!contentLastName.valid && onClickValidation}">
                                        <label for="floatingInput">Last Name</label>
                                    </div>
                                </div>
                                <div class="col-12 col-md-12 mb-4">
                                    <div class="form-floating">
                                        <input [ngClass]="{'is-invalid':!contentEmail.valid && onClickValidation}"
                                            class="form-control rounded" type="email" name="contentEmail"
                                            #contentEmail="ngModel" [(ngModel)]="transferUser.email" required="required"
                                            placeholder="Email" pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                                        <label for="floatingInput">{{"USERS.Email" | translate}}</label>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
                                <button (click)="SendContentTransfer(contentTransferForm.form)" type="button"
                                    class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- content transfer modal end-->

</div>
