import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountService } from 'src/app/services/account.service';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';

@Component({
  selector: 'app-program-change-password',
  templateUrl: './program-change-password.component.html',
  styleUrls: ['./program-change-password.component.scss']
})
export class ProgramChangePasswordComponent implements OnInit {
  confirmPassword: any;
  data: any = {}
  onClickValidation: boolean = false;
  changePasswordButtonDisabled: boolean = false;
  selectectProfileId: string;
  constructor(private accountService: AccountService, private toastService: ToastService, private route: ActivatedRoute, private router: Router, private authService: AuthService) { }

  ngOnInit(): void {
    this.selectectProfileId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
  }

  changePassword(bol) {
    this.onClickValidation = !bol;
    if (!bol) {
      return false;
    }
    if (this.data.password !== this.confirmPassword) {
      this.onClickValidation = true;
      return;
    }
    this.changePasswordButtonDisabled = true;
    this.accountService.staffOrAdminChangePassword(this.data)
      .then((data) => {
        if (!data.status) {
          this.changePasswordButtonDisabled = false;
          this.toastService.error(data.message);
          return;
        }
        // this.authService.getRoles().includes('RROLE_PROGRAM_ADMINISTRATOR') ? this.router.navigate(['/program-admin/profile-setting']) : this.router.navigate(['dashboard/program-admin/profile-setting'])
        this.toastService.success(data.message);
        this.authService.logout();
      }, (error) => {
        this.changePasswordButtonDisabled = false;
        this.toastService.error(error.message);
      });
  }

}
