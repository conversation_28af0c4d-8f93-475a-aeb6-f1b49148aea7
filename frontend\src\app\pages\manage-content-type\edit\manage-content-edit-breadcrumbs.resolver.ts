import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ManageContentEditBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const categoryId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard", active: false
            },
            {
                title: "Manage Contents Type", link: "/dashboard/manage-content", active: false
            },
            {
                title: "Content Type", link: "/dashboard/manage-content/edit/" + categoryId, active: true
            }
        ])



    }
}
