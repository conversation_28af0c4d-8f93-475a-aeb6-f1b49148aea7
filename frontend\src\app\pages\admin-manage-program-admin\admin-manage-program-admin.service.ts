import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { BaseService } from '../../config/base.service';
import { Observable } from 'rxjs';
import { RestResponse } from 'src/app/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class AdminManageProgramAdminService extends BaseService {
  strProgramAdmin: string;
  constructor(public http: HttpClient) {
    super(http, '', '');
  }

  getUserId(id: string) {
    this.strProgramAdmin = id;
  }

  getCourseFilter(filterParam: FilterParam): Promise < RestResponse > {
    return this.getRecords('/api/courses/program/admin', filterParam);
  }

  getTrainingFilterData(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/filter/data', null);
  }

}

