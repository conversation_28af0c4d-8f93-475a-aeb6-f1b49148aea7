import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UserEnrolledService } from './user-enrolled.service';

@Injectable({
    providedIn: 'root'
})
export class UserEnrolledManager extends BaseManager {

    constructor(protected userEnrolledService: UserEnrolledService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userEnrolledService, loadingService, toastService);
    }
}
