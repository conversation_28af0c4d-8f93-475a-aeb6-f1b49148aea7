import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MostCompletedTrainingsService } from './most-completed-trainings.service';

@Injectable({
    providedIn: 'root'
})
export class MostCompletedTrainingsManager extends BaseManager {

    constructor(protected mostCompletedTrainingsService: MostCompletedTrainingsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(mostCompletedTrainingsService, loadingService, toastService);
    }
}
