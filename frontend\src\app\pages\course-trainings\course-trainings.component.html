<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-4 mb-3">
        <div class="checkbox-container">
          <input type="checkbox" id="toggleTable" [(ngModel)]="isViewAllTableVisible">
          <label for="toggleTable" class="checkbox-label">View all trainings for ordering</label>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-12 col-sm-4 text-start">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="Search" appDelayedInput
            (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchTraining">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div class="col-12 col-sm-3">
      </div>
      <div class="col-12 col-sm-6 col-md-5 d-flex align-items-center justify-content-end mb-2 pe-0">
        <button (click)="openFilterTrainingModal()" type="button"
          class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px mb-2 pe-0">
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
        </button>
        <button type="button" class="btn add-button btn-primary btn-lg font-15px add-button-content pe-0 mb-2"
          (click)="onNewRecord()" *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','AddButton')">
          <img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-15px" alt="">Add New Training
        </button>
      </div>
    </div>
    <div class="table-responsive server-side-table" [hidden]="isViewAllTableVisible"
      [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger"
        style="width: 100%;">
        <thead>
          <tr>
            <th style="width: 110px; text-wrap: nowrap;">{{'Training.lastUpdatedDate' | translate}}</th>
            <th style="text-wrap: nowrap;">{{'Training.titles' | translate}}</th>
            <th style="text-wrap: nowrap;">{{'Training.contentType' | translate}}</th>
            <th style="text-wrap: nowrap;">{{'Training.learningSeries' | translate}}</th>
            <th style="width:80px; text-wrap: nowrap;">{{'Training.AccessibilityStatus' | translate}}</th>
            <th></th>
            <th style="width:50px; text-wrap: nowrap;">{{'Training.status' | translate}}</th>
            <th></th>
            <th style="width: 50px; text-wrap: nowrap;" class="text-center">{{'Training.action' |
              translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;let i = index">
            <td width="180">
              <div>{{moment(record.updatedOn).format('DD/MM/YYYY')}}</div>
            </td>
            <td>
              <a (click)="openTrainingDetailPage(record)" class="text-decoration-underline">{{record.title}}</a>
            </td>
            <td>
              <div>{{record.contentTypeDetail.title}}</div>
            </td>
            <td>
              <div>{{record.learningSeriesDetail.title}}</div>
            </td>
            <td>
              <div class="form-check form-switch">
                <input (change)="updateStatusAccssibility(record, i)"
                  [attr.title]="getTooltipText(record.accessibility)" data-bs-toggle="tooltip" data-bs-placement="top"
                  class="form-check-input accessibility-switch toggle-width" type="checkbox"
                  id="flexSwitchCheckCheckedAccssibility" [checked]="record.accessibility === 'Public'"
                  [ngModelOptions]="{standalone: true}">
              </div>
            </td>
            <td></td>
            <td>
              <div class="form-check form-switch">
                <input (change)="updateStatus($event,record)" class="form-check-input toggle-width" type="checkbox"
                  id="flexSwitchCheckChecked" [(ngModel)]="record.isPublish" [ngModelOptions]="{standalone: true}">
                <label class="form-check-label" for="flexSwitchCheckChecked45545"></label>
              </div>
            </td>
            <td></td>
            <td class="text-center custom-action-button text-right">
              <div class="d-flex justify-content-end mb-3 me-2">
                <i class="bi bi-pencil font-21px me-3 cursor-pointer"
                  *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','EditButton') && !isDetailPage && !isPlusButton"
                  [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','EditButton')" title="Edit"
                  (click)="editRecord(record.id)">
                </i>
                <i class="bi bi-trash cursor-pointer font-21px"
                  *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','DeleteButton') && !isPlusButton"
                  [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','DeleteButton')" title="Delete"
                  (click)="remove(record.id)"></i>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- <ng-template *ngIf="isViewAllTableVisible"> -->
    <div [hidden]="!isViewAllTableVisible" class="table-container" cdkDropList (cdkDropListDropped)="drop($event)"
      (cdkDragMoved)="dragMoved($event)">
      <table class="video-table table-responsive server-side-table">
        <thead>
          <tr>
            <th style="text-wrap: nowrap;">{{'Training.videoTitle' | translate}}</th>
            <th style="text-wrap: nowrap;">{{'Training.contentType' | translate}}</th>
            <th style="text-wrap: nowrap;">{{'Training.learningSeries' | translate}}</th>
            <th style="width:50px; text-wrap: nowrap;">{{'Training.status' | translate}}</th>
            <th style="width: 110px; text-wrap: nowrap;">{{'Training.lastUpdatedDate' | translate}}</th>
            <th style="width: 50px; text-wrap: nowrap;" class="text-center">{{'Training.action' |
              translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let video of videos" cdkDrag>
            <td><a (click)="openTrainingDetailPage(video)">{{ video.title }}</a></td>
            <td>{{ video.contentTypeDetail.title }}</td>
            <td>{{ video.learningSeriesDetail.title }}</td>
            <td>
              <div class="form-check form-switch">
                <input (change)="updateStatus($event,video)" class="form-check-input toggle-width" type="checkbox"
                  id="flexSwitchCheckChecked" [(ngModel)]="video.isPublish" [ngModelOptions]="{standalone: true}">
                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
              </div>
            </td>
            <td>{{ moment(video.updatedOn).format('DD/MM/YYYY') }}</td>
            <td class="text-center custom-action-button text-right">
              <div class="d-flex justify-content-end mb-3 me-2">
                <i class="bi bi-pencil font-21px me-3 cursor-pointer"
                  *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','EditButton') && !isDetailPage && !isPlusButton"
                  [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','EditButton')" title="Edit"
                  (click)="editRecord(record.id)">
                </i>
                <i class="bi bi-trash cursor-pointer font-21px"
                  *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','DeleteButton') && !isPlusButton"
                  [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','DeleteButton')" title="Delete"
                  (click)="remove(record.id)"></i>
              </div>
            </td>
            <!-- Custom Drag Preview -->
            <ng-template cdkDragPreview>
              <table class="video-table drag-preview-table">
                <tr>
                  <td><a href="#">{{ video.title }}</a></td>
                  <td>{{ video.contentTypeDetail.title }}</td>
                  <td>{{ video.learningSeriesDetail.title }}</td>
                  <td>
                    <div class="form-check form-switch">
                      <input (change)="updateStatus($event,video)" class="form-check-input toggle-width" type="checkbox"
                        id="flexSwitchCheckChecked" [(ngModel)]="video.isPublish" [ngModelOptions]="{standalone: true}">
                      <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                    </div>
                  </td>
                  <td>{{ moment(video.updatedOn).format('DD/MM/YYYY') }}</td>
                  <td class="text-center custom-action-button text-right">
                    <div class="d-flex justify-content-end mb-3 me-2">
                      <i class="bi bi-pencil font-21px me-3 cursor-pointer"
                        *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','EditButton') && !isDetailPage && !isPlusButton"
                        [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','EditButton')" title="Edit"
                        (click)="editRecord(record.id)">
                      </i>
                      <i class="bi bi-trash cursor-pointer font-21px"
                        *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','DeleteButton') && !isPlusButton"
                        [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','DeleteButton')"
                        title="Delete" (click)="remove(record.id)"></i>
                    </div>
                  </td>
              </table>
            </ng-template>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- </ng-template> -->

    <!-- filter training listing -->
    <div class="modal fade" id="filterTrainingModal" tabindex="-1" aria-labelledby="filterTrainingModalLabel"
      aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="filterTrainingModalLabel">Filter Training</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div *ngIf="filterTrainingModal && filterTrainingModal._isShown" class="modal-body">
            <form #trainingFilterForm="ngForm" novalidate="novalidate">
              <div class="form-floating">
                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                  [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
                  <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
                    [items]="trainingFilterData.data[0].learningSeriesDetail" bindLabel="title" bindValue="id"
                    class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.learningSeries"
                    #learningSeriesId="ngModel">
                  </ng-select>
                </div>
                <label for="language">{{"Training.learningSeries" | translate}}</label>
              </div>
              <div class="form-floating">
                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                  [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                  <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                    [items]="trainingFilterData.data[0].contentTypeDetail" bindLabel="title" bindValue="id"
                    class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.contentType"
                    #contentTypeId="ngModel">
                  </ng-select>
                </div>
                <label for="language">{{"Training.contentTypes" | translate}}</label>
              </div>
              <div class="form-floating mb-3">
                <select class="form-select form-control" name="status" aria-label="Please Select status"
                  [(ngModel)]="filterParam.isPublish" [ngClass]="{'is-invalid':!status.valid && onClickValidation}"
                  required="required" #status="ngModel">
                  <option [ngValue]="undefined" selected disabled>Select Option</option>
                  <option value="true">Published</option>
                  <option value="false">Draft</option>

                </select>
                <label for="type">{{"SubCategory.status" | translate}}</label>
              </div>
              <div class="form-floating">
                <select class="form-select form-control" name="accessibility"
                  aria-label="Please Select accessibility setup" [(ngModel)]="filterParam.accessibility"
                  [ngClass]="{'is-invalid':!accessibility.valid && onClickValidation}" required="required"
                  #accessibility="ngModel">
                  <option [ngValue]="undefined" selected disabled>Select Option</option>
                  <option value="PUBLIC">Public</option>
                  <option value="PRIVATE">Private</option>
                </select>
                <label for="type">{{"SubCategory.ACCESSIBILITY_SETUP" | translate}}</label>
              </div>
              <div class="modal-footer">
                <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
                <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
                  class="btn btn-primary">Filter</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
