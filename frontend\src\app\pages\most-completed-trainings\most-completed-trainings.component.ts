import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MostCompletedTrainingsManager } from './most-completed-trainings.manager';
import { MostCompletedTrainings } from 'src/app/models/mostcompletedtrainings';
import AOS from 'aos';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-most-completed-trainings',
  templateUrl: './most-completed-trainings.component.html',
  styleUrls: ['./most-completed-trainings.component.scss']
})
export class MostCompletedTrainingsComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  completedTrainingModal: any;
  completedTrainingData: any;

  constructor(protected mostCompletedTrainingsManager: MostCompletedTrainingsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router) {
    super(mostCompletedTrainingsManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
    this.completedTrainingModal.hide();
  }
  ngOnInit() {
    this.init();
    this.records = new Array<MostCompletedTrainings>();

  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.completedTrainingModal = new bootstrap.Modal(
        document.getElementById('completedTrainingModal')
      );
    }, 0)
  }
  openTrainingDetailPage(record: any) {
    window.open(
      '/dashboard/training/detail/' + record.id,
      '_blank'
    );
  }

  openUsersList(record: any) {
    AOS.init({ disable: true });
    this.completedTrainingData = record;
    // console.log(record);
    this.completedTrainingModal.show();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    // console.log(value);
    this.refreshRecord();
  }

}
