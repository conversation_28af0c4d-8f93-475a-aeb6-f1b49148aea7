<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
	<div class="dashboard-content-container d-block text-center">
		<form #contentForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
			<div class="row">
				<div class="mt-2 mb-3">
					<h4 class="fw-bold">{{request.recordId == 0 ? "Add New Content Type" : "Edit Content Type"}}</h4>
					<p class="user-edit-msg">Please make sure you fill all the fields before you click on
						{{request.recordId
						==
						0 ? 'save' : 'update'}} button
					</p>
				</div>
			</div>
			<div class="col-12 col-md-12 mb-4">
				<div class="form-floating mb-4 w-100">
					<input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
						class="form-control" type="text" name="title" #title="ngModel" [(ngModel)]="content.title"
						required="required" placeholder="{{'Category.CONTENT_TYPE_TITLE' | translate}}">
					<label for="floatingInput">{{"Category.CONTENT_TYPE_TITLE" | translate}}</label>
				</div>
			</div>
			<div class="col-12 col-md-12 mb-4">
				<div class="form-floating form-floating-textarea mb-4 w-100">
					<textarea maxlength="250" [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
						class="form-control form-description" name="description" #description="ngModel"
						[(ngModel)]="content.description" required="required" placeholder="Description"
						id="floatingTextarea2"></textarea>
					<label for="floatingInput">{{"Moment.description" | translate}}</label>
				</div>
			</div>
			<div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
				<button (click)="save(contentForm.form)"
					class="btn btn-secondary site-button btn-sm large-button save-button rounded-3">{{request.recordId ==
						0 ? 'SAVE' : 'UPDATE'}}</button>
			</div>
		</form>
	</div>
</div>