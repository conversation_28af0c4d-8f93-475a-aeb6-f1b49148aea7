import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { NewsFeedManager } from './news-feed.manager';
import { NewsFeed } from 'src/app/models/newsfeed';
import { AuthService } from 'src/app/shared/auth.services';
import AOS from 'aos';
import * as moment from 'moment';
import { RestResponse } from 'src/app/shared/auth.model';

declare var bootstrap: any;
declare const $: any;
@Component({
	selector: 'app-news-feed',
	templateUrl: './news-feed.component.html',
	styleUrls: ['./news-feed.component.scss']
})

export class NewsFeedComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
	searchNewsFeed: any;
	filterNewsFeedModal: any;
	restrictedUsersModal: any;
	restrictedUsersData: string;
	moment: any = moment;

	constructor(protected newsFeedManager: NewsFeedManager, protected toastService: ToastService,
		protected loadingService: LoadingService, protected commonService: CommonService,
		protected router: Router, public authService: AuthService) {
		super(newsFeedManager, commonService, toastService, loadingService, router);
	}


	ngOnInit() {
		this.request.loadEditPage = false;
		this.records = new Array<NewsFeed>();
		this.init();
	}

	ngAfterViewInit() {
		setTimeout(() => {
			this.filterNewsFeedModal = new bootstrap.Modal(
				document.getElementById('filterNewsFeedModal')
			);
		}, 0)
		setTimeout(() => {
			this.restrictedUsersModal = new bootstrap.Modal(
				document.getElementById('restrictedUsersModal')
			);
		}, 0)
	}

	fromDateOutput(event: any) {
		if (event) {
			this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
		} else {
			delete this.filterParam.startDate
		}
	}

	toDateOutput(event: any) {
		if (event) {
			this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
		} else {
			delete this.filterParam.endDate
		}
	}

	updateStatus(data: any, recordData: any) {
		this.commonService.confirmation('Would you like to change the status of News Feed?', this.updateStatusCallback.bind(this), { id: recordData.id, isPublish: data.currentTarget.checked }, null, null, this.cancelUpdateStatusCallback.bind(this));
	}

	cancelUpdateStatusCallback() {
		this.onCancel();
	}

	async updateStatusCallback(data: any) {
		try {
			this.loadingService.show();
			const response: RestResponse = await this.newsFeedManager.update(data);
			this.loadingService.hide();
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.onCancel();
			this.toastService.success(response.message);
		} catch (error) {
			this.loadingService.hide();
			this.toastService.error(error.message);
		}
	}

	onItemSelection(record: any) {
		this.onAssociatedValueSelected(record);
	}

	onCancel() {
		this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
	}

	onNewRecord() {
		if (!this.isPlusButton) {
			if (this.filterParam) {
				this.router.navigate(['/dashboard/news-feed/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
			} else {
				this.router.navigate(['/dashboard/news-feed/edit/0']);
			}
			return;
		}
		this.request.loadEditPage = true;
	}

	openFilterNewsFeedModal() {
		if (this.searchNewsFeed) {
			this.searchNewsFeed = "";
			delete this.filterParam.searchText;
			this.onCancel();
		}
		AOS.init({ disable: true });
		this.filterNewsFeedModal.show();
	}

	openRestrictedUsersList(record: string) {
		AOS.init({ disable: true });
		this.restrictedUsersData = record;
		this.restrictedUsersModal.show();
	}

	openNewsFeedDetailPage(record: any) {
		this.router.navigate(['/dashboard/news-feed/detail/' + record.id]);
	}

	onClickNewsFeedFilter(valid) {
		this.filterNewsFeedModal.hide()
		this.onCancel();
	}

	resetFilter() {
		delete this.filterParam.isPublish;
		delete this.filterParam.type;
		delete this.filterParam.startDate
		delete this.filterParam.endDate;
		delete this.filterParam.searchText;
		delete this.filterParam.isNewsFeedPublic;
		this.filterNewsFeedModal.hide();
		this.onCancel();
	}

	removeSuccess() {
		this.onCancel();
	}


	ngOnDestroy() {
		this.clean();
	}

	search($event) {
		const value = ($event.target as HTMLInputElement).value;
		this.filterParam.searchText = (value && value != '') ? value.trim() : null;
		this.refreshRecord();
	}

	editRecord(id: any) {
		this.router.navigate(['/dashboard/news-feed/edit/' + id])
	}

}
