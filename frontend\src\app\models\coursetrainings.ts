import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Category } from './category';
import { SubCategory } from './subcategory';
import { Language } from './language';
import { TrainingCategoryMapping } from './trainingcategorymapping';
import { TrainingSubCategoryMapping } from './trainingsubcategorymapping';
import { Content } from './content';
import { LearningSeries } from './learningseries';
import { TrainingLibraryDetail } from './trainingDetails';
export class CourseTrainings extends BaseModel {

	tenantId: number;
	slug: string;
	contentType: string;
	title: string;
	description: string;
	url: string;
	mediaType: string;
	accessibility: string = 'Public';
	isPublish: boolean;
	isPrivate: boolean;
	duration: number;
	thumbnailImageUrl: string;
	prerequisites: string;
	sequence: number;
	course: string;
	coursePart: string;
	learningSeries: string;
	learningSeriesDetail: any;
	isTermsConditions: boolean;
	isTermsAndConditions: boolean;
	PrerequisitesTraining: string;
	isCourseTraining: boolean = false
	contentTypeDetail: string;
	trainingLibraryDetail: TrainingLibraryDetail;
	isNewsFeed: boolean = false;
	uploadRequired: boolean;
	watchRequired: boolean;
	videoUrlTitle: string;
	thumbnailImageUrlTitle: string;


	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
		this.isPublish = false;
		this.mediaType = "VIDEO";
		this.isPrivate = false;
		this.trainingLibraryDetail = new TrainingLibraryDetail();
	}

	static fromResponse(data: any): CourseTrainings {
		const obj = new CourseTrainings();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.isPrivate = data.isPrivate;
		obj.mediaType = data.mediaType ? data.mediaType : "VIDEO";
		obj.title = data.title;
		obj.description = data.description;
		obj.url = data.url;
		obj.thumbnailImageUrl = data.thumbnailImageUrl;
		obj.contentType = data.contentType;
		obj.accessibility = data.accessibility;
		obj.isPublish = data.isPublish;
		obj.prerequisites = data.prerequisites;
		obj.sequence = data.sequence;
		obj.learningSeries = data.learningSeries;
		obj.course = data.course;
		obj.coursePart = data.coursePart;
		obj.isTermsConditions = data.isTermsConditions;
		obj.isTermsAndConditions = data.isTermsAndConditions;
		obj.PrerequisitesTraining = data.PrerequisitesTraining;
		obj.isCourseTraining = data.isCourseTraining;
		obj.contentTypeDetail = data.contentTypeDetail;
		obj.uploadRequired = data.uploadRequired;
		obj.watchRequired = data.watchRequired;
		obj.thumbnailImageUrlTitle = data.thumbnailImageUrlTitle;
		obj.videoUrlTitle = data.videoUrlTitle;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		return true;
	}

	forRequest() {
		this.title = this.trimMe(this.title);
		this.description = this.trimMe(this.description);
		this.url = this.trimMe(this.url);
		return this;
	}
}
