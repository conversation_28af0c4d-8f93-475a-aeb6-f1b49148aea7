<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th>{{'USERS.FirstName' | translate}}</th>
                        <th>{{'USERS.LastName' | translate}}</th>
                        <th width="100">{{'USERS.clickCount' | translate}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>
                            {{record?.userDetail.firstName}}
                        </td>
                        <td>
                            {{record?.userDetail.lastName}}
                        </td>
                        <td>
                            {{record?.clickCount}}
                        </td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
</div>