import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { NewsFeed } from 'src/app/models/newsfeed';
import { NewsFeedManager } from '../news-feed.manager';
import { ToastService } from 'src/app/shared/toast.service';
import { CommonService } from 'src/app/shared/common.service';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { BaseModel } from 'src/app/config/base.model';
import { RestResponse } from 'src/app/shared/auth.model';
import { TinyMceEditorService } from 'src/app/shared/tinyMce-editor.service';
import { Content } from '@angular/compiler/src/render3/r3_ast';
import { LearningSeries } from 'src/app/models/learningseries';
import { FilterParam } from 'src/app/models/filterparam';
import { UsersManager } from '../../users/users.manager';
import { TrainingService } from '../../training/training.service';
import { ManageContentManager } from '../../manage-content-type/manage-content.manager';
import { ManageLearningSeriesManager } from '../../manage-learning-series/manage-learning-series.manager';
import { Constant } from 'src/app/config/constants';
import { log } from 'console';
import { IImage, ImageCompressService, ResizeOptions } from 'ng2-image-compress';

@Component({
  selector: 'app-news-feed-edit',
  templateUrl: './news-feed-edit.component.html',
  styleUrls: ['./news-feed-edit.component.scss']
})
export class NewsFeedEditComponent extends BaseEditComponent implements OnInit {
  public isLoader: boolean;
  readonly MY_CONSTANT = Constant;
  public newsfeed: NewsFeed;
  public contentTypes: Content[];
  public learningSeries: any[];
  public loadingVideo: boolean = false;
  public videoPlaying: boolean = false;
  public acceptType: any[];
  uploader: any;
  fileData: any;
  users: any[] = [];
  usersSelected: any[] = [];
  dropdownSettings = {};
  deletedUsersSelected: any[] = [];
  fileUploadingMessage: string = "UPLOADING..";
  intervalId: NodeJS.Timeout;
  uploadingThumbnail: boolean;
  thumbnailUploader: any;
  fileUploadType: string;

  constructor(protected route: ActivatedRoute, protected newsFeedManager: NewsFeedManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private imgCompressService: ImageCompressService,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService,
    public commonUtil: CommonUtil, public tinyMceEditorService: TinyMceEditorService, private usersManager: UsersManager, private trainingService: TrainingService, private manageContentManager: ManageContentManager, private manageLearningSeriesManager: ManageLearningSeriesManager) {
    super(newsFeedManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.fileUploadType = "";
    this.newsfeed = new NewsFeed();
    this.fetchUsersList();
    this.setDropdownSettings();
    this.newsfeed.isActive = true;
    this.setRecord(this.newsfeed);
    this.contentTypes = new Array<Content>();
    this.learningSeries = new Array<LearningSeries>();
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.init();
    this.getLearningSeriesListOnEdit()
  }

  async fetchAssociatedData() {
    this.contentTypes = await this.manageContentManager.fetchAllData(this.filterParam);
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  async fetchUsersList() {
    let param = new FilterParam();
    const response: RestResponse = await this.usersManager.fetchActiveUsers(param);
    this.users = response.data.map(user => {
      return {
        id: user.id,
        fullName: user.fullName + " (" + user.email + ")",
      }
    });
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  async setlearningSeriesData(filterParam: FilterParam) {
    const response: RestResponse = await this.trainingService.getLearningSeriesByContentTypeId(filterParam);
    if (response.status) {
      this.learningSeries = response.data.map(data => {
        return {
          id: data.learningSeriesDetail.id,
          title: data.learningSeriesDetail.title
        }
      })
    }
  }

  selectContentType(event: any) {
    // this.newsfeed.learningSeries = undefined;
    // let param = new FilterParam();
    // param.strCategoryId = event.id
    // this.setlearningSeriesData(param);
  }

  selectLearningSeries(event: any) {
    this.newsfeed.accessibility = event.accessibility;
    // if (event.accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED) {
    //   this.loadingService.show()
    //   this.filterParam.learningSeriesId = event.id;
    //   this.deleteAllSelectedUsers();
    //   this.fetchlearningUsersList("fetchLearningSeriesUsers", this.filterParam, event.accessibility, false);
    // } else {
    //   this.loadingService.show()
    //   this.deleteAllSelectedUsers();
    //   this.fetchlearningUsersList("fetchActiveUsers", null, null, false);
    // }
  }

  addUser(event: any) {
    this.newsfeed.usersAssignTraining.push({ userId: event.id });
  }

  removeUser(event: any) {
    let index = this.newsfeed.usersAssignTraining.findIndex(user => user.userId == event.id);
    if (this.newsfeed.usersAssignTraining[index].id) {
      this.newsfeed.usersAssignTraining[index].isDeleted = true;
      this.deletedUsersSelected.push(this.newsfeed.usersAssignTraining[index]);
      this.newsfeed.usersAssignTraining.splice(index, 1);
    } else {
      this.newsfeed.usersAssignTraining.splice(index, 1);
    }
  }

  async getLearningSeriesListOnEdit() {
    this.learningSeries = await this.manageLearningSeriesManager.fetchAllData(this.filterParam);
  }

  onFetchCompleted() {
    this.newsfeed = NewsFeed.fromResponse(this.record);
    this.setRecord(this.newsfeed);
    this.selectMediaType(this.newsfeed.mediaType, this.newsfeed.mediaType)
    // if (this.newsfeed.accessibility == 'RESTRICTED') {
    //   let usersSelected: any = [];
    //   this.newsfeed.usersAssignTraining.map(user => {
    //     usersSelected.push({
    //       id: user.userId,
    //       fullName: user.userFirstName + " " + user.userLastName,
    //     })
    //   })
    //   this.usersSelected = usersSelected;
    // }
  }

  selectType(oldVal: string, event: any) {
    if (oldVal !== event) {
      this.newsfeed.mediaType = undefined;
      this.newsfeed.url = undefined;
      this.newsfeed.learningSeries = undefined;
      this.newsfeed.contentType = undefined;
      this.newsfeed.uploadRequired = false;
      this.newsfeed.watchRequired = false;
    }
    this.newsfeed.type = event;
  }

  deleteAllSelectedUsers() {
    if (this.newsfeed.usersAssignTraining && this.newsfeed.usersAssignTraining.length > 0) {
      this.newsfeed.usersAssignTraining.map(user => {
        if (user.id) {
          this.deletedUsersSelected.push({
            ...user,
            isDeleted: true
          })
        }
        return user;
      })
    }
    this.newsfeed.usersAssignTraining = [];
    this.usersSelected = [];
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/news-feed');
  }

  removeFile(fileUrl: string) {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), fileUrl);
  }

  removeFileCallback(fileUrl: string) {
    if (this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO && fileUrl == this.newsfeed.url) {
      this.commonService.deleteVideo(this.newsfeed.url)
    }
    if (fileUrl == this.newsfeed.url) {
      this.newsfeed.url = "";
      this.videoPlaying = false;
      this.newsfeed.duration = 0;
    }
    if (fileUrl == this.newsfeed.thumbnailImageUrl) {
      this.newsfeed.thumbnailImageUrl = "";
    }
  }

  selectMediaType(oldVal: string, event: string) {
    if (oldVal !== event) {
      this.newsfeed.url = "";
    }
    this.newsfeed.mediaType = event;
    if (event === this.MY_CONSTANT.MEDIA_TYPE.IMAGE) {
      this.acceptType = ['image/png', 'image/jpg', 'image/jpeg'];
      this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
    }
    if (event === this.MY_CONSTANT.MEDIA_TYPE.VIDEO) {

      this.acceptType = ['video/mp4', 'video/avi', 'video/x-matroska', 'video/quicktime'];
      this.uploader = this.initializeUploader(null, 'mkv,mp4,avi,mov', null, null, this.toastService, "Only Avi, Mp4, Mkv, Mov are allowed", null)

      this.thumbnailUploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
    }
    this.newsfeed.mediaType = event;
  }

  async uploadVideoOrImage(event: any) {
    this.fileUploadType = 'video';
    if (event) {
      if (this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO) {
        const video: any = await this.commonUtil.loadVideo(event.target.files[0])
        this.newsfeed.duration = video.duration;
        if (this.newsfeed.mediaType === 'VIDEO') {
          this.loadingService.show();

        }
      }
      this.isLoader = true;
      const file = event.target.files[0];
      if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg"
        && event.target.files[0].type != "image/png") {
        this.commonService.convertVideoFormat(file).then(res => {
          this.fileData = {} as any;
          this.fileData.files = [] as any;
          this.fileData.files.push(res);
          this.onFileProcessingCompleted(this.fileData.files);
        });
      }
      else {
        this.fileData = {} as any;
        this.fileData.files = event.target.files;

        if (this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.IMAGE) {
          let images: Array<IImage> = [];
          ImageCompressService.filesToCompressedImageSource(this.fileData.files).then(observableImages => {
            observableImages.subscribe((image) => {
              // console.log(image);
              images.push(image);
            }, (error) => {
              // console.log("Error while converting");
            }, () => {
              let compresImages = new Array<any>();
              let obj: any = {};
              obj.base64 = images[0].compressedImage.imageDataUrl;
              obj.type = images[0].type;
              compresImages.push(this.base64ToFile(obj));
              // console.log(compresImages, 'compress images', this.fileData.files, 'file data files', this.newsfeed.thumbnailImageUrl, 'url')
              this.onFileProcessingCompleted(compresImages);
            });
          });
          return;
        }
        this.onFileProcessingCompleted(this.fileData.files);
      }
    }
  }

  // uploadThumbnail(event: any) {
  //   if (event) {
  //     this.uploadingThumbnail = true
  //     this.fileData = {} as any;
  //     this.fileData.files = event.target.files;
  //     this.onThumbnailFileProcessingCompleted(this.fileData.files);
  //   }
  // }

  uploadThumbnail(event: any) {
    this.fileUploadType = 'thumbnailImage';
    // if (event && event.target.files && event.target.files.length > 0) {
    this.uploadingThumbnail = true;
    this.fileData = {} as any;
    this.fileData.files = event.target.files;
    //this.onThumbnailFileProcessingCompleted(this.fileData.files);
    this.compressThumbnail();
    // }
  }


  compressThumbnail() {
    let images: Array<IImage> = [];
    let option: ResizeOptions = { Resize_Quality: 90, Resize_Max_Width: 500, Resize_Max_Height: 400, Resize_Type: 'jpeg' };
    ImageCompressService.filesToCompressedImageSource(this.fileData.files).then(observableImages => {
      observableImages.subscribe((image) => {
        // console.log(image);
        images.push(image);
      }, (error) => {
        // console.log("Error while converting");
      }, () => {
        let compresImages = new Array<any>();
        let obj: any = {};
        obj.base64 = images[0].compressedImage.imageDataUrl;
        obj.type = images[0].type;
        compresImages.push(this.base64ToFile(obj));
        // console.log(compresImages, 'compress images', this.fileData.files, 'file data files', this.newsfeed.thumbnailImageUrl, 'url')
        this.onThumbnailFileProcessingCompleted(compresImages);
      });
    });
  }

  base64ToFile(obj: any) {
    const byteCharacters = atob(obj.base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, ''));
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: obj.type });
    var extention = obj.type.split('/');
    let file = new File([blob], 'cropImage.' + extention[1], { type: obj.type });
    return file;
  }


  selectAccessibility(oldVal: boolean, event: boolean) {
    // if (oldVal !== event) {
    //   this.deleteAllSelectedUsers();
    // }
    this.newsfeed.isNewsFeedPublic = event;
    this.newsfeed.isPublish = event;
  }

  selectTrainingRequired(event: boolean, type) {
    if (type == this.MY_CONSTANT.TRAINING_REQUIRED.WATCH_REQUIRED) {
      this.newsfeed.watchRequired = event;
      this.newsfeed.uploadRequired = false;
    }
    if (type == this.MY_CONSTANT.TRAINING_REQUIRED.UPLOAD_REQUIRED) {
      this.newsfeed.watchRequired = false;
      this.newsfeed.uploadRequired = event;
    }
  }

  async onUploadSuccess(file: any, files: any) {
    this.loadingService.hide();

    let gumletResponse = null;
    const filePath = this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO && file.streamingPath ? file.streamingPath : file.path;
    if (file.streamingId && this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO && this.fileUploadType == 'video') {

      this.filterParam.gumletId = file.streamingId

      gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

      this.intervalId = setInterval(async () => {
        gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

        if (gumletResponse.status) {

          this.fileUploadingMessage = gumletResponse.data.status ? gumletResponse.data.status.toUpperCase() + ".." : this.fileUploadingMessage = "UPLOADING..";

          if (gumletResponse.data.status == "ready" || gumletResponse.data.status == "queued" || gumletResponse.data.status == "downloading" || gumletResponse.data.status == "downloaded" || gumletResponse.data.status == "validating" || gumletResponse.data.status == "errored") {
            if (gumletResponse.data.status == "errored") {
              // Handle the "errored" status
              // console.log("errored:", gumletResponse.data.responseMessage);
              this.toastService.error('Video is errored while processing');
              this.isLoader = false; // Set loading to false
              clearInterval(this.intervalId); // Clear the interval
              return;
              // You can add additional handling for the error if needed
              // You might want to set a flag or handle the error state appropriately
            }
            else {
              if (this.intervalId) {
                clearInterval(this.intervalId);
              }
              this.newsfeed.url = filePath;
              this.fileUploadingMessage = "UPLOADING..";
              this.isLoader = false;

            }
            // if (this.intervalId) {
            //   clearInterval(this.intervalId);
            // }
            // this.newsfeed.url = filePath;
            // this.fileUploadingMessage = "UPLOADING.."
            // this.isLoader = false;
          }
        } else {
          if (this.intervalId) {
            clearInterval(this.intervalId);
          }
          this.newsfeed.url = file.path;
          this.fileUploadingMessage = "UPLOADING.."
          this.isLoader = false;
        }
      }, 5000);
    } else {
      if (this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.IMAGE && (file.mimeType.match('image.*') || this.fileUploadType == 'thumbnailImage')) {
        this.newsfeed.thumbnailImageUrl = file.path;
        this.uploadingThumbnail = false;
        this.isLoader = false;
      } else {
        this.newsfeed.url = filePath;
        this.isLoader = false;
      }
    }
  }

  onFileProcessingCompleted(files) {
    this.uploader.addToQueue(files);
    this.uploader.uploadAll();
  }

  onThumbnailFileProcessingCompleted(files) {
    this.thumbnailUploader.addToQueue(files);
    this.thumbnailUploader.uploadAll()
  }

  playVideoFromPlayIcon() {
    var videoId = document.getElementById("videoId") as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      videoId.controls = true;
      this.videoPlaying = true;
    });
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;

    });
  }


  checkConditionToReload(records: BaseModel[], selectedRecord: any) {
    if (!records.some(x => x.id === selectedRecord.id)) {
      this.fetchAssociatedData();
    }
  }

  async save(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    if (!this.newsfeed.url && this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO) {
      this.onClickValidation = true;
      return
    }

    if (this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.IMAGE && !this.newsfeed.thumbnailImageUrl) {
      this.onClickValidation = true;
      return
    }

    if(this.newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO){
      this.newsfeed.thumbnailImageUrl = null;
    }else{
      this.newsfeed.url = null;
    }
    // if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
    // 	return;
    // }
    let newsfeed = JSON.parse(JSON.stringify(this.newsfeed));
    newsfeed.usersAssignTraining = newsfeed.usersAssignTraining.concat(this.deletedUsersSelected);
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.manager[method](newsfeed);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchlearningUsersList(methodType: string, filterParam: FilterParam, accessibility: string, editRecord: boolean) {

    try {
      const response: RestResponse = await this.usersManager[methodType](filterParam);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      this.users = response.data.map(user => {
        return {
          id: accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED ? user.userId : user.id,
          fullName: accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED ? user.userFirstName + " " + user.userLastName + " (" + user.userEmail + ")" : user.fullName + " (" + user.email + ")",
        }
      });
      if (accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED && !editRecord) {
        this.usersSelected = this.users;
        const usersId = this.users.map(user => ({ userId: user.id }));
        this.newsfeed.usersAssignTraining = usersId;
      }
      this.loadingService.hide();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
