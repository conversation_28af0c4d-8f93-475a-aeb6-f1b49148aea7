import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { AdminManageProgramAdminService } from './admin-manage-program-admin.service';

@Injectable({
    providedIn: 'root'
})
export class AdminManageProgramAdminManager extends BaseManager {

    constructor(protected adminManageProgramAdminService: AdminManageProgramAdminService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(adminManageProgramAdminService, loadingService, toastService);
    }
}
