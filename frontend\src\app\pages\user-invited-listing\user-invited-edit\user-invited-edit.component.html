<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
    <div class="dashboard-content-container d-block text-center">
        <form #learningSeriesForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
            <div class="row">
                <div class="mt-2 mb-3">
                    <!-- <h4 class="fw-bold">{{request.recordId "Edit User Invited"}}
                    </h4> -->
                    <p class="user-edit-msg">Please make sure you fill all the fields before you click on
                        {{request.recordId ==
                        0 ? 'save' : 'update'}} button
                    </p>
                </div>
            </div>
            <div class="col-12 col-md-12">
                <div class="form-floating mb-4 w-100">
                    <input maxlength="80" [ngClass]="{'is-invalid':!userName.valid && onClickValidation}"
                        class="form-control" type="text" name="userName" #userName="ngModel"
                        [(ngModel)]="courseInvited.username" required="required"
                        placeholder="{{'Course.userName' | translate}}">
                    <label for="floatingInput">{{"Course.userName" | translate}}</label>
                    <app-validation-message [field]="userName" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div>
            </div>
            <div class="col-12">
                <div class="form-floating mb-4">
                    <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control b-r-8"
                        type="email" name="email" #email="ngModel" [(ngModel)]="courseInvited.email" required
                        placeholder="{{'Course.email' | translate}}"
                        pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                    <label for="floatingInput">{{"Course.email" | translate}}</label>
                </div>
                <app-validation-message [field]="email" [onClickValidation]="onClickValidation">
                </app-validation-message>
            </div>
            <div class="col-12 col-md-12">
                <!-- <div class="form-floating mb-4 w-100">
                    <input minlength="3" [ngClass]="{'is-invalid':!code.valid && onClickValidation}"
                        class="form-control" type="text" name="code" #code="ngModel"
                        [(ngModel)]="courseInvited.courseDetail.code" required="required"
                        placeholder="{{'Course.code' | translate}}">
                    <label for="floatingInput">{{"Course.code" | translate}}</label>
                    <app-validation-message [field]="code" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div> -->
                <div class="form-floating">
                    <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                        [ngClass]="{'is-invalid': !selectedCourseCode.valid && onClickValidation}">
                        <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="selectedCourseCode"
                            [items]="publishCourseList" bindLabel="code" bindValue="id"
                            (change)="selectInviteUserCourse($event)"
                            class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="selectedCourseUserId"
                            #selectedCourseCode="ngModel" [searchable]="false">
                        </ng-select>
                    </div>
                    <label for="selectedCourseCode">
                        Choose Code
                    </label>
                </div>
            </div>
            <div class="col-md-12 mt-4 d-flex justify-content-end">
                <button (click)="save(learningSeriesForm.form)"
                    class="btn btn-secondary site-button btn-sm large-button save-button rounded-3">{{request.recordId
                    ==
                    0 ? 'SAVE' : 'UPDATE'}}</button>
            </div>
        </form>
    </div>
</div>