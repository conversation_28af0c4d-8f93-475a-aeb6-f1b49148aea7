import { Component, OnDestroy, OnInit } from '@angular/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { MostWatchNewsFeedsManager } from './most-watch-news-feed.manager';
import { Router } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from 'src/app/shared/auth.services';
import { MostWatchNewsFeed } from 'src/app/models/mostwatchnewsfeed';
import AOS from 'aos';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-most-watched-news-feed',
  templateUrl: './most-watched-news-feed.component.html',
  styleUrls: ['./most-watched-news-feed.component.scss']
})
export class MostWatchedNewsFeedComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  newsFeedVideoProgressModal: any;
  newsFeedVideoProgressData: any;

  constructor(protected mostWatchNewsFeedManager: MostWatchNewsFeedsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router) {
    super(mostWatchNewsFeedManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
    this.newsFeedVideoProgressModal.hide();
  }
  ngOnInit() {
    this.init();
    this.records = new Array<MostWatchNewsFeed>();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.newsFeedVideoProgressModal = new bootstrap.Modal(
        document.getElementById('newsFeedVideoProgressModal')
      );
    }, 0)
  }

  openNewsFeedDetailPage(record: any) {
    window.open(
      '/dashboard/news-feed/detail/' + record.id,
      '_blank'
    );
  }

  openUsersList(record: any) {
    AOS.init({ disable: true });
    this.newsFeedVideoProgressData = record;
    this.newsFeedVideoProgressModal.show();
  }


  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    this.refreshRecord();
  }

}
