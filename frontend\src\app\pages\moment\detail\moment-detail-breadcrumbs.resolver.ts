import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MomentDetailBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const momentId: string | null = route.paramMap.get("id");

    return of([
      {
        title: "Dashboard", link: "/dashboard", active: false
      },
      {
        title: "Manage Moments", link: "/dashboard/moments", active: false
      },
      {
        title: "Moment Detail", link: "/dashboard/moment/detail/" + momentId, active: true
      }
    ])
  }



}
