import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class FarmService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/farm', '/api/admin/farms');
    }

    fetchAvailableFarms(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/available/farms', filterParam);
    }

    verifyFarmCode(farmCode: string): Observable<RestResponse> {
        return this.getRecord(`/api/farm/check-farmcode/` + farmCode);
    }

    fetchAllAvailableFarms(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/all/available/farms', filterParam);
    }

}

