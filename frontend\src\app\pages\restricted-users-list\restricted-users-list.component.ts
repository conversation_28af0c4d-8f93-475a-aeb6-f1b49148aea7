import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { ToastService } from 'src/app/shared/toast.service';
import AOS from 'aos';
import { RestrictedUsersManager } from './restricted-users.manager';
import { RestrictedUsers } from 'src/app/models/restrictedusers';
import { FilterParam } from 'src/app/models/filterparam';
import { RestrictedUsersService } from './restricted-users.service';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-restricted-users-list',
  templateUrl: './restricted-users-list.component.html',
  styleUrls: ['./restricted-users-list.component.scss']
})
export class RestrictedUsersListComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() getRestrictedUsersData: any | undefined;
  @Input() apiUrl: string | undefined;
  @Input() hideVideoOrImage: boolean | undefined;
  @Input() showDescription: boolean | undefined;
  @Input() getFilterParam: FilterParam | undefined;
  recordData: any;
  restrictedUsersVideoOrImageModal: any;
  loadingVideo: boolean = false;
  mediaType: string;
  constructor(protected restrictedUsersManager: RestrictedUsersManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private restrictedUsersService: RestrictedUsersService ) {
    super(restrictedUsersManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
  }

  ngOnInit(){
    this.filterParam = this.getFilterParam
    this.restrictedUsersService.apiUrl = this.apiUrl
    this.init();
    this.records = new Array<RestrictedUsers>();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.restrictedUsersVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('restrictedUsersVideoOrImageModal')
      );
    }, 0)
  }

  getMediaType(url) {
    if(url){
      const extension = url.split(/[#?]/)[0].split('.').pop().trim();
      if (extension == "jpg" || extension == "jpeg" || extension == "png") {
        this.mediaType = "image"
      }
      if (extension == "mkv" || extension == "mp4" || extension == "avi" || extension == 'avi' || extension == "mov") {
        this.mediaType = "video";
      }
    }
  }

  getRestrictedUserMediaType(mediaUrl: any, type: string) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      if (type == 'title') {
        return "Open Image"
      }
      if (type == 'icon') {
        return 'image-solid.svg'
      }
    }
    if (this.mediaType == 'video') {
      if (type == 'title') {
        return "Watch Video"
      }
      if (type == 'icon') {
        return 'video-logo.svg'
      }
    }
  }

  getImageWidthClass(mediaUrl: any) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      return true;
    } else {
      return false;
    }
  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.url);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.restrictedUsersVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.url);
        this.loadingVideo = false;
      }, 0)
    }
  }

}
