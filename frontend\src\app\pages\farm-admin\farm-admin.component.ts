import { Component, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { UsersService } from 'src/app/services/users.service';
import { AuthService } from 'src/app/shared/auth.services';
import { RouteDataService } from 'src/app/shared/title.service';
import { ToastService } from 'src/app/shared/toast.service';
import { FarmAdminService } from './farm-admin.service';
import { FarmAdminManager } from './farm-admin.manager';
import { CommonUtil } from 'src/app/shared/common.util';
import { CommonService } from 'src/app/shared/common.service';
import { FarmAdmin } from 'src/app/models/farmadmin';
import { RestResponse } from 'src/app/shared/auth.model';
import { UsersManager } from '../users/users.manager';
import AOS from 'aos';
import { FarmService } from '../farm/farm.service';
import { Farm } from 'src/app/models/farm';

declare var bootstrap: any;

@Component({
  selector: 'app-farm-admin',
  templateUrl: './farm-admin.component.html',
  styleUrls: ['./farm-admin.component.scss']
})
export class FarmAdminComponent extends BaseListServerSideComponent implements OnInit {

  tabView: string;
  selectedRecord: any = null;
  filterModal: any;
  farms: Farm[];
  private queryParamsProcessed = false; // Flag to track if query params were processed
  
  // Override the records property with the correct type
  records: FarmAdmin[];
  
  // Count variables for the badges
  activeFarmAdminCount: number = 0;
  inActiveFarmAdminCount: number = 0;
  invitedFarmAdminCount: number = 0;
  
  // Tab options array for better maintainability
  tabOptions = [
    { value: 'ACTIVE', label: 'Active' },
    { value: 'INACTIVE', label: 'Inactive' },
    { value: 'INVITED', label: 'Invited' }
  ];

  constructor(
    private readonly route: ActivatedRoute,
    public authService: AuthService,
    protected router: Router,
    private readonly zone: NgZone,
    protected toastService: ToastService,
    protected loadingService: LoadingService,
    private readonly usersService: UsersService,
    public routeDataService: RouteDataService,
    protected userService: UsersService,
    protected usersManager: UsersManager,
    protected farmAdminService: FarmAdminService, protected farmAdminManager: FarmAdminManager,
    protected commonService: CommonService, public commonUtil: CommonUtil,
    protected farmService: FarmService,
    private location: Location
  ) {
    super(farmAdminManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<FarmAdmin>();
    this.route.queryParams.subscribe(params => {
      if (params['farmId'] && !this.queryParamsProcessed) {
        this.queryParamsProcessed = true; // Mark as processed
        this.filterParam.farm = params['farmId'];
        if (params['tabView']) {
          this.filterParam.status = this.tabView = params['tabView'];
        } else {
          this.filterParam.status = this.tabView = 'ACTIVE';
        }
        setTimeout(() => {
          const url = this.router.createUrlTree([], {
            relativeTo: this.route,
            queryParams: {}
          });
          this.location.replaceState(url.toString());
        }, 0);
      } else if (!params['farmId'] && !this.queryParamsProcessed) {
        this.filterParam.status = this.tabView = 'ACTIVE';
        this.queryParamsProcessed = true;
      }
    });
    console.log(this.records, 'records');

    this.fetchFarms();
    this.init();

  }

  fetchFarms() {
    this.farmService.fetchAvailableFarms(this.filterParam).then((response: RestResponse) => {
      this.farms = response.data;
      this.farms.forEach(farm => {
        farm.displayLabel = farm.farmCode.trim() + ' - ' + farm.name;
      });
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.filterModal = new bootstrap.Modal(
        document.getElementById('filterModal')
      );
    }, 0)
  }

  onClickTab(tabType: string) {
    this.filterParam.status = this.tabView = tabType;
    this.refreshRecord();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  activateDeactiveUserConfirmation(data: any, recordData: any) {
    const confirmationMessage = data.currentTarget.checked
      ? 'Are you sure you want to activate this user account?'
      : 'Are you sure you want to deactivate this user account?';

    this.commonService.confirmation(
      confirmationMessage,
      this.activateDeactiveCallback.bind(this),
      { id: recordData.id, isActive: data.currentTarget.checked },
      null,
      null,
      this.cancelActivateDeactiveCallback.bind(this)
    );
  }

  cancelActivateDeactiveCallback() {
    this.onCancel();
  }

  async activateDeactiveCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userService.activateDeactivateUser(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  remove(id: string, path?: string) {
    this.commonService.confirmation('Would you like to delete?', this.removeCallback.bind(this), id, path);
  }

  async removeCallback(id: string, path?: string) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.usersManager.remove(id);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.removeSuccess();
      if (path) {
        this.router.navigateByUrl(path);
      }
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  removeSuccess() {
    this.refreshRecord();
  }

  // Add this method to show all farms in a modal
  showAllFarms(record: any) {
    this.selectedRecord = record;
    setTimeout(() => {
      const modal: any = document.getElementById('allFarmsModal');
      if (modal) {
        // Bootstrap 5 modal
        const bsModal = new (window as any).bootstrap.Modal(modal);
        bsModal.show();
      }
    }, 0);
  }

  openfilterModal() {
    AOS.init({ disable: true });
    this.filterModal.show();
  }

  resetFilter() {
    delete this.filterParam.farm;
    this.filterModal.hide();
    this.onCancel();
  }

  applyFilter(valid) {
    this.filterModal.hide()
    this.onCancel();
  }

  farmAdminCount(farmId: number) {
    this.filterParam.farm = String(farmId);
    this.applyFilter(true);
  }

  updateCountVariables() {
    if (this.records && this.records.length > 0 && this.tabView == 'ACTIVE') {
      this.activeFarmAdminCount = this.records[0]?.activeFarmAdminCount || 0;
      this.inActiveFarmAdminCount = this.records[0]?.inActiveFarmAdminCount || 0;
      this.invitedFarmAdminCount = this.records[0]?.invitedFarmAdminCount || 0;
    }
  }

  onFetchCompleted() {
    // Update count variables when data is fetched
    this.updateCountVariables();
  }

}
