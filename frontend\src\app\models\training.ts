import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Category } from './category';
import { SubCategory } from './subcategory';
import { Language } from './language';
import { TrainingCategoryMapping } from './trainingcategorymapping';
import { TrainingSubCategoryMapping } from './trainingsubcategorymapping';
import { Content } from './content';
import { LearningSeries } from './learningseries';
import { TrainingLibraryDetail } from './trainingDetails';
export class Training extends BaseModel {

	tenantId: number;
	slug: string;
	contentType: string;
	learningSeries: string;
	title: string;
	description: string;
	url: string;
	mediaType: string;
	accessibility: string = 'Public';
	uploadRequired: boolean;
	watchRequired: boolean;
	isPublish: boolean;
	isPrivate: boolean;
	duration: number;
	thumbnailImageUrl: string;
	learningSeriesDetail: any;
	userAssignTrainings: any[];
	prerequisites: string;
	sequence: number;
	course: string;
	coursePart: string;
	isTermsAndConditions: boolean;
	prerequisitesTraining: string;
	isCourseTraining: boolean = true;
	trainingBind: Training[];
	isProgramAdmin: boolean = false;
	isProgramAdminTraining: boolean
	trainingLibrary: string;
	isPopulated: boolean;
	trainingLibraryDetail: TrainingLibraryDetail;
	isNewsFeed: boolean = false;
	isLock: boolean = false;
	videoUrlTitle: string;
	thumbnailImageUrlTitle: string;
	isTermsConditions: string;

	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
		this.isPublish = false;
		this.mediaType = "VIDEO";
		this.isPrivate = false;
		this.description = '';
		this.userAssignTrainings = new Array<any>();
		this.trainingBind = new Array<Training>();
		this.trainingLibraryDetail = new TrainingLibraryDetail();
	}

	static fromResponse(data: any): Training {
		const obj = new Training();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.isPrivate = data.isPrivate;
		obj.mediaType = data.mediaType ? data.mediaType : "VIDEO";
		obj.title = data.title;
		obj.userAssignTrainings = data.userAssignTrainings ? data.userAssignTrainings : [];
		obj.description = data.description;
		obj.url = data.url;
		obj.thumbnailImageUrl = data.thumbnailImageUrl;
		obj.contentType = data.contentType;
		obj.learningSeries = data.learningSeries;
		obj.uploadRequired = data.uploadRequired;
		obj.watchRequired = data.watchRequired;
		obj.accessibility = data.accessibility;
		obj.isPublish = data.isPublish;
		obj.prerequisites = data.prerequisites;
		obj.sequence = data.sequence;
		obj.course = data.course;
		obj.coursePart = data.coursePart;
		obj.isTermsAndConditions = data.isTermsAndConditions;
		obj.prerequisitesTraining = data.PrerequisitesTraining;
		obj.isCourseTraining = data.isCourseTraining;
		obj.trainingLibraryDetail = data.trainingLibraryDetails;
		obj.videoUrlTitle = data.videoUrlTitles;
		obj.thumbnailImageUrlTitle = data.thumbnailImageUrlTitles;
		obj.isTermsConditions = data.isTermsConditionss;

		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		return true;
	}

	forRequest() {
		this.title = this.trimMe(this.title);
		this.description = this.trimMe(this.description);
		this.url = this.trimMe(this.url);
		return this;
	}
}
