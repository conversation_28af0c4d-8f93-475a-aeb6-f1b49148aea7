import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { NotificationManager } from './notification.manager';
import { Notification } from 'src/app/models/notification';
import { RestResponse } from 'src/app/shared/auth.model';
import { NotificationService } from './notification.service';
import { FilterParam } from 'src/app/models/filterparam';
import * as moment from 'moment';

@Component({
    selector: 'app-notification',
    templateUrl: './notification.component.html',
    styleUrls: ['./notification.component.scss']
})
export class NotificationComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {

    records: Notification[];
    notificationSelected: boolean;
    markNotificationSelected: boolean;
    moment: any = moment;
    param: FilterParam;
    offset: number;
    loadMore: boolean;
    totalRecordsCount: number;
    noRecordFound: boolean = false;
    totalPageNumber: number;

    constructor(protected notificationManager: NotificationManager, protected toastService: ToastService,
        protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
        protected router: Router, public commonUtil: CommonUtil, private notificationService: NotificationService) {
        super(notificationManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.records = new Array<Notification>();
        this.param = new FilterParam();
        this.offset = 1;
        this.param.next = 10;
        this.param.offset = this.offset;
        if (this.authService.isAdmin() || this.authService.isProgramAdmin()) {
            this.init();
        }
        else {
            this.fetchNotifications(false);
        }
    }

    ngOnDestroy() {
    }

    removeSuccess() {
        this.onCancel();
    }
    onCancel() {
        this.request.loadEditPage = false;
        if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
                dtInstance.destroy();
            });
        }
        this.init();
    }

    search($event) {
        const value = ($event.target as HTMLInputElement).value;
        this.filterParam.searchText = (value && value != '') ? value.trim() : null;
        this.notificationSelected = false;
        $(".selectAll").prop('checked', false);
        $(".mark-all").prop('checked', false);
        this.refreshRecord();
    }

    selectUnselectAll(event: any) {
        if (event.currentTarget.checked) {
            this.notificationSelected = true;
            $('input[type="checkbox"].remove-checkbox').prop('checked', true);
        } else {
            this.notificationSelected = false;
            $('input[type="checkbox"].remove-checkbox').prop('checked', false);
        }

    }

    onChangeMarkNotification(event: any) {
        if ($('input[type="checkbox"].mark-checkbox.disable-checkbox').length === this.records.length) {
            return;
        }
        if (event.currentTarget.checked) {
            this.markNotificationSelected = true;
            $('input[type="checkbox"].mark-checkbox').prop('checked', true);
        } else {
            this.markNotificationSelected = false;
            $('input[type="checkbox"].mark-checkbox').prop('checked', false);
        }

    }

    selectUnselectCheck() {
        let rowsLength = $('.records-cls').length;
        let checkedCheckboxLength = $('input[type="checkbox"]:checked.remove-checkbox').length;
        if (rowsLength == checkedCheckboxLength)
            $(".selectAll").prop('checked', true)
        else
            $(".selectAll").prop('checked', false)

        if (checkedCheckboxLength > 0)
            this.notificationSelected = true;
        else
            this.notificationSelected = false;
    }

    onselectUnselectMarkCheck() {
        let rowsLength = $('.records-cls').length;
        let checkedCheckboxLength = $('input[type="checkbox"]:checked.mark-checkbox').length;
        if (rowsLength == checkedCheckboxLength)
            $(".mark-all").prop('checked', true);
        else
            $(".mark-all").prop('checked', false);

        if (checkedCheckboxLength > 0)
            this.markNotificationSelected = true;
        else
            this.markNotificationSelected = false;
    }

    onChangeShowEntries(value: any) {
        this.dtOptions.pageLength = parseInt(value);
        this.notificationSelected = false;
        $(".selectAll").prop('checked', false);
        $(".mark-all").prop('checked', false);
        this.refreshRecord();
    }

    removeNotification() {
        let notificationIds = []
        $('input[name="markNotification"]:checked').each((index, ele) => {
            notificationIds.push($(ele).val());
        });
        this.commonService.confirmation("Would you like to delete all selected notifications?",
            this.removeNotificationCallback.bind(this), notificationIds);
    }

    async removeNotificationCallback(data: any) {
        try {
            this.loadingService.show();
            const response: RestResponse = await this.notificationService.removeNotifications(data);
            this.loadingService.hide();
            if (!response.status) {
                this.toastService.error(response.message);
                return;
            }
            $(".selectAll").prop('checked', false);
            this.notificationSelected = false;
            if (this.authService.isAdmin()) {
                this.onCancel();
            }
            else {
                this.fetchNotifications(false);
            }
            this.toastService.success(response.message);
        } catch (error) {
            this.loadingService.hide();
            this.toastService.error(error.message);
        }
    }

    seenNotification() {
        let notificationIds = [];
        $('input[name="markNotification"]:checked').each((index, ele) => {
            if (!$(ele).hasClass("disable-checkbox")) {
                notificationIds.push($(ele).val());
            }
        });
        this.commonService.confirmation("Would you like to mark all selected notifications as read?",
            this.seenNotificationCallback.bind(this), notificationIds);
    }

    async seenNotificationCallback(data: any) {
        try {
            this.loadingService.show();
            const response: RestResponse = await this.notificationService.seenAllNotification(data);
            this.loadingService.hide();
            if (!response.status) {
                this.toastService.error(response.message);
                return;
            }
            $(".mark-all").prop('checked', false);
            this.markNotificationSelected = false;
            if (this.authService.isAdmin()) {
                this.onCancel();
            }
            else {
                this.fetchNotifications(false);
            }
            this.toastService.success(response.message);
        } catch (error) {
            this.loadingService.hide();
            this.toastService.error(error.message);
        }
    }

    async fetchNotifications(loadMore: boolean) {
        try {
            const resp: RestResponse = await this.notificationService.fetchAll(this.param);
            if (!resp.status) {
                this.toastService.error(resp.message);
                return;
            }
            if (!resp.data?.length) {
                this.noRecordFound = true;
            }
            this.totalRecordsCount = resp.data?.length ? resp.data[0].totalCount : 0;
            this.totalPageNumber = Math.ceil(this.totalRecordsCount / this.filterParam.next);
            if (loadMore) {
                this.loadMore = false;
                this.records = this.records.concat(resp.data);
            }
            else {
                this.records = resp.data;
            }
        }
        catch (e) {

        }
    }

    onScrollingFinished() {
        if (this.totalRecordsCount) {
            if (this.records.length >= this.totalRecordsCount && this.filterParam.offset >= this.totalPageNumber) {
                return;
            }
            this.param.next = 10;
            this.param.offset = this.param.offset + 1;
            this.loadMore = true;
            this.fetchNotifications(true);
        }
    }

}