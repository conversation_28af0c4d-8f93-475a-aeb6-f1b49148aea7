import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import * as moment from 'moment';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AssignedSiteStaffTitle implements Resolve<any> {
  staffAssignedSiteStaffPageTitle: string;
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    if (route.queryParams.n) {
      this.staffAssignedSiteStaffPageTitle = route.queryParams.n + ' (Staff)';
    } else {
      this.staffAssignedSiteStaffPageTitle = 'Assigned Site Staff'
    }
    return of(
      this.staffAssignedSiteStaffPageTitle
    )
  }
}
