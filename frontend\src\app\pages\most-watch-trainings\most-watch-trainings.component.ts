import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { MostWatchTrainingsManager } from './most-watch-trainings.manager';
import { ToastService } from 'src/app/shared/toast.service';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { AuthService } from 'src/app/shared/auth.services';
import { Router } from '@angular/router';
import AOS from 'aos';
import { MostWatchTrainings } from 'src/app/models/mostwatchtrainings';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-most-watch-trainings',
  templateUrl: './most-watch-trainings.component.html',
  styleUrls: ['./most-watch-trainings.component.scss']
})
export class MostWatchTrainingsComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  userVideoProgressModal: any;
  userVideoProgressData: any;
  constructor(protected mostWatchTrainingsManager: MostWatchTrainingsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router) {
    super(mostWatchTrainingsManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
    this.userVideoProgressModal.hide();
  }

  ngOnInit() {
    this.init();
    this.records = new Array<MostWatchTrainings>();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.userVideoProgressModal = new bootstrap.Modal(
        document.getElementById('userVideoProgressModal')
      );
    }, 0)
  }

  openTrainingDetailPage(record: any) {
    window.open(
      '/dashboard/training/detail/' + record.id,
      '_blank'
    );
  }
  openUsersList(record: any) {
    AOS.init({ disable: true });
    this.userVideoProgressData = record;
    this.userVideoProgressModal.show();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    this.refreshRecord();
  }

}
