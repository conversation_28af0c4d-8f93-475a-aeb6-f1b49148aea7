import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ManageCourseUserService extends BaseService {
    programAdmin: string;

    constructor(public http: HttpClient) {
        super(http, '/api/course/invite/users', '/api/course/invite/users');
    }

    getUserId(id: string) {
        this.programAdmin = id;
    }

    sendCourseInvite(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/course/invite/users', data);
    }

    sendAdminCourseInvite(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/admin/course/invite/users', data);
    }

}

