import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';

@Injectable({
  providedIn: 'root'
})
export class MomentService extends BaseService {
  momentByUserIdStatus: boolean | false;
  momentByUserId: string | undefined;
  constructor(public http: HttpClient) {
    super(http, '/api/moment', '/api/moments');
  }

  getMomentUserIdStatus(status: boolean, userId: string) {
    this.momentByUserIdStatus = status
    this.momentByUserId = userId;
  }

  fetchAll(filterParam: FilterParam): Promise<RestResponse> {
    if (this.momentByUserIdStatus) {
      return this.getRecords('/api/Moments/' + this.momentByUserId, filterParam);
    } else {
      return this.getRecords('/api/moments', filterParam);
    }
  }

  updateMomentStatus(data: any, status: boolean, isRecurringMoment: boolean) {
    return this.updateRecord(!isRecurringMoment ? '/api/moments/status' : '/api/moments/close', data);
  }

  fetchRecurringLogs(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/momentRecurringLogs', filterParam);
  }

  fetchRejectedLogs(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/momentRejectLogs', filterParam);
  }

  fetchMomentByUser(userId: string, filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/moments/' + userId, filterParam);
  }

  updateStatus(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/moments/status', data);
  }

}

