import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LocalStorageService } from 'angular-2-local-storage';
import { AccountService } from 'src/app/services/account.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnInit {
  public onClickValidation: boolean;
  public showVerifyOtpform: boolean;
  public forgotPasswordButtonDisabled: boolean = false
  public tempPasswordButtonDisabled: boolean = false;
  public userName: string;
  Otp: string;
  constructor(private route: ActivatedRoute, private accountService: AccountService, private toastService: ToastService, private localStorageService: LocalStorageService, private router: Router) { }

  ngOnInit(): void {
    this.onClickValidation = false;
    this.showVerifyOtpform = false;
  }
  async forgotPassword(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return
    }
    this.forgotPasswordButtonDisabled = true
    const data = {
      userName: this.userName
    }
    this.onClickValidation = false;
    try {
      const response: RestResponse = await this.accountService.forgotPassword(data);
      if (!response.status) {
        this.forgotPasswordButtonDisabled = false
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.showVerifyOtpform = true;
      this.forgotPasswordButtonDisabled = false
    } catch (error) {
      this.forgotPasswordButtonDisabled = false
      this.toastService.error(error.message);
    }
  }

  async sendOTP(valid){
    if (!valid) {
      this.onClickValidation = true;
      return
    }
    this.tempPasswordButtonDisabled = true
    const data = {
      userName: this.userName,
      OTP: this.Otp,
    }
    this.onClickValidation = false;
    try {
      const response: RestResponse = await this.accountService.forgotPasswordVerifyOtp(data);
      if (!response.status) {
        this.tempPasswordButtonDisabled = false
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.showVerifyOtpform = true;
      this.tempPasswordButtonDisabled = false
      const forgotPasswordData = {
        userName: this.userName,
      }
      this.localStorageService.set('forgotPasswordUserName',forgotPasswordData);
      this.router.navigateByUrl('/account/recover')
    } catch (error) {
      this.tempPasswordButtonDisabled = false
      this.toastService.error(error.message);
    }
  }


}
