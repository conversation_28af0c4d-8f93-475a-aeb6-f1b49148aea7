import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class AdminManageProgramAdminBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const userId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard", active: false
            },
            {
                title: "Manage Program Admins", link: "/dashboard/program-admin-list", active: false
            },
            {
                title: "Program Admin Details", link: "/dashboard/program-detail/" + userId, active: true
            }
        ])



    }
}
