import {Component, ViewEncapsulation} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import { RoutingState } from './shared/routing-state.service';
import AOS from 'aos';
import { NavigationStart, Router } from '@angular/router';
import { Subscription } from 'rxjs';

export let browserRefresh = false;

@Component({
  selector: '.app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppComponent {
  title = 'Welcome to Application';
  subscription: Subscription;

  constructor(private translate: TranslateService, private routingState: RoutingState, private router: Router) {
    this.subscription = router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        browserRefresh = !router.navigated;
      }
    });
    translate.setDefaultLang('en');
    this.routingState.loadRouting();
    AOS.init();

  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
