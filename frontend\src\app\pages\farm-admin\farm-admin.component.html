<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">
    <div class="user-details-section">
        <div class="row">
            <div class="col-12 ps-0">
                <div class="user-details-section">
                    <ul class="nav nav-pills">
                        <li class="nav-item bg-secondary user-details-btn width-180px"
                            [ngClass]="{'bg-secondary': tabView == 'ACTIVE'}" (click)="onClickTab('ACTIVE')">
                            <a class="btn nav-link" [ngClass]="{' active bg-secondary': tabView == 'ACTIVE'}"
                                aria-current="page">Active</a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{'bg-secondary': tabView == 'INACTIVE'}" (click)="onClickTab('INACTIVE')">
                            <a class="btn nav-link" [ngClass]="{' active bg-secondary': tabView == 'INACTIVE'}">In
                                Active
                            </a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': tabView == 'INVITED'}" (click)="onClickTab('INVITED')">
                            <a class="btn nav-link" [ngClass]="{' active bg-secondary': tabView == 'INVITED'}">Invited
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-12 pe-0 mt-3 d-flex align-items-center justify-content-between">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" appDelayedInput (delayedInput)="search($event)"
                        [(ngModel)]="filterParam.searchText" [delayTime]="1000" placeholder="Search">
                    <i class="bi bi-search pe-3"></i>
                </div>
                <div class="col-12 col-sm-4 text-end pe-0">
                    <button (click)="openfilterModal()" type="button"
                        class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
                        <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                            alt="">Filter
                    </button>
                    <button type="button" class="btn add-button btn-primary btn-lg"
                        [routerLink]="['/dashboard/farm/admin/edit/'+ 0]"
                        *ngIf="authService.isAccessible('FARM_ADMIN','AddButton')">
                        <img src="/assets/images/icons/menu/add_icon.svg" class="me-3 width-15px" alt="">Add New User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="px-0" data-aos="fade-up" data-aos-duration="1000">
        <div [ngClass]="{'no-padding':isDetailPage}">
            <div class="table-responsive server-side-table allocated-users-list"
                [ngClass]="{'has-records': records.length > 0 }">
                <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                    <thead>
                        <tr>
                            <th width="50"></th>
                            <th width="60" class="text-nowrap">{{'Course.fullName' | translate}}</th>
                            <th width="80" class="text-nowrap">{{'Course.email' | translate}}</th>
                            <th width="60" class="text-nowrap">{{'Course.phoneNumber' | translate}}</th>
                            <th width="60" class="text-nowrap">
                                {{'Farm.objName' | translate}}
                            </th>
                            <th width="40" class="text-nowrap" [hidden]="tabView === 'INVITED'">Account Status</th>
                            <th width="100" class="text-nowrap">
                                {{'Course.action' |translate}}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                            <td style="text-align: center;">
                                <img src="/assets/images/icons/menu/user-icon.svg" height="38" width="38" class="ms-3"
                                    alt="">
                            </td>
                            <td class="pe-0">
                                <h5 class="code ms-2" title="View User Profile"
                                    style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                    [routerLink]="['/dashboard/farm/admin/edit/' +record?.id]">
                                    {{record?.fullName}}
                                </h5>
                            </td>
                            <td>
                                {{record?.email}}
                            </td>
                            <td>
                                {{record?.phoneNumber | mask: '************'}}
                            </td>
                            <td>
                                <ng-container *ngIf="record.userFarmDetail?.length; else noFarms">
                                    <span class="badge badge-pill bg-light text-dark farm-badge">
                                        {{ record.userFarmDetail[0]?.farmCode }} - {{ record.userFarmDetail[0]?.farmName
                                        }}
                                    </span>
                                    <span *ngIf="record.userFarmDetail.length > 1"
                                        class="badge badge-pill bg-secondary text-white farm-badge cursor-pointer ms-2"
                                        (click)="showAllFarms(record)">
                                        +{{ record.userFarmDetail.length - 1 }} more
                                    </span>
                                </ng-container>
                                <ng-template #noFarms>
                                    <span class="text-muted small">No Company</span>
                                </ng-template>
                            </td>
                            <td [hidden]="tabView === 'INVITED'">
                                <div class="form-check form-switch">
                                    <input (change)="activateDeactiveUserConfirmation($event,record)"
                                        class="form-check-input toggle-width" title="Activate/Deactivate User"
                                        type="checkbox" id="flexSwitchCheckChecked" [(ngModel)]="record.isActive"
                                        [ngModelOptions]="{standalone: true}">
                                    <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                                </div>
                            </td>
                            <td>
                                <div class="custom-action-button text-right mb-2 d-sm-flex">
                                    <i title="Edit" [routerLink]="['/dashboard/farm/admin/edit/'+record?.id]"
                                        class="bi bi-pencil font-21px me-2 cursor-pointer"></i>
                                    <i *ngIf="authService.isAccessible('FARM_ADMIN','DeleteButton') && !isPlusButton"
                                        [class.disabled]="authService.isDisabled('FARM_ADMIN','DeleteButton')"
                                        title="Delete" (click)="remove(record?.id)"
                                        class="bi bi-trash font-21px cursor-pointer"></i>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>

<!-- Add modal for showing all company codes -->
<div class="modal fade" id="allFarmsModal" tabindex="-1" aria-labelledby="allFarmsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="allFarmsModalLabel">Companies</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="list-group">
                    <li class="list-group-item" *ngFor="let farm of selectedRecord?.userFarmDetail">{{ farm.farmCode }}
                        - {{ farm.farmName }}
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterModalLabel">Filter Company Admins</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div *ngIf="filterModal && filterModal._isShown" class="modal-body">
                <form #filterForm="ngForm" novalidate="novalidate">
                    <div class="form-floating">
                        <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                            [ngClass]="{'is-invalid': !farm.valid && onClickValidation}">
                            <ng-select [items]="farms" bindLabel="displayLabel" bindValue="id" name="farm"
                                [(ngModel)]="filterParam.farm" #farm="ngModel"
                                placeholder="Select {{'Farm.objName' | translate}}">
                            </ng-select>
                        </div>
                        <label for="farm">{{ 'Farm.objName' | translate }}</label>
                    </div>
                    <!-- <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="fromDate" [toDateInput]="toDate"></app-date-range-filter> -->

                    <div class="modal-footer">
                        <button (click)="resetFilter()" type="button"
                            class="text-white btn btn-secondary">Reset</button>
                        <div class="button-spacer"></div>
                        <button (click)="applyFilter(filterForm.form.valid)" type="button"
                            class="btn btn-primary">Filter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>