import { Injectable } from "@angular/core";
import { BaseManager } from "src/app/config/base.manager";
import { LoadingService } from "src/app/services/loading.service";
import { ToastService } from "src/app/shared/toast.service";
import { CategoryAssignedUsersService } from "./category-assigned-users.service";

@Injectable({
    providedIn: 'root'
})

export class CategoryAssignedUsersManager extends BaseManager {

    constructor(protected categoryAssignedUsersService: CategoryAssignedUsersService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(categoryAssignedUsersService, loadingService, toastService);
    }
}
