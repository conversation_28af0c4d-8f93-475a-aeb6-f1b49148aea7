import { Component, OnInit } from '@angular/core';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { Users } from 'src/app/models/users';
import { AccountService } from 'src/app/services/account.service';
import { UsersService } from 'src/app/services/users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';
import { LanguageManager } from '../language/language.manager';
import AOS from 'aos';
import { AuthService } from 'src/app/shared/auth.services';
import { LocalStorageService } from 'angular-2-local-storage';

declare var bootstrap: any;

@Component({
  selector: 'app-staffprofile',
  templateUrl: './staffprofile.component.html',
  styleUrls: ['./staffprofile.component.scss']
})
export class StaffprofileComponent implements OnInit {
  user: Users;
  languages: any = [];
  uploader: any;
  onClickValidation: boolean = false;
  onClickValidationForRequestSiteChange: boolean = false;
  dropdownSettings = {};
  farms: any = [];
  farmIds: any = [];
  userFarms: any = [];
  selectedFarms: any = [];
  requestedFarms: any = [];
  selectedFarmsForRequest: any = [];
  messageForRequestSiteChange: any;
  requestForSiteChangeModal: any;
  sendingRequestForSiteChange: boolean = false;
  userDeletedFarms: any = [];
  profileImageLoader: boolean = false;
  constructor(private accountService: AccountService, private toastService: ToastService, private languageManager: LanguageManager, private usersService: UsersService, private authService: AuthService, private localStorageService: LocalStorageService) {
    this.user = new Users();
  }

  ngOnInit() {
    this.setDropdownSettings();
    this.fetchFarms();
    this.fetchAssociatedData();
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.requestForSiteChangeModal = new bootstrap.Modal(
        document.getElementById('requestForSiteChangeModal')
      );
    }, 0)
  }

  async onClickRequestForSiteChange(valid) {

    if (!valid) {
      this.onClickValidationForRequestSiteChange = true;
      return
    }

    this.sendingRequestForSiteChange = true;

    const data = {
      farmIds: this.farmIds,
      message: this.messageForRequestSiteChange,
    }

    try {
      // this.user.roles = null;
      const response: RestResponse = await this.accountService.staffRequestSiteChange(data);
      if (!response.status) {
        this.setEmptyUserFarmDataOfRequestSiteChange()
        this.toastService.error(response.message);
        return;
      }
      this.setEmptyUserFarmDataOfRequestSiteChange();
      this.toastService.success(response.message);
    } catch (e) {
      this.setEmptyUserFarmDataOfRequestSiteChange();
      this.toastService.error(e.message);
    }
  }

  setEmptyUserFarmDataOfRequestSiteChange() {
    this.onClickValidationForRequestSiteChange = false;
    this.sendingRequestForSiteChange = false;
    this.requestForSiteChangeModal.hide();
    this.messageForRequestSiteChange = null;
    this.farmIds = [];
    this.selectedFarmsForRequest = [];
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }
  async fetchProfileData() {
    try {
      const response: RestResponse = await this.accountService.fetchMe().toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      this.setSelectedFarmsUserEdit(userFarmMapping);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setSelectedFarmsUserEdit(userFarms) {
    if (this.farms !== undefined && userFarms !== undefined) {
      const farms = this.farms.filter((data) => userFarms.some(data2 => data.id === data2.farmId))
      let newFarms = farms.reduce((responseData, { id, ...rest }) => ({ ...responseData, ...{ [id]: { id, ...rest } } }), {});
      if (newFarms && userFarms.length > 0) {
        userFarms.forEach(({ farmId, id }) => {
          newFarms[farmId].userFarmId = id
        });
        let data = Object.values(newFarms);
        this.userFarms = data;
        this.selectedFarms = this.userFarms;
      }
    }
  }

  async fetchFarms() {
    try {
      const response: RestResponse = await this.usersService.GetAllAdminFarms(null);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.farms = response.data;
      this.fetchProfileData();
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  onItemSelect(item: any) {
    if (this.user.userFarmMapping && this.user.userFarmMapping.length > 0) {
      if (!this.user.userFarmMapping.some(el => el.farmId === item.id)) {
        this.setItemsData(item);
      }
    } else {
      this.setItemsData(item);
    }
  }

  onDeSelect(item: any) {
    let deletedUserFarms = []
    this.addDeletedItemsOnUserEdit(item);
    if (this.user.userFarmMapping && this.user.userFarmMapping.length > 0) {
      deletedUserFarms = this.user.userFarmMapping.filter(farmData => farmData.farmId !== item.id);

    }
    this.user.userFarmMapping = deletedUserFarms;
  }

  addDeletedItemsOnUserEdit(item) {
    let userFarmData = this.userFarms.find(farm => farm.id === item.id);
    if (userFarmData) {
      if (this.userDeletedFarms && this.userDeletedFarms.length > 0) {
        if (!this.userDeletedFarms.some(el => el.farmId === userFarmData.id)) {
          this.setDeletedItemsData(userFarmData);
        }
      } else {
        this.setDeletedItemsData(userFarmData);
      }
    }
  }

  uploadProfilePhoto(event: any) {
    if (event && event.target.files.length > 0) {
      this.profileImageLoader = true;
    }
  }

  fileValidationError(data: string, toastService: any) {
    this.profileImageLoader = false;
    toastService.error(data);
  }

  onUploadSuccess(data: any) {
    this.user.profileImageUrl = data.path;
    this.profileImageLoader = false;
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  initializeUploader(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationError("Image size to too large", this.toastService);
          }, 200);

          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationError("only jpg,png,jpeg files are allowed", this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const image = uploadResponse[0];
        image.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(image);
        setTimeout(() => {
          this.onUploadSuccess(image);
        }, 200);
      }
    };

    return uploader;
  }

  setDeletedItemsData(item) {
    this.userDeletedFarms = [
      ...this.userDeletedFarms,
      {
        id: item.userFarmId,
        farmId: item.id,
        isDeleted: true
      },
    ];
  }


  setItemsData(item) {
    this.user.userFarmMapping = [
      ...this.user.userFarmMapping,
      {
        farmId: item.id,
      },
    ];
  }


  async fetchAssociatedData() {
    this.languages = await this.languageManager.fetchAllData(null);
  }

  telInputObject(event: any) {
    if (this.user.countryCode && this.user.phoneNumber) {
      event.setNumber('+' + this.user.countryCode + this.user.phoneNumber);
      return
    } else {
      event.setCountry('sg')
    }
  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  hasError(event: any) {
  }

  openRequestForSiteChangeModal() {
    AOS.init({ disable: true });
    this.requestForSiteChangeModal.show();
  }

  async save(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return;
    }
    if (this.userDeletedFarms && this.userDeletedFarms.length > 0) {
      let updatedFarmsData = this.user.userFarmMapping.concat(this.userDeletedFarms);
      this.user.userFarmMapping = updatedFarmsData;
    }
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.accountService['update'](this.user);
      if (!response.status) {
        this.setEmptyUserFarmData()
        this.toastService.error(response.message);
        return;
      }
      this.setEmptyUserFarmData();
      this.updateUserDataLocalStorage();
      this.toastService.success(response.message);
    } catch (e) {
      this.setEmptyUserFarmData();
      this.toastService.error(e.message);
    }
  }

  updateUserDataLocalStorage() {
    let newUserData = this.authService.getUser();
    const { firstName, lastName, languageId, location, profileImageUrl } = this.user;
    newUserData = { ...newUserData, fullName: firstName + ' ' + lastName, firstName, lastName, location, languageId, profileImageUrl }
    this.localStorageService.set('user', newUserData);

  }

  setEmptyUserFarmData() {
    this.userDeletedFarms = [];
    this.user.userFarmMapping = [];
  }

  onItemSelectForRequest(item: any) {
    if (this.farmIds && this.farmIds.length > 0) {
      if (!this.farmIds.some(id => id === item.id)) {
        this.setItemsDataForRequest(item);
      }
    } else {
      this.setItemsDataForRequest(item);
    }
  }

  setItemsDataForRequest(item) {
    this.farmIds = [
      ...this.farmIds,
      item.id,
    ];
  }

  onDeSelectForRequest(item: any) {
    this.farmIds = this.farmIds.filter(id => id != item.id)
  }

}
