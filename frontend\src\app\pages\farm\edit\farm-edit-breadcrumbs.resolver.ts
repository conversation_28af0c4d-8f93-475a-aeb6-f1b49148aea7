import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class FarmEditBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const farmId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard", active: false
            },
            {
                title: "Manage Companies", link: "/dashboard/farms", active: false
            },
            {
                title: "Company", link: "/dashboard/farm/edit/" + farmId, active: true
            }
        ])



    }
}
