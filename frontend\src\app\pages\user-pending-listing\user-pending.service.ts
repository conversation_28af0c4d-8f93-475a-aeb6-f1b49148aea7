import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class UserPendingService extends BaseService {
    constructor(public http: HttpClient) {
        super(http, '/api/course/pending/request', '/api/course/pending/requests');
    }

    getPendingCourse(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/course/pending/requests', filterParam);
    }

    // getEnrolledgCourse(filterParam: FilterParam): Promise<RestResponse> {
    //     return this.getRecords('/api/course/enrolled/users', filterParam);
    // }

    approveOrRejectPendingCourse(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/course/approve/reject', data);
    }

}

