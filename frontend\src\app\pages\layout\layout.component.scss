.show-subMenu {
  visibility: visible;
}

.hide-subMenu {
  visibility: hidden;
  display: none;
}

.site-request-count-cls {
  position: relative;
  top: 1px;
}

.icons {
  display: inline;
  float: right;

  .notification {
    position: relative;
    display: inline-block;

    .notBtn {
      transition: .5s;
      cursor: pointer;

      .number {
        height: 30px;
        width: 30px;
        background-color: #d63031;
        border-radius: 20px;
        color: #fff;
        text-align: center;
        position: absolute;
        top: -13px;
        left: 42px;
        padding: 0;
        border-style: solid;
        border-width: 2px;

        .count-number {
          font-size: 14px !important;
        }
      }

      .bell-icon {
        font-size: 25pt;
        padding-bottom: 10px;
        color: #000;
        margin-right: 20px;
        margin-left: 20px;
      }
    }

    .box {
      width: 350px;
      border-radius: 10px;
      transition: .5s;
      position: absolute;
      padding: 0;
      left: -300px;
      margin-top: 5px;
      background-color: #f4f4f4;
      box-shadow: 10px 10px 23px #0000001a;
      cursor: context-menu;

      .display {
        position: relative;

        .heading-notification {
          height: 49px !important;
          background-color: #1681ff;
          color: #fff;
          padding: 1rem;
          font-size: 14px;

          .pull-left {
            float: left;

            .notificaction-heading {
              font-weight: 500;
              font-size: 13x;
            }
          }

          .pull-right {
            float: right;

            .all-notification {
              margin-right: 10px !important;
              font-size: 13x;
              cursor: pointer;
            }
          }
        }

        .notification-cont {
          position: absolute;
          width: 100%;
          background-color: #f4f4f4;
          height: auto;
          max-height: 375px;
          overflow-y: scroll;

          .new {
            border-bottom: 1px solid #f7f7f7;
            background-color: #fff !important;
          }

          .sec {
            padding: 0 1px;
            transition: .5s;

            .notification-read {
              padding: 5px !important;
              border-bottom: 1px solid #0000001a;
              position: relative;

              a {
                padding: 8px 20px 2px 20px;
                display: flex;
                align-items: center;

                .media-icon {
                  margin-right: 10px;
                  background: #1681ff;
                  min-width: 35px;
                  height: 35px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  i {
                    color: #fff;
                    font-size: 18px;
                  }
                }

                .message {
                  font-size: 13px;
                  line-height: 16px;
                }

                .profCont {
                  padding-left: 15px;
                  position: relative;

                  .profile {
                    width: 21px;
                    float: left;
                    margin-top: 30px;
                    margin-right: 15px;
                  }
                }

                .notification-txt {
                  vertical-align: top;
                  font-size: 12px;

                  .title-notification {
                    font-weight: 800;
                    font-size: 14px;
                    padding: 5px !important;
                    margin: 0;
                    text-align: left;
                  }

                  .message-notification {
                    font-size: 13px;
                    padding: 0 8px 0 50px;
                    text-align: left;
                    margin-bottom: 5px !important;
                  }
                }
              }

              .unread-message-icon {
                border: 5px solid #1681ff;
                border-radius: 50%;
                background: #1681ff;
                position: absolute;
                top: 7px;
                right: 10px;
              }

              .sub {
                font-size: 12px;
                color: gray;
                margin-bottom: 1px !important;
                text-align: right;
              }
            }
          }
        }
      }
    }
  }
}

.staff-notification-icon {
  margin-top: -45px;
  margin-right: -20px;
}


// svg onclick to active color change 
.learning-logo {
  filter: brightness(0) invert(0);
}

/* Change color to blue when the route is active */
li.active .learning-logo {
  filter: invert(42%) sepia(99%) saturate(2480%) hue-rotate(191deg) brightness(94%) contrast(89%);
}

li.parent-active {
  .parent {
    .learning-logo {
      filter: invert(42%) sepia(99%) saturate(2480%) hue-rotate(191deg) brightness(94%) contrast(89%);
    }
  }
}

.wrapper #sidebar ul.components li.sub-menu-container a.collapsed .bi.bi-chevron-up {
  display: block;
}

//
.sidebar {
  width: 250px;
}

.sidebar .list-unstyled {
  padding: 0px 16px;
  margin: 0;
}

.sidebar .list-unstyled>li {
  margin-bottom: 10px;
}

.sidebar .list-unstyled>li>a {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.sidebar .list-unstyled>li>ul {
  padding-left: 20px;
  margin: 0;
}

// .wrapper #sidebar ul.components li:hover a:not(.sub-a),
// .wrapper #sidebar ul.components li:focus a:not(.sub-a) {
//   color: #000;
// }

// invited tab
.invite-user-modal {
  max-width: 52px;
  max-height: 52px;
  border-radius: 8px !important;
  background: #1681ff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 0;

  img {
    vertical-align: middle;
    padding-bottom: 5px;
  }
}

// @media (min-width: 991px) {
//   .collapse:not(.show) {
//     display: block;
//   }
// }
// @media (min-width: 1200px) {
//   .collapse:not(.show) {
//     display: none;
//   }
// }

.add-button {
  width: 155px;
  white-space: nowrap;
  border-radius: 10px !important;
  margin-right: 48px;
  margin-top: 14px; 
}