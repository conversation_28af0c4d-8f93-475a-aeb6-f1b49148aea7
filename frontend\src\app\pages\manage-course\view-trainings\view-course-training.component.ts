import { Component, OnDestroy, OnInit } from '@angular/core';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { ManageCourseManager } from '../manage-course-manager';
import { CourseTrainingsManager } from '../../course-trainings/course-trainings-manager';
import { CourseTrainingsService } from '../../course-trainings/course-trainings-service';
import { ToastService } from 'src/app/shared/toast.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'src/app/shared/auth.services';
import { ManageCourseService } from '../manage-course-service';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { RouteDataService } from 'src/app/shared/title.service';

@Component({
  selector: 'app-view-course-training',
  templateUrl: './view-course-training.component.html',
  styleUrls: ['./view-course-training.component.scss']
})
export class ViewCourseTrainingComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  moment: any = moment;
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchText: any;
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  trainings: CourseTrainings[];
  course: any;
  courseId: string;

  constructor(protected manageCourseManager: ManageCourseManager, protected courseTrainingsManager: CourseTrainingsManager, private courseTrainingsService: CourseTrainingsService, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService, protected route: ActivatedRoute,
    protected router: Router, public commonUtil: CommonUtil, private manageCourseService: ManageCourseService, public routeDataService: RouteDataService) {
    super(courseTrainingsManager, commonService, toastService, loadingService, router);
  }

  title: string;

  ngOnInit(): void {
    this.trainings = new Array<CourseTrainings>();
    this.request.loadEditPage = false;
    this.filterParam.course = this.route.snapshot.paramMap.get('id');
    this.init();
    this.setCourseTitle();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  async fetchCourseTrainingsData(id: string) {
    this.loadingService.show();
    const resp: RestResponse = await this.courseTrainingsService.fetchCourseTrainings(id).toPromise();
    this.loadingService.hide();
    if (!resp.status) {
      return;
    }
    this.trainings = resp.data;
  }

  setCourseTitle(): void {
    const trainingId: string | null = this.route.snapshot.paramMap.get("id");
    if (trainingId) {
      this.courseTrainingsService.getcourseTitle(trainingId).subscribe(response => {
        const title = response?.data?.title || 'Manage Courses';
        this.routeDataService.setData(this.router.url, title);
      });
    }
  }

  // click to open the training detail page 
  openTrainingDetailPage(id: any) {
    if (this.authService.isProgramAdmin()) {
      this.router.navigate(['/dashboard/program-admin/training/detail/' + id]);
    } else if (this.authService.isAdmin()) {
      this.router.navigate(['/dashboard/training/program/admin/detail/', id]);
    }
  }

}
