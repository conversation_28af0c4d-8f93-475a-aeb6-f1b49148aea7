<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">FarmAssignTraining Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/farm-assign-training']">{{'FarmAssignTraining.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'FarmAssignTraining.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{farmAssignTraining.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #farmassigntrainingForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"FarmAssignTraining.trainingId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="trainings"
			                                       bindLabel="videoTitle"
			                                       bindValue="id"
			                                       name="farmAssignTrainingTrainingId"
			                                       #farmAssignTrainingTrainingId="ngModel"
					                            		[(ngModel)]="farmAssignTraining.trainingId" required="required" #TrainingId="ngModel"
			                                       [ngClass]="{'invalid-field':farmAssignTrainingTrainingId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} training">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('farmAssignTrainingTrainingIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"FarmAssignTraining.farmId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="farms"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="farmAssignTrainingFarmId"
			                                       #farmAssignTrainingFarmId="ngModel"
					                            		[(ngModel)]="farmAssignTraining.farmId" required="required" #FarmId="ngModel"
			                                       [ngClass]="{'invalid-field':farmAssignTrainingFarmId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} farm">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('farmAssignTrainingFarmIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"FarmAssignTraining.assignedDate" | translate}}
		                        </label>
									<my-date-picker name="farmAssignTrainingAssignedDate" [options]="myDatePickerOptions" [(ngModel)]="farmAssignTraining.assignedDateCalendar"
										>
									</my-date-picker>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"FarmAssignTraining.allUserAssigned" | translate}}
		                        </label>
									<div class="material-switch">
										<input id="farmAssignTrainingAllUserAssignedId" name="farmAssignTrainingAllUserAssigned" type="checkbox"  [(ngModel)]="farmAssignTraining.allUserAssigned" />
										<label for="farmAssignTrainingAllUserAssignedId" class="label-primary"></label>
									</div>
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(farmassigntrainingForm.form)"
	        *ngIf="authService.isAccessible('FARMASSIGNTRAINING','AddButton')" 
	        [disabled]="authService.isDisabled('FARMASSIGNTRAINING','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="farmAssignTrainingTrainingIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-training [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-training>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="farmAssignTrainingFarmIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-farm [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-farm>
		            </div>
		        </div>
		    </div>
		</div>

