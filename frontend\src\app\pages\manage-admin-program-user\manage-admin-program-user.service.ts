import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { BaseService } from '../../config/base.service';
import { Observable } from 'rxjs';
import { RestResponse } from 'src/app/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class ManageAdminProgramUserService extends BaseService {
  constructor(public http: HttpClient) {
    super(http, '', '');
  }

  // getCourseFilter(): Promise<RestResponse> {
  //   return this.getRecords('/api/courses', null);
  // }



}

