{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "downlevelIteration": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "node", "experimentalDecorators": true, "importHelpers": true, "target": "es5", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "types": ["node"], "paths": {"exceljs": ["node_modules/exceljs/dist/exceljs.min"]}}}