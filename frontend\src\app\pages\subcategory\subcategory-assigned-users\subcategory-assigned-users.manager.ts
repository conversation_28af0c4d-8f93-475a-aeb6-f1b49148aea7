import { Injectable } from "@angular/core";
import { BaseManager } from "src/app/config/base.manager";
import { LoadingService } from "src/app/services/loading.service";
import { ToastService } from "src/app/shared/toast.service";
import { SubCategoryAssignedUsersService } from "./subcategory-assigned-users.service";

@Injectable({
    providedIn: 'root'
})

export class SubCategoryAssignedUsersManager extends BaseManager {

    constructor(protected subCategoryAssignedUsersService: SubCategoryAssignedUsersService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(subCategoryAssignedUsersService, loadingService, toastService);
    }
}
