.admin-dasboard {
    background-color: #FBFBFB;
    padding: 2rem;

    .d-flex-basis {
        .card {
            height: 400px;
            padding: 2rem;
            box-shadow: 0px 3px 6px #00000029;
            border-radius: 31px;
            border: 0;
            cursor: pointer;

            .card-logo {
                border-radius: 20px;
                padding: 20px;
                width: 80px;
                background-color: #256bc1;

                .cross-icon {
                    position: absolute;
                    top: 20px;
                    right: 15px;
                    width: 60px;
                }
            }

            .numeric {
                width: 115px;
                height: 130px;
                font: normal normal 600 35px/86px Blinker;
                letter-spacing: 0px;
                color: #000;
                opacity: 1;
                white-space: nowrap;
            }
        }

        .card:hover {
            background: transparent linear-gradient(360deg, #07182F 0%, #1681FF 100%) 0% 0% no-repeat padding-box;

            p,
            a,
            .numeric {
                color: #fff;
            }

            //
        }

    }

    .bg-color {
        background: #fff;
        opacity: 1;
        box-shadow: none;
    }
}


@media (min-width: 768px) {
    .admin-dasboard {
        .d-flex-basis {

            .card {
                .numeric {
                    font: normal normal 600 45px/86px Blinker;
                }
            }
        }

    }
}

@media (min-width: 1400px) {
    .admin-dasboard {
        .d-flex-basis {
            flex-basis: 25%;

            .card {
                .numeric {
                    font: normal normal 600 37px/86px Blinker;
                }
            }
        }
    }
}

@media (min-width: 2044px) {
    .admin-dasboard {
        .d-flex-basis {
            .card {
                .numeric {
                    font: normal normal 600 58px/86px Blinker;
                }
            }
        }
    }
}

.custom-modal-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-modal-dialog {
  background: #fff;
  border-radius: 16px;
  max-width: 430px;
  width: 100%;
  box-shadow: 0 4px 32px rgba(0,0,0,0.18);
  padding: 0;
  overflow: hidden;

  .modal-header {
    padding: 1.5rem 2rem 1rem 2rem;
    border-bottom: none;
  }

  .modal-title {
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    width: 100%;
  }

  .modal-body {
    padding: 1rem 2rem 0.5rem 2rem;
  }

  .form-group {
    label {
      font-weight: 500;
      margin-bottom: 0.5rem;
    }
  }

  .form-control {
    border-radius: 8px;
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
  }

  .modal-footer {
    padding: 1rem 2rem 1.5rem 2rem;
    border-top: none;
    display: flex;
    justify-content: flex-end;
    background: none;
  }

  .btn-primary {
    background: #0a1a2f;
    border: none;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 0.6rem 1.5rem;
    transition: background 0.2s;

    &:active,
    &:focus {
      background: #256bc1;
    }
  }

  .alert-danger {
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 1rem;
  }

  .form-floating {
    position: relative;

    .bi-eye,
    .bi-eye-slash {
      position: absolute;
      top: 50%;
      right: 1.2rem;
      transform: translateY(-50%);
      font-size: 1.4rem;
      color: #6c757d;
      cursor: pointer;
      z-index: 2;
      background: #fff;
      padding-left: 0.2rem;
    }
  }
}

