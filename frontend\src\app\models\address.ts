import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { TranslateService } from '@ngx-translate/core';
export class Address extends BaseModel {

	tenantId!: number;
	slug!: string;
	houseNo!: string;
	street!: string;
	city: string = 'Ludhiana';
	state: string = 'Punjab';
	country: string = 'India';
	pincode: string = '141013';
	latitude!: string;
	longitude!: string;

	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
	}

	static fromResponse(data: any): Address {
		const obj = new Address();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.houseNo = data.houseNo;
		obj.street = data.street;
		obj.city = data.city;
		obj.state = data.state;
		obj.country = data.country;
		obj.pincode = data.pincode;
		obj.latitude = data.latitude;
		obj.longitude = data.longitude;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		return true;
	}

	forRequest() {
		this.houseNo = this.trimMe(this.houseNo);
		this.street = this.trimMe(this.street);
		this.city = this.trimMe(this.city);
		this.state = this.trimMe(this.state);
		this.country = this.trimMe(this.country);
		this.pincode = this.trimMe(this.pincode);
		this.latitude = this.trimMe(this.latitude);
		this.longitude = this.trimMe(this.longitude);
		return this;
	}
}
