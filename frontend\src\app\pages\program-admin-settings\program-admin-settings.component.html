<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
    <div class="dashboard-content-container">
        <div class="container program-admin-setting mt-5">
            <form #recordForm="ngForm" novalidate="novalidate">
                <div class="row px-0">
                    <div class="text-start">
                        <h4 class="fw-bold">
                            {{ isAuthor ? 'Author Profile' : 'Setting' }}
                        </h4>
                        <p class="user-edit-msg">Please Make sure fill all the filed before click on save button</p>
                    </div>
                    <div class="text-start mt-2 mb-3">
                        <div class="profile-image-container position-relative"
                            [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
                            <img *ngIf="!profileImageLoader"
                                [src]="user.profileImageUrl ? user.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
                            <div *ngIf="profileImageLoader" class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div *ngIf="!profileImageLoader"
                                class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
                                <label for="file-input">
                                    <i class="bi bi-pencil-fill"></i>
                                    <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect
                                        [uploader]="uploader" id="file-input" type="file"
                                        accept="image/png, image/jpg, image/jpeg" disabled/>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-3 mb-4">
                        <div class="form-floating">
                            <input class="form-control rounded" type="text" name="firstName" #firstName="ngModel"
                                [(ngModel)]="user.firstName" required="required" placeholder="First Name"
                                [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}" disabled>
                            <label for="floatingInput">First Name</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-3 mb-4">
                        <div class="form-floating">
                            <input class="form-control rounded" name="lastname" #lastname="ngModel"
                                [(ngModel)]="user.lastName" required="required" placeholder="Last Name"
                                [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}" disabled>
                            <label for="floatingInput">Last Name</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input class="form-control rounded" name="programName" #programName="ngModel"
                                [(ngModel)]="user.programName" required="required" placeholder="Program Name"
                                [ngClass]="{'is-invalid':!programName.valid && onClickValidation}" disabled>
                            <label for="floatingInput">Program Name</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control"
                                type="email" name="email" #email="ngModel" [(ngModel)]="user.email" required="required"
                                placeholder="Email" pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$" disabled>
                            <label for="floatingInput">{{"USERS.Email" | translate}}</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                        <div class="form-floating">
                            <input autocomplete="off" class="form-control phone_number" type="text" name="phone"
                                ng2TelInput (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                                (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="user.phoneNumber"
                                [ngClass]="{'is-invalid':!phone.valid && onClickValidation}" required="required"
                                placeholder="Phone Number" minLength="7" maxlength="12" pattern="^[0-9]*$"
                                (keypress)="onlyAllowNumbers($event)" (countryChange)="onCountryChange($event)" disabled>
                        </div>
                    </div>
                    <div class="col-12 col-md-12">
                        <div class="form-floating form-floating-textarea mb-4 w-100">
                            <textarea maxlength="250" [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
                                class="form-control form-description" name="description" #description="ngModel"
                                [(ngModel)]="user.about" required="required" placeholder="Description"
                                id="floatingTextarea2" disabled></textarea>
                            <label for="floatingTextarea2">About</label>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 mb-4">
                    </div>
                    <!-- Program Profile Image Upload -->
                    <div *ngIf="!isAuthor" class="col-12 col-md-6 mb-4">
                        <div class="form-control text-center border-2 p-3 border-dark" *ngIf="!user.programProfileImage"
                            [ngClass]="{'is-invalid': !user.programImageUrl && onClickValidation}">
                            <label class="upload-img-button cursor-pointer"
                                [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingProgramImage}">
                                <img src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">
                                <span *ngIf="!uploadingProgramImage">
                                    {{ 'UPLOAD PROGRAM PROFILE IMAGE' | translate }}
                                </span>
                                <span *ngIf="uploadingProgramImage">{{ fileUploadingMessage }}</span>
                                <div *ngIf="uploadingProgramImage" class="spinner-border ms-2" role="status"
                                    style="width: 1.7rem; height: 1.7rem">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <input *ngIf="!uploadingProgramImage" name="program-image" class="d-none"
                                    (change)="uploadProgramImageUrl($event)" id="program-file-input" type="file"
                                    accept="image/png, image/jpg, image/jpeg" />
                            </label>
                        </div>
                        <div *ngIf="user.programProfileImage"
                            class="moment-image-container mb-4 max-width-none position-relative">
                            <img [src]="user.programProfileImage" />
                            <div class="position-absolute delete-video-container"
                                (click)="removeProgramImage(user.programProfileImage)">
                                <i class="bi bi-x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span *ngIf="!isAuthor" (click)="openContentTransferModal()">
                            <img class="me-1" width="25" src="/assets/images/icons/menu/content-transfer.svg" alt="">
                            Content
                            Transfer
                        </span>
                        <div class="admin-setting-button d-flex">
                            <button *ngIf="!isAuthor" class="setting-button border border-dark mx-2 btn rounded-3"
                                [routerLink]="['/dashboard/program-admin/change-password']">Change password
                            </button>
                            <button *ngIf="!isAuthor" (click)="save(recordForm.form.valid)"
                                class="setting-button bg-secondary btn text-light rounded-3">Save
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- content transfer modal -->
    <div class="modal fade" id="contentTransferModal" tabindex="-1" aria-labelledby="contentTransferModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0" style="padding-top: 30px;
                      margin: auto 15px;">
                    <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                        (click)="closeContentTransferModal()"></button>
                </div>
                <div *ngIf="contentTransferModal && contentTransferModal._isShown" class="modal-body"
                    style="padding: 0px 40px;">
                    <div class="modal-content-inside">
                        <h5 class="modal-title fw-bold" id="contentTransferModalLabel">Content Transfer</h5>
                        <p class="modal-heading pt-1" id="contentTransferModalLabel">
                            Please make sure you fill in all the fields before you click on the Send button
                        </p>
                    </div>
                    <form #contentTransferForm="ngForm" novalidate="novalidate">
                        <div class="row px-0">
                            <!-- <div class="col-12 col-md-12 mb-4 px-0">
                                <label for="type" class="mb-2 mt-4">{{"Training.TrainingRequired" |
                                    translate}}
                                </label>
                                <div class="form-check">
                                    <input
                                        (ngModelChange)="selectContentRequired($event, MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXISTING_PROGRAM_ADMIN)"
                                        [ngClass]="{'is-invalid':!existingProgram.valid && onClickValidation}"
                                        required="required" [(ngModel)]="user.existingProgramAdmin"
                                        #existingProgram="ngModel" [value]="true"
                                        class="form-check-input radio-button-cls" type="radio" name="existingProgram"
                                        [id]="'User.existingProgram' | translate">
                                    <label class="form-check-label" [for]="'USERS.existingProgram' | translate">
                                        {{"USERS.existingProgram" | translate}}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input
                                        (ngModelChange)="selectContentRequired($event, MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXTERNAL_PROGRAM_ADMIN)"
                                        [ngClass]="{'is-invalid':!externalProgram.valid && onClickValidation}"
                                        required="required" [ngModel]="user.externalProgramAdmin" [value]="true"
                                        class="form-check-input radio-button-cls" type="radio"
                                        #externalProgram="ngModel" name="externalProgram"
                                        [id]="'User.externalProgram' | translate">
                                    <label class="form-check-label" [for]="'USERS.externalProgram' | translate">
                                        {{"USERS.externalProgram" | translate}}
                                    </label>
                                </div>
                            </div> -->
                            <!-- Conditionally show this section only when "existingProgram" is selected -->
                            <!-- <div *ngIf="selectedProgramAdmin === MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXISTING_PROGRAM_ADMIN"
                                class="col-12 col-md-12 mb-4 px-0">
                                <div class="form-floating">
                                    <div class="mb-4"
                                        [ngClass]="{'is-invalid': users && users.length === 0 && onClickValidation}">
                                        <ng-select placeholder="Select Existing Users" bindLabel="fullNameAndEmail"
                                            [items]="users" bindValue="id"
                                            class="custom-select form-control padding-bottom-8 rounded"
                                            name="usersDropdown" [(ngModel)]="usersSelectedId" #userIds="ngModel">
                                        </ng-select>
                                    </div>
                                </div>
                            </div> -->
                            <!-- <div *ngIf="selectedProgramAdmin === MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXTERNAL_PROGRAM_ADMIN" -->
                            <div class="px-0">
                                <div class="col-12 col-md-12 mb-4">
                                    <div class="form-floating">
                                        <input class="form-control rounded" type="text" name="contentFirstName"
                                            #contentFirstName="ngModel" [(ngModel)]="transferUser.firstName"
                                            required="required" placeholder="First Name"
                                            [ngClass]="{'is-invalid':!contentFirstName.valid && onClickValidation}">
                                        <label for="floatingInput">First Name</label>
                                    </div>
                                </div>
                                <div class="col-12 col-md-12 mb-4">
                                    <div class="form-floating">
                                        <input class="form-control rounded" name="contentLastName"
                                            #contentLastName="ngModel" [(ngModel)]="transferUser.lastName"
                                            required="required" placeholder="Last Name"
                                            [ngClass]="{'is-invalid':!contentLastName.valid && onClickValidation}">
                                        <label for="floatingInput">Last Name</label>
                                    </div>
                                </div>
                                <div class="col-12 col-md-12 mb-4">
                                    <div class="form-floating">
                                        <input [ngClass]="{'is-invalid':!contentEmail.valid && onClickValidation}"
                                            class="form-control rounded" type="email" name="contentEmail"
                                            #contentEmail="ngModel" [(ngModel)]="transferUser.email" required="required"
                                            placeholder="Email" pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                                        <label for="floatingInput">{{"USERS.Email" | translate}}</label>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
                                <button (click)="SendContentTransfer(contentTransferForm.form)" type="button"
                                    class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- content transfer modal end-->

</div>