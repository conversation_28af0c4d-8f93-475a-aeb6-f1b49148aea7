.table> :not(caption)>*>* {
    border: 0px;
}

.modal-footer>* {
    margin: 0 !important;
}

// 
.circular-progress {
    position: relative;
    width: 50px;
    height: 50px;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    color: #555;
}

.circular-progress:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 42px;
    height: 42px;
    background-color: #fff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.circular-progress .progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    z-index: 0;
}

.title-per {
    color: #000 !important;
    font-size: 12px;
    z-index: 1;
}