import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersContactListService } from './userscontactlist.service';

@Injectable({
    providedIn: 'root'
})
export class UsersContactListManager extends BaseManager {

    constructor(protected usersContactListService: UsersContactListService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(usersContactListService, loadingService, toastService);
    }
}
