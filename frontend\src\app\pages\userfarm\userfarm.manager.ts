import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { UserFarmService } from './userfarm.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class UserFarmManager extends BaseManager {

    constructor(private userFarmService: UserFarmService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userFarmService, loadingService, toastService);
    }
}
