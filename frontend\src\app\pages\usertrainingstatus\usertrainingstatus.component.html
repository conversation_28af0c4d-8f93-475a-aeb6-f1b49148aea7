<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserTrainingStatus Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'UserTrainingStatus.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('USERTRAININGSTATUS','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('USERTRAININGSTATUS','AddButton')">
            <span class="hidden-xs">{{'UserTrainingStatus.ADD_NEW_USERTRAININGSTATUS' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Users.objName' | translate}} {{'Users.email' | translate}}</th>
					    		<th>{{'Farm.objName' | translate}} {{'Farm.name' | translate}}</th>
					    		<th>{{'Training.objName' | translate}} {{'Training.videoTitle' | translate}}</th>
					    		<th>{{'UserTrainingStatus.rejectCompletionDate' | translate}}</th>
					    		<th>{{'UserTrainingStatus.userVideoUrl' | translate}}</th>
					    		<th>{{'UserTrainingStatus.isApproved' | translate}}</th>
					    		<th>{{'UserTrainingStatus.assignedDate' | translate}}</th>
					    		<th>{{'UserTrainingStatus.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.userIdDetail" class="primary-color" [routerLink]="['/dashboard/users/detail/'+record.userIdDetail.id]">
				                       		{{record.userIdDetail.email}}
						        		
						        		</a> 
					        		</td>
					        		<td>
				                    		<a *ngIf="record.farmIdDetail" class="primary-color" [routerLink]="['/dashboard/farm/detail/'+record.farmIdDetail.id]">
				                       		{{record.farmIdDetail.name}}
						        		
						        		</a> 
					        		</td>
					        		<td>
				                    		<a *ngIf="record.traningIdDetail" class="primary-color" [routerLink]="['/dashboard/training/detail/'+record.traningIdDetail.id]">
				                       		{{record.traningIdDetail.videoTitle}}
						        		
						        		</a> 
					        		</td>
				        			<td>{{record.rejectCompletionDate|date:'MM/dd/yyyy hh:mm'}} </td>
					        		<td>
					        			<div [innerHtml]="record.userVideoUrl" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        	<td class="text-center">
			                    	<i class="fa" [ngClass]="{'fa-check enabled-icon':record.isApproved,'fa-times disabled-icon':!record.isApproved}"
			                        	aria-hidden="true"></i>
			                    </td>
				        			<td>{{record.assignedDate|date:'MM/dd/yyyy hh:mm'}} </td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('USERTRAININGSTATUS','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('USERTRAININGSTATUS','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('USERTRAININGSTATUS','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('USERTRAININGSTATUS','EditButton')"
                           [routerLink]="['/dashboard/user-training-status/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('USERTRAININGSTATUS','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('USERTRAININGSTATUS','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-usertrainingstatus-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-usertrainingstatus-edit>

<div class="modal fade site-detail-modal right" id="userTrainingStatusDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="userTrainingStatusDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-usertrainingstatus-detail [recordId]="selectedId"></app-usertrainingstatus-detail>
            </div>
        </div>
    </div>
</div>

