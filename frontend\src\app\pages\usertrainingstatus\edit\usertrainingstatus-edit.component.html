<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserTrainingStatus Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/user-training-status']">{{'UserTrainingStatus.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'UserTrainingStatus.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{userTrainingStatus.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #usertrainingstatusForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.userId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="users"
			                                       bindLabel="email"
			                                       bindValue="id"
			                                       name="userTrainingStatusUserId"
			                                       #userTrainingStatusUserId="ngModel"
					                            		[(ngModel)]="userTrainingStatus.userId"  #UserId="ngModel"
			                                       [ngClass]="{'invalid-field':userTrainingStatusUserId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} users">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userTrainingStatusUserIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.farmId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="farms"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="userTrainingStatusFarmId"
			                                       #userTrainingStatusFarmId="ngModel"
					                            		[(ngModel)]="userTrainingStatus.farmId"  #FarmId="ngModel"
			                                       [ngClass]="{'invalid-field':userTrainingStatusFarmId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} farm">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userTrainingStatusFarmIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.traningId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="trainings"
			                                       bindLabel="videoTitle"
			                                       bindValue="id"
			                                       name="userTrainingStatusTraningId"
			                                       #userTrainingStatusTraningId="ngModel"
					                            		[(ngModel)]="userTrainingStatus.traningId"  #TraningId="ngModel"
			                                       [ngClass]="{'invalid-field':userTrainingStatusTraningId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} training">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userTrainingStatusTraningIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.rejectCompletionDate" | translate}}
		                        </label>
									<my-date-picker name="userTrainingStatusRejectCompletionDate" [options]="myDatePickerOptions" [(ngModel)]="userTrainingStatus.rejectCompletionDateCalendar"
										>
									</my-date-picker>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.userVideoUrl" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="userTrainingStatusUserVideoUrl"  [(ngModel)]="userTrainingStatus.userVideoUrl" #UserVideoUrl="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.isApproved" | translate}}
		                        </label>
									<div class="material-switch">
										<input id="userTrainingStatusIsApprovedId" name="userTrainingStatusIsApproved" type="checkbox"  [(ngModel)]="userTrainingStatus.isApproved" />
										<label for="userTrainingStatusIsApprovedId" class="label-primary"></label>
									</div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserTrainingStatus.assignedDate" | translate}}
		                        </label>
									<my-date-picker name="userTrainingStatusAssignedDate" [options]="myDatePickerOptions" [(ngModel)]="userTrainingStatus.assignedDateCalendar"
										>
									</my-date-picker>
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(usertrainingstatusForm.form)"
	        *ngIf="authService.isAccessible('USERTRAININGSTATUS','AddButton')" 
	        [disabled]="authService.isDisabled('USERTRAININGSTATUS','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="userTrainingStatusUserIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-users [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-users>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="userTrainingStatusFarmIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-farm [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-farm>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="userTrainingStatusTraningIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-training [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-training>
		            </div>
		        </div>
		    </div>
		</div>

