import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Constant } from 'src/app/config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
import { ProgramTrainingHistoryManager } from '../program-training-history/program-training-history.manager';
import { CommonService } from 'src/app/shared/common.service';
import { RouteDataService } from 'src/app/shared/title.service';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ProgramTrainingHistoryService } from '../program-training-history/program-training-history.service';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { AuthService } from 'src/app/shared/auth.services';
import { Users } from 'src/app/models/users';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';
import AOS from 'aos';
import { saveAs } from 'file-saver';
import { CourseParticipantsManager } from './course-participants.manager';
import { CourseParticipantsService } from './course-participants.service';
import { FilterParam } from 'src/app/models/filterparam';
import { ManageCourseManager } from '../manage-course/manage-course-manager';
import { Course } from 'src/app/models/course';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-course-participants',
  templateUrl: './course-participants.component.html',
  styleUrls: ['./course-participants.component.scss']
})
export class CourseParticipantsComponent extends BaseListServerSideComponent implements OnInit {
  @Input() assignVideoFromMoment: boolean | false;
  @Output() assignTrainingIdOutput = new EventEmitter<string>();

  readonly MY_CONSTANT = Constant;
  strUserId: string;
  trainingsId: string;
  courseId: string;
  loadingVideo: boolean;
  programUserTrainingVideoModal: any;
  moment: any = moment;
  searchTraining: any;
  isTrainingCsvExport: boolean = false
  filterTrainingModal: any;
  trainingFilterData: RestResponse;
  username: any;
  public training: any;
  searchCourse: any;
  course: Course;

  constructor(protected manageCourseManager: ManageCourseManager, private route: ActivatedRoute, protected courseParticipantsManager: CourseParticipantsManager, protected commonService: CommonService,
    protected toastService: ToastService, protected loadingService: LoadingService, public routeDataService: RouteDataService,
    protected router: Router, private courseParticipantsService: CourseParticipantsService,
    private commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private authService: AuthService, private http: HttpClient) {
    super(courseParticipantsManager, commonService, toastService, loadingService, router);
  }

  async ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.title;
    });
    this.course = new Course();
    this.records = [] as Users[];
    this.strUserId = this.route.snapshot.paramMap.get('id');
    this.courseId = this.route.snapshot.paramMap.get('courseId');
    this.trainingsId = this.route.snapshot.paramMap.get('trainingsId');
    this.courseParticipantsService.getUserId(this.strUserId);
    this.filterParam.course = this.courseId;
    this.filterParam.trainingId = this.trainingsId;
    this.fetchParticipantsRecords(this.filterParam);
    this.init();
    if (this.courseId) {
      this.fetchCourseDetailsRecord(this.courseId);
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.programUserTrainingVideoModal = new bootstrap.Modal(
        document.getElementById('programUserTrainingVideoModal')
      );
    }, 0);
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  resetFilter() {
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.searchText;
    delete this.filterParam.status;
    this.filterTrainingModal.hide();
    this.onCancel();
  }

  onClickTrainingFilter() {
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  approveOrRejectTraining(id: string, status: string) {
    let data = {
      id: id,
      userId: this.strUserId,
      status: status
    }
    const statusText = status == "APPROVED" ? 'approve' : 'reject';
    const confirmatiomMessage = 'Would you like to ' + statusText + ' this training?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectTrainingCallback.bind(this), data);
  }

  async approveOrRejectTrainingCallback(data: boolean) {
    try {
      const response: RestResponse = await this.courseParticipantsService.approveOrRejectTraining(data)
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

  exportTrainingHistroyCsv() {
    this.isTrainingCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], this.username + '_My Training Report_' + moment().format('DD-MM-YYYY') + '.xlsx', { type: 'application/vnd.ms.excel' });

        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isTrainingCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isTrainingCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isTrainingCsvExport = false;
      return;
    }
  }

  download(): Observable<Blob> {
    const strUserId = this.route.snapshot.paramMap.get('id');
    return this.http.post(environment.BaseApiUrl + '/api/trainings/' + 'userid' + '/export',
      this.filterParam, { responseType: 'blob' }).pipe(catchError(this.parseErrorBlob));
  }

  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isTrainingCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }

  watchVideo(record: any) {
    this.loadingVideo = true
    this.programUserTrainingVideoModal.show();
    AOS.init({ disable: true });
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.videoUrl);
      this.loadingVideo = false;
    }, 0)
  }

  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  openTrainingDetailPage(record: any) {
    window.open(
      '/dashboard/program-admin/course/training/detail/' + record.trainingDetail.id,
      '_blank'
    );
  }

  assignTrainingVideoCallback(id: string) {
    this.assignTrainingIdOutput.emit(id);
  }
  assignTrainingVideo(id: string) {
    this.commonService.confirmation('Would you like to assign this training video?', this.assignTrainingVideoCallback.bind(this), id, null, null, null);
  }
  //cours detail page linking
  openCourseDetailPage(record: any) {
    window.open(
      '/dashboard/program-admin/course/detail/' + record.courseDetail.id,
      '_blank'
    );
  }

  onFetchCompleted() {
    let obj: any = {};
    obj.type = "SECOND_LAST_TEXT_REPLACE";
    obj.title = this.username;
    this.commonService.updateData(obj);
    //this.routeDataService.setData(this.router.url, this.training.fullName);
  }

  async handleButtonClick(type: string, id: string): Promise<void> {
    // Create the payload dynamically
    // const filterParam: FilterParam = {
    //   course: type === 'course' ? id : '',
    //   training: type === 'training' ? id : '',
    // };

    let filterParam = new FilterParam();
    filterParam.course = type === 'course' ? id : null,
      filterParam.training = type === 'training' ? id : null,

      // Fetch participants based on the payload
      await this.fetchParticipantsRecords(filterParam);
  }

  async fetchParticipantsRecords(param: FilterParam) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.courseParticipantsService.fetchCourseTrainingParticipants(param);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
      if (this.trainingsId)
        this.setBreadCrumbCourseParticipants()
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchCourseDetailsRecord(id) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseManager.getsCourseDetails(id);
      this.loadingService.hide();
      this.onFetchCompleted();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.course = response.data;
      this.setBreadcrumb();

      // this.layoutComponent.getBreadcrumb()
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  setBreadcrumb() {
    //for course detail page
    const fullUrl = `/dashboard/program-admin/course/detail/${this.course.id}`
    if (this.course.id) {
      this.routeDataService.addBreadcrumb(this.course.title, fullUrl, this.course.id)
    }
    this.setBreadCrumbCourseParticipants();

  }

  //set breadcrumb for course completion page
  setBreadCrumbCourseParticipants() {
    const fullUrl = this.router.url; // Current full URL
    const queryIndex = fullUrl.indexOf('?'); // Find query parameter start
    const cleanUrl = queryIndex > -1 ? fullUrl.slice(0, queryIndex) : fullUrl;
    const title = this.course?.id ? 'Course Participant' : 'Course Training Participant';
    this.routeDataService.addBreadcrumb(title, cleanUrl, this.course.id ?? this.trainingsId)
  }

}
