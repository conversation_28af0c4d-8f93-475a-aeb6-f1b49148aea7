import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { MomentRejectedLogs } from 'src/app/models/momentrejectedlogs';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { MomentRejectedLogsManager } from './momentrejectedlogs.manager';
import * as moment from 'moment';
import AOS from 'aos';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
declare var bootstrap: any;

@Component({
  selector: 'app-moment-rejected-logs-list',
  templateUrl: './moment-rejected-logs-list.component.html',
  styleUrls: ['./moment-rejected-logs-list.component.scss']
})
export class MomentRejectedLogsListComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() getMomentId: string | undefined;
  moment: any = moment;
  recordData: any;
  rejectMomentVideoOrImageModal: any;
  loadingVideo: boolean = false;
  mediaType: string;
  constructor(protected momentRejectedLogsManager: MomentRejectedLogsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(momentRejectedLogsManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
  }

  ngOnInit(){
    this.filterParam.MomentId = this.getMomentId;
    this.records = new Array<MomentRejectedLogs>();
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.rejectMomentVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('rejectMomentVideoOrImageModal')
      );
    }, 0)
  }

  getMediaType(url) {
    const extension = url.split(/[#?]/)[0].split('.').pop().trim();
    if (extension == "jpg" || extension == "jpeg" || extension == "png") {
      this.mediaType = "image"
    }
    if (extension == "mkv" || extension == "mp4" || extension == "avi" || extension == 'avi' || extension == "mov") {
      this.mediaType = "video";
    }
  }

  getMomentMediaType(mediaUrl: any, type: string) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      if (type == 'title') {
        return "Open Image"
      }
      if (type == 'icon') {
        return 'image-solid.svg'
      }
    }
    if (this.mediaType == 'video') {
      if (type == 'title') {
        return "Watch Video"
      }
      if (type == 'icon') {
        return 'video-logo.svg'
      }
    }
  }

  getImageWidthClass(mediaUrl: any) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      return true;
    } else {
      return false;
    }
  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.userVideoUrl);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.rejectMomentVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.userVideoUrl);
        this.loadingVideo = false;
      }, 0)
    }
  }

}
