<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">
    <div class="user-details-section">
        <div class="row">
            <div class="col-12 col-lg-7 ps-0">
                <div class="user-details-section">
                    <ul class="nav nav-pills">
                        <li class="nav-item bg-secondary user-details-btn width-180px"
                            [ngClass]="{'bg-secondary': userDetailsTabs == 'active'}"
                            *ngIf="!authService.isProgramAdmin()" (click)="onClickUserDetailsTab('active')">
                            <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'active'}"
                                aria-current="page">Active</a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': userDetailsTabs == 'inactive'}"
                            *ngIf="!authService.isProgramAdmin()" (click)="onClickUserDetailsTab('inactive')">
                            <a class="btn nav-link"
                                [ngClass]="{' active bg-secondary': userDetailsTabs == 'inactive'}">Inactive
                            </a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': userDetailsTabs == 'invited'}"
                            *ngIf="!authService.isProgramAdmin()" (click)="onClickUserDetailsTab('invited')">
                            <a class="btn nav-link"
                                [ngClass]="{' active bg-secondary': userDetailsTabs == 'invited'}">Invited
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-12 col-lg-5 pe-0 d-flex align-items-center justify-content-end">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" appDelayedInput (delayedInput)="search($event)"
                        [(ngModel)]="filterParam.searchText" [delayTime]="1000" placeholder="Search">
                    <i class="bi bi-search pe-3"></i>
                </div>
                <div class="col-12 col-sm-4 text-end pe-0">
                    <button type="button" class="btn add-button btn-primary btn-lg"
                        [routerLink]="['/dashboard/admin/program/user/edit/'+0]"
                        *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','AddButton')">
                        <img src="/assets/images/icons/menu/add_icon.svg" class="me-3 width-15px" alt="">Add New User
                    </button>
                </div>
                <!-- <button type="button" (click)="openFilterTrainingModal()"
                    class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px pe-0">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button> -->
            </div>
        </div>
    </div>
    <!-- Manage User Tabs -->
    <app-program-admin-active-user *ngIf="userDetailsTabs == 'active'"
        [filterParam]="filterParam"></app-program-admin-active-user>
    <app-program-admin-invited-user *ngIf="userDetailsTabs == 'invited'"
        [filterParam]="filterParam"></app-program-admin-invited-user>
    <app-program-admin-inactive-user *ngIf="userDetailsTabs == 'inactive'"
        [filterParam]="filterParam"></app-program-admin-inactive-user>

</div>
