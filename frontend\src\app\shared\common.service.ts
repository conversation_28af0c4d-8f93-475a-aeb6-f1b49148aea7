import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { RestResponse } from './auth.model';
import { Notification } from "src/app/models/notification";
import { NotificationService } from '../pages/notification/notification.service';
import { ToastService } from './toast.service';
import { Constant } from '../config/constants';
import { AuthService } from './auth.services';
import { BaseService } from '../config/base.service';
import { FilterParam } from '../models/filterparam';
import { filter } from 'rxjs/operators';
import { GumletService } from './gumlet.service';
import { LoadingService } from '../services/loading.service';
import { BehaviorSubject } from 'rxjs';

declare const swal: any;
declare const sweetAlert: any;
declare var require: any;
const { createFFmpeg, fetchFile } = require('@ffmpeg/ffmpeg');

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  private sharedDataSource = new BehaviorSubject<any>(null);
  sharedData$ = this.sharedDataSource.asObservable();
  constructor(private router: Router, private notificationService: NotificationService, private toastService: ToastService, private authService: AuthService, private gumletService: GumletService, private loadingService: LoadingService) {
  }

  confirmation(heading: string, callback: any, param1 = null, param2 = null, param3 = null, cancelCallback: any = null) {
    swal({
      title: heading,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d9534f',
      confirmButtonText: 'Yes',
      cancelButtonText: 'No',
      closeOnConfirm: false,
      allowEscapeKey: false,
      animation: false
    }, (inputValue) => {
      if (inputValue) {
        sweetAlert.close();
        callback(param1, param2, param3);
      } else if (cancelCallback) {
        cancelCallback();
      }
    });
  }

  updateData(data: any) {
    this.sharedDataSource.next(data);
  }

  info(heading: string, subText: string, callback: any, param1 = null, param2 = null) {
    swal({
      title: heading,
      type: 'info',
      showCancelButton: true,
      confirmButtonColor: '#d9534f',
      confirmButtonText: subText,
      cancelButtonText: 'No',
      closeOnConfirm: false,
      allowEscapeKey: false,
      animation: false
    }, () => {
      sweetAlert.close();
      callback(param1, param2);
    });
  }

  convertVideoFormat(file: any): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const { name } = file;
      let fileName = file.name.split(".")[0];
      const ffmpeg = createFFmpeg({
        mainName: 'main',
        log: true,
        corePath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.js',
      });
      await ffmpeg.load();
      setTimeout(() => {
        ffmpeg.exit(); // ffmpeg.exit() is callable only after load() stage.
      }, 1000);
      ffmpeg.FS('writeFile', name, await fetchFile(file));
      await ffmpeg.run('-i', name, "-ssim", "1", "-tune", "ssim", "-preset", "ultrafast", 'output.mp4');
      const data = ffmpeg.FS('readFile', 'output.mp4');
      const blob = new Blob([data.buffer], { type: 'video/mp4' });
      const modifiedFile = new File([blob], fileName + ".mp4", { type: 'video/mp4' });
      resolve(modifiedFile);
    });
  }

  preventRemoveExistingSite(heading: string, callback: any, param1 = null, param2 = null, param3 = null, cancelCallback: any = null) {
    swal({
      title: heading,
      type: 'warning',
      showConfirmButton: false,
      showCancelButton: true,
      cancelButtonText: 'Cancel',
      closeOnConfirm: false,
      allowEscapeKey: false,
      animation: false
    }, (inputValue) => {
      if (inputValue) {
        sweetAlert.close();
        callback(param1, param2, param3);
      } else if (cancelCallback) {
        cancelCallback();
      }
    });
  }

  async viewNotification(notification: Notification) {
    if (!notification.isSeen) {
      const resp: RestResponse = await this.notificationService.seenNotification(notification.id);
      if (!resp.status) {
        this.toastService.error(resp.message);
      }
    }
    this.redirectionToPage(notification.targetType, notification.targetId);
  }

  redirectionToPage(targetType: string, targetId: string) {
    if (!targetId) {
      return;
    }

    switch (targetType) {
      case Constant.NOTIFICATION_TYPE.NEW_STAFF:
        this.router.navigate(['/dashboard/user-details/' + targetId]);
        break;
      case Constant.NOTIFICATION_TYPE.NEW_MOMENT:
        if (this.authService.isSuperAdmin()) {
          this.router.navigate(['/dashboard/moment/detail/' + targetId]);
        }
        else {
          this.router.navigate(['/staff-moments']);
        }
        break;
      case Constant.NOTIFICATION_TYPE.UPDATE_MOMENT:
        if (this.authService.isSuperAdmin()) {
          this.router.navigate(['/dashboard/moment/detail/' + targetId]);
        }
        else {
          this.router.navigate(['/staff-moments']);
        }
        break;
      case Constant.NOTIFICATION_TYPE.USER_ENQUIRY:
        this.router.navigate(['/dashboard/users-enquiry']);
        break;
      case Constant.NOTIFICATION_TYPE.REQUEST_SITE_CHANGE:
        this.router.navigate(['/dashboard/request-site-change']);
        break;
      case Constant.NOTIFICATION_TYPE.UPDATED_REQUEST_SITE_CHANGE:
        this.router.navigate(['/staff-profile']);
        break;
      case Constant.NOTIFICATION_TYPE.NEW_TRAINING_ASSIGNED:
        this.router.navigate(['/staff-assigned-training']);
        break;
      case Constant.NOTIFICATION_TYPE.TRAINING_APPROVED:
        this.router.navigate(['/staff-assigned-training'], { queryParams: { tab: "APPROVED" } });
        break;
      case Constant.NOTIFICATION_TYPE.TRAINING_REJECTED:
        this.router.navigate(['/staff-assigned-training'], { queryParams: { tab: "REJECTED" } });
        break;
      case Constant.NOTIFICATION_TYPE.TRAINING_COMPLETED:
        this.router.navigate(['/dashboard/user-details/' + targetId], { queryParams: { tab: "training" } });
        break;
      case Constant.NOTIFICATION_TYPE.NEW_PROGRAM_ASSIGNED:
        this.router.navigate(['/staff-assigned-training']);
        break;
      case Constant.NOTIFICATION_TYPE.NEW_COURSE_ASSIGNED:
        this.router.navigate(['/staff-assigned-training']);
        break;
      case Constant.NOTIFICATION_TYPE.MESSAGE_MOMENT_VIDEO_UPLOADED:
        if (this.authService.isSuperAdmin()) {
          this.router.navigate(['/dashboard/moment/detail/' + targetId]);
        }
        else {
          this.router.navigate(['/staff-moments']);
        }
        break;
      case Constant.NOTIFICATION_TYPE.TYPE_NEW_USER:
        this.router.navigate(['/dashboard/user-details/' + targetId]);
        break;
      case Constant.NOTIFICATION_TYPE.TYPE_PROGRAM_ADMINISTRATOR_INVITE_ACCEPT:
        this.router.navigate(['/dashboard/program-admin-list']);
        break;
      case Constant.NOTIFICATION_TYPE.TYPE_NEW_PROGRAM_ADMINISTRATOR:
        this.router.navigate(['/dashboard/program-detail/' + targetId]);
        break;
      case Constant.NOTIFICATION_TYPE.TYPE_UPLOADING_REQUIRED_TRAINING_UPLOADED:
        this.router.navigate(['/dashboard/program-admin/course/participants/' + targetId]);
        return;
      case Constant.NOTIFICATION_TYPE.TYPE_COURSE_COMPLETION:
        this.router.navigate(['/dashboard/program-admin/course/users/' + targetId]);
        return;
      case Constant.NOTIFICATION_TYPE.TYPE_USER_SEND_COURSE_REQUEST:
        this.router.navigate(['/dashboard/program-admin/users'], { queryParams: { tab: 'pending' } });
        return;
    }
  }

  async seenAllNotifications(notifications: Notification[]) {
    let notificationsIds = [];
    notifications.forEach((notification: Notification) => {
      if (!notification.isSeen) {
        notificationsIds.push(notification.id);
      }
    });
    if (notificationsIds.length == 0) {
      return;
    }
    const resp: RestResponse = await this.notificationService.seenAllNotification(notificationsIds);
    if (!resp.status) {
      this.toastService.error(resp.message);
      return;
    }
    this.toastService.success(resp.message);
  }

  async getGumletResponse(filterParam: FilterParam) {
    try {
      const response: RestResponse = await this.gumletService.fetchGumletStatus(filterParam).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      return response;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  async deleteVideo(fileUrl: string) {
    this.loadingService.show();
    const apiUrl = '/api/video/remove';
    const data: any = {
      fileName: fileUrl
    }
    try {
      const response: RestResponse = await this.gumletService.saveRecord(apiUrl, data);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      this.loadingService.hide();
      return response;
    } catch (e) {
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  // Restrict special characters except - (used in referral code)
  restrictSpecialCharacters(event: any) {
    const forbiddenCharacters = /[0-9!@#$%^&*(),.?":{}|<>~`'[\]\\+=_-]/;
    if (forbiddenCharacters.test(event.key)) {
      event.preventDefault();
    }
  }
}
