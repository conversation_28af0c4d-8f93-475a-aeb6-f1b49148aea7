<div class="breadcrumb-container">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" data-dismiss="modal" aria-label="Close" *ngIf="recordId">
	      <img src="/assets/images/back.png" class="img-responsive">
	    </a>
	    <a class="menu-icon-button" (click)="commonUtil.toggleMenu()" *ngIf="!recordId">
	      <img src="/assets/images/menu.png" class="img-responsive">
	    </a>
        <div class="project-name-container">
            <h3 class="project-name">Farm Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a data-dismiss="modal" aria-label="Close">{{'Farm.objNames' | translate}}</a></li>
                <li class="active">{{'COMMON.DETAIL' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right"
                title="{{'COMMON.EDIT' | translate}}" *ngIf="authService.isAccessible('FARM','EditButton')"
                data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/farm/edit/'+record.id]">
            <span><i class="fa fa-pencil" aria-hidden="true"></i></span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [ngClass]="{'no-padding':recordId}">
    <div class="site-card">
    	<div class="custom-tab-container">
    		<ul class="nav nav-pills nav-tabs">
		        <li [ngClass]="{'active':tabView == 'DETAIL'}"><a (click)="onChangeTab('DETAIL')">{{'Farm.objName' | translate}}</a></li>
	      </ul>
      	</div>
        <div class="clearfix"></div>
        <div class="custom-tab-content-container">
		      <table class="table table-bordered table-striped" [hidden]="tabView !== 'DETAIL'" *ngIf="tabViewed['DETAIL'] || tabView == 'DETAIL'">
		    	<tbody>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.name" | translate}}
					          </td>
					            <td>{{record.name}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.country" | translate}}
					          </td>
					            <td>{{record.country}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.latitude" | translate}}
					          </td>
					            <td>{{record.latitude}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.longitude" | translate}}
					          </td>
					            <td>{{record.longitude}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.description" | translate}}
					          </td>
					            <td>{{record.description}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.propertyNo" | translate}}
					          </td>
					            <td>{{record.propertyNo}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.farmingUrl" | translate}}
					          </td>
					            <td>{{record.farmingUrl}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.updatedOn" | translate}}
					          </td>
					             <td>{{record.updatedOn|date:'MM/dd/yyyy'}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Farm.isActive" | translate}}
					          </td>
					            <td>{{record.isActive}}</td>
					        </tr>
		    		</tbody>
		    	</table>
        </div>
     </div>
</div>
