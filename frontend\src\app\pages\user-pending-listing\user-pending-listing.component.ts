import { Component, Input, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadingService } from 'src/app/services/loading.service';
import { ActivatedRoute, Router } from '@angular/router';
import { RestResponse } from 'src/app/shared/auth.model';
import { ManageUserCourseDetail } from 'src/app/models/manageusercoursedetail';
import { Constant } from 'src/app/config/constants';
import { UserPendingService } from './user-pending.service';
import { UserPendingManager } from './user-pending.manager';
import { FilterParam } from 'src/app/models/filterparam';
import * as moment from 'moment';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-user-pending-listing',
  templateUrl: './user-pending-listing.component.html',
  styleUrls: ['./user-pending-listing.component.scss']
})
export class UserPendingListingComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  moment: any = moment;
  courseUser: ManageUserCourseDetail;
  userDetailsTabs: string = "profile";
  userId: string | undefined;
  readonly MY_CONSTANT = Constant;
  records: ManageUserCourseDetail[] = [];
  searchPendingUser: any;
  @Input() filterParam: FilterParam;

  constructor(protected userPendingManager: UserPendingManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil,
    private userPendingService: UserPendingService, protected route: ActivatedRoute) {
    super(userPendingManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.courseUser = new ManageUserCourseDetail();
    this.records = new Array<ManageUserCourseDetail>();
    // this.userId = this.route.snapshot.paramMap.get('id');
    // this.manageProgramUsersService.getUserId(this.userId);
    // this.fetchPendingCourseRecords();
    this.init();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  async fetchPendingCourseRecords() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userPendingService.getPendingCourse(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // reload data 
  reloadData() {
  }

  // open profile page
  openUserProfilePage() {
    this.router.navigate(['/dashboard/program-admin/profile-setting']);
  }

  // Approve or Reject

  approveOrRejectTraining(id: string, status: string) {
    let data = {
      Id: id,
      status: status
    }
    const statusText = status == "APPROVED" ? 'approve' : 'reject';
    const confirmatiomMessage = 'Would you like to ' + statusText + ' this pending course request?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectTrainingCallback.bind(this), data);
  }

  async approveOrRejectTrainingCallback(data: { id: number, status: string }) {
    try {
      const response: RestResponse = await this.userPendingService.approveOrRejectPendingCourse(data)
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

}
