import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { SubCategoryService } from './subcategory.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class SubCategoryManager extends BaseManager {

    constructor(private subCategoryService: SubCategoryService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(subCategoryService, loadingService, toastService);
    }
}
