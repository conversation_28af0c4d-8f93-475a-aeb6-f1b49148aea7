.staff-add-moment {
  padding: 0px 4px 20px 4px;
  max-width: 380px;
  text-align: center;

  .staff-add-moment-form {
    text-align: start;
    white-space: nowrap;

    .form-select {
      border-radius: 10px !important;
    }

    .form-description {
      border-radius: 10px !important;
      padding: 10px 20px;
    }

    .upload-img-button {
      padding: 25px 42px !important;
      border: 1px solid;
      border-radius: 10px !important;
      font-weight: 600 !important;
      width: 100%;
      font-size: 14px !important;

      .upload-icon {
        width: 35px;
      }
    }

    .add-moment-button {
      width: 100%;
      padding: 20px 20px;
      color: #fff;
      border-radius: 10px !important;
      font-size: 14px;
    }
  }
}

.form-floating-textarea {
  position: relative;
}

.form-floating-textarea:before {
  content: "";
  position: absolute;
  top: 1px;
  left: 6px;
  width: calc(100% - 14px);
  height: 32px;
  border-radius: 4px;
  background-color: #ffffff;
}

.form-floating-textarea textarea.form-control {
  padding-top: 32px !important;
  min-height: 80px;
}

.delete-video-container {
  top: 9px;
  right: 11px;
  width: 27px;
  height: 27px;
  background: white;
  border-radius: 23px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  i {
    font-size: 25px;
    z-index: 2;
  }
}

.moment-image-container {
  max-width: 410px;
  overflow: hidden;
  position: relative;

  img {
    border-radius: 23px;
    width: 100%;
    height: 250px;
    object-fit: cover;
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .video-container {
    max-width: 100%;
    border-radius: 4px;
    margin: 0 auto;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 23px;
    background: black;
    aspect-ratio: 16/9;

    video {
      width: 100%;
      height: 100%;
      border-radius: 23px;
    }
  }
  .play-button-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: auto;
    pointer-events: none;

    .circle-play-b-cls {
      cursor: pointer;
      pointer-events: auto;
      z-index: 1;

      svg {
        width: 55px;
        fill: #fff;
        stroke: #fff;
        cursor: pointer;
        background-color: rgba(black, 0.2);
        border-radius: 50%;
        opacity: 0.9;
      }
    }
  }
}

.moment-video-width {
  max-width: 410px;
}

@media (min-width: 411px) {
  .staff-add-moment {
    .staff-add-moment-form {
      .upload-img-button {
        font-size: 16px !important;
      }
    }
  }
}

.ng-select-main-container {
  border-radius: 10px !important;
}

.form-description {
  height: 150px !important;
}
