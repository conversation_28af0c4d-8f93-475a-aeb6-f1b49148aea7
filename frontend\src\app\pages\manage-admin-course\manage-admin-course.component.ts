import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import AOS from 'aos';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Course } from 'src/app/models/course';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageAdminCourseService } from './manage-admin-course-service';
import { ManageAdminCourseManager } from './manage-admin-course-manager';
import { saveAs } from 'file-saver';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { AdminManageProgramUserService } from '../admin-manage-program-users/admin-manage-program-users-service';
import { FilterParam } from 'src/app/models/filterparam';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-admin-course',
  templateUrl: './manage-admin-course.component.html',
  styleUrls: ['./manage-admin-course.component.scss']
})

export class ManageAdminCourseComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  moment: any = moment;
  courses: Course;
  publishCourseList: any[];
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  programAdminId: string;
  strProgramAdmin: string;
  record: any = { isPublish: false };
  //modal
  // selectedInviteUserCourse: string;
  dropdownSettings = {};
  adminInviteUserManageCourseModal: any;
  insertedData: { username: string, email: string, id }[] = [];
  Courses: { username: string, email: string } = { username: '', email: '' };
  errorMessage: string = '';
  selectedCourseUserId: string;
  selectedCourseUser: any;
  username: any;
  isCourseCsvExport: boolean;



  constructor(protected maanageAdminCourseManager: ManageAdminCourseManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, protected route: ActivatedRoute, public commonUtil: CommonUtil, private manageAdminCourseService: ManageAdminCourseService, private adminManageProgramUserService: AdminManageProgramUserService, private http: HttpClient) {
    super(maanageAdminCourseManager, commonService, toastService, loadingService, router);
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    this.setDropdownSettings();
    this.records = new Array<Course>();
    this.request.loadEditPage = false;
    this.courses = new Course();
    this.publishCourseList = new Array();
    this.isPlusButton = false
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.fetchPublishRecords(this.filterParam);
    this.strProgramAdmin = this.route.snapshot.paramMap.get('id');
    this.manageAdminCourseService.getUserId(this.strProgramAdmin);
    this.filterParam.strProgramAdmin = this.strProgramAdmin;
    this.init();
    this.programAdminId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1).split("?")[0];
    console.log(this.programAdminId);
    
  }

  async fetchRecords(param, callBack) {
    try {
      this.hasDataLoad = false;
      this.setParam(param);
      this.loadingService.show();

      const response: RestResponse = await this.maanageAdminCourseManager.fetchAll(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
      this.onFetchCompleted();
      callBack({ recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length, recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length, data: [] });
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.adminInviteUserManageCourseModal = new bootstrap.Modal(
        document.getElementById('adminInviteUserManageCourseModal')
      );
    }, 0)
  }

  ngOnDestroy() {
    this.clean();
  }

  removeSuccess() {
    this.onCancel();
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/admin/program/course/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/admin/program/course/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/admin/program/course/edit/' + id])
    this.strProgramAdmin = id;
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  updateIspublish(data: any, recordData: any) {
    this.commonService.confirmation('Would like to change the status?', this.updateIspublishCallback.bind(this), { id: recordData.id, isPublish: data.currentTarget.checked }, null, null, this.cancelupdateIspublishCallback.bind(this));
  }

  cancelupdateIspublishCallback() {
    this.onCancel();
  }

  async updateIspublishCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.maanageAdminCourseManager.updatePublish(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // invite user modal
  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  selectInviteUserCourse(event: any): void {
    if (event) {
      // this.selectedInviteUserCourse = event.title;
      this.selectedCourseUser = event.title;
      this.selectedCourseUserId = event ? event.id : null;
    }
  }

  openInviteCourseUserModal() {
    AOS.init({ disable: true });
    this.adminInviteUserManageCourseModal.show();
    this.courses = new Course();
    this.selectedCourseUserId = null;
    this.insertedData = new Array<any>();
    this.fetchPublishRecords(this.filterParam);
  }

  closeInviteModal() {
    this.adminInviteUserManageCourseModal.hide();
    this.courses = new Course();
  }

  async fetchPublishRecords(filterParam?: FilterParam) {
    try {
      // var course = new Course();
      // course.id = this.selectedCourseId;
      filterParam.offset = null;
      filterParam.next = null;
      this.loadingService.show();
      const response: RestResponse = await this.manageAdminCourseService.getisPublish(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchCourseUsersData() {

    const resp: RestResponse = await this.manageAdminCourseService.getCourseUserRecords(this.filterParam);
    this.loadingService.hide();
    if (!resp.status) {
      return;
    }
    this.records = resp.data;
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.router.navigate(['/dashboard/program-admin/courses']);
  }

  async onClickInviteUserCourse() {
    if (this.insertedData.length == 0) {
      return;
    }
    try {
      this.loadingService.show();
      this.courses.courseUsersMapping = this.insertedData;
      this.courses.id = this.selectedCourseUserId;
      this.courses.programAdmin = this.programAdminId
      const response: RestResponse = await this.adminManageProgramUserService.sendCourseInvite(this.courses);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.insertedData = [];
      this.courses = new Course();
      this.adminInviteUserManageCourseModal.hide();
      this.onSaveSuccess(response.message);
      this.fetchCourseUsersData();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  insertData(form: any, id: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$/;

    // if (!this.courses.username || !this.courses.email) {
    //   this.errorMessage = 'Name and email are required.';
    //   return;
    // }

    if (!emailPattern.test(this.courses.email)) {
      this.errorMessage = 'Invalid email format.';
      return;
    }

    const isDuplicate = this.insertedData.some(data => data.email === this.courses.email);

    if (isDuplicate) {
      this.errorMessage = 'This email is already entered.';
    } else {
      this.insertedData.push({ username: this.courses.username, email: this.courses.email, id: this.courses.id });
      this.errorMessage = '';  // Clear error message if the data is successfully inserted
      this.courses = new Course();
    }
  }

  removeData(index: number) {
    this.insertedData.splice(index, 1);
    this.errorMessage = '';  // Clear error message
  }

  //cours detail page linking
  openCourseDetailPage(record: any) {
    this.router.navigate(['/dashboard/admin/program/course/detail/' + record.id]);
  }

  clearErrorMessage() {
    this.errorMessage = '';
  }


  exportTrainingHistroyCsv() {
    this.isCourseCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], this.username + '_My Course Report_' + moment().format('DD-MM-YYYY') + '.xlsx', { type: 'application/vnd.ms.excel' });

        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isCourseCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isCourseCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isCourseCsvExport = false;
      return;
    }
  }

  download(): Observable<Blob> {
    const strProgramAdmin = this.route.snapshot.paramMap.get('id');
    this.filterParam.strProgramAdmin = strProgramAdmin;
    this.filterParam.next = null,
      this.filterParam.offset = null;

    return this.http.post(environment.BaseApiUrl + '/api/courses/program/admin' + '/export',
      this.filterParam, { responseType: 'blob' }).pipe(catchError(this.parseErrorBlob));
  }
  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isCourseCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }



}
