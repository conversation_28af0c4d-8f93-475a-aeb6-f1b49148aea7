import { TranslateService } from "@ngx-translate/core";
import { BaseModel } from "../config/base.model";
import { ToastService } from "../shared/toast.service";

export class TrainingSubCategoryMapping extends BaseModel{

    subCategoryId: string;
    trainingId: string;
    subCategoryName: string;

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        throw new Error("Method not implemented.");
    }
    forRequest() {
        throw new Error("Method not implemented.");
    }

}