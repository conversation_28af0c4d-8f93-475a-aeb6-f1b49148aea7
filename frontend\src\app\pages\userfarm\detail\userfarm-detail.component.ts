import { Component, OnInit } from '@angular/core';
import {BaseDetailComponent} from '../../../config/base.detail.component';
import { UserFarm } from '../../../models/userfarm';
import { ActivatedRoute, Router } from '@angular/router';
import { UserFarmManager } from '../userfarm.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';

@Component({
  selector: 'app-userfarm-detail',
  templateUrl: './userfarm-detail.component.html',
  styleUrls: ['./userfarm-detail.component.scss']
})
export class UserFarmDetailComponent extends BaseDetailComponent implements OnInit {

	  constructor(protected route: ActivatedRoute, protected userFarmManager: UserFarmManager, protected toastService: ToastService,
	    		  protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService, 
	    		  protected translateService: TranslateService,public commonUtil: CommonUtil ) {
	    	super(userFarmManager, commonService, toastService, loadingService, route, router, translateService);
	    	
	  }

	  ngOnInit() {
	  	this.record = new UserFarm();    
	    this.isDetailPage =  true;
	    this.init();
	  }
  
	  onFetchCompleted() { 
	    super.onFetchCompleted();
	    this.filterParam.relationTable = "UserFarm";
	    this.filterParam.relationId = this.record.id;
	  }
	  
}
