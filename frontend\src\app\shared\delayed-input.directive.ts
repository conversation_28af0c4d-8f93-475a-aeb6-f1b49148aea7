import { CommonModule } from '@angular/common';
import {
    Directive, ElementRef, EventEmitter, Input,
    NgModule,
    OnDestroy, OnInit, Output
} from '@angular/core';
import { fromEvent, Subject, timer } from 'rxjs';
import { debounce, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Directive({
    selector: '[appDelayedInput]'
})
export class DelayedInputDirective implements OnInit, OnDestroy {

    private destroy$ = new Subject<void>();
    @Input() delayTime = 500;
    @Output() delayedInput = new EventEmitter<Event>();

    constructor(private elementRef: ElementRef<HTMLInputElement>) {
    }

    ngOnInit() {
        fromEvent(this.elementRef.nativeElement, 'input')
            .pipe(
                debounce(() => timer(this.delayTime)),
                distinctUntilChanged(
                    null,
                    (event: Event) => (event.target as HTMLInputElement).value
                ),
                takeUntil(this.destroy$),
            )
            .subscribe(e => this.delayedInput.emit(e));
    }

    ngOnDestroy() {
        this.destroy$.next();
    }
}

@NgModule({
    declarations: [DelayedInputDirective],
    imports: [
        CommonModule
    ],
    exports: [DelayedInputDirective]
})
export class DelayedInputModule { }