import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import * as moment from 'moment';
import AOS from 'aos';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { MomentRecurringLogsManager } from './momentrecurringlogs.manager';
import { ToastService } from 'src/app/shared/toast.service';
import { CommonService } from 'src/app/shared/common.service';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { Router } from '@angular/router';
import { CommonUtil } from 'src/app/shared/common.util';
import { MomentRecurringLogs } from 'src/app/models/momentrecurringlogs';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
declare var bootstrap: any;

@Component({
  selector: 'app-recurring-moment-logs',
  templateUrl: './recurring-moment-logs.component.html',
  styleUrls: ['./recurring-moment-logs.component.scss']
})
export class RecurringMomentLogsComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() getMomentId: string | undefined;
  moment: any = moment;
  mediaType: string;
  recurringMomentVideoOrImageModal: any;
  recordData: any;
  loadingVideo: boolean;

  constructor(protected momentRecurringLogsManager: MomentRecurringLogsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(momentRecurringLogsManager, commonService, toastService, loadingService, router);
  }


  ngOnDestroy(){
  }

  ngOnInit(){
    this.filterParam.MomentId = this.getMomentId;
    this.records = new Array<MomentRecurringLogs>();
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.recurringMomentVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('recurringMomentVideoOrImageModal')
      );
    }, 0)
  }


  getMediaType(url) {
    const extension = url.split(/[#?]/)[0].split('.').pop().trim();
    if (extension == "jpg" || extension == "jpeg" || extension == "png") {
      this.mediaType = "image"
    }
    if (extension == "mkv" || extension == "mp4" || extension == "avi" || extension == 'avi' || extension == "mov") {
      this.mediaType = "video";
    }
  }

  getMomentMediaType(mediaUrl: any, type: string) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      if (type == 'title') {
        return "View Image"
      }
      if (type == 'icon') {
        return 'image-solid.svg'
      }
    }
    if (this.mediaType == 'video') {
      if (type == 'title') {
        return "Watch Video"
      }
      if (type == 'icon') {
        return 'video-logo.svg'
      }
    }
  }

  loadVideo(url) {
    return fetch(url)
      .then(resp => resp.blob())
      .then(blob => URL.createObjectURL(blob));
  }

  getImageWidthClass(mediaUrl: any) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      return true;
    } else {
      return false;
    }
  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.userVideoUrl);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.recurringMomentVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.userVideoUrl);
        this.loadingVideo = false;
      }, 0)
    }
  }

}
