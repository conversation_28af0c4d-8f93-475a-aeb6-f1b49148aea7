<div class="site-customer-main-container px-0" data-aos="fade-up" data-aos-duration="1000">
    <div [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table allocated-users-list"
            [ngClass]="{'has-records': records.length > 0 }">
            <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th width="40"></th>
                        <th width="110" class="text-nowrap">{{'Course.fullName' | translate}}</th>
                        <th width="140" class="text-nowrap">{{'Course.email' | translate}}</th>
                        <th width="120" class="text-nowrap">{{'Course.phoneNumber' | translate}}</th>
                        <th width="80" *ngIf="!authService.isProgramAdmin()" class="text-nowrap">
                            {{'Course.programAdminName' |translate}}
                        </th>
                        <th width="80" class="text-nowrap">
                            {{'Course.action' |translate}}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                        <td width="30" style="text-align: center;">
                            <img src="/assets/images/icons/menu/user-icon.svg" height="38" width="38" class="ms-3"
                                alt="">
                        </td>
                        <td width="100px" class="pe-0">
                            <h5 class="code ms-2" title="View User Profile"
                                style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                [routerLink]="['/dashboard/program-detail/' +record?.id]">
                                {{record?.fullName}}
                            </h5>
                        </td>
                        <td>
                            {{record?.email}}
                        </td>
                        <td>
                            {{record?.phoneNumber}}
                        </td>
                        <td>
                            {{record?.programName}}
                        </td>
                        <td>
                            <div class="mb-2 custom-action-button d-sm-flex">
                                <i *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','DeleteButton') && !isPlusButton"
                                    title="Resend" (click)="reloadData(record?.id)"
                                    class="bi bi-arrow-counterclockwise font-21px cursor-pointer me-2">
                                </i>
                                <i class="bi bi-trash font-21px cursor-pointer" title="Delete"
                                    (click)="removeUserRecord(record?.id)"
                                    *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','DeleteButton')"></i>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</div>
