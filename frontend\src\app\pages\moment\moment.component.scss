.momentsByUserIdCls {
  padding: 0px 0px 20px 0px !important;
  width: 100% !important;
  display: table !important;
}

.moment-detail-cls {
  font-size: 28px !important;
  position: relative;
  top: 2px;
  line-height: 0px;
  margin-right: 14px;
  cursor: pointer;
}

.recurring-moment-cls {
  background: #c6ddf7 !important;
}

.recurring-moment-list-cls {
  width: 23px;
  margin-top: -1px;
  margin-right: 5px !important;
}

.logs-modal-content {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.logs-modal-body {
  overflow-y: auto;
  max-height: 60vh;
}

.logs-table {
  table-layout: fixed;
  width: 100%;
}

.logs-table th, .logs-table td {
  vertical-align: top;
  word-break: break-word;
  white-space: pre-line;
  padding: 8px 10px;
}

.log-description {
  font-size: 15px;
  line-height: 1.4;
  color: #222;
  word-break: break-word;
  white-space: pre-line;
}

.log-date {
  font-size: 14px;
  color: #666;
}

.btn-primary.btn-sm.rounded-pill {
  font-size: 15px;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
}

.btn-primary.btn-sm.rounded-pill:hover {
  background: #0056b3;
  color: #fff;
}

.action-taken-icon {
  background: #ffe0b2 !important;
  color: #ff9800 !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 6px 18px !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  display: inline-flex !important;
  align-items: center !important;
  box-shadow: none !important;
  margin-bottom: 6px !important;
  margin-top: 6px !important;
  gap: 6px !important;
}

.action-taken-icon img {
  height: 22px;
  width: 22px;
  filter: none;
}

.recurring-badge {
  background: #00bcd4 !important;
  color: #fff !important;
  font-weight: 600;
  border-radius: 8px;
  padding: 6px 18px;
  font-size: 16px;
}

.nonrecurring-badge {
  background: #ff9800 !important;
  color: #fff !important;
  font-weight: 600;
  border-radius: 8px;
  padding: 6px 18px;
  font-size: 16px;
}

