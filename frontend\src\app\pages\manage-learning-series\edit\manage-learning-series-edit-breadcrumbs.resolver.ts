import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ManageLearningSeriesEditBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const categoryId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard", active: false
            },
            {
                title: "Manage Learning Series", link: "/dashboard/manage-learning-series", active: false
            },
            {
                title: "Learning Series", link: "/dashboard/manage-learning-series/edit/" + categoryId, active: true
            }
        ])



    }
}
