import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class MostCompletedTrainingUsersListService  extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/training', '/api/mostcompleted/training/users');
    }

}

