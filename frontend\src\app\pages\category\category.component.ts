import { Component, OnDestroy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { CategoryManager } from './category.manager';
import { Category } from '../../models/category';
import { Router } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { FilterParam } from 'src/app/models/filterparam';
import { UsersService } from 'src/app/services/users.service';
import AOS from 'aos';
import { CategoryService } from './category.service';


declare const $: any;
declare var bootstrap: any;

@Component({
	selector: 'app-category',
	templateUrl: './category.component.html',
	styleUrls: ['./category.component.scss']
})
export class CategoryComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {

	farms: any[];
	users: any[];
	recordData: Category;
	selectedFarms: any = [];
	selectedUsers: any = [];
	selectedFarmsClone: any = [];
	assignProgramModal: any;
	farmIds: any = [];
	userIds: any = [];
	onClickValidation: boolean;
	optionalValidationMessage: string = "Please Select Site Or User";
	sendingRequestForAssignProgram: boolean;
	dropdownSettings = {};
	dropdownSettingsUsers = {};

	constructor(protected categoryManager: CategoryManager, protected toastService: ToastService,
		protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
		protected router: Router, public commonUtil: CommonUtil, private usersService: UsersService, private categoryService: CategoryService) {
		super(categoryManager, commonService, toastService, loadingService, router);
	}

	ngOnInit() {
		this.request.loadEditPage = false;
		this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
		this.records = new Array<Category>();
		this.filterParam.isOnlyEnglishLanguage = true;
		this.farms = new Array<any>();
		this.users = new Array<any>();
		this.recordData = new Category();
		this.sendingRequestForAssignProgram = false;
		this.setDropdownSettings();
		this.setDropdownSettingsUsers();
		this.init();
	}

	ngAfterViewInit() {
		setTimeout(() => {
			this.assignProgramModal = new bootstrap.Modal(
				document.getElementById('assignProgramModal')
			);
		}, 0)
	}

	onItemSelection(record: any) {
		this.onAssociatedValueSelected(record);
	}

	onCancel() {
		this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
	}

	onNewRecord() {
		if (!this.isPlusButton) {
			if (this.filterParam) {
				this.router.navigate(['/dashboard/category/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
			} else {
				this.router.navigate(['/dashboard/category/edit/0']);
			}
			return;
		}
		this.request.loadEditPage = true;
	}

	removeSuccess() {
		this.onCancel();
	}


	ngOnDestroy() {
		this.clean();
	}

	loadDetailPage(recordId) {
		this.selectedId = recordId;
		setTimeout(() => {
			$('#categoryDetailPage').appendTo('body').modal('show');
			$('#categoryDetailPage').on('hidden.bs.modal', () => {
				setTimeout(() => {
					this.selectedId = undefined;
				});
			});
		}, 500);
	}

	search($event) {
		const value = ($event.target as HTMLInputElement).value;
		this.filterParam.searchText = (value && value != '') ? value.trim() : null;
		this.refreshRecord();
	}

	editRecord(id: any) {
		this.router.navigate(['/dashboard/category/edit/' + id])
	}

	async fetchFarms(categoryId) {
		try {
			let param = new FilterParam();
			if (categoryId != null) {
				param.strCategoryId = categoryId;
			}
			const response: RestResponse = await this.usersService.GetAllAdminFarms(param);
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.farms = response.data;
		} catch (e) {
			this.toastService.error(e.message);
		}
	}

	async fetchUsers() {
		try {
			let param = new FilterParam();
			if (this.recordData != null) {
				param.strCategoryId = this.recordData.id;
			}
			const response: RestResponse = await this.usersService.fetchAll(param);
			this.users = response.data
		} catch (e) {
			this.toastService.error(e.message);
		}
	}

	async openAssignProgramModal(record: any) {
		this.sendingRequestForAssignProgram = false;
		this.setEmptyModalData();
		this.recordData = record;
		await this.fetchFarms(record.id);
		this.selectedFarms = this.farms.filter(x => x.categoryAssignedToFarm == true);
		this.selectedFarmsClone = JSON.parse(JSON.stringify(this.selectedFarms));
		await this.fetchUsers();
		if (this.selectedFarms.length == 0) {
			this.selectedUsers = this.users.filter(x => x.categoryAssignToUser == true);
		}
		AOS.init({ disable: true });
		this.assignProgramModal.show();
	}

	setEmptyModalData() {
		this.farmIds = [];
		this.userIds = [];
		this.recordData = undefined;
		this.selectedFarms = [];
		this.selectedUsers = [];
		this.onClickValidation = false;
	}

	onItemSelectForFarm(item: any) {
		if (this.farmIds && this.farmIds.length > 0) {
			if (!this.farmIds.some(id => id === item.id)) {
				this.setItemsDataForFarm(item);
			}
		} else {
			this.setItemsDataForFarm(item);
		}
		if (this.farmIds.length > 0) {
			this.fetchUsers();
		}
	}

	setItemsDataForFarm(item) {
		this.farmIds = [
			...this.farmIds,
			item.id,
		];
	}

	onDeSelectForFarm(item: any) {
		if (this.selectedFarmsClone.find(x => x.id == item.id)) {
			this.selectedFarms.push(item);
			this.selectedFarms = [...this.selectedFarms];
			this.commonService.preventRemoveExistingSite("You can't remove this site because this program already assign to users?", null, null);
		}
	}

	onDeSelectForUser(item: any) {
		if (this.userIds && this.userIds.length > 0) {
			this.userIds = this.userIds.filter(id => id !== item.id);

		}
	}

	onItemSelectForUser(item: any) {
		if (this.userIds && this.userIds.length > 0) {
			if (!this.userIds.some(id => id === item.id)) {
				this.setItemsDataForUser(item);
			}
		} else {
			this.setItemsDataForUser(item);
		}
	}

	setItemsDataForUser(item) {
		this.userIds = [
			...this.userIds,
			item.id,
		];
	}

	async onClickAssignProgramorm(valid) {
		if (this.userIds.length == 0 && this.farmIds.length == 0) {
			this.onClickValidation = true;
			return
		}
		const data = {
			categoryId: this.recordData.id,
			isActive: true,
			isDeleted: false,
			farmIds: this.farmIds?.length ? this.farmIds : null,
			userIds: this.userIds?.length ? this.userIds : null,
		}
		this.sendingRequestForAssignProgram = true;
		try {
			// this.user.roles = null;
			const response: RestResponse = await this.categoryService.assignProgram(this.removeNullDataFromObject(data));
			this.sendingRequestForAssignProgram = false;
			if (!response.status) {
				this.setEmptyModalData()
				this.toastService.error(response.message);
				return;
			}
			this.assignProgramModal.hide();
			this.setEmptyModalData();
			this.onCancel();
			this.toastService.success(response.message);
		} catch (e) {
			this.sendingRequestForAssignProgram = false;
			this.setEmptyModalData();
			this.toastService.error(e.message);
		}
	}

	removeNullDataFromObject(obj: any) {
		for (var propName in obj) {
			if (obj[propName] === null || obj[propName] === undefined) {
				delete obj[propName];
			}
		}
		return obj
	}

	setDropdownSettings() {
		this.dropdownSettings = {
			singleSelection: false,
			idField: 'id',
			enableCheckAll: false,
			textField: 'name',
			// itemsShowLimit: 3,
			allowSearchFilter: true
		};
	}

	setDropdownSettingsUsers() {
		this.dropdownSettingsUsers = {
			singleSelection: false,
			idField: 'id',
			enableCheckAll: false,
			textField: 'fullName',
			// itemsShowLimit: 3,
			allowSearchFilter: true
		};
	}

	unAssignFarmOrUser(record) {
		if (record.assignFarmCount > 0) {
			this.router.navigate(['/dashboard/category-assigned-sites/' + record.id]);
			return;
		}
		this.router.navigate(['/dashboard/category-assigned-users/' + record.id]);
	}
}
