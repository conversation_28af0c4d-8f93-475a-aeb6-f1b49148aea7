import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Farm } from './farm';
import { UserAssignTraining } from './userassigntraining';
import { Users } from './users';
export class Moment extends BaseModel {

	tenantId: number;
	slug: string;
	title: string;
	farmDetail: Partial<Farm>[];
	farmId: string;
	description: string;
	mediaType: string;
	mediaUrl: string;
	assignTrainingIdDetail: UserAssignTraining;
	assignTrainingId: string;
	status: string;
	isRecurring: boolean = true;
	createdByUserDetail: Users;
	recurringLogsCount: number;
	rejectLogsCount: number;
	type: string;
	userVideo: string;
	instruction: string;
	completedBy: number;
	farmsIds: string[];
	course: string;
	training: string;
	closedDate: Date | null = null;
	startDate: Date | null = null;
	expiryDate: Date | null = null;
	trainingTitle: string;
	courseTitle: string;


	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
		this.farmDetail = new Array<Farm>();
	}

	static fromResponse(data: any): Moment {
		// ...existing code...
		const obj = new Moment();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.title = data.title;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.farmDetail = data.farmDetail;
		obj.farmId = data.farmId;
		obj.description = data.description;
		obj.mediaType = data.mediaType;
		obj.mediaUrl = data.mediaUrl;
		obj.assignTrainingIdDetail = data.assignTrainingIdDetail;
		obj.assignTrainingId = data.assignTrainingId;
		obj.createdByUserDetail = data.createdByUserDetail;
		obj.recurringLogsCount = data.recurringLogsCount;
		obj.rejectLogsCount = data.rejectLogsCount;
		obj.status = data.status;
		obj.isRecurring = data.isRecurring;
		obj.type = data.type;
		obj.userVideo = data.userVideo;
		obj.instruction = data.instruction;
		obj.completedBy = data.completedBy;
		obj.course = data.course;
		obj.training = data.training;
		obj.closedDate = data.closedDate ? new Date(data.closedDate) : null;
		obj.startDate = data.startDate ? new Date(data.startDate) : null;
		obj.expiryDate = data.expiryDate ? new Date(data.expiryDate) : null;
		obj.trainingTitle = data.trainingTitle;
		obj.courseTitle = data.courseTitle;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.title)) {
			form.controls.title.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	forRequest() {
		this.title = this.trimMe(this.title);
		this.description = this.trimMe(this.description);
		this.mediaType = this.trimMe(this.mediaType);
		this.mediaUrl = this.trimMe(this.mediaUrl);
		this.status = this.trimMe(this.status);
		return this;
	}
}

