<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container p-0">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="mb-4">
            <h4>Most Clicked News Feeds</h4>
            <hr class="m-0" />
        </div>
        <label>Show
            <select (change)="onChangeShowEntries($event.target.value)" name="assined-users-length"
                aria-controls="assined-users-show-entries"
                class="form-select show-entries form-select-sm height-44px padding-right-30">
                <option value="10" selected="selected">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select> Rows</label>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th style="text-wrap: nowrap;">{{'NewsFeed.title' | translate}}</th>
                        <!-- <th style="width: 150px;">{{'NewsFeed.Views' | translate}}</th> -->
                        <th style="width: 80px;">{{'NewsFeed.Counts' | translate}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>
                            <a (click)="openNewsFeedDetailPage(record)"
                                class="text-decoration-underline">{{record.title}}</a>
                        </td>
                        <!-- <td>
                            {{record.clickCount}}
                        </td> -->
                        <td>
                            <a class="text-decoration-underline" (click)="openUsersList(record)">
                                {{record.clickCount}}
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal fade modal-xl" id="newsFeedVideoProgressModal" aria-hidden="true"
    aria-labelledby="newsFeedVideoProgressModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newsFeedVideoProgressModalLabel">News Feed Click Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <app-news-feed-click-logs
                    [getnewsFeedVideoProgressData]="newsFeedVideoProgressData && newsFeedVideoProgressData"
                    *ngIf="newsFeedVideoProgressModal && newsFeedVideoProgressModal._isShown"></app-news-feed-click-logs>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>