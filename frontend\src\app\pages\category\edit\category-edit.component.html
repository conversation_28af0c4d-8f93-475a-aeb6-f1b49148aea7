<div data-aos="zoom-in"  data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
	<div class="dashboard-content-container">
		<form #categoryForm="ngForm" novalidate="novalidate">
			<div class="row">
				<div class="mt-2 mb-3">
					<h4 class="fw-bold">{{request.recordId == 0 ? "New Program" : "Edit Program"}}</h4>
					<p class="user-edit-msg">Please make sure you fill all the fields before you click on save button</p>
				</div>
				<div *ngFor="let category of categoryFormInputs" class="col-12 col-md-6 col-lg-6 col-xl-4">
					<div class="category-language w-100 mb-4 p-3 bg-secondary d-flex justify-content-between">
						<h6 class="lh-lg program-heading text-light">{{category.languageName}}</h6>
						<img [src]="getImageFetchByLanguageName(category.languageName)" class="img-fluid me-2" alt="">
					</div>
					<div class="video-title">
						<div class="form-floating mb-4 w-100 category-language ">
							<input (ngModelChange)="category.languageName == MY_CONSTANT.languages.English ? addCategoryTitleToRemainingLanguages($event) : null"  [ngClass]="{'is-invalid':!title.valid && onClickValidation}" class="form-control"
								type="text" [name]="category.languageName" #title="ngModel" [(ngModel)]="category.title"
								required="required" placeholder="{{'Category.title' | translate}}">
							<label for="floatingInput">{{"Category.title" | translate}}</label>
						</div>
						<div class="form-floating form-floating-textarea mb-4 w-100 category-language">
							<textarea (ngModelChange)="category.languageName == MY_CONSTANT.languages.English ? addDescriptionToRemainingLanguages($event) : null" [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
								class="form-control form-description" [name]="category.languageName+'Description'" #description="ngModel"
								[(ngModel)]="category.description" required="required" placeholder="Description"
								id="floatingTextarea2"></textarea>
							<label for="floatingInput">{{"Category.description" | translate}}</label>
						</div>
					</div>
				</div>
				<div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
					<button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
						type="button" (click)="save(categoryForm.form)"
						*ngIf="authService.isAccessible('CATEGORY','AddButton')"
						[disabled]="authService.isDisabled('CATEGORY','AddButton')">
						Save
					</button>
				</div>
			</div>
		</form>
	</div>
</div>




<!-- <div class="breadcrumb-container" *ngIf="!isPlusButton">
	<div class="col-md-12 breadcrumb-detail-container">
		<a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
			<img src="/assets/images/menu.png" class="img-responsive">
		</a>
		<div class="project-name-container">
			<h3 class="project-name">Category Administration</h3>
			<ol class="breadcrumb">
				<li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
				<li><a [routerLink]="['/dashboard/category']">{{'Category.objNames' | translate}}</a></li>
				<li class="active" *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'Category.objName' |
					translate}}</li>
				<li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{category.name}}
				</li>
			</ol>
		</div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
	<div class="site-card">
		<form #categoryForm="ngForm" novalidate="novalidate">
			<div class="row justify-content-start">
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Category.title" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255" name="categoryTitle"
								required="required" [(ngModel)]="category.title" #Title="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Category.description" | translate}}
						</label>
						<textarea class="form-control" rows="5" name="categoryDescription" required="required"
							[(ngModel)]="category.description" #Description="ngModel"></textarea>
					</div>
				</div> -->
<!-- <div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Category.languageId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="languages" bindLabel="name" bindValue="id" name="categoryLanguageId"
								#categoryLanguageId="ngModel" [(ngModel)]="category.languageId" required="required"
								#LanguageId="ngModel"
								[ngClass]="{'invalid-field':categoryLanguageId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} language">
							</ng-select>
							<span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('categoryLanguageIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Category.commonTitle" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255"
								name="categoryCommonTitle" [(ngModel)]="category.commonTitle" #CommonTitle="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Category.groupCode" | translate}}
						</label>
						<div class="select-width select-outer-options-body">
							<select class="form-control select-2" name="categoryGroupCode"
								[(ngModel)]="category.groupCode" #groupCode="ngModel">
								<option value="null">{{'COMMON.SELECT_OPTION' | translate}}</option>
								<option [value]="groupCode.id" *ngFor="let groupCode of groupCodeOptions">
									{{groupCode.name | translate}}
								</option>
							</select>
						</div>
					</div>
				</div> -->
<!-- </div>
		</form>
		<div class="clearfix"></div>
		<div class="col-md-12 no-padding text-right">
			<button title="Save" class="btn btn-primary site-button" type="button" (click)="save(categoryForm.form)"
				*ngIf="authService.isAccessible('CATEGORY','AddButton')"
				[disabled]="authService.isDisabled('CATEGORY','AddButton')">
				{{"COMMON.SAVE" | translate}}
			</button>
			<button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button"
				(click)="navigate()">
				{{"COMMON.CANCEL" | translate}}
			</button>
			<div class="clearfix"></div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="modal fade nav-scroll" id="categoryLanguageIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-language [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-language>
			</div>
		</div>
	</div>
</div> -->
