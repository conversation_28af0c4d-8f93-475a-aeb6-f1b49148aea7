<div class="breadcrumb-container">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" data-dismiss="modal" aria-label="Close" *ngIf="recordId">
	      <img src="/assets/images/back.png" class="img-responsive">
	    </a>
	    <a class="menu-icon-button" (click)="commonUtil.toggleMenu()" *ngIf="!recordId">
	      <img src="/assets/images/menu.png" class="img-responsive">
	    </a>
        <div class="project-name-container">
            <h3 class="project-name">UserTrainingStatus Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a data-dismiss="modal" aria-label="Close">{{'UserTrainingStatus.objNames' | translate}}</a></li>
                <li class="active">{{'COMMON.DETAIL' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right"
                title="{{'COMMON.EDIT' | translate}}" *ngIf="authService.isAccessible('USERTRAININGSTATUS','EditButton')"
                data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/user-training-status/edit/'+record.id]">
            <span><i class="fa fa-pencil" aria-hidden="true"></i></span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [ngClass]="{'no-padding':recordId}">
    <div class="site-card">
    	<div class="custom-tab-container">
    		<ul class="nav nav-pills nav-tabs">
		        <li [ngClass]="{'active':tabView == 'DETAIL'}"><a (click)="onChangeTab('DETAIL')">{{'UserTrainingStatus.objName' | translate}}</a></li>
	      </ul>
      	</div>
        <div class="clearfix"></div>
        <div class="custom-tab-content-container">
		      <table class="table table-bordered table-striped" [hidden]="tabView !== 'DETAIL'" *ngIf="tabViewed['DETAIL'] || tabView == 'DETAIL'">
		    	<tbody>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.userId" | translate}}
					          </td>
					    		<td>
					            	<a data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/users/detail/'+record.userIdDetail.id]"  *ngIf="record.userIdDetail">
					    			{{record.userIdDetail.email}}
					    			</a>
					    		</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.farmId" | translate}}
					          </td>
					    		<td>
					            	<a data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/farm/detail/'+record.farmIdDetail.id]"  *ngIf="record.farmIdDetail">
					    			{{record.farmIdDetail.name}}
					    			</a>
					    		</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.traningId" | translate}}
					          </td>
					    		<td>
					            	<a data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/training/detail/'+record.traningIdDetail.id]"  *ngIf="record.traningIdDetail">
					    			{{record.traningIdDetail.videoTitle}}
					    			</a>
					    		</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.rejectCompletionDate" | translate}}
					          </td>
					             <td>{{record.rejectCompletionDate|date:'MM/dd/yyyy'}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.userVideoUrl" | translate}}
					          </td>
					            <td>{{record.userVideoUrl}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.isApproved" | translate}}
					          </td>
					        	<td>
					        		<span class="label label-info">{{record.isApproved ? 'Yes' : 'No'}}</span>
				                </td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.assignedDate" | translate}}
					          </td>
					             <td>{{record.assignedDate|date:'MM/dd/yyyy'}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.updatedOn" | translate}}
					          </td>
					             <td>{{record.updatedOn|date:'MM/dd/yyyy'}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"UserTrainingStatus.isActive" | translate}}
					          </td>
					            <td>{{record.isActive}}</td>
					        </tr>
		    		</tbody>
		    	</table>
        </div>
     </div>
</div>
