import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Constant } from 'src/app/config/constants';
import { Users } from 'src/app/models/users';
import { AssignedUsersService } from 'src/app/services/assigned-users.service';
import { LoadingService } from 'src/app/services/loading.service';
import { TrainingAssignedUsersService } from 'src/app/services/training-assigned-users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UserAssignTrainingService } from '../userassigntraining/userassigntraining.service';
import { TrainingAssignedUsersManager } from './training-assigned-users.manager';

declare const $: any;
@Component({
  selector: 'app-training-assigned-users',
  templateUrl: './training-assigned-users.component.html',
  styleUrls: ['./training-assigned-users.component.scss']
})
export class TrainingAssignedUsersComponent extends BaseListServerSideComponent implements OnInit {
  userSelected: boolean;
  readonly MY_CONSTANT = Constant;

  constructor(private route: ActivatedRoute, protected trainingAssignedUsersManager: TrainingAssignedUsersManager, protected commonService: CommonService, protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private trainingAssignedUsersService: TrainingAssignedUsersService, private userAssignTrainingService: UserAssignTrainingService) {
    super(trainingAssignedUsersManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.records = [] as Users[];
    const trainingId = this.route.snapshot.paramMap.get('id');
    this.trainingAssignedUsersService.getTrainingId(trainingId);
    this.init();
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.userSelected = false;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    this.userSelected = false;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  selectUnselectAll(event: any) {
    if (event.currentTarget.checked) {
      this.userSelected = true;
      $('tbody').find('input[type="checkbox"]').prop('checked', true);
    } else {
      this.userSelected = false;
      $('tbody').find('input[type="checkbox"]').prop('checked', false);
    }

  }

  selectUnselectCheck() {
    let rowsLength = $('tbody').find('.records-cls').length;
    let checkedCheckboxLength = $('tbody').find('input[type="checkbox"]:checked').length;
    if (rowsLength == checkedCheckboxLength)
      $(".selectAll").prop('checked', true)
    else
      $(".selectAll").prop('checked', false)

    if (checkedCheckboxLength > 0)
      this.userSelected = true;
    else
      this.userSelected = false;
  }

  unassignedUser() {
    let trainingIds = []
    $('input[name="trainingId"]:checked').each(function () {
      trainingIds.push(this.value);
    });
    this.commonService.confirmation(trainingIds.length == 1 ? "Would you like to unassigned staff?" : "Would you like to unassigned multiple staff?",
    this.unAssignedUserCallback.bind(this),trainingIds);
  }

  async unAssignedUserCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userAssignTrainingService.unassignTraining(data,'multiple');
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }


}
