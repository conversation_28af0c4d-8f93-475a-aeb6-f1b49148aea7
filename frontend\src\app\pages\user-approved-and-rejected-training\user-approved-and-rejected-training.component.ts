import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { UserApprovedAndRejectedTraining } from 'src/app/models/userapprovedandrejectedtraining';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { UserApprovedAndRejectedTrainingManager } from './user-approved-and-rejected-training.manager';
import * as moment from 'moment';
import AOS from 'aos';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
declare var bootstrap: any;

@Component({
  selector: 'app-user-approved-and-rejected-training',
  templateUrl: './user-approved-and-rejected-training.component.html',
  styleUrls: ['./user-approved-and-rejected-training.component.scss']
})
export class UserApprovedAndRejectedTrainingComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() trainingType: string | undefined
  moment: any = moment;
  recordData: any;
  filterApprovedOrRejectedTrainingModal: any;
  loadingVideo: boolean = false;
  watchVideoModalApprovedRejected: any;
  fromDate: any;
  toDate: any;
  constructor(protected userApprovedAndRejectedTrainingManager: UserApprovedAndRejectedTrainingManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, protected route: ActivatedRoute, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(userApprovedAndRejectedTrainingManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.records = new Array<UserApprovedAndRejectedTraining>();
    this.filterParam.strUserId = this.route.snapshot.paramMap.get('id');
    this.filterParam.status = this.trainingType;
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.watchVideoModalApprovedRejected = new bootstrap.Modal(
        document.getElementById('watchVideoModalApprovedRejected')
      );
      this.filterApprovedOrRejectedTrainingModal = new bootstrap.Modal(
        document.getElementById('filterApprovedOrRejectedTrainingModal')
      );
    }, 0);
  }

  openFilterTrainingModal() {
    AOS.init({ disable: true });
    this.filterApprovedOrRejectedTrainingModal.show();
  }

  ngOnDestroy() {
    this.clean();
  }

  onClickTrainingFilter(valid) {
    this.filterApprovedOrRejectedTrainingModal.hide()
    this.onCancel();
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  resetFilter() {
    delete this.filterParam.categorySearch;
    this.fromDate = null;
    this.toDate = null;
    delete this.filterParam.startDate
    delete this.filterParam.endDate;
    delete this.filterParam.subCategorySearch;
    delete this.filterParam.SearchCommonTitle;
    this.filterApprovedOrRejectedTrainingModal.hide();
  }

  watchVideo(record: any) {
    this.recordData = record;
    this.loadingVideo = true
    this.watchVideoModalApprovedRejected.show();
    AOS.init({ disable: true });
    this.loadVideoFromUrl.UrlToBlobUrl(record.userVideoUrl)
      .then(blobUrl => { // now it's loaded
        document.body.className = 'loaded';
        setTimeout(() => {
          let vid = document.getElementById('staff-video') as HTMLVideoElement;
          this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
          vid.addEventListener('canplaythrough', (event) => {
            this.loadingVideo = false;
          })
        }, 0);
      }).catch((err) => console.log(err));
  }

  ngOnChanges() {
    if (this.dtElement) {
      this.filterParam.strUserId = this.route.snapshot.paramMap.get('id');
      this.filterParam.status = this.trainingType;
      this.refreshRecord();
      this.resetFilter();
    }
    $("#watchVideoModalApprovedRejected").on("hide.bs.modal", function () {
      var videoElement = document.getElementById('staff-video') as HTMLVideoElement;
      videoElement.pause();
      videoElement.removeAttribute('src'); // empty source
      videoElement.load();
    });

  }

}
