import { Component, OnInit } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { AssignedUsersService } from 'src/app/services/assigned-users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';

@Component({
  selector: 'app-staff-assigned-sites',
  templateUrl: './staff-assigned-sites.component.html',
  styleUrls: ['./staff-assigned-sites.component.scss'],
  providers: [FilterParam]
})
export class StaffAssignedSitesComponent implements OnInit {
  records: any;
  totalRecordsCount: number;
  totalPageNumber: number;
  next: number = 10;
  offset: number = 1;
  loadMore: boolean = false;
  constructor(private assignedUsersService: AssignedUsersService, private filterParam: FilterParam, private authService: AuthService, private toastService: ToastService) { }

  ngOnInit() {
    this.filterParam.userId = this.authService.getUser().id;
    this.filterParam.next = this.next;
    this.filterParam.offset = this.offset;
    this.fetchRecords(false);
  }

  async fetchRecords(loadMore: boolean) {
    try {
      const response: RestResponse = await this.assignedUsersService.fetchAll(this.filterParam);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if (loadMore) {
        this.loadMore = false;
        const newAssignedSites = response.data;
        this.records = this.records.concat(newAssignedSites);
      } else {
        this.records = response.data;
      }
      this.totalRecordsCount = response.data?.length ? response.data[0].totalCount : 0;
      this.totalPageNumber = Math.ceil(this.totalRecordsCount / this.filterParam.next);
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  onScrollingFinished() {
    if (this.totalRecordsCount) {
      if (this.records.length >= this.totalRecordsCount && this.filterParam.offset >= this.totalPageNumber) {
        return false;
      }
      this.filterParam.next = 10;
      this.filterParam.offset = this.filterParam.offset + 1;
      this.loadMore = true;
      this.fetchRecords(true);
    }
  }

}
