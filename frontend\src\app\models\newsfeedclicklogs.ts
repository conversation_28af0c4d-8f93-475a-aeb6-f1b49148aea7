import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Category } from './category';
import { Language } from './language';
export class NewsFeedClickLogs extends BaseModel {

  tenantId: number;
  slug: string;
  categoryId: string;
  title: string;
  description: string;
  url: string;

  constructor() {
    super();
    this.isDeleted = false;
    this.isActive = true;
  }

  isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
    if (this.isNullOrUndefinedAndEmpty(this.title)) {
      form.controls.title.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  forRequest() {
    this.title = this.trimMe(this.title);
    this.description = this.trimMe(this.description);
    return this;
  }
}
