import { Component, On<PERSON><PERSON>roy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { ManageLearningSeriesManager } from './manage-learning-series.manager';
import { Category } from '../../models/category';
import { Router } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { UsersService } from 'src/app/services/users.service';
import AOS from 'aos';
import { ManageLearningSeriesService } from './manage-learning-series.service';
import { Content } from '@angular/compiler/src/render3/r3_ast';
import { LearningSeries } from 'src/app/models/learningseries';
import * as moment from 'moment';


declare const $: any;
declare var bootstrap: any;

@Component({
	selector: 'app-manage-learning-series',
	templateUrl: './manage-learning-series.component.html',
	styleUrls: ['./manage-learning-series.component.scss']
})
export class ManageLearningSeriesComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {

	recordData: LearningSeries;
	farmIds: any = [];
	userIds: any = [];
	onClickValidation: boolean;
	restrictedUsersModal: any;
	restrictedUsersData: string;
	moment: any = moment;

	constructor(protected ManageLearningSeriesManager: ManageLearningSeriesManager, protected toastService: ToastService,
		protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
		protected router: Router, public commonUtil: CommonUtil, private usersService: UsersService, private ManageLearningSeriesService: ManageLearningSeriesService) {
		super(ManageLearningSeriesManager, commonService, toastService, loadingService, router);
	}

	ngOnInit() {
		this.request.loadEditPage = false;
		this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
		this.records = new Array<LearningSeries>();
		this.init();
	}

	ngAfterViewInit() {
		setTimeout(() => {
			this.restrictedUsersModal = new bootstrap.Modal(
				document.getElementById('restrictedUsersModal')
			);
		}, 0)
	}

	getContentTypes(items: any) {
		var titles = items.map(function (item) {
			return "<div>" + item['title'] + "</div>";
		}).join("   ");
		return titles
	}

	openRestrictedUsersList(record: any) {
		AOS.init({ disable: true });
		this.filterParam.learningSeriesId = record.id
		this.restrictedUsersData = record;
		this.restrictedUsersModal.show();
	}

	onItemSelection(record: any) {
		this.onAssociatedValueSelected(record);
	}

	onCancel() {
		this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
	}

	onNewRecord() {
		if (!this.isPlusButton) {
			if (this.filterParam) {
				this.router.navigate(['/dashboard/manage-learning-series/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
			} else {
				this.router.navigate(['/dashboard/manage-learning-series/edit/0']);
			}
			return;
		}
		this.request.loadEditPage = true;
	}

	removeSuccess() {
		this.onCancel();
	}

	search($event) {
		const value = ($event.target as HTMLInputElement).value;
		this.filterParam.searchText = (value && value != '') ? value.trim() : null;
		this.refreshRecord();
	}


	ngOnDestroy() {
		this.clean();
	}

	editRecord(id: any) {
		this.router.navigate(['/dashboard/manage-learning-series/edit/' + id])
	}
}
