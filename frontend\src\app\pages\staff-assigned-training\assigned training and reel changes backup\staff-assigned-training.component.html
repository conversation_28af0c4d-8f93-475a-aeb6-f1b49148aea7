<div data-aos="fade-up" data-aos-duration="1000" class="staff-assigned-training container" [ngClass]="{'videos-reel-cls': currentTrainingData}">
  <div *ngIf="!currentTrainingData">
    <div class="text-center">
      <button type="button" class="btn filter-buttton me-2 bg-dark text-light btn-lg">
        <img src="/assets/images/icons/menu/filter.svg" class="moment-icon me-2 img-fluid" alt="">FILTER
      </button>
      <button (click)="changeTrainingData('INPROGRESS')"
        [ngClass]="{'bg-secondary staff-active-training-button': trainingTabs == 'INPROGRESS'}" type="button"
        class="btn staff-training-buttons btn-lg">ASSIGNED TRAINING</button>
      <button (click)="changeTrainingData('APPROVED')"
        [ngClass]="{'bg-secondary staff-active-training-button': trainingTabs == 'APPROVED'}" type="button"
        class="btn staff-training-buttons training-buttons btn-lg">APPROVED TRAINING</button>
      <button (click)="changeTrainingData('REJECTED')"
        [ngClass]="{'bg-secondary staff-active-training-button': trainingTabs == 'REJECTED'}" type="button"
        class="btn staff-training-buttons training-buttons btn-lg">REJECTED TRAINING</button>
    </div>
    <div *ngIf="trainingData.length == 0" class="staff-assigned-content p-5 text-center">No Assigned Trainings Found</div>
    <div *ngFor="let training of trainingData" class="staff-assigned-content">
      <div class="row">
        <div class="col-8">
          <h4 class="fw-bold content-msg">{{training.trainingIdDetail?.videoTitle}}</h4>
          <p class="text-secondary">{{moment(training.assignedDate).format('LL')}}</p>
        </div>
        <div class="col-4 text-end">
          <img class="px-2" [src]="training.status == 'REJECTED' ? '/assets/images/close-logo.png' : training.status == 'INPROGRESS' ? '/assets/images/icons/menu/refresh.svg' : (training.status == 'APPROVED' || training.status == 'COMPLETED') && '/assets/images/icons/menu/confirmation.svg'">
          <p class="f-s-10">{{training.status}}</p>
        </div>
        <div class="col-6">
          <h6>Program</h6>
          <p class="fw-bold content-msg">{{training.categoryIdDetail?.title}}</p>
        </div>
        <div class="col-6">
          <h6>Course</h6>
          <p class="fw-bold content-msg">{{training.subcategoryIdDetail?.title}}</p>
        </div>
        <div class="col-12">
          <div class="card">
            <div class="d-flex justify-content-between">
              <button class="video-icon mb-3 btn bg-secondary"><img src="/assets/images/icons/menu/v_camera.svg"
                  alt=""></button>
              <p class="card-content ms-2">{{training.trainingIdDetail?.description}}</p>

            </div>
            <span (click)="readMoreDescription(training)" class="text-white" *ngIf="training.trainingIdDetail.description && training.trainingIdDetail.description.length > 63">Read More</span>
            <button (click)="watchVideo(training)" class="watch-video-button btn">WATCH VIDEO</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="trainingDescriptionModal" aria-hidden="true" aria-labelledby="trainingDescriptionModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content" *ngIf="trainingDescriptionModal && trainingDescriptionModal._isShown">
          <div class="modal-header">
            <h5 class="modal-title" id="trainingDescriptionModalLabel">{{recordData?.name}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            {{recordData?.description}}
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <app-assigned-training-reels-video (trainingList)="backToTrainingListCallback()" *ngIf="currentTrainingData" [currentVideoData]="currentTrainingData"
    [trainingData]="trainingData"></app-assigned-training-reels-video>
</div>
