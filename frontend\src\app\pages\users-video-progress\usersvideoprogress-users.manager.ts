import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersVideoProgressService } from './usersvideoprogress-users.service';

@Injectable({
    providedIn: 'root'
})
export class UsersVideoProgressManager extends BaseManager {

    constructor(protected usersVideoProgressService: UsersVideoProgressService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(usersVideoProgressService, loadingService, toastService);
    }
}
