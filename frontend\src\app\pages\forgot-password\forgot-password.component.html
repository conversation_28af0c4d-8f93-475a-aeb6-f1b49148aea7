<div class="site-login-page">
  <div class="row g-0">
    <div class="col-12 col-lg-5 col-xl-3 col-xxl-3 offset-lg-1 offset-xl-1 offset-xxl-2 login-left-side-section">
      <div data-aos="fade-left" data-aos-duration="1000" class="login-form-section">
        <div class="margin-bottom-40">
          <img src="/assets/images/logo.svg" class="img-fluid" width="200">
        </div>
        <h2>{{showVerifyOtpform ?'Temp Password' : 'Forgot Password'}}</h2>
        <p>There are many variations of passages of the majority</p>
        <div class="clearfix"></div>
        <form autocomplete="off" novalidate="novalidate" #loginForm="ngForm" *ngIf="!showVerifyOtpform">
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input minlength="7"  class="form-control" type="text" name="useUserName" required="required"
                placeholder="{{'LOGIN.EMAIL' | translate}}" [ngClass]="{'is-invalid':!userNameRef.valid && onClickValidation}" #userNameRef="ngModel" [(ngModel)]="userName">
              <label for="useUserName"> {{"LOGIN.EMAIL" | translate}}</label>
            </div>
          </div>
          <div class="col-12 margin-bottom-40">
            <button [disabled]="forgotPasswordButtonDisabled" class="btn text-light bg-dark site-button large-button full-width uppercase-text"
              (click)="forgotPassword(loginForm.form.valid)">
              FORGOT PASSWORD </button>
          </div>
          <p class="term-condition-text">
            Back to <a [routerLink]="['/login']" class="text-secondary">
              Login</a>
          </p>
        </form>
        <form autocomplete="off" novalidate="novalidate" #tempPasswordForm="ngForm" *ngIf="showVerifyOtpform">
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input class="form-control" type="email" name="tempPassword" required="required" placeholder="" [ngClass]="{'is-invalid':!tempPasswordRef.valid && onClickValidation}" #tempPasswordRef="ngModel" [(ngModel)]="Otp">
              <label for="useUserName"> {{"LOGIN.TEMP_PASSWORD" | translate}}</label>
            </div>
            <!-- <app-validation-message [field]="useUserName" [onClickValidation]="onClickValidation">
                        </app-validation-message> -->
          </div>
          <div class="col-12 margin-bottom-40">
            <button class="btn text-light bg-dark site-button large-button full-width uppercase-text"
              (click)="sendOTP(tempPasswordForm.form.valid)">
              SUBMIT </button>
          </div>
        </form>
      </div>
    </div>
    <div data-aos="fade-right" data-aos-duration="1000"
      class="col-12 col-lg-7 col-xl-8 col-xxl-7 forgot-right-side-section">
    </div>
  </div>
</div>
