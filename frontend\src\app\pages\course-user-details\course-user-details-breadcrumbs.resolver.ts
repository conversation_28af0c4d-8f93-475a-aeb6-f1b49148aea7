import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { from, Observable, of } from 'rxjs';
import { CourseUserDetailsService } from './course-user-details.service';
import { RouteDataService } from 'src/app/shared/title.service';
import { switchMap } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class CourseUserDetailsBreadcrumbs implements Resolve<any> {
    // resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    //     const userId: any | null = route.paramMap.get("id");

    //     return of([
    //         {
    //             title: "Dashboard", link: "/dashboard/program-admin", active: false
    //         },
    //         {
    //             title: "Manage User", link: "/dashboard/program-admin/users", active: false
    //         },
    //         {
    //             title: "User Details", link: "/dashboard/program-admin/user-details/" + userId, active: true
    //         }
    //     ])



    // }
    constructor(protected courseUserDetailsService: CourseUserDetailsService, public routeDataService: RouteDataService) {

    }
    title: string;
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const userId: string | null = route.paramMap.get("id");
        return from(this.courseUserDetailsService.getcourseTitle(userId)).pipe(
            switchMap(response => {
                const title = response?.data?.title || 'User Details';
                // this.routeDataService.setData('title', title);
                return of([
                    { title: "Dashboard", link: "/dashboard/program-admin", active: false },
                    { title: 'Manage Seekers', link: "/dashboard/seekers/enrolled", active: false },
                    { title: "User Details", link: "/dashboard/program-admin/user-details/" + userId, active: false },
                ])
            })
        );
    }
}
