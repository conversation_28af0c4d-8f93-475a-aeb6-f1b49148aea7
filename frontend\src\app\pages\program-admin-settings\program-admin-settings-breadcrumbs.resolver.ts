import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProgramAdminSettingsBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const selectedAuthorProfileId: string | null = route.paramMap.get("id");

    return of([
      {
        title: "Dashboard", link: "/dashboard", active: false
      },
      {
        title: "Setting", link: "/dashboard/program-admin", active: false
      },
      // {
      //   title: "Setting", link: "/dashboard/program-admin/admin-setting/" + selectedAuthorProfileId, active: true
      // }
    ])
  }

}
