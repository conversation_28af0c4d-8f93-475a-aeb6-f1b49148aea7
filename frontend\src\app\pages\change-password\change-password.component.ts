import { Component, OnInit } from '@angular/core';
import { ToastService } from '../../shared/toast.service';
import { AccountService } from '../../services/account.service';
import { RestResponse } from '../../shared/auth.model';
import { LoadingService } from '../../services/loading.service';
import { Profile } from '../../models/profile';
import { CommonUtil } from '../../shared/common.util';

@Component({
    selector: 'app-change-password',
    templateUrl: './change-password.component.html',
    styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {

    onClickValidation: boolean;
    isPasswordValid = true;
    data: Profile;

    constructor(private toastService: ToastService, private accountService: AccountService, private loadingService: LoadingService,
        public commonUtil: CommonUtil) {
        this.onClickValidation = false;
        this.data = new Profile();
    }

    ngOnInit() {
    }

    update(form): void {
        this.onClickValidation = !form.valid;

        // Validate form fields
        if (!form.valid) {
            return;
        }

        // Check password validity
        if (!this.isPasswordValid) {
            this.toastService.error('New password must include at least one special character, one number, one uppercase letter, and be at least 8 characters long.');
            return;
        }

        // Check password match
        if (this.data.password !== this.data.confirmPassword) {
            this.toastService.error('Passwords do not match.');
            return;
        }

        // Proceed with API call
        this.loadingService.show();
        this.accountService.changePassword(this.data.forRequest())
            .then((data: RestResponse) => {
                this.loadingService.hide();
                if (!data.status) {
                    this.toastService.error(data.message);
                    return;
                }

                this.data = {} as any;
                this.toastService.success(data.message);
            })
            .catch((error) => {
                this.loadingService.hide();
                this.toastService.error(error.message);
            });
    }

}