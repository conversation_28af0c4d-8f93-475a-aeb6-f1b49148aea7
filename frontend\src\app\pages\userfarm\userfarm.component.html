<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserFarm Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'UserFarm.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('USERFARM','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('USERFARM','AddButton')">
            <span class="hidden-xs">{{'UserFarm.ADD_NEW_USERFARM' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Users.objName' | translate}} {{'Users.email' | translate}}</th>
					    		<th>{{'Farm.objName' | translate}} {{'Farm.name' | translate}}</th>
					    		<th>{{'UserFarm.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.userIdDetail" class="primary-color" [routerLink]="['/dashboard/users/detail/'+record.userIdDetail.id]">
				                       		{{record.userIdDetail.email}}
						        		
						        		</a> 
					        		</td>
					        		<td>
				                    		<a *ngIf="record.farmIdDetail" class="primary-color" [routerLink]="['/dashboard/farm/detail/'+record.farmIdDetail.id]">
				                       		{{record.farmIdDetail.name}}
						        		
						        		</a> 
					        		</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('USERFARM','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('USERFARM','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('USERFARM','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('USERFARM','EditButton')"
                           [routerLink]="['/dashboard/user-farm/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('USERFARM','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('USERFARM','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-userfarm-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-userfarm-edit>

<div class="modal fade site-detail-modal right" id="userFarmDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="userFarmDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-userfarm-detail [recordId]="selectedId"></app-userfarm-detail>
            </div>
        </div>
    </div>
</div>

