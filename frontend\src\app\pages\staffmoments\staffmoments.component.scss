.staffmoments-bg {
  background-color: #fbfbfb;

  .staffmoments {
    text-align: center;
    max-width: 380px !important;

    .filter-buttton {
      width: 100%;
      border-radius: 10px !important;
      padding: 15px 20px;

      .moment-icon {
        width: 22px;
      }
    }

    .staffmoments-content {
      margin-top: 25px;
      background-color: #ffffff;
      padding: 20px 4px;
      max-width: 380px;
      text-align: start;

      .content-msg {
        font-size: 13px;
      }

      .content-heading {
        color: #71828a;
        font-size: 12.5px;
        white-space: nowrap;
      }

      .white-space {
        white-space: nowrap;
      }

      .f-s-10 {
        font-size: 10px;
      }

      .manage-view-button {
        margin-top: 10px;
        padding: 12px 10px;
        width: 100%;
        border-radius: 10px !important;
        border: 1px solid #000000;
      }

      .confirmation-icon {
        background-color: #d9f4e4 !important;
        color: #07c526;
        font-size: 15px;
        width: 100px;
        height: 40px;
        border: none !important;
        border-radius: 7px;
      }
    }
  }

  @media (min-width: 411px) {
    .staffmoments {
      .staffmoments-content {
        white-space: nowrap;
      }
    }
  }

  .loading-container-video-training {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: black;
  }

  .waiting-icon {
    background-color: #ffebd1 !important;
    color: #ee8c0c;
    font-size: 15px;
    width: 130px;
    height: 40px;
    border: none !important;
    border-radius: 7px;
  }

  .close-icon {
    background-color: #f9eae5;
    color: #d85143;
    font-size: 15px;
    width: 100px;
    height: 40px;
    border: none !important;
    border-radius: 7px;
  }

  #staff-video {
    width: 100%;
    height: 100%;
    aspect-ratio: 16/9;
    background-color: black;
  }
}

.recurring-moment-cls {
  background: #c6ddf7 !important;
}
