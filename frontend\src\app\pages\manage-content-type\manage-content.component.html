<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
	<div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
		<div class="row">
			<div class="col-12 col-sm-4 text-start">
				<div class="custom-input-group">
					<input class="form-control search-form-control" placeholder="Search" appDelayedInput
						(delayedInput)="search($event)" [delayTime]="1000">
					<i class="bi bi-search pe-3"></i>
				</div>
			</div>
			<div class="col-12 col-sm-4">
			</div>
			<div class="col-12 col-sm-4 text-end pe-0 mb-2">
				<button type="button" class="btn add-button btn-primary btn-lg font-15px add-button-content" (click)="onNewRecord()"
					*ngIf="authService.isAccessible('CATEGORY','AddButton')">
					<img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-15px" alt="">Add Content Type
				</button>
			</div>
		</div>
		<div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
			<table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
				<thead>
					<tr>
						<th>{{'Category.CONTENT_TYPE_TITLE' | translate}}</th>
						<th>{{'Category.description' | translate}}</th>
						<th class="text-center" style="width: 50px;">{{'Category.action' | translate}}</th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let record of records;">
						<td>{{record.title}}</td>
						<td>
							{{record.description}}
						</td>
						<td class="text-center custom-action-button text-right">
							<div class="d-flex justify-content-end mb-3">
								<i class="bi bi-pencil font-21px me-2 cursor-pointer"
									*ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
									[class.disabled]="authService.isDisabled('CATEGORY','EditButton')" title="Edit"
									(click)="editRecord(record.id)">
								</i>
								<i class="bi bi-trash cursor-pointer font-21px"
									*ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
									[class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')" title="Delete"
									(click)="remove(record.id)"></i>
							</div>
						</td>
					</tr>
					<!-- <tr *ngIf="records.length===0">
						<td class="text-center" colspan="5">
							{{"COMMON.NORECORDS" | translate}}
						</td>
					</tr> -->
				</tbody>
			</table>
		</div>
	</div>
</div>
<!--
<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Category Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Category.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('CATEGORY','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('CATEGORY','AddButton')">
            <span class="hidden-xs">{{'Category.ADD_NEW_CATEGORY' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Category.title' | translate}}</th>
					    		<th>{{'Category.description' | translate}}</th>
					    		<th>{{'Language.objName' | translate}} {{'Language.name' | translate}}</th>
					    		<th>{{'Category.commonTitle' | translate}}</th>
					    		<th>{{'Category.groupCode' | translate}}</th>
					    		<th>{{'Category.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        			<td>{{record.title}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
				                    		<a *ngIf="record.languageIdDetail" class="primary-color" [routerLink]="['/dashboard/language/detail/'+record.languageIdDetail.id]">
				                       		{{record.languageIdDetail.name}}

						        		</a>
					        		</td>
					        			<td>{{record.commonTitle}}</td>
					        			<td>{{record.groupCode}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('CATEGORY','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('CATEGORY','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('CATEGORY','EditButton')"
                           [routerLink]="['/dashboard/category/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>
            </table>
    </div>
</div>

<app-category-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-category-edit>
-->
<!-- <div class="modal fade site-detail-modal right" id="manageContentDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="manageContentDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-manage-content-detail [recordId]="selectedId"></app-manage-content-detail>
            </div>
        </div>
    </div>
</div> -->