<div data-aos="fade-up" data-aos-duration="1000">
  <div class="row">
    <div class="col-12 col-sm-4 text-start">
    </div>
    <div class="col-12 col-sm-3">
    </div>
    <div class="col-12 col-sm-5 d-flex align-items-center justify-content-end">
      <button (click)="openFilterTrainingModal()" type="button"
        class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg padding-10">
        <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-22px" alt="">Filter
      </button>
    </div>
  </div>
  <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
    <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
      <thead>
        <tr>
          <th width="100">Date</th>
          <th>{{'SubCategory.program' | translate}}</th>
          <th>{{'SubCategory.course' | translate}}</th>
          <th>{{'Training.videoTitle' | translate}}</th>
          <!-- <th>{{ trainingTabs == 'approved-training' || trainingTabs == 'rejected-training' ? 'Approved' :
                'Approve/Reject'}}</th> -->
          <th width="100">Language</th>
          <th width="50">Assign</th>
          <th width="120">Status</th>
          <th width="120">Approve/Reject</th>
          <th width="80">User Video</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let record of records;">
          <td>{{record.assignedDate !=null ? moment(record.assignedDate).format('DD-MM-YYYY') : moment().format('DD-MM-YYYY')}}</td>
          <td>
            <div *ngFor="let category of record.trainingCategoryMappingList; let last = last;">{{category.categoryName}}</div>
          </td>
          <td><div *ngFor="let subCategory of record.trainingSubCategoryMappingList; let last = last;">{{subCategory.subCategoryName}}</div></td>
          <td>
            {{record?.trainingIdDetail?.commonVideoTitle}}
          </td>
          <!-- <td>{{ trainingTabs == 'approved-training' ? 'approved icon' : trainingTabs ==
                'rejected-training' ? 'rejected icon' : trainingTabs == 'training-assigned' &&
                'Approve/Reject'}}</td> -->
          <td width="120">{{record?.languageIdDetail?.name}}</td>
          <td width="100">
            <input
              (click)="!record.status ? assignedTrainingToUser($event,record) : record.status == MY_CONSTANT.TRAINING_STATUS.INPROGRESS && unassignTrainingToUser($event, record)"
              [name]="'assignTraining'+record.trainingId" class="form-check-input common-icons checked-blue"
              [id]="'assignTrainingId'+record.trainingId" type="checkbox" [checked]="record.status ? true : false"
              [disabled]="record.status == MY_CONSTANT.TRAINING_STATUS.COMPLETED ? true : false" value=""
              id="flexCheckChecked">
          </td>
          <td>
            <div *ngIf="record.status; else noStatus" class="status-button"
              [ngClass]="{'status-button-progress-color': record.status == MY_CONSTANT.TRAINING_STATUS.INPROGRESS, 'status-button-completed-color': record.status == MY_CONSTANT.TRAINING_STATUS.COMPLETED}">
              <img
                [src]="record.status == 'INPROGRESS' ? '/assets/images/icons/menu/refresh.svg' : '/assets/images/icons/menu/confirmation.svg'"
                class="px-2" alt="">{{record.status && record.status ==
              MY_CONSTANT.TRAINING_STATUS.INPROGRESS ? 'IN PROGRESS' : MY_CONSTANT.TRAINING_STATUS.COMPLETED}}
            </div>
            <ng-template #noStatus>-</ng-template>
          </td>
          <td>
            <div *ngIf="record.status && record.status == MY_CONSTANT.TRAINING_STATUS.COMPLETED; else noStatus">
              <i (click)="approveTraining(record)" class="bi bi-check-circle px-2 fs-3 cursor-pointer"></i>
              <i (click)="rejectTraining(record)" class="bi bi-x-circle fs-3 cursor-pointer"></i>
            </div>
            <ng-template #noStatus>-</ng-template>
          </td>
          <td>
            <button (click)="watchVideo(record)"
              *ngIf="record.status == MY_CONSTANT.TRAINING_STATUS.COMPLETED; else notCompleted"
              class="user-video-button bg-secondary">
              <img src="/assets/images/icons/menu/watch-video.svg" class="px-2">WATCH VIDEO
            </button>
            <ng-template #notCompleted>-</ng-template>
          </td>
          <!-- <td class="text-center custom-action-button text-right">
                <div class="d-flex justify-content-end mb-3">
                  <button *ngIf="authService.isAccessible('FARM','DeleteButton') && !isPlusButton"
                    [class.disabled]="authService.isDisabled('FARM','DeleteButton')" title="Delete"
                    class="btn btn-secondary text-light action-button" (click)="remove(record.trainingId)">
                    RE-ASSIGN STAFF
                  </button>
                  <button [routerLink]="['/dashboard/assigned-users/'+record.trainingId]" type="button"
                    class="btn btn-secondary icon-button">
                    <img src="/assets/icons/employees.svg" alt="">
                  </button>
                  <button
                    *ngIf="authService.isAccessible('FARM','EditButton') && !isDetailPage && !isPlusButton"
                    [class.disabled]="authService.isDisabled('FARM','EditButton')" title="Edit"
                    class="btn btn-outline-light action-button" (click)="editRecord(record.trainingId)">
                    <i class="bi bi-pencil"></i> EDIT
                  </button>
                  <button *ngIf="authService.isAccessible('FARM','DeleteButton') && !isPlusButton"
                    [class.disabled]="authService.isDisabled('FARM','DeleteButton')" title="Delete"
                    class="btn btn-primary action-button" (click)="remove(record.trainingId)">
                    <i class="bi bi-trash"></i> DELETE
                  </button>
                </div>
              </td> -->
        </tr>
        <!-- <tr *ngIf="records.length===0">
          <td class="text-center" colspan="5">
            {{"COMMON.NORECORDS" | translate}}
          </td>
        </tr> -->
      </tbody>
    </table>
    <div class="modal fade" id="watchVideoModal" aria-hidden="true" aria-labelledby="watchVideoModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="watchVideoModalLabel">{{recordData?.trainingIdDetail?.commonVideoTitle}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div *ngIf="loadingVideo" class="loading-container-video-training">
              <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
              <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <video autoplay playsinline [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
              id="staff-video"></video>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade" id="filterAssignedTrainingModal" tabindex="-1"
    aria-labelledby="filterAssignedTrainingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="filterAssignedTrainingModalLabel">Filter Assigned Training</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div *ngIf="filterAssignedTrainingModal && filterAssignedTrainingModal._isShown" class="modal-body">
          <form #trainingFeedFilterForm="ngForm" novalidate="novalidate">
            <mat-form-field class="accent w-100">
              <mat-label>Select Status</mat-label>
              <mat-select name="training_status" [(ngModel)]="filterParam.status">
                <mat-option value="INPROGRESS">IN PROGRESS</mat-option>
                <mat-option value="COMPLETED">COMPLETED</mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field class="example-form-field w-100">
              <mat-label>Search Video Title...</mat-label>
              <input [(ngModel)]="filterParam.SearchCommonTitle" name="searchCommonTitle" matInput type="text">
              <button mat-button *ngIf="filterParam.SearchCommonTitle" matSuffix mat-icon-button aria-label="Clear"
                (click)="filterParam.SearchCommonTitle=''">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
            <mat-form-field class="example-form-field w-100">
              <mat-label>Search Program...</mat-label>
              <input [(ngModel)]="filterParam.categorySearch" name="searchCategory" matInput type="text">
              <button mat-button *ngIf="filterParam.categorySearch" matSuffix mat-icon-button aria-label="Clear"
                (click)="filterParam.categorySearch=''">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
            <mat-form-field class="example-form-field w-100">
              <mat-label>Search Course...</mat-label>
              <input [(ngModel)]="filterParam.subCategorySearch" name="searchSubCategory" matInput type="text">
              <button mat-button *ngIf="filterParam.subCategorySearch" matSuffix mat-icon-button aria-label="Clear"
                (click)="filterParam.subCategorySearch=''">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
            <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
              [fromDateInput]="fromDate" [toDateInput]="toDate"></app-date-range-filter>
            <div class="modal-footer">
              <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
              <button (click)="onClickTrainingFilter(trainingFeedFilterForm.form.valid)" type="button"
                class="btn btn-primary">Filter</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserAssignTraining Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'UserAssignTraining.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('USERASSIGNTRAINING','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','AddButton')">
            <span class="hidden-xs">{{'UserAssignTraining.ADD_NEW_USERASSIGNTRAINING' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Users.objName' | translate}} {{'Users.email' | translate}}</th>
					    		<th>{{'Training.objName' | translate}} {{'Training.videoTitle' | translate}}</th>
					    		<th>{{'UserAssignTraining.status' | translate}}</th>
					    		<th>{{'UserAssignTraining.userVideoUrl' | translate}}</th>
					    		<th>{{'UserAssignTraining.assignedDate' | translate}}</th>
					    		<th>{{'Farm.objName' | translate}} {{'Farm.name' | translate}}</th>
					    		<th>{{'UserAssignTraining.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.userIdDetail" class="primary-color" [routerLink]="['/dashboard/users/detail/'+record.userIdDetail.id]">
				                       		{{record.userIdDetail.email}}

						        		</a>
					        		</td>
					        		<td>
				                    		<a *ngIf="record.trainingIdDetail" class="primary-color" [routerLink]="['/dashboard/training/detail/'+record.trainingIdDetail.id]">
				                       		{{record.trainingIdDetail.videoTitle}}

						        		</a>
					        		</td>
					        			<td>{{record.status}}</td>
					        		<td>
					        			<div [innerHtml]="record.userVideoUrl" style="max-height: 100px;overflow:auto"></div>
					        		</td>
				        			<td>{{record.assignedDate|date:'MM/dd/yyyy hh:mm'}} </td>
					        		<td>
				                    		<a *ngIf="record.farmIdDetail" class="primary-color" [routerLink]="['/dashboard/farm/detail/'+record.farmIdDetail.id]">
				                       		{{record.farmIdDetail.name}}

						        		</a>
					        		</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('USERASSIGNTRAINING','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('USERASSIGNTRAINING','DetailButton')"
				              (click)="loadDetailPage(record.trainingId)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('USERASSIGNTRAINING','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','EditButton')"
                           [routerLink]="['/dashboard/user-assign-training/edit/'+record.trainingId]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('USERASSIGNTRAINING','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','DeleteButton')"
	                           (click)="remove(record.trainingId)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>
            </table>
    </div>
</div>

<app-userassigntraining-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-userassigntraining-edit>

<div class="modal fade site-detail-modal right" id="userAssignTrainingDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="userAssignTrainingDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-userassigntraining-detail [recordId]="selectedId"></app-userassigntraining-detail>
            </div>
        </div>
    </div>
</div> -->
