import { HttpClient } from '@angular/common/http';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexYAxis,
  ApexTitleSubtitle,
  ApexGrid,
  ChartComponent,
  ApexMarkers,
  ApexResponsive,
  ApexPlotOptions,
  ApexLegend,
  ApexFill,
  ApexStroke,
  ApexAnnotations,
  ApexTooltip,
  ApexDataLabels
} from 'ng-apexcharts';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminDashboardManager } from './program-admin-dashboard.manager';
import { Course } from 'src/app/models/course';
import { RestResponse } from 'src/app/shared/auth.model';
import * as moment from 'moment';
import AOS from 'aos';
import { ProgramAdminDashboardService } from './program-admin-dashboard.service';
import { FilterParam } from 'src/app/models/filterparam';
import { data } from 'jquery';
declare const $: any;
declare var bootstrap: any;

export type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  responsive: ApexResponsive[];
  yaxis: ApexYAxis;
  title: ApexTitleSubtitle;
  grid: ApexGrid;
  colors: string[];  // Optional property to hold custom colors
  markers: ApexMarkers;  // Optional property to hold marker configuration,
  plotOptions: ApexPlotOptions;
  dataLabels: ApexDataLabels;
  legend: ApexLegend;
  fill: ApexFill;
  stroke: ApexStroke;
  labels: string[];
  annotations: ApexAnnotations;
  tooltip: ApexTooltip;
};
@Component({
  selector: 'app-program-admin-dashboard',
  templateUrl: './program-admin-dashboard.component.html',
  styleUrls: ['./program-admin-dashboard.component.scss']
})
export class ProgramAdminDashboardComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  FilterData: RestResponse;
  filterTrainingParam = new FilterParam();
  courseUserDetailFilter: any;
  trainingUserDetailFilter: any;
  courseDetail: any[] = [];       // Array to hold courseDetail data
  trainingDetail: any[] = [];
  trainingFilterData: any[] = [];
  courseFilterData: any[] = [];      // Array to hold trainingDetail data
  selectedCourseId: string | null = null; // Holds the selected course ID
  public chartOption: Partial<ChartOptions>;
  public chartTrainingOption: Partial<ChartOptions>;
  record: any;
  courseIds: number[] = [];  // Use an array for multiple selections
  trainingIds: number[] = [];  // Use an array for multiple selections
  userDetailsTabs: any;
  publishCourseList: any[];
  selectedCourseUserId: string;
  data: any[];
  pendingCourseCount: number = 0;
  unacceptedCourseCount: number = 0;
  totalExternalUserCount: number = 0;
  totalRequestCount: number = 0;

  constructor(protected programAdminDashboardManager: ProgramAdminDashboardManager, protected programAdminDashboardService: ProgramAdminDashboardService, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil) {
    super(programAdminDashboardManager, commonService, toastService, loadingService, router);
  }

  async ngOnInit() {
    this.record = new Array<Course>();
    this.courseDetail = new Array();
    this.trainingDetail = new Array();
    this.publishCourseList = new Array();
    this.filterParam = new FilterParam();
    this.filterTrainingParam = new FilterParam();
    this.courseFilterData = new Array();
    this.trainingFilterData = new Array();
    await this.fetchDashboardData();
    this.fetchPublishRecords(this.filterParam);
    this.init();
  }

  ngOnDestroy(): void {
  }

  removeSuccess() {
    this.onCancel();
  }

  onCancel() {
    this.init();
  }

  //course user
  selectInviteUserCourse(event: any): void {
    if (event) {
      // this.selectedInviteUserCourse = event.title;
      this.courseIds = event.title;
      this.selectedCourseUserId = event ? event.id : null;
    }
  }

  async fetchPublishRecords(param?: FilterParam) {
    try {
      // var course = new Course();
      // course.id = this.selectedCourseId;
      this.courseIds = null;
      this.loadingService.show();
      const response: RestResponse = await this.programAdminDashboardService.getisPublish(param);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  loadChart(firstTimeLoad: boolean) {
    if (firstTimeLoad) {
      const dataValues = this.courseDetail.length > 0 ? this.courseDetail.map(item => item.courseClickCount) : [0];
      const dataCategories = this.courseDetail.length > 0 ? this.courseDetail.map(item => item.title) : ['No Data'];
      const isFilterApplied = this.filterParam && (this.filterParam.type || (this.filterParam.startDate && this.filterParam.endDate));
      const chartType = 'bar';
      const chartTitle = isFilterApplied
        ? `Course Views (${this.filterParam.type ? this.filterParam.type.charAt(0).toUpperCase() + this.filterParam.type.slice(1) : 'Date Range'})`
        : "Course Views Over Time";
      // data value space adjustment 
      const maxValue = Math.max(...dataValues);
      const yAxisMax = 700; //maxValue + (maxValue * 0.1);
      // Define an array of 5 colors for the bars
      const uniqueColors = this.generateUniqueColors(dataValues.length);
      //dataValues[0] =  45;
      this.chartOption = {
        series: [
          {
            name: "Course Views",
            data: dataValues,
          },
        ],
        chart: {
          type: chartType,
          height: 400,
          width: '100%',
          toolbar: {
            show: false,
          },
        },
        title: {
          text: chartTitle,
          style: {
            fontSize: '12px',
            fontFamily: 'Roboto',
          },
        },
        xaxis: {
          categories: dataCategories,
          labels: {
            style: {
              fontSize: '10px',
              fontWeight: 'bold',
              fontFamily: 'Roboto',
            },
            rotateAlways: true, // Ensures rotation is always applied
            rotate: -45, // Set to -45 degrees for diagonal alignment
            maxHeight: 120, // Adjust the maximum height for label visibility
            // formatter: function (value: string) {
            //   // Truncate the label and add ellipsis if it's too long
            //   return value.length > 10 ? value.slice(0, 10) + '...' : value;
            // return value.toLowerCase();
            // },
          },
        },
        yaxis: {
          title: {
            text: " ", // Optional: Set a title or leave it empty
          },
          labels: {
            style: {
              fontSize: '12px',
            },
            formatter: function (value: number) {
              return value.toFixed(0); // Ensure labels show as whole numbers
            },
          },
          min: 0, // Start Y-axis at 0
          max: 300, // End Y-axis at 700
          tickAmount: 6,
        },
        grid: {
          show: false,
        },
        plotOptions: {
          bar: {
            distributed: true, // Ensures each bar gets its individual color
          },
        },
        colors: [
          ({ value, seriesIndex, dataPointIndex }: { value: number; seriesIndex: number; dataPointIndex: number; }) => {
            return uniqueColors[dataPointIndex];
          }
        ] as any[],
        tooltip: {
          enabled: true,
          x: {
            formatter: function (val: any, opts: any) {
              const index = opts.dataPointIndex; // Get the hovered bar index
              return dataCategories[index]; // Return the full value of dataCategories
            },
          },
          y: {
            formatter: function (val: number) {
              return `${val} Views`; // Append "Views" to the tooltip value
            },
          },
        },
      };
    }
    else {
      let courseDetail: any[] = [];
      this.courseFilterData.forEach((element) => {
        let obj: any = {};
        obj.name = element.title;
        obj.data = element.chartData && element.chartData.length > 0 ? element.chartData.map((item) => item.clickCount) : [0];

        obj.data.splice(0, 0, element.initialCount ? element.initialCount : 0);
        courseDetail.push(obj);
      });


      const dataCategories = this.courseFilterData.length > 0 ? this.courseFilterData[0].chartData.map((item) => item.name) : ['No Data'];

      dataCategories.splice(0, 0, "");

      this.chartOption = {
        series: courseDetail,
        chart: {
          type: 'line',
          height: 400,
          width: '100%',
          toolbar: {
            show: false,
          },
        },
        dataLabels: { enabled: true },
        title: {
          text: "Course",
          style: {
            fontSize: '12px',
          },
        },
        xaxis: {
          categories: dataCategories,
          labels: {
            style: {
              fontSize: '12px',
              fontWeight: 'bold',
            },
          },
        },
        yaxis: {
          min: 0,
          max: 300,
          tickAmount: 6,
          title: {
            text: " ",
          },
          labels: {
            style: {
              fontSize: '12px',
            },
            formatter: function (value: number) {
              return value.toFixed(0); // Ensure labels show as whole numbers
            },
          },
        },
        grid: {
          show: true,
        },
        plotOptions: {
          bar: {
            distributed: true,
          },
        },
        colors: this.generateUniqueColors(courseDetail.length),
        markers: {
          size: 5,
          colors: ['blue', 'purple', 'pink', 'red', 'grey'],
          strokeColors: '#fff',
          strokeWidth: 2,
          hover: {
            size: 7,
          },
        },
        legend: {
          show: true,
          position: 'top',
          horizontalAlign: 'center',
          fontSize: '12px',
          labels: {
            colors: 'black',
            useSeriesColors: false,
          },
        },
      };
    }
  }

  loadTrainingChart(firstTimeLoadTraining: boolean) {
    if (firstTimeLoadTraining) {
      const dataValues = this.trainingDetail.length > 0 ? this.trainingDetail.map(item => item.trainingClickCount) : [0];
      const dataCategories = this.trainingDetail.length > 0 ? this.trainingDetail.map(item => item.title) : ['No Data'];
      const isFilterApplied = this.filterTrainingParam && (this.filterTrainingParam.type || (this.filterTrainingParam.startDate && this.filterTrainingParam.endDate));
      const chartType = 'bar';
      const chartTitle = isFilterApplied
        ? `Training Views (${this.filterTrainingParam.type ? this.filterTrainingParam.type.charAt(0).toUpperCase() + this.filterTrainingParam.type.slice(1) : 'Date Range'})`
        : "Training Views Over Time";
      const uniqueColors = this.generateUniqueColors(dataValues.length);
      this.chartTrainingOption = {
        series: [
          {
            name: "Training Views",
            data: dataValues,
            // data: [680, 80, 500, 100, 300],
          },
        ],
        chart: {
          type: chartType,
          height: 400,
          width: '100%',
          toolbar: {
            show: false,
          },
        },
        title: {
          text: chartTitle,
          style: {
            fontSize: '12px',
          },
        },
        xaxis: {
          categories: dataCategories,
          labels: {
            style: {
              fontSize: '10px',
              fontWeight: 'bold',
            },
            rotateAlways: true, // Ensures rotation is always applied
            rotate: -45, // Set to -45 degrees for diagonal alignment
            maxHeight: 120, // Adjust the maximum height for label visibility
            // formatter: function (value: string) {
            //   // Truncate the label and add ellipsis if it's too long
            //   return value.length > 10 ? value.slice(0, 10) + '...' : value;
            // },
          },
          // tooltip: {
          //   enabled: true, // Enable tooltips for x-axis labels
          //   formatter: function (val: string) {
          //     return val; // Display the full label on hover
          //   },
          // },
        },
        yaxis: {
          title: {
            text: " ", // Optional: Set a title or leave it empty
          },
          labels: {
            style: {
              fontSize: '12px',
            },
            formatter: function (value: number) {
              return value.toFixed(0); // Ensure labels show as whole numbers
            },
          },
          min: 0, // Start Y-axis at 0
          max: 300, // End Y-axis at 700
          tickAmount: 6,
        },
        grid: {
          show: false,
        },
        plotOptions: {
          bar: {
            distributed: true, // Ensures each bar gets its individual color
          },
        },
        colors: [
          ({ value, seriesIndex, dataPointIndex }: { value: number; seriesIndex: number; dataPointIndex: number; }) => {
            return uniqueColors[dataPointIndex];
          }
        ] as any[],
      };
    } else {
      let trainingDetail: any[] = [];
      this.trainingFilterData.forEach((element) => {
        let obj: any = {};
        obj.name = element.title;
        obj.data = element.chartData && element.chartData.length > 0 ? element.chartData.map((item) => item.clickCount) : [0];

        obj.data.splice(0, 0, element.initialCount ? element.initialCount : 0);
        trainingDetail.push(obj);
      });

      const dataCategories = this.trainingFilterData.length > 0 ? this.trainingFilterData[0].chartData.map((item) => item.name) : ['No Data'];
      dataCategories.splice(0, 0, "");
      this.chartTrainingOption = {
        series: trainingDetail,
        chart: {
          type: 'line',
          height: 400,
          width: '100%',
          toolbar: {
            show: false,
          },
        },
        dataLabels: { enabled: true },
        title: {
          text: "Training",
          style: {
            fontSize: '12px',
          },
        },
        xaxis: {
          categories: dataCategories,
          labels: {
            style: {
              fontSize: '12px',
              fontWeight: 'bold',
            },
          },
        },
        yaxis: {
          min: 0,
          max: 300,
          tickAmount: 6,
          title: {
            text: " ",
          },
          labels: {
            style: {
              fontSize: '12px',
            },
          },
        },
        grid: {
          show: true,
        },
        plotOptions: {
          bar: {
            distributed: true,
          },
        },
        colors: this.generateUniqueColors(trainingDetail.length),
        markers: {
          size: 5,
          colors: ['blue', 'purple', 'pink', 'red', 'grey'],
          strokeColors: '#fff',
          strokeWidth: 2,
          hover: {
            size: 7,
          },
        },
        legend: {
          show: true,
          position: 'top',
          horizontalAlign: 'center',
          fontSize: '12px',
          labels: {
            colors: 'black',
            useSeriesColors: false,
          },
        },
      };
    }
  }

  getBarColors = (): string => {
    const color = `rgba(${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, 0.85)`;
    console.log(color);
    return color;
  };

  getColorByIndex(courseDetail) {
    for (let i = 0; i < courseDetail.length; i++) {
      return this.getColor(i);
    }
  }

  getColorByIndexTraining(trainingDetail) {
    for (let i = 0; i < trainingDetail.length; i++) {
      return this.getColor(i);
    }
  }

  generateUniqueColors(count: number): string[] {
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      // Generate random RGB color
      const color = `rgba(${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, 0.85)`;
      colors.push(color);
    }
    return colors;
  }

  // Helper method to generate a single random color
  getRandomColor(): string {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }

  getColor(index) {
    switch (index) {
      case 0:
        return '#0000FF';
      case 1:
        return '#800080';
      case 2:
        return '#FFC0CB';
      case 3:
        return '#FF0000';
      case 4:
        return '#808080';
      default:
        return '#000000';
    }
  }

  capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  async fetchDashboardData(filterParam?: FilterParam) {
    try {
      this.loadingService.show();
      const response = await this.programAdminDashboardService.getDashboardRecords(filterParam);
      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      // Ensure courseDetail data is available in the response
      const data = response.data && response.data[0] ? response.data[0] : {};
      this.courseDetail = data.courseDetail || [];
      this.trainingDetail = data.trainingDetail || [];
      if (response.data && response.data[0]?.courseDetail) {
        this.courseDetail = response.data[0].courseDetail; // Bind courseDetail
        await this.loadChart(true);
      }
      if (response.data && response.data.length > 0) {
        this.pendingCourseCount = response.data[0].pendingCourseRequests;
        this.totalExternalUserCount = response.data[0].totalExternalCourseInvitation;
      }
      if (response.data && response.data.length > 0) {
        this.unacceptedCourseCount = response.data[0].unacceptedCourseInvitation;
        this.totalRequestCount = response.data[0].totalCourseRequests;
      }
      if (response.data && response.data[0]?.trainingDetail) {
        this.trainingDetail = response.data[0].trainingDetail; // Bind courseDetail
        await this.loadTrainingChart(true);
      } else {
        this.courseDetail = []; // Handle case with no courseDetail data
        this.trainingDetail = []; // Handle case with no courseDetail data
        this.loadChart(true);
        this.loadTrainingChart(true);
      }
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async filterCourseData(filterParam?: FilterParam) {
    try {
      this.loadingService.show();
      const response = await this.programAdminDashboardService.filterCourseData(filterParam);
      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      const formatInterval = {
        DAILY: (start: string) => moment(start).format('Do MMM YYYY'),
        WEEKLY: (start: string, end: string) => `${moment(start).format('DD MMM')} - ${moment(end).format('DD MMM')}`,
        MONTHLY: (start: string) => moment(start).format('MMMM YYYY'),
        QUARTERLY: (start: string, end: string) => `${moment(start).format('MMM YYYY')} - ${moment(end).format('MMM YYYY')}`,
        ANNUALLY: (start: string) => moment(start).format('YYYY'),
      };

      response.data.forEach(element => {
        element.chartData.forEach(data => {
          data.name = formatInterval[this.filterParam.intervalType]?.(data.startDate, data.endDate) || 'Unknown Interval';
        });
      });

      this.courseFilterData = response.data;

      this.loadChart(false);

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async filterTrainingData(filterTrainingParam?: FilterParam) {
    try {
      this.loadingService.show();
      const response = await this.programAdminDashboardService.filterTrainingData(filterTrainingParam);
      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }

      const formatInterval = {
        DAILY: (start: string) => moment(start).format('Do MMM YYYY'),
        WEEKLY: (start: string, end: string) => `${moment(start).format('DD MMM')} - ${moment(end).format('DD MMM')}`,
        MONTHLY: (start: string) => moment(start).format('MMMM YYYY'),
        QUARTERLY: (start: string, end: string) => `${moment(start).format('MMM YYYY')} - ${moment(end).add(1, 'days').format('MMM YYYY')}`,
        ANNUALLY: (start: string) => moment(start).format('YYYY'),
      };

      response.data.forEach(element => {
        element.chartData.forEach(data => {
          data.name = formatInterval[this.filterTrainingParam.intervalType]?.(data.startDate, data.endDate) || 'Unknown Interval';
        });
      });

      this.trainingFilterData = response.data;
      this.loadTrainingChart(false);

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  //filter course
  onClickCourseDetailFilter(valid) {
    if (!this.filterParam.courseIds) {
      this.filterParam.courseIds = this.courseDetail.map(course => course.id);
    }
    if (this.filterParam.intervalType === 'CUSTOM') {
      this.fetchDashboardData(this.filterParam);
      this.courseUserDetailFilter.hide();

      return;
    }
    else {
      this.filterCourseData(this.filterParam);
      this.courseUserDetailFilter.hide();
    }
  }

  // filter training
  onClickTrainingDetailFilter(valid) {
    if (!this.filterTrainingParam.trainingIds) {
      this.filterTrainingParam.trainingIds = this.trainingDetail.map(training => training.id);
    }
    if (this.filterTrainingParam.intervalType === 'CUSTOM') {
      this.fetchDashboardData(this.filterTrainingParam);
      this.trainingUserDetailFilter.hide();
      return;
    }
    else {
      this.filterTrainingData(this.filterTrainingParam);
      this.trainingUserDetailFilter.hide();
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('courseUserDetailFilter');
      if (modalElement) {
        this.courseUserDetailFilter = new bootstrap.Modal(modalElement);
      }
    }, 0);
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('trainingUserDetailFilter');
      if (modalElement) {
        this.trainingUserDetailFilter = new bootstrap.Modal(modalElement);
      }
    }, 0);
  }

  openCourseDetailFilter() {
    AOS.init({ disable: true });
    this.courseUserDetailFilter.show();
    //this.courseDetail = new Array();
    //this.fetchDashboardData(this.filterParam);
  }

  openTainingDetailFilter() {
    AOS.init({ disable: true });
    this.trainingUserDetailFilter.show();
  }

  resetFilter() {
    delete this.filterParam.courseIds;
    delete this.filterParam.intervalType;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    this.courseUserDetailFilter.hide();
    this.loadChart(true);
    this.onCancel();
  }

  resetTrainingFilter() {
    delete this.filterTrainingParam.trainingIds;
    delete this.filterTrainingParam.intervalType;
    delete this.filterTrainingParam.startDate;
    delete this.filterTrainingParam.endDate;
    this.trainingUserDetailFilter.hide();
    this.loadTrainingChart(true);
    this.onCancel();
  }

  fromDateOutput(event: any) {
    console.log(event);
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    console.log(event);
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  //training filter 
  fromDateOutputTraining(event: any) {
    if (event) {
      this.filterTrainingParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterTrainingParam.startDate
    }
  }

  toDateOutputTraining(event: any) {
    if (event) {
      this.filterTrainingParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterTrainingParam.endDate
    }
  }

  onOptionTrainingChange(selectedOption: string) {
    const currentDate = moment();

    // Reset custom dates if not selected
    if (selectedOption !== 'CUSTOM') {
      delete this.filterTrainingParam.startDate;
      delete this.filterTrainingParam.endDate;
    }

    switch (selectedOption) {
      case 'DAILY':
        this.filterTrainingParam.startDate = currentDate.clone().subtract(10, 'days').format('YYYY-MM-DD');
        this.filterTrainingParam.endDate = currentDate.format('YYYY-MM-DD');
        break;

      case 'WEEKLY':
        this.filterTrainingParam.startDate = currentDate.clone().subtract(1, 'month').format('YYYY-MM-DD');
        this.filterTrainingParam.endDate = currentDate.format('YYYY-MM-DD');
        break;

      case 'MONTHLY':
        this.filterTrainingParam.startDate = currentDate.clone().subtract(6, 'months').format('YYYY-MM-DD');
        this.filterTrainingParam.endDate = currentDate.format('YYYY-MM-DD');
        break;

      case 'QUARTERLY': {
        const currentYear = currentDate.year();
        const currentMonth = currentDate.month() + 1;
        const quarterStartMonths = [1, 5, 9, 13];
        const currentQuarterIndex = Math.floor((currentMonth - 1) / 4);

        const quarterStart = moment(`${currentYear - 1}-${quarterStartMonths[currentQuarterIndex]}-01`, 'YYYY-MM-DD');
        const quarterEnd = moment(`${currentYear}-${quarterStartMonths[currentQuarterIndex]}-01`, 'YYYY-MM-DD').add(4, 'months').subtract(1, 'day');

        this.filterTrainingParam.startDate = quarterStart.format('YYYY-MM-DD');
        this.filterTrainingParam.endDate = quarterEnd.format('YYYY-MM-DD');
        break;
      }

      case 'ANNUALLY':
        this.filterTrainingParam.startDate = currentDate.clone().subtract(1, 'year').format('YYYY-MM-DD');
        this.filterTrainingParam.endDate = currentDate.format('YYYY-MM-DD');
        break;

      default:
        this.filterTrainingParam.startDate = '';
        this.filterTrainingParam.endDate = '';
        break;
    }
  }

  onOptionChange(selectedOption: string) {
    const currentDate = moment();
    switch (selectedOption) {
      case 'DAILY':
        this.filterParam.startDate = currentDate.clone().subtract(10, 'days').format('YYYY-MM-DD');
        this.filterParam.endDate = currentDate.format('YYYY-MM-DD');
        break;
      case 'WEEKLY':
        this.filterParam.startDate = currentDate.clone().subtract(2, 'month').format('YYYY-MM-DD');
        this.filterParam.endDate = currentDate.format('YYYY-MM-DD');
        break;
      case 'MONTHLY':
        this.filterParam.startDate = currentDate.clone().subtract(6, 'months').format('YYYY-MM-DD');
        this.filterParam.endDate = currentDate.format('YYYY-MM-DD');
        break;
      // case 'quarterly':
      //   this.filterParam.startDate = currentDate.clone().subtract(3, 'months').format('YYYY-MM-DD');
      //   this.filterParam.endDate = currentDate.format('YYYY-MM-DD');
      //   break;
      case 'QUARTERLY': {
        // Calculate the current quarter start and end based on today
        const currentYear = currentDate.year();
        const currentMonth = currentDate.month() + 1; // Months are zero-based

        // Determine the quarter based on the current month
        const quarterStartMonths = [1, 5, 9, 13]; // Start months for Q1, Q2, Q3, Q4
        const currentQuarterIndex = Math.floor((currentMonth - 1) / 4); // Determine the current quarter (0-3)

        // Calculate start and end dates for the last year to current date
        const quarterStart = moment(`${currentYear - 1}-${quarterStartMonths[currentQuarterIndex]}-01`, 'YYYY-MM-DD'); // Start of last year's quarter
        const quarterEnd = moment(`${currentYear}-${quarterStartMonths[currentQuarterIndex]}-01`, 'YYYY-MM-DD').add(4, 'months').subtract(1, 'days'); // End of current quarter

        // Assign values to filterParam
        this.filterParam.startDate = quarterStart.format('YYYY-MM-DD');
        this.filterParam.endDate = quarterEnd.format('YYYY-MM-DD');
        break;
      };
      case 'ANNUALLY':
        this.filterParam.startDate = currentDate.clone().subtract(1, 'year').format('YYYY-MM-DD');
        this.filterParam.endDate = currentDate.format('YYYY-MM-DD');
        break;
      case 'CUSTOM':
        // Reset dates for manual selection
        this.filterParam.startDate = '';
        this.filterParam.endDate = '';
        break;
      default:
        this.filterParam.startDate = '';
        this.filterParam.endDate = '';
        break;
    }
  }

}