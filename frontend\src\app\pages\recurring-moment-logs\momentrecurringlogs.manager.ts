import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MomentRecurringLogsService } from './momentrecurringlogs.service';

@Injectable({
    providedIn: 'root'
})
export class MomentRecurringLogsManager extends BaseManager {

    constructor(protected momentRecurringLogsService: MomentRecurringLogsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(momentRecurringLogsService, loadingService, toastService);
    }
}
