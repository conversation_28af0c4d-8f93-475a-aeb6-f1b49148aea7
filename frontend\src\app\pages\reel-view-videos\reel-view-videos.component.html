<div class="video-reel-cls">
  <swiper [height]="browserHeight" [noSwiping]="true" (init)="init($event)" (slideChange)="onChangeSlide($event)"
    (reachBeginning)="reachBeginning()" (reachEnd)="reachEnd()" [allowSlideNext]="allowNextSlide"
    [initialSlide]="currentIntialSlide" [allowSlidePrev]="allowSlidePrev" direction="vertical" #swiperVirtualRef
    [slidesPerView]="1" class="mySwiper" id="mySwiperID">
    <ng-template swiperSlide *ngFor="let training of trainingData; index as i">
      <!-- <div *ngIf="!training.videoLoaded" class="d-flex align-items-center justify-content-center h-100">
        <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
        <div class="spinner-border text-light" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      [ngStyle]="{'display': training.videoLoaded ? 'block' : 'none' }" -->
      <div class="video-wrapper">
        <div class="video-container" id="video-container"
          (click)="!currentVideoWaiting ? playVideoFromPlayIcon(training.id): null">
          <div *ngIf="currentVideoWaiting == training.id" class="d-flex align-items-center justify-content-center h-100">
            <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
            <div class="spinner-border text-light" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
          <div class="play-button-wrapper">
            <div title="Play video" class="play-gif circle-play-b-cls" [id]="'circle-play-b'+training.id">
              <!-- SVG Play Button -->
              <svg *ngIf="currentPlayVideo == training.id" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
              </svg>
            </div>
          </div>
          <div class="back-button-reel-video position-absolute" (click)="backToPreviousPage(training)">
            <img src="/assets/images/icons/menu/arrow-left-solid-white.svg" />
          </div>
          <div class="video-inner-content-container">
            <div class="video-title-reel">
              <p class="text-white">{{training.trainingIdDetail.videoTitle}}</p>
            </div>
            <div class="video-credit-upload-icon">
              <div class="video-credit-container d-flex align-items-center">
                <img src="/assets/images/icons/menu/video-credit-icon.svg" alt="">
                <p class="mx-1 m-0 text-white">{{training.trainingIdDetail.videoCredit}}</p>
              </div>
              <div class="upload-video-container">
                <label class="fw-bold" [for]="'file-input'+training.id"
                  [ngStyle]="{'padding-top': training.status != checkTypeOfVideos(VideosType) ? '7px' : '' }">
                  <img *ngIf="training.status == checkTypeOfVideos(VideosType) && !trainingIds.includes(training.id)"
                    src="/assets/images/icons/menu/upload-icon-white.svg" class="img-fluid" alt="" />
                  <i *ngIf="training.status != checkTypeOfVideos(VideosType)" class="bi bi-check-circle px-2 text-white"></i>
                  <div *ngIf="trainingIds.includes(training.id)"
                    class="d-flex align-items-center justify-content-center h-100">
                    <div class="spinner-border text-light" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </div>
                </label>
                <p class="text-white">{{trainingIds.includes(training.id) ? 'UPLOADING' :
                  !trainingIds.includes(training.id) && training.status == checkTypeOfVideos(VideosType) ? VideosType == 'moment' ? 'Upload Video/Image' : VideosType === 'assigned-training' && 'Upload Video' : VideosType == 'moment' ? 'File Uploaded' : VideosType == 'assigned-training' && 'Video Uploaded'  }}</p>
                <input *ngIf="training.status == checkTypeOfVideos(VideosType) && !trainingIds.includes(training.id)"
                  (change)="uploadVideo(training.id,$event)" [name]="training.id+'Video'" [id]="'file-input'+training.id"
                  type="file" [accept]="acceptMediaType" />
              </div>
            </div>
          </div>
          <video autoplay playsinline  [ngStyle]="{'display': !currentVideoWaiting ? 'block' : 'none'}"
            *ngIf="currentActiveVideo == training.id" [id]="'videoId'+training.id" nodownload oncontextmenu="return false"
            >
            <source [src]="training.trainingIdDetail.videoUrl">
          </video>
        </div>
      </div>
    </ng-template>
  </swiper>
</div>

