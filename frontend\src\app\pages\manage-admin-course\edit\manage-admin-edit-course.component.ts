import { Compo<PERSON>, <PERSON>ement<PERSON>ef, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { Constant } from 'src/app/config/constants';
import { Content } from 'src/app/models/content';
import { Course } from 'src/app/models/course';
import { CoursePart } from 'src/app/models/coursepart';
import { Training } from 'src/app/models/training';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { TrainingManager } from '../../training/training.manager';
import { TrainingService } from '../../training/training.service';
import { ManageContentManager } from '../../manage-content-type/manage-content.manager';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';
import { IImage, ImageCompressService, ResizeOptions } from 'ng2-image-compress';
import { AuthService } from 'src/app/shared/auth.services';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { environment } from 'src/environments/environment';
import { RestResponse } from 'src/app/shared/auth.model';
import { NgForm } from '@angular/forms';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ManageCoursePartsManager } from '../../manage-course/manage-courseparts-manager';
import { ManageCourseTrainingService } from '../../manage-course/manage-course-training-service';
import { ManageCoursePartsService } from '../../manage-course/manage-courseparts-service';
import { ManageCourseTrainingManager } from '../../manage-course/manage-course-training-manager';
import { ManageAdminCourseService } from '../manage-admin-course-service';
import { ManageAdminCourseManager } from '../manage-admin-course-manager';

@Component({
  selector: 'app-manage-admin-edit-course',
  templateUrl: './manage-admin-edit-course.component.html',
  styleUrls: ['./manage-admin-edit-course.component.scss']
})
export class ManageAdminEditCourseComponent extends BaseEditComponent implements OnInit, OnDestroy {
  @Output() publishClicked: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('coursePartAccordion') coursePartAccordion: ElementRef;
  @ViewChild('containerRef', { static: false }) containerRef!: ElementRef;

  selectedCourseId: any;
  public course: Course;
  public coursepart: CoursePart;
  public contentTypes: Content[];
  public training: Training;
  selectedCurrentType: string;
  selectPrerequisitesTrainingType: string;
  public loading: boolean = false;
  public videoPlaying: boolean = false;
  readonly MY_CONSTANT = Constant;
  private fileData: any;
  uploadingThumbnail: boolean;
  uploader: any;
  users: any[] = [];
  usersSelected: any[] = [];
  fileUploadingMessage: string = "UPLOADING..";
  intervalId: NodeJS.Timeout;
  dropdownSettings = {};
  deletedUsersSelected: any[] = [];
  thumbnailUploader: FileUploader;
  fileUploadType: string;
  isCollapsedClosed: boolean = false;
  formSubmitted = false;
  courseAccordionId: string;
  trainings: Training[];
  activeIndex: number;
  activeTraningIndex: string;
  actievePartIndex: number;
  activeTraningUploderIndex: number;
  coursePartEditMode: boolean = true;
  courseTraining = {
    accessibility: false  // Default to Private
  };

  activeCoursePartIndex: number | string = '';
  showCourserPart: boolean = true
  isEditing: number | null = null;
  initialPositions: any = {};
  trainingLibrary: any[] = [];
  showTrainingLibraryDropdown = false;
  selectedLibraryOption: any = null;
  preRequisitesTraining: any[] = [];
  filteredPreRequisitesTraining: any[] = [];
  searchTerm: string = '';
  filteredTraininglLibrary: any[] = [];
  showDropdown: boolean = false;
  public Editor = ClassicEditor;
  public editorConfig = {
    toolbar: [
      'heading', '|',
      'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'highlight', '|',
      'link', 'imageUpload', 'blockQuote', 'codeBlock', '|',
      'bulletedList', 'numberedList', 'outdent', 'indent', '|',
      'alignment', 'horizontalLine', 'insertTable', 'mediaEmbed', '|',
      'fontSize', 'fontColor', 'fontBackgroundColor', '|',
      'undo', 'redo', 'removeFormat', 'sourceEditing'
    ],
    height: 'auto',
    shouldNotGroupWhenFull: true
  };

  isSelectLeverageTraining: boolean;

  constructor(protected route: ActivatedRoute, private manageAdminCourseService: ManageAdminCourseService, protected maanageAdminCourseManager: ManageAdminCourseManager, protected trainingManager: TrainingManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private manageContentManager: ManageContentManager, private trainingService: TrainingService,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService,
    private manageCoursePartsService: ManageCoursePartsService, private manageCoursePartsManager: ManageCoursePartsManager, private eRef: ElementRef, private imgCompressService: ImageCompressService,
    private manageCourseTrainingService: ManageCourseTrainingService, private manageCourseTrainingManager: ManageCourseTrainingManager, public commonUtil: CommonUtil) {
    super(maanageAdminCourseManager, commonService, toastService, loadingService, route, router, translateService,);
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  publish(data: any) {
    this.publishClicked.emit(data);
  }

  ngOnInit() {
    this.init();
    this.course = new Course();
    this.trainings = new Array<Training>();
    this.course.isActive = true;
    this.coursepart = new CoursePart();
    this.course.isActive = true;
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.fileUploadType = "";
    this.setDropdownSettings();
    this.training = new Training();
    this.training.isActive = true;
    this.setRecord(this.training);
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.contentTypes = new Array<Content>();
    this.init();
    this.uploader = this.initializeUploaderTraining(null, 'mp4,mkv', null, null, this.toastService, "Only Mkv, Mp4 files are allowed", null);
    this.thumbnailUploader = this.initializeUploaderTraining(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.isCollapsedClosed = !(this.request.recordId === 0 || !this.course.code);
    this.fetchTrainingLibrary();

  }

  toggleCollapse() {
    this.isCollapsedClosed = !this.isCollapsedClosed;
  }

  selectAccessibility(partIndex: number, trainingIndex: number, event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const isChecked = inputElement.checked;
    // Update the model based on the checkbox state
    this.course.courseParts[partIndex].trainings[trainingIndex].accessibility = isChecked ? 'Private' : 'Public';
  }

  saveAsDraft(form) {
    this.navigate('/dashboard/program-admin/courses');
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      allowSearchFilter: true
    };
  }

  selectContentType(event: any, partIndex, trainingIndex) {
    this.selectedCurrentType = event.title;
    this.course.courseParts[partIndex].trainings[trainingIndex].uploadRequired = event.title === 'Apply';
    this.course.courseParts[partIndex].trainings[trainingIndex].watchRequired =
      !this.course.courseParts[partIndex].trainings[trainingIndex].uploadRequired;
  }

  removeFileCustom(parentIndex, childIndex, type) {

    this.commonService.confirmation('Would you like to delete?', this.removeFileCallbackCustom.bind(this), parentIndex, childIndex, type);
  }

  removeFileCallbackCustom(parentIndex, childIndex, type) {
    if (type == "thumbnailImageUrl") {
      this.course.courseParts[parentIndex].trainings[childIndex].thumbnailImageUrl = "";
    }
    else if (type == "videoUrl") {
      this.course.courseParts[parentIndex].trainings[childIndex].url = "";
    }
  }

  selectPrerequisitesTraining(event: any) {
    this.selectPrerequisitesTrainingType = event.title;
  }

  async fetchAssociatedData() {
    this.contentTypes = await this.manageContentManager.fetchAllData(this.filterParam);
  }

  uploadThumbnail(event: any, training: any, courseIndex: number, traningIndex: number) {
    this.fileUploadType = 'thumbnailImage';
    this.activeTraningUploderIndex = traningIndex;
    this.actievePartIndex = courseIndex;
    this.uploadingThumbnail = true;
    this.fileData = {} as any;
    this.fileData.files = event.target.files;
    this.compressThumbnail(training);
  }

  compressThumbnail(training: any) {
    let images: Array<IImage> = [];
    let option: ResizeOptions = { Resize_Quality: 90, Resize_Max_Width: 500, Resize_Max_Height: 400, Resize_Type: 'jpeg' };
    ImageCompressService.filesToCompressedImageSource(this.fileData.files).then(observableImages => {
      observableImages.subscribe((image) => {
        images.push(image);
      }, (error) => {
      }, () => {
        let compresImages = new Array<any>();
        let obj: any = {};
        obj.base64 = images[0].compressedImage.imageDataUrl;
        obj.type = images[0].type;
        compresImages.push(this.base64ToFile(obj));
        this.onThumbnailFileProcessingCompleted(compresImages);
      });
    });
  }

  base64ToFile(obj: any) {
    const byteCharacters = atob(obj.base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, ''));
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: obj.type });
    var extention = obj.type.split('/');
    let file = new File([blob], 'cropImage.' + extention[1], { type: obj.type });
    return file;
  }

  onThumbnailFileProcessingCompleted(files) {
    this.thumbnailUploader.addToQueue(files);
    this.thumbnailUploader.uploadAll();
  }

  async uploadVideo(event: any, training: any, courseIndex: number, traningIndex: number) {

    this.fileUploadType = 'thumbnailVideo';
    this.activeTraningUploderIndex = traningIndex;
    this.actievePartIndex = courseIndex;

    const video: any = await this.commonUtil.loadVideo(event.target.files[0]);
    training.duration = video.duration;
    if (training.mediaType === 'VIDEO') {
      this.loadingService.show();
    }
    this.loading = true;

    const file = event.target.files[0];
    if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg" && event.target.files[0].type != "image/png") {
      this.commonService.convertVideoFormat(file).then(res => {
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    } else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  async onUploadSuccessTraining(file: any, training: any) {
    this.loadingService.hide();
    const filePath = file.streamingPath ? file.streamingPath : file.path;

    switch (this.fileUploadType) {
      case 'thumbnailImage':
        this.course.courseParts[this.actievePartIndex].trainings[this.activeTraningUploderIndex].thumbnailImageUrl = filePath;
        this.uploadingThumbnail = false;
        break;

      case 'thumbnailVideo':
        let gumletResponse = null;

        if (file.streamingId) {
          this.filterParam.gumletId = file.streamingId;
          gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

          this.intervalId = setInterval(async () => {
            gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

            if (gumletResponse && gumletResponse.status) {
              this.fileUploadingMessage = gumletResponse.data.status ? gumletResponse.data.status.toUpperCase() + ".." : "UPLOADING..";

              if (gumletResponse.data.status == "ready" || gumletResponse.data.status == "queued" || gumletResponse.data.status == "downloading" || gumletResponse.data.status == "downloaded" || gumletResponse.data.status == "validating" || gumletResponse.data.status === "errored") {
                if (gumletResponse.data.status == "errored") {
                  this.toastService.error('Video is errored while processing');
                  this.loading = false;
                  clearInterval(this.intervalId);
                  return;
                } else {
                  if (this.intervalId) {
                    clearInterval(this.intervalId);
                  }
                  this.course.courseParts[this.actievePartIndex].trainings[this.activeTraningUploderIndex].url = filePath;

                  this.fileUploadingMessage = "UPLOADING..";
                  this.loading = false;
                }
              }
            } else {
              if (this.intervalId) {
                clearInterval(this.intervalId);
              }
              this.course.courseParts[this.actievePartIndex].trainings[this.activeTraningUploderIndex].url = filePath;
              this.fileUploadingMessage = "UPLOADING..";
              this.loading = false;
            }
          }, 5000);
        } else {
          this.course.courseParts[this.actievePartIndex].trainings[this.activeTraningUploderIndex].url = filePath;
          this.loading = false;
        }
        break;
    }
  }

  fileValidationErrorTraining(data: string, toastService: any) {
    this.loading = false;
    toastService.error(data);
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessTraining(file, files);
        }, 200);
      }
    };

    uploader.onErrorItem = (fileItem, response) => {
      this.loading = false;
      toastService.error('Something error occurred please try again later');
    }

    return uploader;
  }

  playVideoFromPlayIcon() {
    var videoId = document.getElementById("training_video") as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true;
        this.videoPlaying = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      this.videoPlaying = true
      videoId.controls = true;
    });
    videoId.addEventListener("ended", (event) => {
      this.videoPlaying = false
      videoId.controls = false;

    });
  }

  async saveParts(form: any, coursePart: CoursePart, index: number) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    try {
      this.loadingService.show();
      coursePart.course = this.course.id;
      const method = !coursePart.id ? 'save' : 'update';

      const response: RestResponse = await this.manageCoursePartsManager[method](coursePart);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if (method === 'save') {
        this.course.courseParts[index].id = response.data;
        this.course.courseParts[index].showCoursePart = true;
      }
      this.activeIndex = -1;
      this.isEditing = null;
      this.onNewRecord();

      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async saveCourseTrainingForm(form: any, training: Training, childIndex: number, parentIndex: number) {
    const isPopulated = this.course.courseParts[parentIndex].trainings[childIndex].isPopulated;
    if (isPopulated) {
      training.isActive = true;
      training.isDeleted = false;
      // delete training.isPopulated;
    }
    else {
      this.onClickValidation = !form.valid;
      if (!form.valid) {
        return;
      }
    }
    try {
      this.loadingService.show();

      training.coursePart = this.course.courseParts[parentIndex].id;
      training.course = this.course.id;
      const method = !training.id ? 'save' : 'update';

      let trainingTemp = JSON.parse(JSON.stringify(training));
      trainingTemp.trainingBind = null;
      const response: RestResponse = await this.manageCourseTrainingManager[method](trainingTemp);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.course.courseParts[parentIndex].trainings[childIndex].id = response.data ? response.data : training.id;
      this.activeTraningIndex = "";
      this.getPrerequisitesTraining();
      training.isProgramAdminTraining = false
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async addUpdateCourse(form: any) {
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord && !this.course.id ? 'save' : 'update';
      if (this.course.courseParts && this.course.courseParts.length > 0) {
        const lastIndex = this.course.courseParts.length - 1;
        if (!this.course.courseParts[lastIndex].id) {
          // Remove the last item if it does not have an id
          this.course.courseParts.pop();
        }
      }
      const response: RestResponse = await this.manager[method](this.course);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.isCollapsedClosed = true;
      if (!this.course.id || this.course.id == '0') {
        let course = new CoursePart();
        this.course.courseParts.push(course);
        this.activeIndex = 0;
      }
      this.addCoursePartIfNeeded(this.course.courseParts);
      this.course.id = response.data;
      this.selectedCourseId = this.course.id;
      this.addCoursePartSection();
      this.fetchPrerequisitesTrainingData();
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchPrerequisitesTrainingData() {
    this.loadingService.show();
    const resp: RestResponse = await this.trainingService.fetchPrerequisitesTraining(this.course.id).toPromise();
    this.loadingService.hide();
    if (!resp.status) {
      return;
    }
    this.trainings = resp.data;
  }

  async goToNextPanel(form: NgForm) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.isCollapsedClosed = false;
    // Handle form submission logic here

    this.addUpdateCourse(form);
    // Update accordion header after form submission

  }

  addCoursePartSection() {
    const headerSpan = document.querySelector('#headingOne .ms-2');
    if (headerSpan) {
      headerSpan.innerHTML = `
          ${this.course.code}
          <span class="mx-2">
            <img src="/assets/images/icons/menu/line.svg">
          </span>
          ${this.course.title}
        `;
    }
  }

  onNewRecord() {
    //check if there are any unsaved Courseparts
    let anyNotSavedcourseParts = this.course.courseParts.filter((cp) => {
      return !cp.id;
    });
    //check if no unsaved Courseparts then add new Coursepart
    if (anyNotSavedcourseParts.length == 0) {
      this.course.courseParts.push(new CoursePart());
    }
  }

  onAddNewTraining(index, event: MouseEvent) {
    const elementId = `collapseCoursePart${index}`;

    if (!this.course.courseParts[index].trainings) {
      this.course.courseParts[index].trainings = [];
    }

    let anyNotSavedTrainings = this.course.courseParts[index].trainings.filter((t) => {
      return !t.id;
    });
    if (anyNotSavedTrainings.length == 0) {
      this.course.courseParts[index].trainings.push(new Training());
      this.getPrerequisitesTraining();
    }
    this.activeTraningIndex = index + "_" + (this.course.courseParts[index].trainings.length - 1);
    setTimeout(() => this.scrollToAccordion(index), 100);
    this.selectedLibraryOption = null
  }

  onNewCoursepartTraininge(index) {
    this.course.courseParts[index].trainings.push(new Training());
  }

  editRecord(id: any) {
    this.filteredPreRequisitesTraining = this.preRequisitesTraining.filter((t) => { return t.id !== id });
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
  }

  onFetchCompleted() {
    // this.onClickValidation=true
    this.course = Course.fromResponse(this.record);
    if (!this.course.courseParts || this.course.courseParts.length == 0) {
      let course = new CoursePart();
      this.course.courseParts.push(course);
    }
    this.initializePositions();
    this.onNewRecord()
    this.getPrerequisitesTraining()
  }

  async onFileProcessingCompleted(files: any) {
    this.uploader.addToQueue(files);

    this.uploader.uploadAll();
  }

  getCourseTrainings(trainings: Training[], notIncludedIndex: number, training: Training) {
    let filterTrainings = trainings.filter((t, index) => {
      return index != notIncludedIndex && t.id;
    });
    training.trainingBind = filterTrainings;
  }

  // Course part delete process
  removePart(id, i) {
    this.removeCoursePart(id, i);
  }
  async removeCoursePartCallback(id: string, parentIndex: number) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCoursePartsManager.remove(id);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.course.courseParts.splice(parentIndex, 1);
      if (!this.course.courseParts || this.course.courseParts.length == 0) {
        let course = new CoursePart();
        this.course.courseParts.push(course);
      }
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }
  removeCoursePart(id: string, parentIndex: number) {
    this.commonService.confirmation('Would you like to delete Course Part?', this.removeCoursePartCallback.bind(this), id, parentIndex);
  }

  // Course part training delete process
  removeIndex(id, i, j) {
    this.removeTraining(id, i, j);
  }
  async removeTrainingCallback(id: string, parentIndex: number, childIndex: number) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseTrainingManager.remove(id);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.course.courseParts[parentIndex].trainings.splice(childIndex, 1);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }
  removeTraining(id: string, parentIndex: number, childIndex: number) {
    this.commonService.confirmation('Would you like to delete Course Part Training?', this.removeTrainingCallback.bind(this), id, parentIndex, childIndex);
  }

  toggleCoursePart(index: number) {
    this.activeIndex = this.activeIndex === index ? -1 : index;
  }

  toggleTraining(partIndex: number, trainingIndex: number) {
    const index = partIndex + '_' + trainingIndex;
    this.activeTraningIndex = this.activeTraningIndex === index ? '' : index;
  }

  // toggle status update publish
  async toPublishAll() {
    try {
      var course = new Course();
      course.id = this.selectedCourseId;
      course.isPublish = true;
      course.description = this.course.description;

      this.loadingService.show();
      const response: RestResponse = await this.maanageAdminCourseManager.updatePublish(course);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      // this.onCancel();
      this.toastService.success(response.message);
      this.navigate('/dashboard/program-admin/courses');
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  editCoursePartRecord(st: string) {
    this.coursePartEditMode = true;
  }

  scrollToAccordion(index: number): void {
    const lastIndex = this.course.courseParts[index].trainings.length - 1;
    const elementId = `collapseCoursePartTraining${index + '_' + lastIndex}`;
    const element = document.getElementById(elementId);

    if (element) {
      // First, scroll the element into view
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Adjust scroll position after a short delay to ensure smooth scrolling
      setTimeout(() => {
        const elementRect = element.getBoundingClientRect();
        const absoluteElementTop = elementRect.top + window.pageYOffset;
        const middle = absoluteElementTop - (window.innerHeight / 2) + (elementRect.height / 2);
        window.scrollTo({ top: middle, behavior: 'smooth' });
      }, 500);
    }
  }

  showCourseMain(currentIndex: number) {
    if (currentIndex === this.course.courseParts.length - 1) {
      this.startEditing(currentIndex, 'title');
    }
    const elementId = `collapseCoursePart${currentIndex}`;
    const element = document.getElementById(elementId);
    const canToggle = this.course.courseParts[currentIndex].showCourseMain && this.course.courseParts[currentIndex].showTrainingPart;
    const canReInitialise = !this.course.courseParts[currentIndex].showCourseMain && !this.course.courseParts[currentIndex].showTrainingPart && !this.course.courseParts[currentIndex].showCoursePart;
    ;
    if (element && element.classList.contains('show') && canToggle) {
      this.course.courseParts[currentIndex].showCourseMain = false;
      this.course.courseParts[currentIndex].showTrainingPart = false;
      return;
    } else if (this.course.courseParts[currentIndex].showCoursePart && !this.course.courseParts[currentIndex].showTrainingPart) {
      this.course.courseParts[currentIndex].showTrainingPart = false
      this.course.courseParts[currentIndex].showCourseMain = false;
      this.course.courseParts[currentIndex].showCoursePart = false;
    }
    else if (element && element.classList.contains('show') && canReInitialise) {
      return;
    }
    else {
      // Toggle the showCourseMain property for the current index
      this.course.courseParts[currentIndex].showCourseMain = !this.course.courseParts[currentIndex].showCourseMain;
      this.course.courseParts[currentIndex].showTrainingPart = !this.course.courseParts[currentIndex].showTrainingPart;
      this.course.courseParts[currentIndex].showCoursePart = false;

      // If the showCourseMain property at the current index is true, set all others to false
      if (this.course.courseParts[currentIndex].showCourseMain) {
        for (let i = 0; i < this.course.courseParts.length; i++) {
          if (i !== currentIndex) {
            this.course.courseParts[i].showCourseMain = false;
            this.course.courseParts[i].showTrainingPart = false;
          }
        }
      }
    }
    // get prerequisites training List common for a course part
    // this.getPrerequisitesTraining()
  }

  showCoursePart(currentIndex: number, event: MouseEvent) {
    this.activeIndex = currentIndex;
    // Toggle the showCoursePart property for the current index
    this.course.courseParts[currentIndex].showCoursePart = !this.course.courseParts[currentIndex].showCoursePart;
    this.course.courseParts[currentIndex].showTrainingPart = false;
    this.course.courseParts[currentIndex].showCourseMain = false

    // If the showCourseMain property at the current index is true, set all others to false
    if (this.course.courseParts[currentIndex].showCoursePart) {
      for (let i = 0; i < this.course.courseParts.length; i++) {
        if (i !== currentIndex) {
          this.course.courseParts[i].showCoursePart = false;
        }
      }
    }
    if (this.course.courseParts[currentIndex].showCoursePart) {
      event.stopPropagation()
    }
  }

  showTrainingPart(currentIndex: number, event: MouseEvent) {
    this.activeIndex = currentIndex;
    this.onAddNewTraining(currentIndex, event)
    this.course.courseParts[currentIndex].showCourseMain = !this.course.courseParts[currentIndex].showCourseMain;
    this.course.courseParts[currentIndex].showTrainingPart = !this.course.courseParts[currentIndex].showTrainingPart;
    this.course.courseParts[currentIndex].showCoursePart = false;

    // If the showCourseMain property at the current index is true, set all others to false
    if (this.course.courseParts[currentIndex].showCourseMain) {
      for (let i = 0; i < this.course.courseParts.length; i++) {
        if (i !== currentIndex) {
          this.course.courseParts[i].showCourseMain = false;
          this.course.courseParts[i].showTrainingPart = false;
        }
      }
    }
    if (this.course.courseParts[currentIndex].showTrainingPart) {
      event.stopPropagation()
    }
  }

  toggleAccordion(index: number, event: Event) {
    event.stopPropagation();
    this.activeIndex = this.activeIndex == index ? -1 : index;
    this.showCourseMain(index);
  }

  addNewCoursePart() {
    this.activeIndex = this.course.courseParts.length - 1;
  }

  startEditing(index: number, source: string) {
    if (source === 'title' && this.isEditing) {
      this.cancelEditing();
      return
    }
    this.isEditing = index;
  }

  cancelEditing(index?: number) {
    this.isEditing = null;
    if (index === this.course.courseParts.length - 1) {
      this.course.courseParts[index] = new CoursePart();
    }
  }

  toggleActiveIndex(i: number, event: Event): void {
    this.activeIndex = this.activeIndex === i ? -1 : i;
    this.showCourseMain(i);
    event.stopPropagation();
  }

  // Handle the drop event
  drop(event: CdkDragDrop<any[]>, index: number) {
    if (parseInt(this.activeTraningIndex) !== -1 && !this.isNullOrUndefined(this.activeTraningIndex)) {
      this.toastService.error('Cannot reorder while in edit mode');
      return; // Exit if any training is in edit mode
    }

    const trainings = this.course.courseParts[index].trainings;
    const disabledIndex = this.getDisabledIndex(trainings);

    // Only allow drop if it's above the disabled index
    if (event.currentIndex < disabledIndex) {
      moveItemInArray(trainings, event.previousIndex, event.currentIndex);
      this.updateSequences(trainings);

      // Check if positions have changed
      if (this.havePositionsChanged(index)) {
        const payload = this.generatePayload(trainings);
        this.updateTrainingOrder(payload);
      } else {
        return;
      }
    }
  }

  // Determine the index of the first item without an ID
  getDisabledIndex(trainings: any[]): number {
    for (let i = 0; i < trainings.length; i++) {
      if (!trainings[i].id) {
        return i; // Return the index of the first item without an ID
      }
    }
    return trainings.length; // If all items have an ID, return the length to allow all items to be draggable
  }

  // Update sequence values
  updateSequences(trainings: Training[]) {
    trainings.forEach((training, index) => {
      training.sequence = (index + 1); // Update the sequence based on the index
    });
  }

  // Generate payload
  generatePayload(trainings: Training[]): any[] {
    return trainings.map(training => ({
      id: training.id,
      sequence: training.sequence
    }));
  }

  async updateTrainingOrder(orderData: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageAdminCourseService.changeOrder(orderData);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }

      this.onSaveSuccess(response.message);
      this.initializePositions()
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // Check if positions have changed
  havePositionsChanged(index: number): boolean {
    const currentPositions = this.course.courseParts[index]?.trainings?.map(training => training.id);
    return !this.initialPositions[index].every((id, pos) => id === currentPositions[pos]);
  }

  // Initialize initial positions
  initializePositions() {
    this.course.courseParts.forEach((coursePart, index) => {
      this.initialPositions[index] = coursePart?.trainings?.map(training => training.id);
    });
  }

  //  using for Roman numerals
  toRoman(num: number): string {
    const romanNumerals: { [key: number]: string } = {
      1: 'I', 2: 'II', 3: 'III', 4: 'IV', 5: 'V', 6: 'VI', 7: 'VII', 8: 'VIII', 9: 'IX',
      10: 'X', 11: 'XI', 12: 'XII', 13: 'XIII', 14: 'XIV', 15: 'XV', 16: 'XVI', 17: 'XVII',
      18: 'XVIII', 19: 'XIX', 20: 'XX', 40: 'XL', 50: 'L', 90: 'XC', 100: 'C', 400: 'CD',
      500: 'D', 900: 'CM', 1000: 'M'
    };
    const keys = Object.keys(romanNumerals).map(Number).reverse();
    let result = '';

    keys.forEach(key => {
      while (num >= key) {
        result += romanNumerals[key];
        num -= key;
      }
    });

    return result;
  }

  async fetchTrainingLibrary() {
    try {
      var course = new Course();
      course.id = this.selectedCourseId;
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseTrainingService.getTrainingLibrary(null);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainingLibrary = response.data;
      this.filteredTraininglLibrary = JSON.parse(JSON.stringify(this.trainingLibrary));
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // Method to toggle the visibility of the dropdown
  toggleDropdown() {
    this.showTrainingLibraryDropdown = !this.showTrainingLibraryDropdown;
  }

  async onLibraryOptionChange(event: any, partIndex: number, trainingIndex: number) {
    if (event && event.id) {
      let isExisting = false;
      // Loop through all trainings in the specified course part
      this.course.courseParts[partIndex].trainings.forEach(training => {
        if (training.trainingLibrary === event.id) {
          isExisting = true;
        }
      });

      // If the id already exists, return early
      if (isExisting) {
        this.toastService.error('Training already exists in the course part');
        return;
      }

      await this.fetchTraining(event.id, partIndex, trainingIndex);
    } else {
      return;
    }
  }

  async fetchTraining(id: string, partIndex: number, trainingIndex: number) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseTrainingService.getLibrary(id).toPromise();
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      const keysToDelete = [
        'contentTypeDetail',
        'learningSeries',
        'learningSeriesDetail',
        'isPrivate',
        'trainingBind',
        'isViewed',
        'totalCount',
        'trainingBind',
      ];

      // Delete the unwanted keys from response.data
      keysToDelete.forEach(key => delete response.data[key]);
      // const trainingIndex = this.course.courseParts[this.activeIndex].trainings.length - 1;
      this.course.courseParts[partIndex].trainings[trainingIndex] = new Training();
      this.course.courseParts[partIndex].trainings[trainingIndex] = response.data;

      this.course.courseParts[partIndex].trainings[trainingIndex].trainingLibrary = id
      this.course.courseParts[partIndex].trainings[trainingIndex].isPopulated = true
      this.course.courseParts[partIndex].trainings[trainingIndex].id = undefined
      this.course.courseParts[partIndex].trainings[trainingIndex].isCourseTraining = true;
      this.course.courseParts[partIndex].trainings[trainingIndex].isLock = false
      this.fetchISProgramAdminTrainingStatus(id);

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchISProgramAdminTrainingStatus(id: string) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseTrainingService.getProgramAdminLibraryStatus(id).toPromise();
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      // need to verify lines
      // const trainingIndex = this.course.courseParts[this.activeIndex].trainings.length - 1;
      // this.course.courseParts[this.activeIndex].trainings[trainingIndex].isProgramAdminTraining = true;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async getPrerequisitesTraining() {
    try {
      this.filterParam.course = this.course.id
      const response: RestResponse = await this.manageCourseTrainingService.fetchPrerequisitesTrainings(this.filterParam);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.preRequisitesTraining = response.data;
      this.filteredPreRequisitesTraining = response.data;
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  filterTrainingLibrary(event: any, partIndex: number, trainingIndex: number) {
    this.searchTerm = event.target.value;
    if (this.searchTerm === '') {
      this.filteredTraininglLibrary = this.trainingLibrary;
    } else {
      this.filteredTraininglLibrary = this.trainingLibrary.filter(training =>
        training.title.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
      this.isSelectLeverageTraining = false; // when input the text not select from dropdown
    }
    // Loop through the filtered library to check for a match
    let matchFound = false;

    this.filteredTraininglLibrary.forEach(training => {
      if (training.title.toLowerCase() === this.searchTerm.toLowerCase()) {
        matchFound = true;
      }
    });

    const isdeleteBackward = event.inputType === 'deleteContentBackward';
    if (!matchFound && !this.course.courseParts[partIndex].trainings[trainingIndex]?.trainingLibraryDetail?.id) {
      return //in case the training does not have trainingLibrary
    }

    if (!matchFound && isdeleteBackward) {
      this.course.courseParts[partIndex].trainings[trainingIndex] = new Training();
      this.filteredTraininglLibrary = JSON.parse(JSON.stringify(this.trainingLibrary));
    }

  }

  selectTrainingLibrary(training: any, partIndex: number, trainingIndex: any) {
    this.searchTerm = training.title;
    this.showDropdown = false;
    this.isSelectLeverageTraining = true;
    this.onLibraryOptionChange(training, partIndex, trainingIndex);
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    const clickedInside = this.containerRef.nativeElement.contains(event.target);

    if (!clickedInside) {
      this.showDropdown = false;
    }
  }

  addCoursePartIfNeeded(courseParts: CoursePart[]): void {
    if (!courseParts) {
      courseParts = [];
    }

    const coursePartsLength = courseParts.length;

    // Check if the array is empty or the last item does not have an id
    if (coursePartsLength === 0 || (courseParts[coursePartsLength - 1] && !courseParts[coursePartsLength - 1].id)) {
      // Only add a new CoursePart if there's no item without an id already in the array
      let lastPart = courseParts[coursePartsLength - 1];

      if (!lastPart || lastPart.id) {
        let course = new CoursePart();
        courseParts.push(course);
      }
    }
  }

}
