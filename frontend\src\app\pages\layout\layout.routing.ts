import { ActivatedRoute, Routes } from '@angular/router';
import { AuthGuard } from '../../shared/auth.guard';
import { AccountSettingsComponent } from '../account-settings/account-settings.component';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { LandingComponent } from '../landing/landing.component';
import { UsersEditComponent } from '../users/edit/users-edit.component';
import { UsersComponent } from '../users/users.component';
import { LayoutComponent } from './layout.component';

// import dashboard/staff-change-password all entity pages

import { CategoryDetailComponent } from './../category/detail/category-detail.component';
import { CategoryEditComponent } from './../category/edit/category-edit.component';
import { FarmDetailComponent } from './../farm/detail/farm-detail.component';
import { FarmEditComponent } from './../farm/edit/farm-edit.component';
import { FarmComponent } from './../farm/farm.component';
import { FarmAssignTrainingDetailComponent } from './../farmassigntraining/detail/farmassigntraining-detail.component';
import { FarmAssignTrainingEditComponent } from './../farmassigntraining/edit/farmassigntraining-edit.component';
import { FarmAssignTrainingComponent } from './../farmassigntraining/farmassigntraining.component';
import { LanguageDetailComponent } from './../language/detail/language-detail.component';
import { LanguageEditComponent } from './../language/edit/language-edit.component';
import { LanguageComponent } from './../language/language.component';
import { MomentDetailComponent } from './../moment/detail/moment-detail.component';
import { MomentEditComponent } from './../moment/edit/moment-edit.component';
import { MomentComponent } from './../moment/moment.component';
import { SubCategoryDetailComponent } from './../subcategory/detail/subcategory-detail.component';
import { SubCategoryEditComponent } from './../subcategory/edit/subcategory-edit.component';
import { SubCategoryComponent } from './../subcategory/subcategory.component';
import { TrainingDetailComponent } from './../training/detail/training-detail.component';
import { TrainingEditComponent } from './../training/edit/training-edit.component';
import { TrainingComponent } from './../training/training.component';
import { UserAssignTrainingDetailComponent } from './../userassigntraining/detail/userassigntraining-detail.component';
import { UserAssignTrainingEditComponent } from './../userassigntraining/edit/userassigntraining-edit.component';
import { UserAssignTrainingComponent } from './../userassigntraining/userassigntraining.component';
import { UserFarmDetailComponent } from './../userfarm/detail/userfarm-detail.component';
import { UserFarmEditComponent } from './../userfarm/edit/userfarm-edit.component';
import { UserFarmComponent } from './../userfarm/userfarm.component';
import { UserTrainingStatusDetailComponent } from './../usertrainingstatus/detail/usertrainingstatus-detail.component';
import { UserTrainingStatusEditComponent } from './../usertrainingstatus/edit/usertrainingstatus-edit.component';
import { UserTrainingStatusComponent } from './../usertrainingstatus/usertrainingstatus.component';
import { FarmAssignedUsersComponent } from '../users/farm-assigned-users/farm-assigned-users.component';
import { StaffAssignedTrainingComponent } from '../staff-assigned-training/staff-assigned-training.component';
import { TrainingFeedComponent } from '../training-feed/training-feed.component';
import { StaffmomentsComponent } from '../staffmoments/staffmoments.component';
import { UserDetailsComponent } from '../user-details/user-details.component';
import { StaffAddMomentComponent } from '../staff-add-moment/staff-add-moment.component';
import { StaffprofileComponent } from '../staffprofile/staffprofile.component';
import { StaffChangepasswordComponent } from '../staff-changepassword/staff-changepassword.component';
import { StaffAssignedSitesComponent } from '../staff-assigned-sites/staff-assigned-sites.component';
import { ViewStaffComponent } from '../view-staff/view-staff.component';
import { AdminSettingComponent } from '../admin-setting/admin-setting.component';
import { RequestForSiteChangeComponent } from '../request-for-site-change/request-for-site-change.component';
import { AssignedTrainingReelsVideoComponent } from '../staff-assigned-training/assigned-training-reels-video/assigned-training-reels-video.component';
import { UsersContactListComponent } from '../users-contact-list/users-contact-list.component';
import { TrainingAssignedUsersComponent } from '../training-assigned-users/training-assigned-users.component';

// import all breadcrumbs and titles dynamic
import { UserEditBreadcrumbs } from '../users/edit/user-edit-breadcrumbs.resolver';
import { UserEditTitle } from '../users/edit/user-edit-title.resolver';
import { UsersBreadcrumbs } from '../users/users-breadcrumbs.resolver';
import { FarmEditTitle } from '../farm/edit/farm-edit-title.resolver';
import { FarmEditBreadcrumbs } from '../farm/edit/farm-edit-breadcrumbs.resolver';
import { SubCategoryEditTitle } from '../subcategory/edit/subcategory-edit-title.resolver';
import { SubCategoryEditBreadcrumbs } from '../subcategory/edit/subcategory-edit-breadcrumbs.resolver';
import { TrainingEditTitle } from '../training/edit/training-edit-title.resolver';
import { TrainingEditBreadcrumbs } from '../training/edit/training-edit-breadcrumbs.resolver';
import { UserDetailsTitle } from '../user-details/user-details-title.resolver';
import { UserDetailsBreadcrumbs } from '../user-details/user-details-breadcrumbs.resolver';
import { TrainingAssignedUsersBreadcrumbs } from '../training-assigned-users/training-assigned-users-breadcrumbs.resolver';
import { TrainingDetailBreadcrumbs } from '../training/detail/training-detail-breadcrumbs.resolver';
import { MomentDetailBreadcrumbs } from '../moment/detail/moment-detail-breadcrumbs.resolver';
import { AssignedSiteMomentsTitle } from '../staff-assigned-sites/assignedtitle-moments-title.resolver';
import { AssignedSiteStaffTitle } from '../staff-assigned-sites/assignedtitle-staff-title.resolver';
import { TrainingAssignedFarmsComponent } from '../training/training-assigned-farms/training-assigned-farms.component';
import { NotificationComponent } from '../notification/notification.component';
import { CategoryAssignedUsersComponent } from '../category/category-assigned-users/category-assigned-users.component';
import { SubCategoryAssignedUsersComponent } from '../subcategory/subcategory-assigned-users/subcategory-assigned-users.component';
import { SupplierChainManagerComponent } from '../supplier-chain-manager/supplier-chain-manager.component';
import { SupplierChainManagerEditComponent } from '../supplier-chain-manager/edit/supplier-chain-manager-edit.component';
import { ManageContentComponent } from '../manage-content-type/manage-content.component';
import { ManageContentEditComponent } from '../manage-content-type/edit/manage-content-edit.component';
import { ManageContentEditTitle } from '../manage-content-type/edit/manage-content-edit-title.resolver';
import { ManageContentEditBreadcrumbs } from '../manage-content-type/edit/manage-content-edit-breadcrumbs.resolver';
import { ManageLearningSeriesEditComponent } from '../manage-learning-series/edit/manage-learning-series-edit.component';
import { ManageLearningSeriesEditTitle } from '../manage-learning-series/edit/manage-learning-series-edit-title.resolver';
import { ManageLearningSeriesEditBreadcrumbs } from '../manage-learning-series/edit/manage-learning-series-edit-breadcrumbs.resolver';
import { ManageLearningSeriesComponent } from '../manage-learning-series/manage-learning-series.component';
import { NewsFeedComponent } from '../news-feed/news-feed.component';
import { NewsFeedEditComponent } from '../news-feed/edit/news-feed-edit.component';
import { NewsFeedEditTitle } from '../news-feed/news-feed-edit-title.resolver';
import { NewsFeedEditBreadcrumbs } from '../news-feed/news-feed-edit-breadcrumbs.resolver';
import { NewsFeedDetailComponent } from '../news-feed/detail/news-feed-detail.component';
import { NewsFeedDetailBreadcrumbs } from '../news-feed/detail/news-feed-detail-breadcrumbs.resolver';
import { ManageCourseComponent } from '../manage-course/manage-course.component';
import { ProgramAdminDashboardComponent } from '../program-admin-dashboard/program-admin-dashboard.component';
import { CourseEditComponent } from '../manage-course/edit/course-edit.component';
import { CourseEditBreadcrumbs } from '../manage-course/edit/course-edit-breadcrumbs.resolver';
import { CourseEditTitle } from '../manage-course/edit/course-edit-title.resolver';
import { CourseTrainingsComponent } from '../course-trainings/course-trainings.component';
import { CourseAllocatedUsersListComponent } from '../course-allocated-users-list/course-allocated-users-list.component';
import { CourseDetailComponent } from '../manage-course/detail/course-detail.component';
import { CourseLearningSeriesEditComponent } from '../manage-course-learning-series/edit/course-learning-series-edit.component';
import { ManageCourseLearningSeriesComponent } from '../manage-course-learning-series/manage-course-learning-series.component';
import { CourseTrainingEditComponent } from '../course-trainings/edit/course-training-edit.component';
import { ViewCourseTrainingComponent } from '../manage-course/view-trainings/view-course-training.component';
import { CourseTrainingEditTitle } from '../course-trainings/edit/course-training-edit-title.resolver';
import { CourseTrainingEditBreadcrumbs } from '../course-trainings/edit/course-training-edit-breadcrumbs.resolver';
import { ManageCourseLearningSeriesEditTitle } from '../manage-course-learning-series/edit/manage-course-learning-series-edit-title.resolver';
import { ManageCourseLearningSeriesEditBreadcrumbs } from '../manage-course-learning-series/edit/manage-course-learning-series-edit-breadcrumbs.resolver';
import { FarmAssignedUsersBreadcrumbs } from '../users/farm-assigned-users/farm-assigned-users-breadcrumbs.resolver';
import { Constant } from '../../config/constants';
import { CourseTrainingDetailComponent } from '../course-trainings/course-training-detail/course-training-detail.component';
import { CourseTrainingDetailBreadcrumbs } from '../course-trainings/course-training-detail/course-training-detail-breadcrumbs.resolver';
import { ViewCourseTrainingBreadcrumbs } from '../manage-course/view-trainings/view-course-training.breadcrumbs.resolver';
import { ProgramAdminSettingsComponent } from '../program-admin-settings/program-admin-settings.component';
import { ProgramChangePasswordComponent } from '../program-change-password/program-change-password.component';
import { ManageProgramUserComponent } from '../manage-program-user/manage-program-user.component';
import { TrainingLibraryComponent } from '../training-library/training-library.component';
import { TrainingCardDetailComponent } from '../training-library/training-card-detail/training-card-detail.component';
import { CourseUserDetailsComponent } from '../course-user-details/course-user-details.component';
import { CourseUserDetailsTitle } from '../course-user-details/course-user-details-title.resolver';
import { CourseUserDetailsBreadcrumbs } from '../course-user-details/course-user-details-breadcrumbs.resolver';
import { UserMyCourseComponent } from '../user-my-course/user-my-course.component';
import { UserLoginProfileComponent } from '../user-login-profile/user-login-profile.component';
import { ManageAdminProgramUserComponent } from '../manage-admin-program-user/manage-admin-program-user.component';
import { UserInvitedEditComponent } from '../user-invited-listing/user-invited-edit/user-invited-edit.component';
import { AdminManageProgramAdminComponent } from '../admin-manage-program-admin/admin-manage-program-admin.component';
import { ProgramUserTrainingDetailComponent } from '../program-user-training-detail/program-user-training-detail.component';
import { ProgramUserTrainingDetailBreadcrumbs } from '../program-user-training-detail/program-user-training-detail-breadcrumbs.resolver';
import { ProgramAdminSettingsBreadcrumbs } from '../program-admin-settings/program-admin-settings-breadcrumbs.resolver';
import { AdminProgramUserTrainingDetailBreadcrumbs } from '../program-user-training-detail/admin-program-user-training-detail-breadcrumbs.resolver ';
import { AdminManageProgramAdminBreadcrumbs } from '../admin-manage-program-admin/admin-manage-program-admin-breadcrumbs.resolver';
import { AdminManageProgramAdminTitle } from '../admin-manage-program-admin/admin-manage-program-admin-title.resolver';
import { AdminViewCourseTrainingBreadcrumbs } from '../manage-course/view-trainings/admin-view-course-training.breadcrumbs.resolver';
import { AdminCourseEditTitle } from '../manage-admin-course/edit/manage-admin-edit-course-title.resolver';
import { AdminCourseEditBreadcrumbs } from '../manage-admin-course/edit/manage-admin-edit-course-breadcrumbs.resolver';
import { CourseDetailBreadcrumbs } from '../manage-course/detail/course-detail-breadcrumbs.resolver';
import { ProgramUserEditComponent } from '../program-user-edit/program-user-edit.component';
import { ProgramUserEditBreadcrumbs } from '../program-user-edit/program-user-breadcrumbs.resolver';
import { ProgramUserEditTitle } from '../program-user-edit/program-user-title.resolver';
import { ManageAdminEditCourseComponent } from '../manage-admin-course/edit/manage-admin-edit-course.component';
import { ViewUserLogsComponent } from '../view-user-logs/view-user-logs.component';
import { ViewUserLogsBreadcrumbs } from '../view-user-logs/view-user-logs-breadcrumbs.resolver';
import { ManageCourseAccessComponent } from '../manage-course-access/manage-course-access.component';
import { CourseParticipantsComponent } from '../course-participants/course-participants.component';
import { CourseParticipantsBreadcrumbs } from '../course-participants/course-participants-breadcrumbs.resolver';
import { CourseParticipantsTitle } from '../course-participants/course-participants-title.resolver';
import { CourseAllocatedTitle } from '../course-allocated-users-list/course-allocated-title.resolver';
import { CourseAllocatedBreadcrumbs } from '../course-allocated-users-list/course-allocated-breadcrumbs.resolver';
import { CourseTrainingParticipantsTitle } from '../course-participants/course-training-participants-title.resolver';
import { CourseTrainingParticipantsBreadcrumbs } from '../course-participants/course-training-participants-breadcrumbs.resolver';
import { CourseDetailTitle } from '../manage-course/detail/course-detail-title.resolver';
import { FarmAdminComponent } from '../farm-admin/farm-admin.component';
import { FarmAdminEditComponent } from '../farm-admin/farm-admin-edit/farm-admin-edit.component';
import { MomentEditTitle } from '../moment/edit/moment-edit-title.resolver';
import { MomentEditBreadcrumbs } from '../moment/edit/moment-edit-breadcrumbs.resolver';


export const LAYOUTROUTING: Routes = [
  {
    path: '', component: LayoutComponent, children: [
      { path: '', component: LandingComponent },
      { path: 'program-admin', component: ProgramAdminDashboardComponent },
      { path: 'change/password', component: ChangePasswordComponent },
      { path: 'account/settings', component: AccountSettingsComponent },
      {
        path: 'users',
        component: UsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
          title: 'Manage Seekers'
        },
        resolve: {
          breadcrumbs: UsersBreadcrumbs
        }
      },
      {
        path: 'user/edit/:id', component: UsersEditComponent, canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'] },
        resolve: {
          title: UserEditTitle,
          breadcrumbs: UserEditBreadcrumbs
        }

      },
      // Add Admin Program User
      {
        path: 'admin/program/user/edit/:id', component: ProgramUserEditComponent, canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] },
        resolve: {
          title: ProgramUserEditTitle,
          breadcrumbs: ProgramUserEditBreadcrumbs
        }

      },

      {
        path: 'user-details/:id', component: UserDetailsComponent, canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'] },
        resolve: {
          title: UserDetailsTitle,
          breadcrumbs: UserDetailsBreadcrumbs
        }

      },
      // Admin Course Access
      {
        path: 'course/access',
        component: ManageCourseAccessComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Manage Course Access',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Course Access", link: "/dashboard/course/access", active: true },
          ]
        }
      },
      // Admin Course Access End
      {
        path: 'admin-setting',
        component: AdminSettingComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN],
          title: 'Settings',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Settings", link: "/dashboard/admin-setting", active: true },
          ]
        }
      },
      {
        path: 'staff-profile',
        component: StaffprofileComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Profile',
        }
      },
      {
        path: 'staff-change-password',
        component: StaffChangepasswordComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Change Password',
        }
      },
      {
        path: 'admin-change-password',
        component: StaffChangepasswordComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
          title: 'Change Password',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Settings", link: "/dashboard/admin-setting", active: false },
            { title: "Change Password", link: "/dashboard/admin-change-password", active: true },
          ]
        }
      },
      {
        path: 'assigned-users/:id',
        component: FarmAssignedUsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Assigned Users',
        },
        resolve: {
          breadcrumbs: FarmAssignedUsersBreadcrumbs
        }
      },
      {
        path: 'training-assigned-users/:id',
        component: TrainingAssignedUsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
          title: 'Assigned Users',
        },
        resolve: {
          breadcrumbs: TrainingAssignedUsersBreadcrumbs
        }
      },
      {
        path: 'manage-content',
        component: ManageContentComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.FARM_ADMIN],
          title: 'Manage Content Type',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Content Type", link: "/dashboard/manage-content", active: true },
          ]
        }
      },
      {
        path: 'manage-learning-series',
        component: ManageLearningSeriesComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN, Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR],
          title: 'Manage Learning Series',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Learning Series", link: "/dashboard/manage-learning-series", active: true },
          ]
        }
      },
      {
        path: 'sub-category',
        component: SubCategoryComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN],
          title: 'Courses for Training',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Courses", link: "/dashboard/category", active: true },
          ]
        }
      },
      {
        path: 'training',
        component: TrainingComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN, Constant.ROLES.FARM_ADMIN],
          title: 'Manage Training',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Training", link: "/dashboard/training", active: true },
          ]
        }
      },
      {
        path: 'farms',
        component: FarmComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Manage Companies',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Companies", link: "/dashboard/farms", active: true },
          ]
        }
      },
      {
        path: 'farm/edit/:id',
        component: FarmEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] },
        resolve: {
          title: FarmEditTitle,
          breadcrumbs: FarmEditBreadcrumbs
        }
      },
      {
        path: 'farm/detail/:id',
        component: FarmDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'farm/admins',
        component: FarmAdminComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Manage Company Admins',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Company Admins", link: "/dashboard/farm/admins", active: true },
          ]
        }
      },
      {
        path: 'farm/admin/edit/:id',
        component: FarmAdminEditComponent,
        canActivate: [AuthGuard],
        data: { 
          roles: ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
          title: 'Manage Company Admin',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Company Admins", link: "/dashboard/farm/admins", active: false },
            { title: "Company Admin", link: "/dashboard/farm/admin/edit/:id", active: true },
          ]
        },
      
      },
      {
        path: 'language',
        component: LanguageComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'language/edit/:id',
        component: LanguageEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'language/detail/:id',
        component: LanguageDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      // {
      //   path: 'category',
      //   component: CategoryComponent,
      //   canActivate: [AuthGuard],
      //   data: { roles: ['ROLE_ADMIN'] }
      // },
      {
        path: 'manage-content/edit/:id',
        component: ManageContentEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN] },
        resolve: {
          title: ManageContentEditTitle,
          breadcrumbs: ManageContentEditBreadcrumbs
        }
      },
      {
        path: 'manage-learning-series/edit/:id',
        component: ManageLearningSeriesEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN, Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR] },
        resolve: {
          title: ManageLearningSeriesEditTitle,
          breadcrumbs: ManageLearningSeriesEditBreadcrumbs
        }
      },
      {
        path: 'category/detail/:id',
        component: CategoryDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.SUPER_ADMIN] }
      },
      // {
      //   path: 'sub-category',
      //   component: SubCategoryComponent,
      //   canActivate: [AuthGuard],
      //   data: { roles: ['ROLE_ADMIN'] }
      // },
      {
        path: 'sub-category/edit/:id',
        component: SubCategoryEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.SUPER_ADMIN] },
        resolve: {
          title: SubCategoryEditTitle,
          breadcrumbs: SubCategoryEditBreadcrumbs
        }
      },
      {
        path: 'sub-category/detail/:id',
        component: SubCategoryDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.SUPER_ADMIN] }
      },
      {
        path: 'user-farm',
        component: UserFarmComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'user-farm/edit/:id',
        component: UserFarmEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'user-farm/detail/:id',
        component: UserFarmDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      // {
      //   path: 'training',
      //   component: TrainingComponent,
      //   canActivate: [AuthGuard],
      //   data: { roles: ['ROLE_ADMIN'] }
      // },
      {
        path: 'training/edit/:id',
        component: TrainingEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN] },
        resolve: {
          title: TrainingEditTitle,
          breadcrumbs: TrainingEditBreadcrumbs
        }
      },
      {
        path: 'training/detail/:id',
        component: TrainingDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN],
          title: 'Training Detail',
        },
        resolve: {
          breadcrumbs: TrainingDetailBreadcrumbs
        }
      },
      {
        path: 'news-feed/detail/:id',
        component: NewsFeedDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN],
          title: 'News Feed Detail',
        },
        resolve: {
          breadcrumbs: NewsFeedDetailBreadcrumbs
        }
      },
      {
        path: 'user-assign-training',
        component: UserAssignTrainingComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'] }
      },
      {
        path: 'staff-assigned-training',
        component: StaffAssignedTrainingComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Assigned Trainings',
        }
      },
      {
        path: 'staff-reel-trainings',
        component: AssignedTrainingReelsVideoComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF', 'ROLE_PROGRAM_ADMINISTRATOR'],
          title: 'Training Feed',
        }
      },
      {
        path: 'user-assign-training/edit/:id',
        component: UserAssignTrainingEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'user-assign-training/detail/:id',
        component: UserAssignTrainingDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'farm-assign-training',
        component: FarmAssignTrainingComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'staff-assigned-sites',
        component: StaffAssignedSitesComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Assigned Sites',
        }
      },
      {
        path: 'site-view-staff/:id',
        component: ViewStaffComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF']
        },
        resolve: {
          title: AssignedSiteStaffTitle
        }
      },
      {
        path: 'farm-assign-training/edit/:id',
        component: FarmAssignTrainingEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'request-site-change',
        component: RequestForSiteChangeComponent,
        canActivate: [AuthGuard],
        data: {
          title: 'Request Site Change',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Request Site Change", link: "/dashboard/request-site-change", active: true },
          ],
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN']
        }
      },
      {
        path: 'farm-assign-training/detail/:id',
        component: FarmAssignTrainingDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'moments',
        component: MomentComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
          title: 'Manage Moments',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Moments", link: "/dashboard/moments", active: true },
          ]
        }
      },
      {
        path: 'news-feed',
        component: NewsFeedComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
          title: 'Manage News Feed',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage News Feed", link: "/dashboard/news-feed", active: true },
          ]
        }
      },
      {
        path: 'staff-moments',
        component: StaffmomentsComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Moments',
        }
      },
      {
        path: 'staff-moments/:id',
        component: StaffmomentsComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
        },
        resolve: {
          title: AssignedSiteMomentsTitle
        }
      },
      {
        path: 'staff-add-moment',
        component: StaffAddMomentComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Add New Moment',
        }
      },

      {
        path: 'moment',
        component: MomentComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'] }
      },
      {
        path: 'moment/edit/:id',
        component: MomentEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'] },
        resolve: {
          title: MomentEditTitle,
          breadcrumbs: MomentEditBreadcrumbs
        }
      },
      {
        path: 'news-feed/edit/:id',
        component: NewsFeedEditComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.FARM_ADMIN],
        },
        resolve: {
          title: NewsFeedEditTitle,
          breadcrumbs: NewsFeedEditBreadcrumbs
        }
      },
      {
        path: 'moment/detail/:id',
        component: MomentDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'],
          title: 'Moment Detail',
        },
        resolve: {
          breadcrumbs: MomentDetailBreadcrumbs
        }
      },
      {
        path: 'user-training-status',
        component: UserTrainingStatusComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'user-training-status/edit/:id',
        component: UserTrainingStatusEditComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },
      {
        path: 'user-training-status/detail/:id',
        component: UserTrainingStatusDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] }
      },

      {
        path: 'training-feed',
        component: TrainingFeedComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN],
          title: 'Training Feed',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Training Feed", link: "/dashboard/training-feed", active: true },
          ]
        }
      },
      {
        path: 'users-enquiry',
        component: UsersContactListComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'Users Enquiry',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Users Enquiry", link: "/dashboard/users-enquiry", active: true },
          ]
        }
      },
      // admin new changes
      {
        path: 'program-admin-list',
        component: ManageAdminProgramUserComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'Manage Program Admins',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Program Admins", link: "/dashboard/program-admin-list", active: true },
          ]
        }
      },
      {
        path: 'program-detail/:id',
        component: AdminManageProgramAdminComponent, canActivate: [AuthGuard],
        data: { roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] },
        resolve: {
          title: AdminManageProgramAdminTitle,
          breadcrumbs: AdminManageProgramAdminBreadcrumbs
        }
      },
      //
      {
        path: 'training-assigned-sites/:id',
        component: TrainingAssignedFarmsComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Assigned Site Users',
        }
      },
      {
        path: 'notifications',
        component: NotificationComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Manage Notifications',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Notifications", link: "/dashboard/notifications", active: true },
          ]
        }
      },
      {
        path: 'program-admin/notifications',
        component: NotificationComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_PROGRAM_ADMINISTRATOR'],
          title: 'Manage Notifications',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Notifications", link: "/dashboard/notifications", active: true },
          ]
        }
      },
      {
        path: 'staff-notifications',
        component: NotificationComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_STAFF'],
          title: 'Notifications',
        }
      },
      {
        path: 'category-assigned-users/:id',
        component: CategoryAssignedUsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Assigned Users',
        }
      },
      {
        path: 'category-assigned-sites/:id',
        component: CategoryAssignedUsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Assigned Sites',
        }
      },
      {
        path: 'subcategory-assigned-users/:id',
        component: SubCategoryAssignedUsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Assigned Users',
        }
      },
      {
        path: 'subcategory-assigned-sites/:id',
        component: SubCategoryAssignedUsersComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Assigned Sites',
        }
      },
      {
        path: 'teams',
        component: SupplierChainManagerComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN],
          title: 'Teams',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Teams", link: "/dashboard/teams", active: true },
          ]
        }
      },
      {
        path: 'team/edit/:id',
        component: SupplierChainManagerEditComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.SUPER_ADMIN],
          title: 'Manage Teams',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Teams", link: "/dashboard/teams", active: false },
            { title: "Team", link: "/dashboard/team/0", active: true },
          ]
        }
      },

      //start program admin region//
      {
        path: 'program-admin/courses',
        component: ManageCourseComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN, Constant.ROLES.FARM_ADMIN],
          title: 'Manage Course',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Manage Course", link: "/dashboard/program-admin/courses", active: true },
          ]
        }
      },
      {
        path: 'program-admin/course/edit/:id',
        component: CourseEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN, Constant.ROLES.FARM_ADMIN] },
        resolve: {
          title: CourseEditTitle,
          breadcrumbs: CourseEditBreadcrumbs
        }
      },
      {
        path: 'admin/program/course/edit/:id',
        component: ManageAdminEditCourseComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN] },
        resolve: {
          title: AdminCourseEditTitle,
          breadcrumbs: AdminCourseEditBreadcrumbs
        }
      },
      {
        path: 'program-admin/course/detail/:id',
        component: CourseDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.FARM_ADMIN] },
        resolve: {
          title: CourseDetailTitle,
          breadcrumbs: CourseDetailBreadcrumbs
        }
      },
      {
        path: 'program-admin/preview/course/detail/:id',
        component: CourseDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN,Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.FARM_ADMIN] },
        resolve: {
          title: CourseDetailTitle,
          breadcrumbs: CourseDetailBreadcrumbs
        }
      },
      {
        path: 'admin/program/course/detail/:id',
        component: CourseDetailComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ADMIN,Constant.ROLES.SUPER_ADMIN] },
        resolve: {
          title: CourseDetailTitle,
          breadcrumbs: CourseDetailBreadcrumbs
        }
      },
      {
        path: 'program-admin/course/users/:id',
        component: CourseAllocatedUsersListComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_FARM_ADMIN'] },
        resolve: {
          title: CourseAllocatedTitle,
          breadcrumbs: CourseAllocatedBreadcrumbs
        }
      },
      {
        path: 'admin/program/course/users/:id',
        component: CourseAllocatedUsersListComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN'] },
        resolve: {
          title: CourseAllocatedTitle,
          breadcrumbs: CourseAllocatedBreadcrumbs
        }
      },
      {
        path: 'program-admin/manage-course-learning-series',
        component: ManageCourseLearningSeriesComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR],
          title: 'Manage Learning Series',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Manage Learning Series", link: "/dashboard/program-admin/manage-course-learning-series", active: true },
          ]
        }
      },
      {
        path: 'program-admin/manage-course-learning-series/edit/:id',
        component: CourseLearningSeriesEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR] },
        resolve: {
          title: ManageCourseLearningSeriesEditTitle,
          breadcrumbs: ManageCourseLearningSeriesEditBreadcrumbs
        }
      },
      {
        path: 'program-admin/trainings',
        component: CourseTrainingsComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR],
          title: 'Manage Training',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Manage Training", link: "/dashboard/program-admin/trainings", active: true },
          ]
        }
      },
      {
        path: 'program-admin/trainings/edit/:id',
        component: CourseTrainingEditComponent,
        canActivate: [AuthGuard],
        data: { roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR] },
        resolve: {
          title: CourseTrainingEditTitle,
          breadcrumbs: CourseTrainingEditBreadcrumbs
        }
      },
      {
        path: 'program-admin/course/trainings/:id',
        component: ViewCourseTrainingComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.ADMIN],
          title: 'Manage Course Training',
        },
        resolve: {
          breadcrumbs: ViewCourseTrainingBreadcrumbs
        }
      },
      {
        path: 'admin/program/course/trainings/:id',
        component: ViewCourseTrainingComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'Manage Course Training',
        },
        resolve: {
          breadcrumbs: AdminViewCourseTrainingBreadcrumbs
        }
      },
      {
        path: 'program-admin/course/training/detail/:id',
        component: CourseTrainingDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'Course Training Detail',
        },
        resolve: {
          breadcrumbs: CourseTrainingDetailBreadcrumbs
        }
      },
      {
        path: 'program-admin/training/detail/:id',
        component: ProgramUserTrainingDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'Training Detail',
        },
        resolve: {
          breadcrumbs: ProgramUserTrainingDetailBreadcrumbs
        }
      },
      {
        path: 'training/program/admin/detail/:id',
        component: ProgramUserTrainingDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'Training Detail',
        },
        resolve: {
          breadcrumbs: AdminProgramUserTrainingDetailBreadcrumbs
        }
      },
      {
        path: 'program-admin/profile-setting/:id',
        component: ProgramAdminSettingsComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR],
          title: 'Setting',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: true },
            { title: "Setting", link: "/dashboard/program-admin/profile-setting/0", active: false, },
          ]
        },
        // resolve: {
        //   breadcrumbs: ProgramAdminSettingsBreadcrumbs
        // }
      },
      {
        path: 'program-admin/training/library',
        component: TrainingLibraryComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR],
          title: 'Training Library',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Training Library", link: "/dashboard/program-admin/training/library", active: true },
          ]
        }
      },
      {
        path: 'program-admin/training/library/:id',
        component: TrainingCardDetailComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR],
          title: 'Training Library',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Training Library", link: "/dashboard/program-admin/training/library", active: true },
          ]
        }
      },
      {
        path: 'program-admin/change-password',
        component: ProgramChangePasswordComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_PROGRAM_ADMINISTRATOR'],
          title: 'Change Password',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Settings", link: "/dashboard/program-admin/profile-setting/0", active: true },
            { title: "Change Password", link: "/dashboard/program-admin/change-password", active: true },
          ]
        }
      },
      {
        path: 'program-admin/users',
        component: ManageProgramUserComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_PROGRAM_ADMINISTRATOR'],
          title: 'Manage Seekers',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Manage Seekers", link: "/dashboard/program-admin/users", active: true },
          ]
        }
      },
      {
        path: 'seekers/enrolled',
        component: ManageProgramUserComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN','ROLE_PROGRAM_ADMINISTRATOR'],
          title: 'Manage Seekers Enrollment',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Seekers Enrollment", link: "/dashboard/seekers/enrolled", active: true },
          ]
        }
      },
      {
        path: 'program-admin/user/course',
        component: UserMyCourseComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_PROGRAM_ADMINISTRATOR'],
          title: 'Users Course',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard/program-admin", active: false },
            { title: "Manage Users", link: "/dashboard/program-admin/user/course", active: true },
          ]
        }
      },
      {
        path: 'program-admin/user-details/:id',
        component: CourseUserDetailsComponent, canActivate: [AuthGuard],
        data: { roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] },
        resolve: {
          title: CourseUserDetailsTitle,
          breadcrumbs: CourseUserDetailsBreadcrumbs
        }
      },
      {
        path: 'program-admin/user/invited/:id',
        component: UserInvitedEditComponent,
        canActivate: [AuthGuard],
        data: {
          roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN'],
          title: 'Manage Users',
          breadcrumbs: [
            { title: "Dashboard", link: "/dashboard", active: false },
            { title: "Manage Users", link: "/dashboard/seekers/enrolled", active: true },
          ]
        }
      },

      {
        path: 'program-admin/user/edit/:id', component: UserLoginProfileComponent, canActivate: [AuthGuard],
        data: { roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] },
        // resolve: {
        //   title: UserEditTitle,
        //   breadcrumbs: UserEditBreadcrumbs
        // }

      },
      {
        path: 'program-admin/view/user/logs/:id',
        component: ViewUserLogsComponent,
        canActivate: [AuthGuard],
        data: {
          roles: [Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.ADMIN, Constant.ROLES.SUPER_ADMIN],
          title: 'View User Logs',
        },
        resolve: {
          breadcrumbs: ViewUserLogsBreadcrumbs
        }
      },
      {
        path: 'program-admin/course/participants/:courseId',
        component: CourseParticipantsComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN','ROLE_SUPER_ADMIN', Constant.ROLES.FARM_ADMIN] },
        resolve: {
          title: CourseParticipantsTitle,
          breadcrumbs: CourseParticipantsBreadcrumbs
        }
      },
      {
        path: 'program-admin/course/training/participants/:trainingsId',
        component: CourseParticipantsComponent,
        canActivate: [AuthGuard],
        data: { roles: ['ROLE_PROGRAM_ADMINISTRATOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN'] },
        resolve: {
          title: CourseTrainingParticipantsTitle,
          breadcrumbs: CourseTrainingParticipantsBreadcrumbs
        }
      },

      //end program admin region//
    ]
  },
];
