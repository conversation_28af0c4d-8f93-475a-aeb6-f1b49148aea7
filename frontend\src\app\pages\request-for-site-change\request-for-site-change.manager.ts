import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { RequestSiteChangeService } from 'src/app/services/request-site-change.service';

@Injectable({
    providedIn: 'root'
})
export class RequestForSiteChangeManager extends BaseManager {

    constructor(requestSiteChangeService: RequestSiteChangeService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(requestSiteChangeService, loadingService, toastService);
    }
}
