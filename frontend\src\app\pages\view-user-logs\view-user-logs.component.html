<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
    <div class="allocated-users-list" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 col-md-8 d-flex">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="Search" appDelayedInput
                        (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchTraining">
                    <i class="bi bi-search pe-3"></i>
                </div>
                <!-- <button (click)="openUserLogsFilterTrainingModal()" type="button"
                    class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button> -->
            </div>
            <div class="col-12 col-md-4">

            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <tbody>
                    <tr class="records-cls" *ngFor="let record of records;">
                        <td width="30" style="text-align: center;">
                            <img src="/assets/images/icons/menu/user-icon.svg" height="38" width="38" class="ms-3"
                                alt="">
                        </td>
                        <td width="100px" class="pe-0">
                            <h5 class="code ms-2" title="View User Profile"
                                style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                [routerLink]="['/dashboard/user-details/' +record?.userId]"
                                [queryParams]="{ tab: 'profile', 'username': record?.usersDetail?.fullName }"
                                queryParamsHandling="merge">
                                {{record?.userDetail?.firstName}}
                            </h5>
                        </td>
                        <td></td>
                        <td>
                            {{record?.userDetail?.email}}
                        </td>
                        <td>
                            <div>{{moment(record?.updatedOn).format('DD/MM/YYYY')}}</div>
                        </td>
                        <td>
                            <div *ngIf="record?.status else noStatus">
                                <div [ngClass]="commonUtil.getTitleStatusColor(record?.status)"
                                    class="status-button semi-bold">
                                    <img class="me-1" [src]="commonUtil.getTitleStatusImg(record?.status)">
                                    {{commonUtil.getTitleStatus(record.status)}}
                                </div>
                            </div>
                            <ng-template #noStatus>-</ng-template>
                        </td>
                        <td>
                            <button
                                *ngIf="record.videoUrl && record?.status !== MY_CONSTANT.TRAINING_STATUS.INPROGRESS; else NoVideo"
                                (click)="watchVideo(record)" class="user-video-button bg-secondary">
                                <img src="/assets/images/icons/menu/watch-video.svg"></button>
                            <ng-template #NoVideo>-</ng-template>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i *ngIf="record && record.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                                    (click)="approveOrRejectTraining(record.id, 'APPROVED')"
                                    class="bi bi-check-circle fs-4 cursor-pointer" title="Approved"></i>
                                <i *ngIf="record && record.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                                    (click)="approveOrRejectTraining(record.id, 'REJECTED')"
                                    class="bi bi-x-circle ps-2 fs-4 cursor-pointer" title="Rejected"></i>
                                <ng-container
                                    *ngIf="!record || record.status !== MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;">
                                    <span>-</span>
                                </ng-container>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- watch video modal -->
<div class="modal fade" id="programUserLogsTrainingVideoModal" aria-hidden="true"
    aria-labelledby="programUserLogsTrainingVideoModalLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="programUserLogsTrainingVideoModalLabel"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body"
                *ngIf="programUserLogsTrainingVideoModal && programUserLogsTrainingVideoModal._isShown">
                <div>
                    <div *ngIf="loadingVideo" class="loading-container-video-training">
                        <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
                        id="staff-video"></video>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!--  -->

<!-- filter -->
<div class="modal fade" id="userLogsFilterTrainingModal" tabindex="-1"
    aria-labelledby="userLogsFilterTrainingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userLogsFilterTrainingModalLabel">Filter Course</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div *ngIf="userLogsFilterTrainingModal && userLogsFilterTrainingModal._isShown" class="modal-body">
                <form #FilterForm="ngForm" novalidate="novalidate">
                    <div class="userMyCourseFilter" *ngIf="userDetailsTabs == 'course'">
                        <div class="form-floating mb-4 w-100">
                            <input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                                class="form-control" type="text" name="title" #title="ngModel"
                                [(ngModel)]="courseFilterParam.courseTitle" required="required"
                                placeholder="{{'Course.title' | translate}}">
                            <label for="floatingInput">{{"Course.title" | translate}}</label>
                            <app-validation-message [field]="title" [onClickValidation]="onClickValidation">
                            </app-validation-message>
                        </div>
                        <div class="form-floating">
                            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                [ngClass]="{'is-invalid':!courseTrainingStatus.valid && onClickValidation}">
                                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                    name="courseTrainingStatus"
                                    [items]="trainingFilterData.data[0].trainingStatusDetail" bindLabel="status"
                                    bindValue="id" class="custom-multiselect form-control padding-bottom-8"
                                    [(ngModel)]="courseFilterParam.status" #courseTrainingStatus="ngModel">
                                </ng-select>
                            </div>
                            <label for="language">{{"Training.TrainingStatus" | translate}}</label>
                        </div>
                        <app-date-range-filter (fromDateOutput)="fromDateOutput($event)"
                            (toDateOutput)="toDateOutput($event)" [fromDateInput]="courseFilterParam.startDate"
                            [toDateInput]="courseFilterParam.endDate">
                        </app-date-range-filter>
                    </div>
                    <div class="modal-footer">
                        <button (click)="resetFilter()" type="button"
                            class="text-white btn btn-secondary me-2">Reset</button>
                        <button (click)="onClickCourseDetailFilter(FilterForm.form.valid)" type="button"
                            class="btn btn-primary">Filter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--  -->