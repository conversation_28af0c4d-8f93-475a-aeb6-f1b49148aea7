import { TranslateService } from "@ngx-translate/core";
import { BaseModel } from "../config/base.model";
import { ToastService } from "../shared/toast.service";

export class CategorySubCategoryMapping extends BaseModel{

    categoryId: string;
    subCategoryId: string;
    categoryName: string;

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        throw new Error("Method not implemented.");
    }
    forRequest() {
        throw new Error("Method not implemented.");
    }

}