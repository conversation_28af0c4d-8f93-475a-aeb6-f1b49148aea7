import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class UserEnrolledService extends BaseService {
    userId: string;

    constructor(public http: HttpClient) {
        super(http, '/api/course/enrolled/user', '/api/course/enrolled/users');
    }

    getUserId(id: string) {
        this.userId = id;
    }

    // getPendingCourse(filterParam: FilterParam): Promise<RestResponse> {
    //     return this.getRecords('/api/course/pending/requests', filterParam);
    // }

    getEnrolledgCourse(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/course/enrolled/users', filterParam);
    }

    revokeAccessCourse(id: string): Promise<RestResponse> {
        return this.updateRecord('/api/revoke/courses/' + id,null)
    }

}

