import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ManageCoursePartsService } from './manage-courseparts-service';

@Injectable({
    providedIn: 'root'
})
export class ManageCoursePartsManager extends BaseManager {

    constructor(protected manageCoursePartsService: ManageCoursePartsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageCoursePartsService, loadingService, toastService);
    }
  
}
