{"PROFILE": "Profile", "ENGLISH": "English", "ITALIAN": "Italian", "LOGOUT": "Logout", "CATEGORIES": "Categories", "ACCOUNT SETTINGS": "Account settings", "SETTINGS": "Settings", "FIRST NAME": "First name", "LAST NAME": "Last name", "EMAIL": "Email", "MOBILE NO": "Mobile", "SAVE": "Save", "CHANGE PASSWORD": "Change password", "OLD PASSWORD": "Current password", "NEW PASSWORD": "New password", "CONFIRM NEW PASSWORD": "Confirm new password", "RECOVER PASSWORD": "Reset Password", "PASSWORD": "Password", "CONFIRM PASSWORD": "Confirm password", "SIGN IN": "Log In", "STAY SIGNED IN": "Stay signed in", "FORGOT PASSWORD": "Forgot password", "RESET": "Reset", "BACK TO LOGIN": "Back to login", "ADMIN": "ADMIN", "ADMIN_SHORT": "AD", "WELCOME": "Welcome", "LOGIN": {"EMAIL": "Email", "PASSWORD": "Password", "FORGOT_PASSWORD": "Forgot password", "SIGN_IN": "Log In", "BACK_TO_LOGIN": "Back to login", "RESET": "Reset", "RECOVER_PASSWORD": "Recover Password", "MOBILE_NUMBER": "Mobile Number", "TEMP_PASSWORD": "Enter Temp Password", "MOBILE_OR_EMAIL": "Mobile Number Or Email"}, "EDIT": {"BASIC_INFO": "Basic Info", "ACTIVE": "Active", "ADD_NEW_RECORD": "Add New Record"}, "DASHBOARD": {"ACCOUNT_SETTINGS": "Profile", "CHANGE_PASSWORD": "Change Password", "LOGOUT": "Logout", "CONTACT": "Contact", "objName": "Dashboard", "PROFILE": "Profile"}, "COMMON": {"ADD": "Add", "NEW": "New", "UPDATE": "Update", "EDIT": "Edit", "DELETE": "Delete", "HOME": "Home", "CANCEL": "Cancel", "SAVE": "Save", "SELECT": "Select", "SELECT_OPTION": "Select Option", "RUNREPORT": "Run Report", "REPORT": "Report", "DETAIL": "Detail", "EXCELREPORT": "Export as EXCEL", "PDFREPORT": "Export as PDF", "PRINTREPORT": "Print Report", "NORECORDS": "No Records Available", "ACTION": "Action", "YES": "Yes", "NO": "No", "BACK": "Back", "CROP_IMAGES": "Crop Images", "APPLY_CROP": "Apply Changes", "UPLOAD_ALL": "Upload All", "NEW_PASSWORD": "New Password", "CONFIRM_PASSWORD": "Confirm Password", "REQUIRED_SELECT_VALIDATION_MESSAGE": "Please select a valid value.", "REQUIRED_INPUT_VALIDATION_MESSAGE": "Please provide a valid value.", "REQUIRED_EMAIL_VALIDATION_MESSAGE": "Please provide a valid email address.", "REQUIRED_MOBILE_VALIDATION_MESSAGE": "Please provide a valid mobile.", "REQUIRED_PATTERN_VALIDATION_MESSAGE": "Please provide a valid value.", "REQUIRED_FILE_VALIDATION_MESSAGE": "Please upload a file", "REQUIRED_INPUT_MIN_VALIDATION_MESSAGE": "Please provide a valid value. Min allowed value: ", "REQUIRED_INPUT_MAX_VALIDATION_MESSAGE": "Please provide a valid value. Max allowed value: ", "REQUIRED_MASK_VALIDATION_MESSAGE": "Please provide a valid value. Value will be like: ", "YEAR_INPUT_VALIDATION_MESSAGE": "Please provide a valid year value.", "MONTH_INPUT_VALIDATION_MESSAGE": "Please provide a valid month value."}, "USERS": {"objNames": "Users", "Location": "Location", "ADD_NEW_USERS": "Add New User", "NEW_USER": "New User", "Name": "Name", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "Role": "Role", "SelectRole": "Select Role", "PhoneNumber": "Phone Number", "programName": "Program Name", "Active": "Active", "Action": "Action", "YES": "Yes", "NO": "No", "MobileNo": "Mobile No", "TrainingHistory": "Training History", "Moments": "Moments", "Status": "Status", "language": "Language", "site": "Select Site", "Sites": "Site", "TrainingStatus": "Training Status", "Team": "Team", "Farm": "Farm", "PhoneNumberConfirmed": "Phone No. Verified", "EmailConfirmed": "<PERSON><PERSON>", "clickCount": "Click Count", "existingProgram": "Existing Program Admin", "externalProgram": "External Program Admin", "farm": "Company"}, "Farm": {"objName": "Company", "objNames": "Companies", "manageFarms": "Manage Companies", "Detail": "Company Detail", "ADD_SITE": "Add Site", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "country": "Country", "location": "Location", "latitude": "Latitude", "longitude": "Longitude", "description": "Description", "propertyNo": "Identifier", "farmingUrl": "Farming Url", "identifier": "Company Code", "staffCount": "Staff Count", "farmAdminCount": "Company Admin Count"}, "FarmAdmin": {"objName": "Company Admin", "objNames": "Company Admins"}, "Language": {"objName": "Language", "objNames": "Languages", "Detail": "Language Detail", "ADD_NEW_LANGUAGE": "Add New Language", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "country": "Country"}, "Category": {"objName": "Category", "objNames": "Categories", "Detail": "Category Detail", "ADD_NEW_CATEGORY": "Add Program", "CONTENT_TYPE_TITLE": "Title", "id": "id", "tenantId": "Tenant", "slug": "Slug", "action": "Action", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "title": "Program Title", "titleOfList": "Program", "description": "Description", "languageId": "LanguageId", "commonTitle": "Common Title", "GROUPCODE_": "", "groupCode": "Group Code"}, "SubCategory": {"objName": "Sub Category", "objNames": "Sub Categories", "Detail": "Sub Category Detail", "ADD_NEW_SUBCATEGORY": "Add Course", "id": "id", "tenantId": "Tenant", "slug": "Slug", "status": "Status", "createdBy": "Created By", "CONTENT_TYPES": "Content Types", "ACCESSIBILITY_SETUP": "Accessibility Setup", "ACCESSIBILITY": "Accessibility", "updatedBy": "Updated By", "action": "Action", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "categoryId": "Program", "title": "Course Title", "LEARNING_SERIES_TITLE": "Title", "description": "Description", "languageId": "Language", "commonTitle": "Common Title", "groupCode": "Group Code", "program": "Program", "course": "Course"}, "UserFarm": {"objName": "UserFarm", "objNames": "User Farms", "Detail": "UserFarm Detail", "ADD_NEW_USERFARM": "Add New UserFarm", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "userId": "User", "farmId": "Farm"}, "Training": {"objName": "Training", "objNames": "Trainings", "Detail": "Training Detail", "Views": "Views", "Clicks": "<PERSON>licks", "ADD_NEW_TRAINING": "Add Training", "id": "id", "tenantId": "Tenant", "slug": "Slug", "videoCredit": "Video Credit", "title": "Video Title", "titles": " Training Title", "courseTraining": "Training Title", "contentTypes": "Content Type", "action": "Action", "contentType": "Content Type", "learningSeries": "Learning Series", "uploadRequired": "Uploading Required", "watchRequired": "Watching Required", "Accessibility": "Accessibility", "AccessibilityStatus": "Private/Public", "status": "Publish", "private": "Private", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "date": "Date", "updatedOn": "Updated On", "lastUpdatedDate": "Last Updated Date", "isDeleted": "Deleted?", "isActive": "Active?", "categoryId": "Program", "subCategoryId": "Course", "languageId": "Language", "videoTitle": "Video Title", "description": "Description", "videoUrl": "Video Url", "publishedForTrainingFeed": "Publish", "commonVideoTitle": "Common Video Title", "groupCode": "Group Code", "UsersList": "Select Users", "FullName": "Full Name", "VideoOrImage": "Video/Image", "UserVideo": "Uploaded Video", "TrainingStatus": "Status", "TrainingRequired": "Completion Required", "completion": "Completion", "rejectedLogs": "Rejected Logs", "trainingHistory": "Training", "sequence": "Sequence", "prerequisites": "Prerequisites Training", "author": "Author", "videoUrlTitle": "Video", "thumbnailImageUrlTitle": "Thumbnail Image"}, "NewsFeed": {"objName": "Training", "objNames": "Trainings", "Detail": "Training Detail", "ADD_NEW_TRAINING": "Add Training", "id": "id", "tenantId": "Tenant", "slug": "Slug", "videoCredit": "Video Credit", "title": "Title", "type": "Type", "mediaType": "Media Type", "contentTypes": "Content Types", "action": "Action", "contentType": "Content Type", "learningSeries": "Learning Series", "uploadRequired": "Uploading Required", "watchRequired": "Watching Required", "Accessibility": "Accessibility", "status": "Publish", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "lastUpdatedDate": "Updated Date", "isDeleted": "Deleted?", "isActive": "Active?", "categoryId": "Program", "subCategoryId": "Course", "languageId": "Language", "videoTitle": "Video Title", "description": "Description", "videoUrl": "Video Url", "publishedForTrainingFeed": "Publish", "commonVideoTitle": "Common Video Title", "groupCode": "Group Code", "url": "url", "Views": "Views", "Counts": "<PERSON>licks"}, "UserAssignTraining": {"objName": "User Assign Trainings", "objNames": "User Assign Trainings", "Detail": "User Assign Trainings Detail", "ADD_NEW_USERASSIGNTRAINING": "Add New User Assign Trainings", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "userId": "User", "trainingId": "Training", "status": "Status", "userVideoUrl": "User Video Url", "assignedDate": "Assigned Date", "farmId": "Farm"}, "FarmAssignTraining": {"objName": "Farm Assign Trainings", "objNames": "Farm Assign Trainings", "Detail": "Farm Assign Trainings Detail", "ADD_NEW_FARMASSIGNTRAINING": "Add New Farm Assign Trainings", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "trainingId": "Training", "farmId": "Farm", "assignedDate": "Assigned Date", "allUserAssigned": "All User Assigned"}, "UsersContactList": {"fullName": "Name", "email": "Email", "phoneNumber": "Phone Number", "message": "Message", "countryCode": "Country Code"}, "RequestSiteChangeList": {"fullName": "First Name", "status": "Status", "message": "Message", "sitesRequest": "Sites Request", "action": "Action"}, "MomentRejectedLogs": {"fullName": "Full Name", "staffUploadedDate": "Staff Uploaded Date", "momentCreatedDate": "Moment Created Date", "momentRejectionDate": "Moment Rejection Date", "staffVideoOrImage": "Staff Video/Image"}, "MomentRecurringLogs": {"fullName": "Full Name", "staffUploadedDate": "Staff Uploaded Date", "momentCreatedDate": "Moment Created Date", "staffVideoOrImage": "Staff Video/Image"}, "Moment": {"objNames": "Moments", "objName": "Moment", "Instruction": "Instruction", "Detail": "Moment Detail", "ADD_NEW_MOMENT": "Add New Moment", "id": "id", "title": "Title", "selectSite": "Select Site", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "farmId": "Farm", "SelectType": "Select Type", "description": "Description", "mediaType": "Media Type", "mediaUrl": "Media Url", "assignTrainingId": "Assign Training", "userVideo": "User Video", "instruction": "Instruction", "completedBy": "Completed By", "status": "Status", "momentRecurring": "Recurring Moment", "UPLOAD_IMAGE_OR_VIDEO": "UPLOAD MOMENT VIDEO/IMAGE"}, "UserTrainingStatus": {"objName": "User Training Status", "objNames": "User Training Status", "Detail": "User Training Status Detail", "ADD_NEW_USERTRAININGSTATUS": "Add New User Training Status", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "userId": "User", "farmId": "Farm", "traningId": "Traning", "rejectCompletionDate": "Reject Completion Date", "userVideoUrl": "User Video Url", "isApproved": "Is Approved", "assignedDate": "Assigned Date"}, "Course": {"objName": "Course", "objNames": "Courses", "id": "id", "tenantId": "Tenant", "slug": "Slug", "title": "Course Title", "parttitle": "Part Title", "name": "Name", "fullName": "Full Name", "programAdminName": "Program", "userName": "User Name", "phoneNumber": "Phone Number", "email": "Email", "code": "Course Code", "status": "Course Status", "description": "Description", "certificate": "Course Certificate", "chooseCourse": "Choose Course", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "lastUpdatedDate": "Last Updated Date", "isDeleted": "Deleted?", "isActive": "Active?", "action": "Action", "completion": "Course Completion", "chooseFrequency": "<PERSON><PERSON>ncy", "programCourse": "Program Admin Course", "courseDescription": "About This Course"}, "courseLearning": {"id": "id", "title": "Learning Series Title", "description": "Description", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "lastUpdatedDate": "last Updated Date", "isDeleted": "Deleted?", "isActive": "Active?", "action": "Action"}, "courseTraining": {"id": "id", "title": "Learning Series", "description": "Description", "contentType": "Content Type", "trainingHistory": "Training", "trainingStatus": "Status", "UserVideo": "Uploaded Video", "course": "Course", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "lastUpdatedDate": "Last Updated Date", "isDeleted": "Deleted?", "isActive": "Active?", "coursePart": "Part", "action": "Action"}, "ErrorCodeMessages": {"1001": "Some Error during selection of 'Users'", "1002": "Some Error during fetch of 'Users'", "1003": "Some Error during creation of new 'Users'", "1004": "Some Error during modification of 'Users'", "1005": "Some Error during removal of 'Users'", "2001": "Some Error during selection of 'Farms'", "2002": "Some Error during fetch of 'Farm'", "2003": "Some Error during creation of new 'Farm'", "2004": "Some Error during modification of 'Farm'", "2005": "Some Error during removal of 'Farm'", "3001": "Some Error during selection of 'Languages'", "3002": "Some Error during fetch of 'Language'", "3003": "Some Error during creation of new 'Language'", "3004": "Some Error during modification of 'Language'", "3005": "Some Error during removal of 'Language'", "4001": "Some Error during selection of 'Categories'", "4002": "Some Error during fetch of 'Category'", "4003": "Some Error during creation of new 'Category'", "4004": "Some Error during modification of 'Category'", "4005": "Some Error during removal of 'Category'", "5001": "Some Error during selection of 'Sub Categories'", "5002": "Some Error during fetch of 'Sub Category'", "5003": "Some Error during creation of new 'Sub Category'", "5004": "Some Error during modification of 'Sub Category'", "5005": "Some Error during removal of 'Sub Category'", "6001": "Some Error during selection of 'User Farms'", "6002": "Some Error during fetch of 'UserFarm'", "6003": "Some Error during creation of new 'UserFarm'", "6004": "Some Error during modification of 'UserFarm'", "6005": "Some Error during removal of 'UserFarm'", "7001": "Some Error during selection of 'Trainings'", "7002": "Some Error during fetch of 'Training'", "7003": "Some Error during creation of new 'Training'", "7004": "Some Error during modification of 'Training'", "7005": "Some Error during removal of 'Training'", "8001": "Some Error during selection of 'User Assign Trainings'", "8002": "Some Error during fetch of 'User Assign Trainings'", "8003": "Some Error during creation of new 'User Assign Trainings'", "8004": "Some Error during modification of 'User Assign Trainings'", "8005": "Some Error during removal of 'User Assign Trainings'", "9001": "Some Error during selection of 'Farm Assign Trainings'", "9002": "Some Error during fetch of 'Farm Assign Trainings'", "9003": "Some Error during creation of new 'Farm Assign Trainings'", "9004": "Some Error during modification of 'Farm Assign Trainings'", "9005": "Some Error during removal of 'Farm Assign Trainings'", "10001": "Some Error during selection of 'Moments'", "10002": "Some Error during fetch of 'Moment'", "10003": "Some Error during creation of new 'Moment'", "10004": "Some Error during modification of 'Moment'", "10005": "Some Error during removal of 'Moment'", "11001": "Some Error during selection of 'User Training Status'", "11002": "Some Error during fetch of 'User Training Status'", "11003": "Some Error during creation of new 'User Training Status'", "11004": "Some Error during modification of 'User Training Status'", "11005": "Some Error during removal of 'User Training Status'"}, "Notification": {"message": "Message", "markAsRead": "<PERSON>", "createdOn": "Created On"}, "Supplier": {"objName": "Supplier", "objNames": "Suppliers", "Detail": "Supplier Detail", "ADD_NEW_SUPPLIER": "Add Supplier", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "PhoneNumber": "Phone Number", "Active": "Active", "Action": "Action", "MobileNo": "Mobile No", "Status": "Status", "language": "Language", "companyName": "Team Name", "companyCode": "Team Code"}}