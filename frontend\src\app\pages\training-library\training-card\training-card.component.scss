:host {
  display: block;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.training-card {
  border-radius: 16px;
  overflow: hidden;
  width: 100%;

  &-content {
    padding: 16px 0;
  }

  &-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  &-author {
    display: flex;
    align-items: center;

    &-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    &-name {
      font-weight: bold;
      color: #333;
      position: relative;
    }
  }

  &-date {
    font-weight: 600;
    margin-left: 20px;
    margin-top: 12px;
  }

  &-title {
    margin: 0;
    font-size: 12px;
    font-weight: 600;
  }

  &-divider {
    border: none;
    border-top: 1px solid #707070;
    margin: 8px 0;
  }

  &-footer {
    margin-top: 16px;
  }

  &-description {
    margin: 14px 0 0;
    color: #666;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    height: 50px;
  }
}

@media (min-width: 1400px) {
  .training-card {
    &-title {
      font-size: 16px;
    }
  }
}

.video-container {
  position: relative;
  width: 100%;
  padding-top: 53.25%;
  /* 16:9 Aspect Ratio */
  overflow: hidden;
  border-radius: 16px;
  background-color: #000;

  &:hover {
    cursor: pointer;
  }
}

// .training-card-video {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   object-fit: contain;
//   background-color: #000;
//   /* Ensures the video or poster image fills the container without distortion */
// }

.training-card-video {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: #000;
  right: 0;
  transform: translate(-50%, -50%);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: opacity 0.3s;
  opacity: 1;
  border-radius: 16px;
  pointer-events: none;
}

.video-container:hover .video-overlay {
  opacity: 0;
}

.landscape-video {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.portrait-video {
  height: 100%;
  width: auto;
  object-fit: cover;
}