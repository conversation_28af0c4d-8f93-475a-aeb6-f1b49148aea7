import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import AOS from 'aos';
import * as moment from 'moment';
import { Course } from 'src/app/models/course';
import { ManageCourseManager } from '../manage-course/manage-course-manager';
import { ToastService } from 'src/app/shared/toast.service';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { AuthService } from 'src/app/shared/auth.services';
import { ManageCourseService } from '../manage-course/manage-course-service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { UserMyCourseService } from './user-my-course-service';
import { UserMyCourseManager } from './user-my-course-manager';
import { Users } from 'src/app/models/users';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { saveAs } from 'file-saver';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { FilterParam } from 'src/app/models/filterparam';
import { RouteDataService } from 'src/app/shared/title.service';
import { LayoutComponent } from '../layout/layout.component';
import { ManageCourseUserService } from '../program-users/manage-course-users-service';
import { UsersService } from 'src/app/services/users.service';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-user-my-course',
  templateUrl: './user-my-course.component.html',
  styleUrls: ['./user-my-course.component.scss']
})
export class UserMyCourseComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  moment: any = moment;
  courses: Course;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  record: any = { isPublish: false };
  strUserId: string;
  username: any;
  viewUserCourseModal: any;
  trainingRecords: Course[];
  searchTraining: any;
  filterTrainingModal: any;
  isTrainingCsvExport: boolean = false
  courseViewTrainingVideoModal: any;
  loadingVideo: boolean;
  usersInviteUserManageCourseModal: any;
  @Input() filterParam: FilterParam;
  @Input() type: string;
  inviteUserManageCourseModal: any;
  selectedCourseUserId: string;
  selectedCourseProgramUserId: string;
  insertedData: { username: string, email: string, id }[] = [];
  isSelfInvite: boolean = true; // Default to self-invite
  publishCourseList: any[];
  programCourseList: any[];
  onClickValidation = false;
  errorMessage: string = '';
  user: Users;



  constructor(protected userMyCourseManager: UserMyCourseManager, protected toastService: ToastService,private usersService: UsersService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService, public routeDataService: RouteDataService,
    protected router: Router, public commonUtil: CommonUtil, private userMyCourseService: UserMyCourseService, protected route: ActivatedRoute, private http: HttpClient, private loadVideoFromUrl: LoadVideoFromUrl, private manageCourseUserService: ManageCourseUserService) {
    super(userMyCourseManager, commonService, toastService, loadingService, router);

  }

  async ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    // this.filterParam = new FilterParam();
    // this.records = new Array<Course>();
    // this.trainingRecords = new Array<Course>();
    // this.request.loadEditPage = false;
    // this.courses = new Course();
    this.records = [] as Users[];
    // this.isPlusButton = false
    this.strUserId = this.route.snapshot.paramMap.get('id');
    this.filterParam.strUserId = this.strUserId;
    // this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.userMyCourseService.getUserId(this.strUserId);
    this.fetchPublishRecords(this.filterParam);
    this.init();
    this.fetchUserDetail()
  }

  // async fetchRecords(param, callBack) {
  //   try {
  //     this.hasDataLoad = false;
  //     this.setParam(param);
  //     this.loadingService.show();
  //     const response: RestResponse = await this.userMyCourseManager.fetch(this.strUserId);
  //     this.loadingService.hide();
  //     if (!response.status) {
  //       this.toastService.error(response.message);
  //       return;
  //     }
  //     this.records = response.data;
  //     this.onFetchCompleted();
  //     callBack({ recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length, recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length, data: [] });
  //   } catch (error) {
  //     this.loadingService.hide();
  //     this.toastService.error(error.message);
  //   }
  // }

  closeInviteModal() {
    this.usersInviteUserManageCourseModal.hide();
    this.courses = new Course();
  }

  ngOnDestroy() {
    this.clean();
  }

  removeData(id: string, path?: string) {
    this.commonService.confirmation('Would you like to revoke access?', this.removeCallback.bind(this), id, path);
  }

  async removeCallback(id: string, path?: string) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userMyCourseService.remove(id);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.removeSuccess();
      // this.init();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  // fetch view training
  async fetchCourseUserRecord(course, userId) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userMyCourseService.getViewTraining(course, userId).toPromise();
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainingRecords = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // view user course training Modal

  ngAfterViewInit() {
    setTimeout(() => {
      this.viewUserCourseModal = new bootstrap.Modal(
        document.getElementById('viewUserCourseModal')
      );
    }, 0);
    setTimeout(() => {
      this.courseViewTrainingVideoModal = new bootstrap.Modal(
        document.getElementById('courseViewTrainingVideoModal')
      );
    }, 0);
    setTimeout(() => {
      this.inviteUserManageCourseModal = new bootstrap.Modal(
        document.getElementById('inviteUserManageCourseModal')
      );
    }, 0)
  }

  openInviteCourseUserModal() {
    AOS.init({ disable: true });
    this.inviteUserManageCourseModal.show();
    this.courses = new Course();
    this.selectedCourseUserId = null;
    this.selectedCourseProgramUserId = null;
    this.insertedData = new Array<any>();
    this.fetchPublishRecords();
    if (this.authService.getRoles().includes('ROLE_ADMIN')) {
      this.fetchProgramActiveUserRecords();
    }

  }

  //program active user course
  async fetchProgramActiveUserRecords(filterParam?: FilterParam) {
    try {
      var course = new Course();
      course.id = this.selectedCourseProgramUserId;
      this.loadingService.show();
      const response: RestResponse = await this.userMyCourseService.getProgramCourse(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.programCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  //course user
  async fetchPublishRecords(param?: FilterParam) {
    try {
      // var course = new Course();
      // course.id = this.selectedCourseId;
      this.selectedCourseUserId = null;
      this.loadingService.show();
      const response: RestResponse = await this.userMyCourseService.getisPublish(param);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async onClickInviteUserCourse() {
    if (this.insertedData.length == 0) {
      return;
    }
    try {
      this.loadingService.show();
      this.courses.courseUsersMapping = this.insertedData;
      this.courses.id = this.selectedCourseUserId;

      if (this.authService.getRoles().includes('ROLE_ADMIN')) {
        this.courses.programAdmin = this.isSelfInvite ? this.authService.getUser().id : this.selectedCourseProgramUserId;
      }

      const response: RestResponse = (this.authService.getRoles().includes('ROLE_ADMIN')) ? await this.manageCourseUserService.sendAdminCourseInvite(this.courses)
        : await this.manageCourseUserService.sendCourseInvite(this.courses);

      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }

      this.insertedData = [];
      this.courses = new Course();
      this.inviteUserManageCourseModal.hide();
      this.onSaveSuccess(response.message);
      this.fetchCourseUsersData();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }


  async fetchCourseUsersData() {

    const resp: RestResponse = await this.userMyCourseService.getCourseUserRecords(this.filterParam);
    this.loadingService.hide();
    if (!resp.status) {
      return;
    }
    this.records = resp.data;
  }

  insertData(form: any, id: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$/;

    // if (!this.courses.username || !this.courses.email) {
    //   this.errorMessage = 'Name and email are required.';
    //   return;
    // }

    if (!emailPattern.test(this.courses.email)) {
      this.errorMessage = 'Invalid email format.';
      return;
    }

    const isDuplicate = this.insertedData.some(data => data.email === this.courses.email);

    if (isDuplicate) {
      this.errorMessage = 'This email is already entered.';
    } else {
      this.insertedData.push({ username: this.courses.username, email: this.courses.email, id: this.courses.id });
      this.errorMessage = '';  // Clear error message if the data is successfully inserted
      this.courses = new Course();
    }
  }

  removeInviteData(index: number) {
    this.insertedData.splice(index, 1);
    this.errorMessage = '';  // Clear error message
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.router.navigate(['/dashboard/program-admin/courses']);
  }


  openviewUserCourseModal(course) {
    // if (this.searchTraining) {
    //   this.searchTraining = "";
    //   this.onCancel();
    // }
    this.fetchCourseUserRecord(course, this.strUserId);
    AOS.init({ disable: true });
    this.viewUserCourseModal.show();
  }
  // filter
  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }
  resetFilter() {
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.searchText;
    delete this.filterParam.status;
    this.filterTrainingModal.hide();
    this.onCancel();
  }
  onClickTrainingFilter() {
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  // export Csv
  exportTrainingHistroyCsv() {
    this.isTrainingCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], this.username + '_Course Report_' + moment().format('DD-MM-YYYY') + '.xlsx', { type: 'application/vnd.ms.excel' });
        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isTrainingCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isTrainingCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isTrainingCsvExport = false;
      return;
    }
  }


  download(): Observable<Blob> {
    const strUserId = this.route.snapshot.paramMap.get('id');
    return this.http.post(environment.BaseApiUrl + '/api/courses/' + 'userid' + '/export',
      this.filterParam, { responseType: 'blob' }).pipe(catchError(this.parseErrorBlob));
  }

  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isTrainingCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }

  //cours detail page linking
  openCourseDetailPage(id: any) {
    this.router.navigate(['/dashboard/program-admin/course/detail/' + id]);
  }

  removeSuccess() {
    this.refreshRecord();
  }
  // onFetchCompleted() {
  //   this.hasDataLoad = true;
  //   this.routeDataService.setData(this.router.url, this.courses.courseDetail.userDetails.fullName);
  // }

  openTrainingDetailPage(record: any) {
    window.open(
      '/dashboard/program-admin/course/training/detail/' + record.trainingDetail.id,
      '_blank'
    );
  }

  watchVideo(record: any) {
    this.loadingVideo = true
    this.courseViewTrainingVideoModal.show();
    AOS.init({ disable: true });
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.videoUrl);
      this.loadingVideo = false;
    }, 0)
  }

  approveOrRejectTraining(id: string, status: string) {
    let data = {
      id: id,
      userId: this.strUserId,
      status: status
    }
    const statusText = status == "APPROVED" ? 'approve' : 'reject';
    const confirmatiomMessage = 'Would you like to ' + statusText + ' this training?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectTrainingCallback.bind(this), data);
  }

  async approveOrRejectTrainingCallback(data: boolean) {
    try {
      const response: RestResponse = await this.userMyCourseService.approveOrRejectTraining(data);
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
      // Wait for the toast to display and then refresh the page
      setTimeout(() => {
        window.location.reload();
      }, 1500); // Adjust the delay if needed to allow the user to see the success message

    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

  //Breadcrumb set
  onFetchCompleted() {
    let obj: any = {};
    obj.type = "TEXT_REPLACE";
    obj.title = this.username;
    // this.commonService.updateData(obj);
    // this.routeDataService.setData(this.router.url, this.username);
  }

  async fetchUserDetail() {
    try {
      const id=this.route.snapshot.paramMap.get('id')
      const response: RestResponse = await this.usersService.fetch(id).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.request.isNewRecord = false;
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      this.setbrreadCrumbTitle();
      // this.setSelectedFarmsUserEdit(userFarmMapping);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }


  setbrreadCrumbTitle(){
    // this.layoutComponent.breadcrumbs.forEach((data, index) => {
    //   if (index == this.layoutComponent.breadcrumbs.length - 1) {
    //     data.title = this.user.fullName;
    //   }
    // })
    // this.layoutComponent.breadcrumbs = this.layoutComponent.breadcrumbs
    // this.routeDataService.setData(this.router.url, this.user.fullName);
        this.routeDataService.setData(this.router.url, this.user.fullName);
  }

}
