import { Routes } from '@angular/router';
import { AuthGuard } from './shared/auth.guard';
import { LoginComponent } from './pages/login/login.component';
import { ForbiddenComponent } from './pages/forbidden/forbidden.component';
import { NotFoundComponent } from './pages/not-found/not-found.component';
import { RecoverPasswordComponent } from './pages/recover-password/recover-password.component';
import { ForgotPasswordComponent } from './pages/forgot-password/forgot-password.component';
import { RegisterUserComponent } from './pages/register-user/register-user.component';
import { VerifyOtpComponent } from './pages/verify-otp/verify-otp.component';
import { StaffHomepageComponent } from './pages/staff-homepage/staff-homepage.component';
import { SetNewPasswordComponent } from './pages/set-new-password/set-new-password.component';
import { Constant } from './config/constants';
import { ConfirmEmailComponent } from './confirm-email/confirm-email.component';
import { TAndCComponent } from './t-and-c/t-and-c.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { ProgramAdministratorRegisterComponent } from './pages/program-administrator-register/program-administrator-register.component';

export const ROUTES: Routes = [
  { path: '', canActivate: [AuthGuard], component: StaffHomepageComponent, data: { roles: ['ROLE_ANONYMOUS'] } },
  { path: 'login', canActivate: [AuthGuard], component: LoginComponent, data: { roles: ['ROLE_ANONYMOUS'] } },
  { path: 'register', canActivate: [AuthGuard], component: RegisterUserComponent, data: { roles: ['ROLE_ANONYMOUS'] } },
  { path: 'program-register', canActivate: [AuthGuard], component: ProgramAdministratorRegisterComponent, data: { roles: ['ROLE_ANONYMOUS'] } },
  { path: 'verify-otp', canActivate: [AuthGuard], component: VerifyOtpComponent, data: { roles: ['ROLE_ANONYMOUS'] } },
  {
    path: 'account/recover',
    canActivate: [AuthGuard],
    component: RecoverPasswordComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'gp',
    canActivate: [AuthGuard],
    component: SetNewPasswordComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'forgot-password',
    canActivate: [AuthGuard],
    component: ForgotPasswordComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'confirm-email',
    canActivate: [AuthGuard],
    component: ConfirmEmailComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'terms-and-conditions',
    canActivate: [AuthGuard],
    component: TAndCComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'privacy-policy',
    canActivate: [AuthGuard],
    component: PrivacyPolicyComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./pages/layout/layout.module').then(m => m.LayoutModule),
    canActivate: [AuthGuard],
    data: { roles: [Constant.ROLES.SUPER_ADMIN, Constant.ROLES.ADMIN, Constant.ROLES.ROLE_PROGRAM_ADMINISTRATOR, Constant.ROLES.FARM_ADMIN] }
  },
  {
    path: 'terms-and-conditions',
    canActivate: [AuthGuard],
    component: TAndCComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: 'privacy-policy',
    canActivate: [AuthGuard],
    component: PrivacyPolicyComponent,
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: '',
    loadChildren: () => import('./pages/layout/layout.module').then(m => m.LayoutModule),
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_STAFF'] }
  },
  { path: '403', component: ForbiddenComponent },
  { path: '404', component: NotFoundComponent },
  { path: '**', redirectTo: '/404' }
];
