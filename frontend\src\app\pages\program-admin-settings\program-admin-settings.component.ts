import { Component, OnInit } from '@angular/core';
import { LocalStorageService } from 'angular-2-local-storage';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { Users } from 'src/app/models/users';
import { AccountService } from 'src/app/services/account.service';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';
import AOS from 'aos';
import { Constant } from 'src/app/config/constants';
import { FilterParam } from 'src/app/models/filterparam';
import { ActivatedRoute, Router } from '@angular/router';
import { VideoTraining } from 'src/app/models/trainingLibrary';
declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-program-admin-settings',
  templateUrl: './program-admin-settings.component.html',
  styleUrls: ['./program-admin-settings.component.scss']
})
export class ProgramAdminSettingsComponent implements OnInit {
  user: Users;
  onClickValidation: boolean = false;
  profileImageLoader: boolean = false;
  programProfileImg: boolean = false;
  uploadingProgramImage: boolean = false;
  uploader: any;
  fileUploadType: any;
  public uploadingImage: boolean;
  fileUploadingMessage: string = "UPLOADING..";
  programImage: boolean;

  // content transfer
  dropdownSettingsForUsers = {};
  selectedProgramAdmin: string | null = null; // Initialize with null or appropriate default value
  readonly MY_CONSTANT = Constant;
  users: Users[];
  transferUser: Users;
  contentTransferModal: any;
  selectedOption: string = '';
  selectedAdmin: string = '';
  deletedUsersSelected: any[] = [];
  usersSelectedId: string;
  training: VideoTraining;
  filterParam: FilterParam;
  selectedAuthorProfileId: string;
  isAuthor: boolean;

  constructor(private accountService: AccountService, private toastService: ToastService, protected route: ActivatedRoute, private authService: AuthService, private localStorageService: LocalStorageService, private loadingService: LoadingService, protected commonService: CommonService, protected router: Router) {
    this.user = new Users();
  }

  ngOnInit() {
    this.users = new Array<Users>();
    this.transferUser = new Users();
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)
    this.fileUploadType = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)

    this.selectedAuthorProfileId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);

    if (this.selectedAuthorProfileId && this.selectedAuthorProfileId != '0') {
      // If the ID exists, assign it to filterParam and fetch profile data
      // Fetch profile data when ID is present
      this.fetchAuthorProfile(this.selectedAuthorProfileId);
    } else {
      this.fetchProfileData();
    }
    this.setDropdownSettingsForUsers();
    this.fetchUsersList();
  }


  setDropdownSettingsForUsers() {
    this.dropdownSettingsForUsers = {
      singleSelection: true,  // Set this to true for single select
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  async fetchProfileData() {
    try {
      const response: RestResponse = await this.accountService.fetchMe().toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.user = response.data;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  async fetchAuthorProfile(id) {
    try {
      const response: RestResponse = await this.accountService.fetchAuthodProfile(id).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.user = response.data;
      this.isAuthor = true;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  initializeUploader(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationError("Image size to too large", this.toastService);
          }, 200);

          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationError("only jpg,png,jpeg files are allowed", this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const image = uploadResponse[0];
        image.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(image);
        setTimeout(() => {
          this.onUploadSuccess(image);
        }, 200);
      }
    };

    return uploader;
  }

  uploadProfilePhoto(event: any) {
    if (event && event.target.files.length > 0) {
      this.profileImageLoader = true;
    }
    this.programImage = false;
  }

  // Program Profile Image Upload
  uploadProgramImageUrl(event: any) {
    if (event && event.target.files.length > 0) {
      this.uploadingProgramImage = true;
      const file = event.target.files[0];
      this.fileUploadType.addToQueue([file]);
      this.fileUploadType.onSuccessItem = (fileItem, response) => {
        const uploadResponse = JSON.parse(response);
        if (uploadResponse.length > 0) {
          this.user.programProfileImage = uploadResponse[0].path;
          this.uploadingProgramImage = false;
        }
      };
    }
    this.programImage = true;
  }

  fileValidationError(data: string, toastService: any) {
    this.profileImageLoader = false;
    this.programProfileImg = false;
    toastService.error(data);
  }

  onUploadSuccess(data: any) {
    if (!this.programImage) {
      this.user.profileImageUrl = data.path;
      this.profileImageLoader = false;
    } else {
      this.user.programProfileImage = data.path;
      this.programProfileImg = false;
    }
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  telInputObject(event: any) {
    if (this.user.countryCode && this.user.phoneNumber) {
      event.setNumber('+' + this.user.countryCode + this.user.phoneNumber);
      return
    } else {
      event.setCountry('sg')
    }

  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  hasError(event: any) {
  }

  async save(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return;
    }
    const data = {
      id: this.user.id,
      firstName: this.user.firstName,
      lastName: this.user.lastName,
      phoneNumber: this.user.phoneNumber,
      programName: this.user.programName,
      countryCode: this.user.countryCode,
      email: this.user.email,
      about: this.user.about,
      profileImageUrl: this.user.profileImageUrl,
      programProfileImage: this.user.programProfileImage
    }
    this.loadingService.show();
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.accountService['update'](data);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      this.loadingService.hide();
      this.toastService.success(response.message);
      this.updateUserDataLocalStorage();
    } catch (e) {
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  updateUserDataLocalStorage() {
    let newUserData = this.authService.getUser();
    const { firstName, lastName, profileImageUrl, programProfileImage, programName, about } = this.user;
    newUserData = { ...newUserData, fullName: firstName + ' ' + lastName, firstName, lastName, profileImageUrl, programProfileImage, programName, about }
    this.localStorageService.set('user', newUserData);

  }

  removeProgramImage(fileUrl: string) {
    this.commonService.confirmation('Would you like to delete?', this.removeProgramImageCallback.bind(this), fileUrl);
  }

  removeProgramImageCallback(fileUrl: string) {
    if (fileUrl === this.user.programProfileImage) {
      this.user.programProfileImage = null;
    }
  }

  removeFile(fileUrl: string) {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), fileUrl);
  }

  removeFileCallback(fileUrl: string) {
    0
    if (fileUrl === this.user.profileImageUrl) {
      this.user.profileImageUrl = null;
    }
  }

  //content transfer

  addUser(event: any) {
    let user = new Users();
    user.userId = event.id;
    console.log(user, "user");
    this.users.push(user);
  }

  // removeUser(event: any) {
  //   let index = this.users.findIndex(user => user.userId == event.id);
  //   if (this.user[index].id) {
  //     this.user[index].isDeleted = true;
  //     this.deletedUsersSelected.push(this.user[index]);
  //     this.users.splice(index, 1);
  //   } else {
  //     this.users.splice(index, 1);
  //   }
  // }

  // deleteAllSelectedUsers() {
  //   if (this.user && this.users.length > 0) {
  //     this.users.map(user => {usersDropdown
  //       if (user.id) {
  //         this.deletedUsersSelected.push({
  //           ...user,
  //           isDeleted: true
  //         })
  //       }
  //       return user;
  //     })
  //   }
  //   this.users = [];
  //   this.usersSelected = [];
  // }

  async fetchUsersList() {
    let param = new FilterParam();
    const response: RestResponse = await this.accountService.fetchActiveUsers(param);
    this.users = response.data;
    this.users.forEach(x => x.fullNameAndEmail = x.fullName + " (" + x.email + ")");
  }

  closeContentTransferModal() {
    this.contentTransferModal.hide();
    // this.user = new Users();
  }

  openContentTransferModal() {
    AOS.init({ disable: true });
    this.transferUser = new Users();
    this.contentTransferModal.show();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.contentTransferModal = new bootstrap.Modal(
        document.getElementById('contentTransferModal')
      );
    }, 0)
  }

  selectContentRequired(event: boolean, type): void {
    this.selectedProgramAdmin = type;
    // Reset the opposite radio button value
    if (type === this.MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXISTING_PROGRAM_ADMIN) {
      this.user.existingProgramAdmin = event;
      this.user.externalProgramAdmin = false;
    } else if (type === this.MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXTERNAL_PROGRAM_ADMIN) {
      this.user.existingProgramAdmin = false;
      this.user.externalProgramAdmin = event;
    }
  }

  onlyAllowNumbers(event: KeyboardEvent): void {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }

  async SendContentTransfer(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    // let data: any;
    // if (this.selectedProgramAdmin === this.MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXISTING_PROGRAM_ADMIN) {
    //   // Data for existing program admin
    //   data = {
    //     user: this.usersSelectedId,
    //     isActive: this.transferUser.isActive,
    //     isDeleted: this.transferUser.isDeleted,
    //   };
    // } else if (this.selectedProgramAdmin === this.MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXTERNAL_PROGRAM_ADMIN) {
    //   // Data for external program admin
    //   data = {
    //     firstName: this.transferUser.firstName,
    //     lastName: this.transferUser.lastName,
    //     email: this.transferUser.email,
    //     isActive: this.transferUser.isActive,
    //     isDeleted: this.transferUser.isDeleted,
    //   };
    // }
    this.loadingService.show();
    try {
      const response: RestResponse = await this.accountService.saveContentTransfer(this.transferUser);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      $("#contentTransferModal").modal("hide");
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }


  onSaveSuccess(message: any) {
    this.toastService.success(message);
    // this.navigate('/dashboard/program-admin/');
  }

}


