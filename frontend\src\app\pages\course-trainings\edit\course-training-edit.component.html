<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
    <div class="dashboard-content-container d-block text-center">
        <form #trainingForm="ngForm" novalidate="novalidate" class="text-left d-inline-block custom-form">
            <div class="row">
                <div class="mt-2 mb-3">
                    <h4 class="fw-bold">{{request.recordId == 0 ? "Add New Training" : "Edit Training"}}</h4>
                    <p class="user-edit-msg">Please make sure you fill all the fields before you click on
                        {{request.recordId ==
                        0 ? 'save' : 'update'}} button
                    </p>
                </div>
            </div>
            <div class="col-12 col-md-12">
                <div class="form-floating mb-4 w-100 category-language">
                    <input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                        class="form-control" type="text" name="title" #title="ngModel" [(ngModel)]="trainings.title"
                        required="required" placeholder="{{'Training.titles' | translate}}">
                    <label for="floatingInput">{{"Training.titles" | translate}}</label>
                </div>
            </div>
            <div class="col-12 col-md-12">
                <div class=" mb-4 w-100 category-language">
                    <label class="color-dark-grey p-2" for="trainingLibraryDetail">{{"Course.description" |
                        translate}}</label>
                    <ckeditor [editor]="Editor" id="trainingLibraryDetail" name="trainingLibraryDetail"
                        #trainingLibraryDetail="ngModel" [(ngModel)]="trainings.description" [config]="editorConfig"
                        required>
                    </ckeditor>
                    <app-validation-message [field]="trainingLibraryDetail" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div>
            </div>
            <div class="col-12 col-md-12">
                <div class="form-floating">
                    <div class="mb-4 mt-2 form-control select-width ng-select-main-container"
                        [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
                        <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
                            required="required" [items]="learningSeries" bindLabel="title" bindValue="id"
                            (change)="selectLearningSeries($event)"
                            class="custom-multiselect form-control padding-bottom-8"
                            [(ngModel)]="trainings.learningSeries" #learningSeriesId="ngModel" [searchable]="false">
                        </ng-select>
                    </div>
                    <label for="language">{{"Training.learningSeries" | translate}}</label>
                </div>
            </div>
            <div class="col-12 col-md-12">
                <div class="form-floating">
                    <div class="mb-4 form-control select-width ng-select-main-container"
                        [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                        <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                            [items]="contentTypes" bindLabel="title" bindValue="id" (change)="selectContentType($event)"
                            required="required" class="custom-multiselect form-control padding-bottom-8"
                            [(ngModel)]="trainings.contentType" #contentTypeId="ngModel" [searchable]="false">
                        </ng-select>
                    </div>
                    <label for="language">{{"Training.contentTypes" | translate}}</label>
                </div>
            </div>
            <div class="row">

                <!-- <div class="col-12 col-md-6 pe-0">
                    <div class="form-floating mb-4 w-100">
                        <input [ngClass]="{'is-invalid':!sequence.valid && onClickValidation}" class="form-control"
                            type="number" name="sequence" #sequence="ngModel" [(ngModel)]="trainings.sequence"
                            required="required" placeholder="{{'Training.sequence' | translate}}">
                        <label for="floatingInput">{{"Training.sequence" |
                            translate}}</label>
                    </div>
                </div> -->
                <!-- <div class="col-12 col-md-6 ps-0">
                    <div class="form-floating mb-4 w-100 category-language">
                        <input maxlength="80" [ngClass]="{'is-invalid':!videoUrlTitle.valid && onClickValidation}"
                            class="form-control" type="text" name="videoUrlTitle" #videoUrlTitle="ngModel" [(ngModel)]="trainings.videoUrlTitle"
                            required="required" placeholder="{{'Training.videoUrlTitle' | translate}}">
                        <label for="floatingInput">{{"Training.videoUrlTitle" | translate}}</label>
                    </div>
                </div>
                <div class="col-12 col-md-6 pe-0">
                    <div class="form-floating mb-4 w-100 category-language">
                        <input maxlength="80" [ngClass]="{'is-invalid':!thumbnailImageUrlTitle.valid && onClickValidation}"
                            class="form-control" type="text" name="thumbnailImageUrlTitle" #thumbnailImageUrlTitle="ngModel" [(ngModel)]="trainings.thumbnailImageUrlTitle"
                            required="required" placeholder="{{'Training.thumbnailImageUrlTitle' | translate}}">
                        <label for="floatingInput">{{"Training.thumbnailImageUrlTitle" | translate}}</label>
                    </div>
                </div> -->
                <div class="col-12 col-md-6 ps-0">
                    <div class="video-title">
                        <div *ngIf="!trainings.url"
                            class="upload-video-container mb-4 form-control text-light bg-secondary p-3"
                            [ngClass]="{'is-invalid': !trainings.url && onClickValidation}">
                            <label *ngIf="!trainings.url"
                                [ngClass]="{'d-flex align-items-center justify-content-center': !trainings.url, 'cursor-default': loading}"
                                class="fw-bold" for="file-input">
                                <img src="/assets/images/icons/menu/upload-video-icon.svg"
                                    class="img-fluid me-2 mb-2 width-28-px text-center" alt="" />
                                {{loading ?
                                fileUploadingMessage : 'UPLOAD VIDEO HERE' }}
                                <div *ngIf="loading" class="spinner-border ms-2" role="status"
                                    style="width: 1.5rem; height: 1.5rem">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </label>
                            <input *ngIf="!trainings.url && !loading" name="trainingVideo"
                                (change)="uploadVideo($event)" id="file-input" type="file" accept="video/*" />
                        </div>
                        <div *ngIf="trainings.url" class="video-wrapper mb-4"
                            [ngStyle]="{'display': trainings.url ? 'block' : 'none' }">
                            <div class="video-container" id="video-container">
                                <div class="play-button-wrapper">
                                    <div (click)="playVideoFromPlayIcon()" title="Play video"
                                        class="play-gif circle-play-b-cls" id="circle-play-b">
                                        <!-- SVG Play Button -->
                                        <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 80 80">
                                            <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="position-absolute delete-video-container"
                                    (click)="removeFile(trainings.url)">
                                    <i class="bi bi-x"></i>
                                </div>
                                <video playsinline class="mw-100" [src]="trainings.url" id="training_video"
                                    controlslist="nodownload">
                                    Your browser does not support HTML video.
                                </video>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 pe-0">
                    <div *ngIf="!trainings.thumbnailImageUrl"
                        class="mb-4  form-control text-center border-2 border-dark upload-img-container"
                        [ngClass]="{'is-invalid': !trainings.thumbnailImageUrl && onClickValidation}">
                        <label id="file-input-thumbnail" class="upload-img-button cursor-pointer mt-1"
                            [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingThumbnail}"><img
                                src="/assets/images/icons/menu/image-thumbnail.svg"
                                class="me-2 mb-2 width-28-px upload-icon text-center" alt="">
                            <br>
                            {{!uploadingThumbnail ? ('UPLOAD VIDEO THUMBNAIL' | translate) : ('UPLOADING THUMBNAIL..' |
                            translate) }}
                            <div *ngIf="uploadingThumbnail" class="spinner-border ms-2" role="status"
                                style="width: 1.5rem; height: 1.5rem; padding:10px;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <input *ngIf="!uploadingThumbnail" name="thumbnailUrl" class="d-none"
                                (change)="uploadThumbnail($event)" id="file-input-thumbnail" type="file"
                                accept="image/png, image/jpg, image/jpeg" />
                        </label>
                    </div>
                    <div *ngIf="trainings.thumbnailImageUrl" class="moment-image-container mb-4 max-width-none">
                        <img [src]="trainings.thumbnailImageUrl" />
                        <div class="position-absolute delete-video-container"
                            (click)="removeFile(trainings.thumbnailImageUrl)">
                            <i class="bi bi-x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-12 clearfix">
                <div class="form-check">
                    <input class="form-check-input custom-form-check ms-0" type="checkbox" name="isTermsConditions"
                        [checked]="trainings.isTermsConditions"
                        [ngClass]="{'is-invalid':!isTermsConditions.valid && onClickValidation}" id="flexCheckDefault"
                        [(ngModel)]="trainings.isTermsConditions" #isTermsConditions="ngModel" required="required">
                    <label class="form-check-label ms-2 mt-1" for="flexCheckDefault">
                        I have read and accept the
                        <a href="https://integrax.co/terms-and-conditions" target="_blank">
                            Terms & Conditions
                        </a>
                        for uploading this video to <span class="fw-bold">Integrax</span>
                    </label>
                </div>
            </div>
            <div class="col-12 col-md-12">
                <div class="d-flex justify-content-start align-items-center">
                    <span class="me-2 text-start">
                        <label for="flexSwitchCheckChecked" class="form-label">Private</label>
                    </span>
                    <div class="form-check form-switch d-inline-block">
                        <input (change)="selectAccessibility($event)"
                            class="form-check-input toggle-width custom-switch" type="checkbox"
                            id="flexSwitchCheckChecked" [checked]="trainings.accessibility === 'Public'">
                    </div>
                    <span class="ms-2">
                        <label for="flexSwitchCheckChecked" class="form-label">Public</label>
                    </span>
                </div>
            </div>
            <!-- <div class="col-12 col-md-12">
                <div class="form-check publish-training-check-cls">
                    <input class="form-check-input" type="checkbox" name="isPublish" value="" id="flexCheckDefault"
                        [(ngModel)]="trainings.isPublish">
                    <label class="form-check-label" for="flexCheckDefault">
                        {{"Training.publishedForTrainingFeed" | translate}}
                    </label>
                </div>
            </div> -->
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <div>
                        <button class="btn btn-danger site-button btn-sm large-button save-button rounded-3"
                            [routerLink]="['/dashboard/program-admin/trainings']">
                            {{ 'Cancel' }}
                        </button>
                    </div>
                    <div class="d-flex">
                        <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3 me-2"
                            type="button" (click)="saveTrainingsForm(trainingForm.form,!trainings.isPublish,'Update_Publish' )">
                            {{!trainings.isPublish ? 'publish':'Unpublish'}}
                        </button>
                        <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                            type="button" (click)="saveTrainingsForm(trainingForm.form, false, 'Update')">
                            {{request.recordId ==
                            0 ? 'SAVE As Draft' : 'UPDATE'}}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>