import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageAdminProgramUserService } from './manage-admin-program-user.service';
import { ManageAdminProgramUserManager } from './manage-admin-program-user.manager';
import { ProgramAdminActiveUserComponent } from '../program-admin-active-user/program-admin-active-user.component';
import { ProgramAdminInvitedUserComponent } from '../program-admin-invited-user/program-admin-invited-user.component';
import { ProgramAdminInactiveUserComponent } from '../program-admin-inactive-user/program-admin-inactive-user.component';
import { UsersService } from 'src/app/services/users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { RouteDataService } from 'src/app/shared/title.service';
import { Users } from 'src/app/models/users';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-admin-program-user',
  templateUrl: './manage-admin-program-user.component.html',
  styleUrls: ['./manage-admin-program-user.component.scss']
})
export class ManageAdminProgramUserComponent extends BaseListServerSideComponent implements OnInit {
  userDetailsTabs: string = "active";
  @ViewChild(ProgramAdminActiveUserComponent) programAdminActiveUserComponent: ProgramAdminActiveUserComponent;
  @ViewChild(ProgramAdminInvitedUserComponent) programAdminInvitedUserComponent: ProgramAdminInvitedUserComponent;
  @ViewChild(ProgramAdminInactiveUserComponent) programAdminInactiveUserComponent: ProgramAdminInactiveUserComponent;

  currentActiveTab: string;
  userDetails: boolean = true;
  filterParam: FilterParam;
  searchText: string;
  filterTrainingModal: any;
  selectedCourseUserId: string;
  selectedCourseUser: any;
  strProgramAdmin: string
  // courses: Course;
  selectedCourseId: any;
  moment: any = moment;
  publishCourseList: any[];
  user: Users;


  constructor(private route: ActivatedRoute, public authService: AuthService, protected router: Router, private zone: NgZone, protected toastService: ToastService,
    protected loadingService: LoadingService, private usersService: UsersService,public routeDataService: RouteDataService,
    protected manageAdminProgramUserService: ManageAdminProgramUserService, protected manageAdminProgramUserManager: ManageAdminProgramUserManager,
    protected commonService: CommonService, public commonUtil: CommonUtil) {
    super(manageAdminProgramUserManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.filterParam = new FilterParam();
    this.route.queryParams.subscribe(params => {
      this.currentActiveTab = params.tab ?? 'active';
    });
    this.publishCourseList = new Array<any>();
    this.userDetailsTabs = this.currentActiveTab ? this.currentActiveTab : "active";
    this.strProgramAdmin = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    // this.fetchPublishRecords();
    // this.fetchUserDetail()
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    if (this.userDetailsTabs === "active") {
      this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    }
    this.filterChildRecords();

  }

  selectInviteUserCourse(event: any): void {
    if (event) {
      this.selectedCourseUser = event.title;
      this.selectedCourseUserId = event ? event.id : null;
    }
  }

  filterChildRecords() {
    switch (this.currentActiveTab) {
      case 'active':
        this.programAdminActiveUserComponent.refreshRecord();
        break;
      case 'invited':
        this.programAdminInvitedUserComponent.refreshRecord();
        break;
      case 'inactive':
        this.programAdminInactiveUserComponent.refreshRecord();
        break;
    }
  }

  //filter
  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('filterTrainingModal');
      if (modalElement) {
        this.filterTrainingModal = new bootstrap.Modal(modalElement);
      }
    }, 0);
  }

  resetFilter() {
    delete this.filterParam.course;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    this.selectedCourseUserId = null;
    this.filterTrainingModal.hide();
    // this.filterChildRecords();
    // this.onCancel();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }


  onClickUserDetailsTab(name: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          // queryParams: { tab: name },
          queryParamsHandling: 'merge'
        });
    });
    this.userDetailsTabs = name;
    this.filterParam = new FilterParam();
  }

  onNewRecord() {
    this.router.navigate(['/dashboard/program-detail/edit/0'])
  }

  async fetchUserDetail() {
    try {
      const id = this.route.snapshot.paramMap.get('id')
      const response: RestResponse = await this.usersService.fetch(id).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.request.isNewRecord = false;
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      this.setbrreadCrumbTitle();
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setbrreadCrumbTitle() {
    this.routeDataService.setData(this.router.url, this.user.fullName);
  }

}
