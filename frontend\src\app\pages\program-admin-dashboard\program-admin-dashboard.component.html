<div data-aos="fade-up" data-aos-duration="1000" class="admin-dasboard container-fluid">
    <div class="row d-flex">
        <div class="col-12 col-lg-6">
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="inner-container">
                        <div *ngIf="chartOption">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="semi-bold ms-2">Course</h5>
                                <!-- Course Filtering Options -->
                                <button (click)="openCourseDetailFilter()" type="button"
                                    class="btn font-15px height-51px filter">
                                    <i class="bi bi-filter filter-icon"></i>Filter
                                </button>
                            </div>
                            <apx-chart [series]="chartOption?.series" [chart]="chartOption?.chart"
                                [yaxis]="chartOption.yaxis" [xaxis]="chartOption.xaxis" [stroke]="chartOption.stroke"
                                [markers]="chartOption.markers" [legend]="chartOption.legend"
                                [annotations]="chartOption.annotations" [grid]="chartOption.grid"
                                [responsive]="chartOption.responsive" [colors]="chartOption.colors"
                                [tooltip]="chartOption.tooltip">
                            </apx-chart>
                        </div>
                        <!-- <div class="user-details">
                            <h6 class="fw-bold">NUMBER OF USERS</h6>
                            <div *ngIf="courseDetail.length > 0; else noCourses">
                                <table style="border-collapse: collapse; width: 100%;">
                                    <tbody>
                                        <tr>
                                            <td style="font-size: 13px;" class="fw-bold">
                                                User Type
                                            </td>
                                            <td style="font-size: 13px;" *ngFor="let course of courseDetail">{{
                                                course.title }}</td>
                                        </tr>
                                        <tr>
                                            <td style="font-size: 13px;" class="fw-bold">
                                                Count
                                            </td>
                                            <td style="font-size: 13px;" *ngFor="let courseCount of courseDetail">
                                                {{ courseCount.courseClickCount }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <ng-template #noCourses>
                                <p>No Record Found</p>
                            </ng-template>
                        </div> -->
                    </div>
                </div>
                <div class="col-12">
                    <h5 class="semi-bold ms-2">Seekers</h5>
                </div>
                <div class="col-12">
                    <a [routerLink]="['/dashboard/program-admin/users']" [queryParams]="{ tab: 'pending' }">
                        <div class="card custom-card" style="padding: 16px;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex flex-column">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-shape icon-shape-tertiary rounded me-3 bg-light">
                                                <img src="/assets/images/icons/menu/course-request-logo.svg" alt=""
                                                    class="img-fluid">
                                            </div>
                                            <div class="d-flex flex-column">
                                                <span style="font-size: 22px;font-weight: 600;">Pending Course
                                                    Request</span>
                                                <span class="h6 mb-0" style="font-size: 16px;">
                                                    {{pendingCourseCount}} Pending /
                                                    {{totalExternalUserCount}} Requested</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-5 col-lg-6">
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="inner-container">
                        <div *ngIf="chartTrainingOption">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="semi-bold ms-2">Training</h5>
                                <!-- Training Filtering Options -->
                                <button (click)="openTainingDetailFilter()" type="button"
                                    class="btn font-15px height-51px filter">
                                    <i class="bi bi-filter filter-icon"></i>Filter
                                </button>
                            </div>
                            <apx-chart [series]="chartTrainingOption?.series" [chart]="chartTrainingOption?.chart"
                                [yaxis]="chartTrainingOption.yaxis" [xaxis]="chartTrainingOption.xaxis"
                                [stroke]="chartTrainingOption.stroke" [markers]="chartTrainingOption.markers"
                                [legend]="chartTrainingOption.legend" [annotations]="chartTrainingOption.annotations"
                                [grid]="chartTrainingOption.grid" [responsive]="chartTrainingOption.responsive"
                                [colors]="chartTrainingOption.colors" [tooltip]="chartTrainingOption.tooltip">
                            </apx-chart>
                        </div>
                        <!-- <div class="user-details">
                            <h6 class="fw-bold">NUMBER OF USER TRAININGS</h6>
                            <div *ngIf="trainingDetail.length > 0; else noTraining">
                                <table style="border-collapse: collapse; width: 100%;">
                                    <tbody>
                                        <tr>
                                            <td style="font-size: 13px;" class="fw-bold">
                                                Trainings Type
                                            </td>
                                            <td style="font-size: 13px;"
                                                *ngFor="let training of trainingDetail | slice: 0:5">
                                                {{training.title }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="font-size: 13px;" class="fw-bold">
                                                Count
                                            </td>
                                            <td style="font-size: 13px;"
                                                *ngFor="let trainingCount of trainingDetail | slice: 0:5">
                                                {{ trainingCount.trainingClickCount }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <ng-template #noTraining>
                                <p>No Record Found</p>
                            </ng-template>
                        </div> -->
                    </div>
                </div>
                <div class="col-12">
                    <h5 class="semi-bold ms-2"></h5>
                </div>
                <div class="col-12">
                    <a [routerLink]="['/dashboard/program-admin/users']" [queryParams]="{ tab: 'invited' }">
                        <div class="card custom-card" style="padding: 16px; margin-top: 22px;">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-between">
                                    <!-- Icon Section -->
                                    <div class="d-flex align-items-center">
                                        <div class="icon-shape icon-shape-tertiary rounded me-4 bg-light"
                                            style="padding: 10px;">
                                            <img src="/assets/images/icons/menu/registered.svg" alt="icon"
                                                class="img-fluid" style="height: 40px; width: auto;">
                                        </div>
                                        <!-- Text Content Section -->
                                        <div>
                                            <span style="font-size: 22px; font-weight: 600;">Pending Course
                                                Invitation</span>
                                            <br />
                                            <span class="h6 mb-0" style="font-size: 16px;">
                                                {{ unacceptedCourseCount }} Pending / {{ totalRequestCount }} Invited
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>


            </div>
        </div>
        <!-- Filter Course Modal -->
        <div class="modal fade" id="courseUserDetailFilter" tabindex="-1" aria-labelledby="courseUserDetailFilterLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="courseUserDetailFilterLabel">Filter
                            Courses</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div *ngIf="courseUserDetailFilter && courseUserDetailFilter._isShown" class="modal-body">
                        <form #FilterForm="ngForm" novalidate="novalidate">
                            <div class="form-floating">
                                <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                                    [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                                    <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                        name="selectedCourseUser" clearable="false" [items]="publishCourseList"
                                        [multiple]="true" [disabled]="filterParam?.courseIds?.length >= 5"
                                        bindLabel="title" bindValue="id" (change)="selectInviteUserCourse($event)"
                                        class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="filterParam.courseIds" #selectedCourseUser="ngModel"
                                        [searchable]="true">
                                    </ng-select>
                                </div>
                                <label for="selectedCourseUser">
                                    {{"Course.chooseCourse" | translate}}
                                </label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select form-control select-options" name="type"
                                    aria-label="Please Select Type" [(ngModel)]="filterParam.intervalType"
                                    (change)="onOptionChange($event.target.value)"
                                    [ngClass]="{'is-invalid': !type.valid && onClickValidation}" required="required"
                                    #type="ngModel">
                                    <option [ngValue]="undefined" disabled>
                                        {{ 'COMMON.SELECT_OPTION' | translate }}
                                    </option>
                                    <option value="DAILY">Daily</option>
                                    <option value="WEEKLY">Weekly</option>
                                    <option value="MONTHLY">Monthly</option>
                                    <option value="QUARTERLY">Quarterly</option>
                                    <option value="ANNUALLY">Annually</option>
                                    <option value="CUSTOM">Custom</option>
                                </select>
                                <label for="selectedCourseUser">
                                    {{"Course.chooseFrequency" | translate}}
                                </label>
                            </div>
                            <app-date-range-filter [fromDateInput]="filterParam.startDate"
                                [toDateInput]="filterParam.endDate" (fromDateOutput)="fromDateOutput($event)"
                                (toDateOutput)="toDateOutput($event)" pageType="programAdminDashboard">
                            </app-date-range-filter>
                            <div class="modal-footer">
                                <button (click)="resetFilter()" type="button"
                                    class="text-white btn btn-secondary me-2">Reset</button>
                                <button
                                    *ngIf="(!filterParam.courseIds?.length) || (filterParam.courseIds?.length > 0 && filterParam.intervalType)"
                                    (click)="onClickCourseDetailFilter(FilterForm.form.valid)" type="button"
                                    class="btn btn-primary">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!--  -->
        <!-- Filter Training Modal -->
        <div class="modal fade" id="trainingUserDetailFilter" tabindex="-1"
            aria-labelledby="trainingUserDetailFilterLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="trainingUserDetailFilterLabel">Filter
                            Training</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div *ngIf="trainingUserDetailFilter && trainingUserDetailFilter._isShown" class="modal-body">
                        <form #FilterTrainingForm="ngForm" novalidate="novalidate">
                            <div class="form-floating mb-3">
                                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                    [ngClass]="{'is-invalid': trainingDetailId.invalid && onClickValidation}">
                                    <!-- Adding the 'multiple' attribute for multi-select functionality -->
                                    <ng-select placeholder="{{ 'COMMON.SELECT_OPTION' | translate }}"
                                        name="trainingDetailId" [items]="trainingDetail" bindLabel="title"
                                        bindValue="id" [multiple]="true"
                                        [disabled]="filterTrainingParam?.trainingIds?.length >= 5"
                                        class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="filterTrainingParam.trainingIds" #trainingDetailId="ngModel">
                                    </ng-select>
                                </div>
                                <label for="language">Select Training Titles</label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select form-control select-options" name="typeTaining"
                                    aria-label="Please Select Type" [(ngModel)]="filterTrainingParam.intervalType"
                                    (change)="onOptionTrainingChange($event.target.value)"
                                    [ngClass]="{'is-invalid': !typeTaining.valid && onClickValidation}"
                                    required="required" #typeTaining="ngModel">
                                    <option [ngValue]="undefined" disabled>
                                        {{ 'COMMON.SELECT_OPTION' | translate }}
                                    </option>
                                    <option value="DAILY">Daily</option>
                                    <option value="WEEKLY">Weekly</option>
                                    <option value="MONTHLY">Monthly</option>
                                    <option value="QUARTERLY">Quarterly</option>
                                    <option value="ANNUALLY">Annually</option>
                                    <option value="CUSTOM">Custom</option>
                                </select>
                                <label for="selectedCourseUser">
                                    {{"Course.chooseFrequency" | translate}}
                                </label>
                            </div>
                            <app-date-range-filter [disabled]="!filterTrainingParam.intervalType"
                                [fromDateInput]="filterTrainingParam.startDate"
                                [toDateInput]="filterTrainingParam.endDate"
                                (fromDateOutput)="fromDateOutputTraining($event)"
                                (toDateOutput)="toDateOutputTraining($event)" pageType="programAdminDashboard">
                            </app-date-range-filter>


                            <div class="modal-footer">
                                <button (click)="resetTrainingFilter()" type="button"
                                    class="text-white btn btn-secondary me-2">Reset</button>
                                <button
                                    *ngIf="(!filterTrainingParam.trainingIds?.length) || (filterTrainingParam.trainingIds?.length > 0 && filterTrainingParam.intervalType)"
                                    (click)="onClickTrainingDetailFilter(FilterTrainingForm.form.valid)" type="button"
                                    class="btn btn-primary">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!--  -->
    </div>