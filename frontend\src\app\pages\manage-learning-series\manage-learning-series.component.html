<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
	<div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
		<div class="row">
			<div class="col-12 col-sm-4 text-start">
				<div class="custom-input-group">
					<input class="form-control search-form-control" placeholder="Search" appDelayedInput
						(delayedInput)="search($event)" [delayTime]="1000">
					<i class="bi bi-search pe-3"></i>
				</div>
			</div>
			<div class="col-12 col-sm-4">
			</div>
			<div class="col-12 col-sm-4 text-end pe-0 mb-2">
				<button type="button"
					class="btn add-button btn-primary btn-lg font-15px manage-learning-series-add-button"
					(click)="onNewRecord()" *ngIf="authService.isAccessible('CATEGORY','AddButton')">
					<img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-15px" alt="">Add Learning Series
				</button>
			</div>
		</div>
		<div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
			<table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
				<thead>
					<tr>
						<th width="80" style="text-wrap: nowrap;">{{'Training.lastUpdatedDate' | translate}}</th>
						<!-- <th>Program Name</th> -->
						<!-- <th width="130">Created By</th> -->
						<th width="100">{{'SubCategory.LEARNING_SERIES_TITLE' | translate}}</th>
						<th width="130">{{'SubCategory.description' | translate}}</th>
						<!-- <th style="width: 255px;">{{'SubCategory.CONTENT_TYPES' | translate}}</th> -->
						<!-- <th style="width: 80px;">{{'SubCategory.ACCESSIBILITY' | translate}}</th> -->
						<th style="width: 30px;" class="text-end">{{'SubCategory.action' | translate}}</th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let record of records;">
						<td>
							<div>{{moment(record.updatedOn).format('DD/MM/YYYY')}}</div>
						</td>
						<!-- <td>{{record.userDetails.programName}}</td> -->
						<!-- <td class="pe-0">
							<h5 class="code" title="View User Profile" style="font-size:13px; white-space: nowrap;"
								[routerLink]="['/dashboard/program-detail/' +record?.userDetails?.id]">
								{{record?.userDetails?.fullName}}
							</h5>
						</td> -->
						<td>{{record.title}}</td>
						<td [title]="record.description">
							{{ record.description | slice:0:50 }}{{ record.description.length > 50 ? '...' : '' }}
						</td>
						<!-- <td>
							<span class="float-start custom_tags_cls mb-2 me-2"
								*ngFor="let item of record.learningSeriesContentTypeMappingDetail, let i = index">
								{{item.title}}{{i === record.learningSeriesContentTypeMappingDetail.length - 1 ? '' : '
								'}}
							</span>
						</td> -->
						<!-- <td>
							<img [title]="record.accessibility == 'RESTRICTED' ? 'Restricted' : record.accessibility == 'PUBLIC' && 'Public'"
								[src]="record.accessibility == 'RESTRICTED' ? '/assets/images/icons/menu/users-red.svg' : record.accessibility == 'PUBLIC' && '/assets/images/icons/menu/users-green.svg'"
								class="width-27px mb-3" />
						</td> -->
						<td class="text-center custom-action-button text-right">
							<div class="d-flex justify-content-end mb-3">
								<img src="/assets/images/icons/menu/restriction.svg"
									*ngIf="authService.isAccessible('TRAINING','RestrictedUsers') && !isDetailPage && !isPlusButton && record.accessibility == 'RESTRICTED' && record.restrictedUsersCount && record.restrictedUsersCount > 0"
									[class.disabled]="authService.isDisabled('TRAINING','RestrictedUsers')"
									title="Restricted Users" (click)="openRestrictedUsersList(record)"
									class=" me-2 width-27px cursor-pointer" />
								<i *ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
									[class.disabled]="authService.isDisabled('CATEGORY','EditButton')" title="Edit"
									(click)="editRecord(record.id)"
									class="bi bi-pencil font-21px me-2 cursor-pointer"></i>
								<i *ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
									[class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')" title="Delete"
									(click)="remove(record.id)" class="bi bi-trash font-21px cursor-pointer"></i>
							</div>
						</td>
					</tr>
					<!-- <tr *ngIf="records.length===0">
						<td class="text-center" colspan="5">
							{{"COMMON.NORECORDS" | translate}}
						</td>
					</tr> -->
				</tbody>
			</table>
		</div>
	</div>
</div>
<!--
<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Category Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Category.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('CATEGORY','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('CATEGORY','AddButton')">
            <span class="hidden-xs">{{'Category.ADD_NEW_CATEGORY' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Category.title' | translate}}</th>
					    		<th>{{'Category.description' | translate}}</th>
					    		<th>{{'Language.objName' | translate}} {{'Language.name' | translate}}</th>
					    		<th>{{'Category.commonTitle' | translate}}</th>
					    		<th>{{'Category.groupCode' | translate}}</th>
					    		<th>{{'Category.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        			<td>{{record.title}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
				                    		<a *ngIf="record.languageIdDetail" class="primary-color" [routerLink]="['/dashboard/language/detail/'+record.languageIdDetail.id]">
				                       		{{record.languageIdDetail.name}}

						        		</a>
					        		</td>
					        			<td>{{record.commonTitle}}</td>
					        			<td>{{record.groupCode}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('CATEGORY','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('CATEGORY','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('CATEGORY','EditButton')"
                           [routerLink]="['/dashboard/category/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>
            </table>
    </div>
</div>

<app-category-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-category-edit>
-->
<!-- <div class="modal fade site-detail-modal right" id="ManageLearningSeriesDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="ManageLearningSeriesDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-manage-content-detail [recordId]="selectedId"></app-manage-content-detail>
            </div>
        </div>
    </div>
</div> -->

<div class="modal fade modal-xl" id="restrictedUsersModal" aria-hidden="true" aria-labelledby="restrictedUsersModal"
	tabindex="-1">
	<div class="modal-dialog modal-dialog-scrollable">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="restrictedUsersModalLabel">User Restrcited List</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<app-restricted-users-list [showDescription]="true" [hideVideoOrImage]="true"
					[getRestrictedUsersData]="restrictedUsersData && restrictedUsersData"
					*ngIf="restrictedUsersModal && restrictedUsersModal._isShown"
					[apiUrl]="'/api/learningseries/assigned/users'"
					[getFilterParam]="filterParam"></app-restricted-users-list>
			</div>
			<div class="modal-footer">
				<button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
