import { AfterViewInit, Component, Directive, Input, OnInit } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { StaffAssignedTrainingService } from 'src/app/services/staff-assigned-training.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import * as moment from 'moment';
import AOS from 'aos';
import { ActivatedRoute, Router } from '@angular/router';
import { Constant } from 'src/app/config/constants';
import { param } from 'jquery';

declare var bootstrap: any;

@Component({
  selector: 'app-staff-assigned-training',
  templateUrl: './staff-assigned-training.component.html',
  styleUrls: ['./staff-assigned-training.component.scss'],
  providers: [FilterParam]
})
export class StaffAssignedTrainingComponent implements OnInit, AfterViewInit {
  trainingTabs: any = "INPROGRESS"
  trainingData: any = [];
  currentVideoData: any;
  recordData: any;
  moment: any = moment;
  baseUrl: any = '/api/user/' + this.authService.getUser().id;
  trainingDescriptionModal: any;
  filterStaffTrainingModal: any;
  fromDate: any;
  toDate: string;
  noTrainingData: boolean;
  next: number = 10;
  totalPageNumber: number;
  offset: number = 1;
  totalRecordsCount: number;
  loadMore: boolean;
  currentActiveTab: string;
  constructor(private authService: AuthService, private staffassignedTrainingService: StaffAssignedTrainingService, private toastService: ToastService,
    private filterParam: FilterParam, private router: Router, private route: ActivatedRoute) { }

  ngOnInit() {
    this.filterParam.next = this.next;
    this.filterParam.offset = this.offset;
    this.route.queryParams.subscribe(params => {
      this.currentActiveTab = params.tab;
    });
    this.trainingTabs = this.currentActiveTab !=null ? this.currentActiveTab : "INPROGRESS";
    if (this.trainingTabs == 'INPROGRESS') {
      this.fetchAllTrainings('/api/user/' + this.authService.getUser().id + '/assigned-trainings', "INPROGRESS", false);
    }
    else {
      this.fetchAllTrainings('/api/user/' + this.authService.getUser().id + '/assigned-trainings-status', this.trainingTabs, false);
    }
  }

  changeTrainingData(data: string) {
    if (data !== this.trainingTabs) {
      this.removeFilterData();
      this.filterParam.next = 10;
      this.filterParam.offset = 1;
      let url = data == "INPROGRESS" ? '/assigned-trainings' : '/assigned-trainings-status'
      this.fetchAllTrainings(this.baseUrl + url, data, false);

    }
    this.trainingTabs = data;
  }

  removeFilterData() {
    this.fromDate = null;
    this.toDate = null;
    delete this.filterParam.startDate
    delete this.filterParam.endDate;
    delete this.filterParam.categorySearch;
    delete this.filterParam.subCategorySearch;
    delete this.filterParam.SearchCommonTitle;
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.trainingDescriptionModal = new bootstrap.Modal(
        document.getElementById('trainingDescriptionModal')
      );
    }, 0)
    setTimeout(() => {
      this.filterStaffTrainingModal = new bootstrap.Modal(
        document.getElementById('filterStaffTrainingModal')
      );
    }, 0)
  }

  async fetchAllTrainings(url: string, status: string, loadMore: boolean) {
    this.noTrainingData = false
    this.filterParam.status = status;
    try {
      const response: RestResponse = await this.staffassignedTrainingService.fetchAllTrainings(this.filterParam, url);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if (!response.data?.length) {
        this.noTrainingData = true
      }
      if (this.currentActiveTab != null) {
        var yourCurrentUrl = window.location.href.split('?')[0];
        window.history.replaceState({}, '', yourCurrentUrl);
        this.currentActiveTab = null;
      }
      this.totalRecordsCount = response.data?.length ? response.data[0].totalCount : 0;
      this.totalPageNumber = Math.ceil(this.totalRecordsCount / this.filterParam.next);
      if (loadMore) {
        this.loadMore = false;
        const newTrainingData = this.setTrainingData(response);
        this.trainingData = this.trainingData.concat(newTrainingData);
      } else {
        this.trainingData = this.setTrainingData(response);
      }
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setTrainingData(response: any) {
    const trainingData = (response.data || []).map(training => ({ videoLoaded: false, uploader: null, ...training }));

    return trainingData
  }

  watchVideo(trainingData: any) {
    this.currentVideoData = trainingData
  }

  backToTrainingList(data: any) {
    this.currentVideoData = null;

    if (data.status == Constant.TRAINING_STATUS.COMPLETED) {
      this.trainingData = this.trainingData.map(training => {
        if (training.id == data.id) {
          training.status == Constant.TRAINING_STATUS.COMPLETED
        }
        return training
      }).filter(training => training.status != Constant.TRAINING_STATUS.COMPLETED)
    }
  }

  readMoreDescription(record: any) {
    AOS.init({ disable: true });
    this.recordData = {
      description: record.trainingIdDetail.description,
      name: record.trainingIdDetail.videoTitle,
    };
    this.trainingDescriptionModal.show();
  }

  openFilterTrainingModal() {
    AOS.init({ disable: true });
    this.filterStaffTrainingModal.show();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  onClickTrainingFilter(valid) {
    this.filterParam.next = 10;
    this.filterParam.offset = 1
    let url = this.trainingTabs == "INPROGRESS" ? '/assigned-trainings' : '/assigned-trainings-status'
    this.fetchAllTrainings(this.baseUrl + url, this.trainingTabs, false);
    this.filterStaffTrainingModal.hide()
  }

  resetFilter() {
    this.removeFilterData();
    this.filterParam.next = 10;
    this.filterParam.offset = 1
    let url = this.trainingTabs == "INPROGRESS" ? '/assigned-trainings' : '/assigned-trainings-status'
    this.fetchAllTrainings(this.baseUrl + url, this.trainingTabs, false);
    this.filterStaffTrainingModal.hide();
  }

  onScrollingFinished() {
    if (this.trainingData.length >= this.totalRecordsCount && this.filterParam.offset >= this.totalPageNumber) {
      return false
    }
    this.filterParam.next = 10;
    this.filterParam.offset = this.filterParam.offset + 1;
    this.loadMore = true;
    let url = this.trainingTabs == "INPROGRESS" ? '/assigned-trainings' : '/assigned-trainings-status'
    this.fetchAllTrainings(this.baseUrl + url, this.trainingTabs, true);
  }

}
