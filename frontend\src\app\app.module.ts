import { HttpClient, HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { LocalStorageModule } from 'angular-2-local-storage';
import { DataTablesModule } from 'angular-datatables';
import { NgxMaskModule } from 'ngx-mask';
import { AppRoutingModule } from './app-routing.module';
import { RecaptchaModule, RecaptchaFormsModule } from 'ng-recaptcha';
import { AppComponent } from './app.component';
import { AutoFocusDirective } from './directives/auto-focus.directive';
import { MaterialAppModule } from './material.module';
import { ForbiddenComponent } from './pages/forbidden/forbidden.component';
import { LoginComponent } from './pages/login/login.component';
import { NotFoundComponent } from './pages/not-found/not-found.component';
import { RecoverPasswordComponent } from './pages/recover-password/recover-password.component';
import { HttpAuthInterceptor } from './shared/http.interceptor';
import { SharedModuleModule } from './shared/shared-module.module';
import { ForgotPasswordComponent } from './pages/forgot-password/forgot-password.component';
import { DelayedInputModule } from './shared/delayed-input.directive';
import { VerifyOtpComponent } from './pages/verify-otp/verify-otp.component';
import { RegisterUserComponent } from './pages/register-user/register-user.component';
import {Ng2TelInputModule} from 'ng2-tel-input';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { StaffHomepageComponent } from './pages/staff-homepage/staff-homepage.component';
import { environment } from 'src/environments/environment';
import { ConfirmEmailComponent } from './confirm-email/confirm-email.component';
import { TAndCComponent } from './t-and-c/t-and-c.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { ImageCompressService,ResizeOptions,ImageUtilityService } from 'ng2-image-compress';
import { ProgramAdministratorRegisterComponent } from './pages/program-administrator-register/program-administrator-register.component';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';

@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    NotFoundComponent,
    ForbiddenComponent,
    RecoverPasswordComponent,
    AutoFocusDirective,
    ForgotPasswordComponent,
    VerifyOtpComponent,
    RegisterUserComponent,
    StaffHomepageComponent,
    ConfirmEmailComponent,
    TAndCComponent,
    PrivacyPolicyComponent,
    ProgramAdministratorRegisterComponent
  ],
  imports: [
    Ng2TelInputModule,
    RecaptchaModule,
    RecaptchaFormsModule,
    BrowserModule,
    HttpClientModule,
    FormsModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MaterialAppModule,
    MatNativeDateModule,
    DelayedInputModule,
    CKEditorModule,
    NgMultiSelectDropDownModule.forRoot(),
    LocalStorageModule.forRoot({
      prefix: 'site',
      storageType: 'localStorage'
    }),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    SharedModuleModule,
    DataTablesModule,
    NgxMaskModule.forRoot(),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpAuthInterceptor,
      multi: true
    },
    ImageCompressService,ResizeOptions
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}

// required for AOT compilation
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, '/assets/i18n/', '.json?cacheBuster='+ new Date());
}
