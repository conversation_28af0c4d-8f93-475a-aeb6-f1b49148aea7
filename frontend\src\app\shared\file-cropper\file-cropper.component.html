<div class="modal fade" id="cropImagePopup" role="dialog" data-keyboard="false" data-backdrop="static"
     *ngIf="isOpenCrop">
  <div class="modal-dialog associated-dialog file-cropper-popup">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" (click)="close()">&times;</button>
        <h4 class="modal-title">{{"COMMON.CROP_IMAGES" |  translate}}</h4>
      </div>
      <div class="modal-body">
        <div class="col-md-8 no-padding">
          <image-cropper [imageBase64]="request.imageBase64" [maintainAspectRatio]="true" [aspectRatio]="4 / 3"
                         (imageCropped)="imageCropped($event)">
          </image-cropper>
        </div>
        <div class="col-md-4">
          <img class="preview-image img-responsive" [src]="request.croppedImage"/>
        </div>
        <div class=" clearfix"></div>
        <div class="image-container" style="max-width:fit-content;" *ngIf="data.files.length>1">
          <div *ngFor="let file of data.files; let i = index" class="image-preview">
            <img class="preview-image" [src]="file.base64" (click)="selectCropFile(i)"
                 *ngIf="file.type == 'image/png' || file.type == 'image/jpeg' || file.type == 'image/jpg'"
                 [ngClass]="{'selected-crop-image': (i == request.selectedIndex)}"/>
          </div>
        </div>
        <div class="text-right margin-top-10">
          <button title="Apply" class="btn btn-primary large-btn site-button" type="button"
                  (click)="applyCrop()" *ngIf="!hasCropped">
            {{"COMMON.APPLY_CROP" |  translate}}
          </button>
          <button title="Save" class="btn btn-primary site-button large-btn" type="button" (click)="uploadImages()"
                  *ngIf="hasCropped">
            {{"COMMON.UPLOAD_ALL" |  translate}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
