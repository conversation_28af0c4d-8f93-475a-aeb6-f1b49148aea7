.table-container {
  max-height: 600px; // Fixed height of 600px
  overflow-y: auto; // Enable vertical scrolling
  overflow-x: auto; // Enable horizontal scrolling if needed
  display: block;
  margin-top: 6px;

  // Responsive adjustments for smaller screens
  @media (max-width: 768px) {
    .video-table {
      display: block;
      overflow-x: auto;
      width: 100%;
    }

    .video-table thead,
    .video-table tbody,
    .video-table th,
    .video-table td,
    .video-table tr {
      display: block;
    }

    .video-table thead {
      float: left;
    }

    .video-table tbody {
      width: auto;
      position: relative;
      overflow-x: auto;
    }

    .video-table td {
      text-align: right;
      padding-left: 50%;
      position: relative;
      overflow-x: auto;

      &:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        padding-left: 10px;
        font-weight: bold;
        text-align: left;
        white-space: nowrap;
      }
    }

    .video-table tr {
      margin-bottom: 1rem;
    }
  }
}

.video-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  th,
  td {
    padding: 5px 15px;
    text-align: left;
    font-size: 14px;
  }

  thead {
    background-color: rgb(211 215 219);
  }

  th {
    font-weight: 600;
    padding: 15px;
  }

  tbody tr:nth-child(odd) {
    background-color: rgb(242 242 242);
  }

  tbody tr {
    cursor: move; // Change cursor to move
    border: none;
  }

  a {
    text-decoration: none;
    color: #007bff;

    &:hover {
      text-decoration: underline;
    }
  }

  input[type="checkbox"] {
    width: 20px;
    height: 20px;
  }

  .edit,
  .delete {
    margin-right: 10px;
    font-size: 18px;
    color: #555;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .delete {
    color: #d9534f;
  }

  tbody cdkDragPlaceholder {
    background: #e0e0e0;
    border: dashed 2px #c0c0c0;
  }

  // Styling for the drag preview
  .drag-preview-table {
    width: 100%;
    border-collapse: collapse;
    background-color: rgba(151, 146, 146, 0.87);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    td {
      padding: 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
      background-color: #aca4a4;
    }

    .edit,
    .delete {
      color: #555;
    }

    .delete {
      color: #d9534f;
    }

    // Disable pointer events on the drag preview
    pointer-events: none;
  }
}

.checkbox-container {
  display: flex;
  align-items: center;
  /* Vertically centers the checkbox and label */
  justify-content: flex-start;
  /* Horizontally centers the checkbox and label */
}

.checkbox-container input[type="checkbox"] {
  transform: scale(1.2);
  /* Increases the size of the checkbox */
  margin-right: 10px;
  /* Space between the checkbox and the label (adjust if needed) */
}

.checkbox-label {
  font-size: 16px;
  /* Increases the font size of the label text */
  margin: 0;
  /* Removes default margin */
  margin-bottom: 2px;
}

/* When the switch is not checked */
.form-switch .accessibility-switch:not(:checked) {
  margin-left: -2.5em;
  background-color: #f44336;
  border: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3ccircle cx='4' cy='4' r='4' fill='%23fff'/%3e%3c/svg%3e");
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 16px;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}