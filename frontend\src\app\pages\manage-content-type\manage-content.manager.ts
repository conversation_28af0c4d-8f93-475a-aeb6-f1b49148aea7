import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { ManageContentService } from './manage-content.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class ManageContentManager extends BaseManager {

    constructor(protected manageContentService: ManageContentService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageContentService, loadingService, toastService);
    }
}
