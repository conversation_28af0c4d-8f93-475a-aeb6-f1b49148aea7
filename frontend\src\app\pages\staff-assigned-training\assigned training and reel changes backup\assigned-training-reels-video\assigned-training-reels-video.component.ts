import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { timeStamp } from 'console';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { StaffAssignedTrainingService } from 'src/app/services/staff-assigned-training.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';
import { threadId } from 'worker_threads';

@Component({
  selector: 'app-assigned-training-reels-video',
  templateUrl: './assigned-training-reels-video.component.html',
  styleUrls: ['./assigned-training-reels-video.component.scss']
})
export class AssignedTrainingReelsVideoComponent implements OnInit, OnDestroy {
  @Input() currentVideoData: any;
  @Input() trainingData: any;
  @Output() trainingList = new EventEmitter<void>();
  currentIntialSlide: number;
  public currentActiveVideo: string = null;
  public currentPlayVideo: string = null;
  allowNextSlide: boolean = true;
  allowSlidePrev: boolean = true;
  trainingIds: any = [];
  public currentVideoWaiting: string = null

  public browserHeight: any = null;
  constructor(private toastService: ToastService, private staffAssignedTrainingService: StaffAssignedTrainingService) { }

  ngOnInit() {
    this.browserHeight = window.innerWidth <= 600 ? window.innerHeight : 590;
    this.trainingData.map((training, index, row) => {
      training.uploader = training.status == "INPROGRESS" ? this.initializeUploaderTraining(null, 'mp4,mkv', null, null, this.toastService, "Only Mkv, Mp4 files are allowed", null, training) : null;
      // this.loadVideo(training.videoUrl)
      //   .then(blobUrl => { // now it's loaded
      //     document.body.className = 'loaded';
      //     let vid = document.getElementById('videoId' + training.id) as HTMLVideoElement;
      //     vid.src = blobUrl; // we just set our mediaElement's src to this blobURL
      //     vid.onload = () => URL.revokeObjectURL(blobUrl);
      //     training.videoLoaded = true;
      //   }).catch((err) => console.log(err));
      if (training.id == this.currentVideoData.id) {
        this.currentIntialSlide = index;
        if (index == 0) {
          this.allowNextSlide = true;
          this.allowSlidePrev = false;
        }
        if (index + 1 === row.length) {
          this.allowNextSlide = false;
          this.allowSlidePrev = true;
        }
      }
    })
  }

  resizeDiv() {
    if (window.innerWidth <= 600) {
      let h = window.innerHeight;

      this.browserHeight = h;
    } else {
      this.browserHeight = 590;
    }
  }

  ngOnDestroy() {
  }


  init(event: any) {
    window.addEventListener('resize', () => this.resizeDiv());
    if (event.activeIndex == 0) {
      this.onChangeSlide(event);
    }
  }



  removeEndedVideoEventListener(videoId: any) {
    videoId.removeEventListener("ended", (event) => {
    });
  }

  addEndedVideoEventListener(videoId: any, id: string) {
    videoId.addEventListener("ended", (event) => {
      this.currentPlayVideo = id
    });
  }

  loadVideo(url) {
    return fetch(url)
      .then(resp => resp.blob())
      .then(blob => URL.createObjectURL(blob));
  }

  onChangeSlide(event: any) {
    this.trainingData.map((training, index, row) => {
      if (index === event.activeIndex) {
        this.currentActiveVideo = training.id;
        this.currentPlayVideo = null;
        this.currentVideoWaiting = training.id;
        setTimeout(() => {
          var videoId = document.getElementById("videoId" + training.id) as HTMLMediaElement | null;
          videoId.addEventListener('canplaythrough', (event) => {
            this.currentVideoWaiting = null;
            this.addEndedVideoEventListener(videoId, training.id)
          });
        }, 0)
      }
    })
    if (event.activeIndex > 0 && event.activeIndex + 1 < this.trainingData.length) {
      this.allowNextSlide = true;
      this.allowSlidePrev = true;
    }
  }

  backToAssignedTrainingList() {
    this.trainingList.emit();
  }

  reachEnd() {
    this.allowNextSlide = false;
    this.allowSlidePrev = true;
    this.toastService.info('End of training videos');
  }

  reachBeginning() {
    this.allowNextSlide = true;
    this.allowSlidePrev = false;
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string, training: any) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService, training);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService, training);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessTraining(file, files, training);
        }, 200);
      }
    };

    uploader.onErrorItem = (fileItem, response) => {
      this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== training.id)
      toastService.error('Something error occurred please try again later');
    }
    return uploader;
  }

  fileValidationErrorTraining(data: string, toastService: any, training: any) {
    this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== training.id)
    toastService.error(data);
  }

  async onUploadSuccessTraining(file: any, files: any, trainingData: any) {
    const data = {
      id: trainingData.id,
      isActive: true,
      isDeleted: false,
      userVideoUrl: file.path,
      userId: trainingData.userId,
      trainingId: trainingData.trainingId
    }
    try {
      const response: RestResponse = await this.staffAssignedTrainingService.updateStatusOfTraining(data);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainingData.map(training => {
        if (trainingData.id == training.id) {
          training.status = "COMPLETED";
        }
        return training
      })
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }

    this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== trainingData.id)
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  uploadVideo(trainingId: string) {
    if (trainingId) {
      this.trainingIds.push(trainingId);
    }
  }

  playVideoFromPlayIcon(id: string) {
    var videoId = document.getElementById("videoId" + id) as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        this.currentPlayVideo = null;
        this.addEndedVideoEventListener(videoId, id)
      } else {
        this.currentPlayVideo = id;
        videoId.pause();
        this.removeEndedVideoEventListener(videoId)
      }
    }
  }
}
