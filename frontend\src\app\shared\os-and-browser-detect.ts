import { Injectable } from '@angular/core';
import { constants } from 'buffer';
import { Constant } from '../config/constants';

declare const $: any;

@Injectable({
  providedIn: 'root'
})
export class OsAndBrowserDetect {

  constructor() {
  }

  OsDetails() {
    if (navigator.userAgent.indexOf("Win") != -1)
      return Constant.OS_VERSION.Windows_OS;
    if (navigator.userAgent.indexOf("Macintosh") != -1)
      return Constant.OS_VERSION.Mac_OS;
    if (navigator.userAgent.indexOf("Linux") != -1)
      return Constant.OS_VERSION.Linux_OS;
    if (navigator.userAgent.indexOf("Android") != -1)
      return Constant.OS_VERSION.Android_OS;
    if (navigator.userAgent.indexOf("like Mac") != -1)
      return Constant.OS_VERSION.IOS;
  }

  BrowserDetect() {
    let userAgent = navigator.userAgent;

    if (userAgent.match(/chrome|chromium|crios/i)) {
      return Constant.BROWSER_DETECT.chrome
    } else if (userAgent.match(/firefox|fxios/i)) {
      return Constant.BROWSER_DETECT.firefox
    } else if (userAgent.match(/safari/i)) {
      return Constant.BROWSER_DETECT.safari
    } else if (userAgent.match(/opr\//i)) {
      return Constant.BROWSER_DETECT.opera
    } else if (userAgent.match(/edg/i)) {
      return Constant.BROWSER_DETECT.edge
    } else {
      return "No browser detection";
    }
  }
}
