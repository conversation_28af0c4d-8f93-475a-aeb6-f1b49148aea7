import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { ManageLearningSeriesManager } from '../manage-learning-series.manager';

import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
import { Content } from 'src/app/models/content';
import { ManageContentManager } from '../../manage-content-type/manage-content.manager';
import { LearningSeries } from 'src/app/models/learningseries';
import { FilterParam } from 'src/app/models/filterparam';
import { UsersManager } from '../../users/users.manager';
declare const $: any;

@Component({
	selector: 'app-manage-learning-series-edit',
	templateUrl: './manage-learning-series-edit.component.html',
	styleUrls: ['./manage-learning-series-edit.component.scss']
})

export class ManageLearningSeriesEditComponent extends BaseEditComponent implements OnInit {
	public learningSeries: LearningSeries;
	readonly MY_CONSTANT = Constant;
	contentTypes: any[] = [];
	contentTypesSelected: any[] = [];
	dropdownSettingsForContentTypes = {};
	dropdownSettingsForUsers = {};
	deletedContentTypes: any[] = [];
	deletedUsersSelected: any[] = [];
	users: any[] = [];
	usersSelected: any[] = [];
	constructor(protected route: ActivatedRoute, protected ManageLearningSeriesService: ManageLearningSeriesManager,
		protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
		protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService,
		private manageContentManager: ManageContentManager
		, public commonUtil: CommonUtil, private usersManager: UsersManager) {
		super(ManageLearningSeriesService, commonService, toastService, loadingService, route, router, translateService);
	}

	ngOnInit() {
		this.setDropdownSettingsForContentTypes();
		this.setDropdownSettingsForUsers();
		this.fetchContentTypes();
		// this.fetchUsersList();
		this.contentTypes = new Array<any>();
		this.init();
		this.learningSeries = new LearningSeries();
		this.learningSeries.isActive = true;

		this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
	}

	// async fetchUsersList() {
	// 	let param = new FilterParam();
	// 	const response: RestResponse = await this.usersManager.fetchActiveUsers(param);
	// 	this.users = response.data.map(user => {
	// 		return {
	// 			id: user.id,
	// 			fullName: user.fullName + " (" + user.email + ")",
	// 		}
	// 	});
	// }

	setDropdownSettingsForContentTypes() {
		this.dropdownSettingsForContentTypes = {
			singleSelection: false,
			idField: 'id',
			enableCheckAll: false,
			textField: 'title',
			// itemsShowLimit: 3,
			allowSearchFilter: true
		};
	}
	setDropdownSettingsForUsers() {
		this.dropdownSettingsForUsers = {
			singleSelection: false,
			idField: 'id',
			enableCheckAll: false,
			textField: 'fullName',
			// itemsShowLimit: 3,
			allowSearchFilter: true
		};
	}

	// selectAccessibility(oldVal: string, event: string) {
	// 	if (oldVal !== event) {
	// 		this.deleteAllSelectedUsers();
	// 	}
	// 	this.learningSeries.accessibility = event;
	// }

	// deleteAllSelectedUsers() {
	// 	if (this.learningSeries.learningSeriesAssign && this.learningSeries.learningSeriesAssign.length > 0) {
	// 		this.learningSeries.learningSeriesAssign.map(user => {
	// 			if (user.id) {
	// 				this.deletedUsersSelected.push({
	// 					...user,
	// 					isDeleted: true
	// 				})
	// 			}
	// 			return user;
	// 		})
	// 	}
	// 	this.learningSeries.learningSeriesAssign = [];
	// 	this.usersSelected = [];
	// }

	// addUser(event: any) {
	// 	this.learningSeries.learningSeriesAssign.push({ userId: event.id });
	// }

	// removeUser(event: any) {
	// 	let index = this.learningSeries.learningSeriesAssign.findIndex(user => user.userId == event.id);
	// 	if (this.learningSeries.learningSeriesAssign[index].id) {
	// 		this.learningSeries.learningSeriesAssign[index].isDeleted = true;
	// 		this.deletedUsersSelected.push(this.learningSeries.learningSeriesAssign[index]);
	// 		this.learningSeries.learningSeriesAssign.splice(index, 1);
	// 	} else {
	// 		this.learningSeries.learningSeriesAssign.splice(index, 1);
	// 	}
	// }

	async fetchContentTypes() {
		this.contentTypes = await this.manageContentManager.fetchAllData(null);
	}

	onFetchCompleted() {
		this.learningSeries = LearningSeries.fromResponse(this.record);
		this.setRecord(this.learningSeries);
		let contentTypesSelected: any = [];
		this.learningSeries?.learningSeriesContentTypeMappingDetail?.map(content => {
			contentTypesSelected.push({
				id: content.contentType,
				title: content.title,
			})
		})
		this.contentTypesSelected = contentTypesSelected;
		let usersSelected: any = [];
		// this.learningSeries.learningSeriesAssign.map(user => {
		// 	usersSelected.push({
		// 		id: user.userId,
		// 		fullName: user.userFirstName + " " + user.userLastName,
		// 	})
		// })
		this.usersSelected = usersSelected;
	}

	onSaveSuccess(message: any) {
		this.toastService.success(message);
		this.navigate('/dashboard/manage-learning-series');
	}

	addContentType(event: any) {
		this.learningSeries.learningSeriesContentTypeMappingDetail.push({ contentType: event.id });
	}

	removeContentType(event: any) {
		let index = this.learningSeries.learningSeriesContentTypeMappingDetail.findIndex(content => content.contentType == event.id);
		// console.log(index);
		if (this.learningSeries.learningSeriesContentTypeMappingDetail[index].id) {
			this.learningSeries.learningSeriesContentTypeMappingDetail[index].isDeleted = true;
			this.deletedContentTypes.push(this.learningSeries.learningSeriesContentTypeMappingDetail[index]);
			this.learningSeries.learningSeriesContentTypeMappingDetail.splice(index, 1);
		} else {
			this.learningSeries.learningSeriesContentTypeMappingDetail.splice(index, 1);
		}
	}

	async save(form: any) {
		this.onClickValidation = !form.valid;
		if (!form.valid) {
			return;
		}

		// if (this.learningSeries.learningSeriesContentTypeMappingDetail.length == 0) {
		// 	this.onClickValidation = true;
		// 	return
		// }

		// if (this.learningSeries.learningSeriesAssign.length == 0 && this.learningSeries.accessibility == this.MY_CONSTANT.LEARTNING_SERIES_ACCESSIBILITY.RESTRICTED) {
		// 	this.onClickValidation = true;
		// 	return
		// }

		let learningSeries = JSON.parse(JSON.stringify(this.learningSeries));
		// learningSeries.learningSeriesContentTypeMappingDetail = learningSeries.learningSeriesContentTypeMappingDetail.concat(this.deletedContentTypes);
		// learningSeries.learningSeriesAssign = learningSeries.learningSeriesAssign.concat(this.deletedUsersSelected);
		try {
			this.loadingService.show();
			const method = this.request.isNewRecord ? 'save' : 'update';
			const response: RestResponse = await this.manager[method](learningSeries);
			this.loadingService.hide();
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.onSaveSuccess(response.message);
		} catch (error) {
			this.loadingService.hide();
			this.toastService.error(error.message);
		}
	}
}
