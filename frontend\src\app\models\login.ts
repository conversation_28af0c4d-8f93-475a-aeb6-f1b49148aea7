import { CommonUtil } from "../shared/common.util";

export class Login {
  userName: string;
  email: string;
  password: string;

  constructor() {
  }

  isValidLoginRequest(form: any) {
    if (CommonUtil.isNullOrUndefined(this.userName) || this.userName.trim() === '') {
      form.controls.useUserName.setErrors({ invalid: true });
      return false;
    }
    if (CommonUtil.isNullOrUndefined(this.password) || this.password.trim() === '') {
      form.controls.userPassword.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  isValidForgotPasswordRequest(form: any) {
    if (CommonUtil.isNullOrUndefined(this.userName)) {
      form.controls.useUserName.setErrors({ invalid: true });
      return false;
    }
    return true;
  }
}
