$primary: #07182f;
$secondary: #1681ff;
$scroll-bar-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
$scroll-bar-color: #173050;
$blue-button-color: #02b0f3;
@import "bootstrap";
@import url("https://fonts.googleapis.com/css2?family=Blinker:wght@400;600;700&display=swap");
@import "~bootstrap-icons/font/bootstrap-icons.css";
@import "~@ng-select/ng-select/themes/default.theme.css";
@import "assets/scss/common";
@import "assets/scss/login";
@import "assets/scss/register";
@import "assets/scss/dashboard-layout";
@import "~@angular/material/prebuilt-themes/indigo-pink.css";

.sweet-alert {
  background: #ffffff;
  box-shadow: 0px 3px 8px #15223214;

  .sa-icon {
    display: none !important;
  }

  h2 {
    color: #111 !important;
    font-weight: 500;
    font-family: "Blinker", sans-serif !important;
  }

  button {
    display: inline-block;
    padding: 8px 50px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    // border: 1px solid transparent;
    margin: 26px 23px 0 23px !important;
    text-transform: uppercase;
    border-radius: 0px !important;
    font-family: "Blinker", sans-serif !important;

    &.cancel {
      // color: $primary !important;
      background-color: $secondary !important;
      border-color: $primary !important;

      &:hover,
      &:focus,
      &:active {
        text-decoration: none !important;
      }
    }
  }
}

.loading-link-cls {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  img {
    width: 42px;
  }
}

.accordion-button::after {
  margin-right: 20px;

}

.accordion-button:not(.collapsed)::after {
  margin-right: 20px;
}

.accordion-title {
  font-size: 24px !important;
  font-weight: 600;
}

.add-new-course {
  border: 1px solid $secondary !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // padding: 10px 20px;
}

.add-course-training {
  background-color: $primary !important;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // padding: 10px 20px;
}

.upload-img-container {
  border: 1px dashed #07182F !important;
  border-radius: 20px !important;
  opacity: 1;
  padding: 34px 40px !important;
}

// .video-title {
.upload-video-container {
  border-radius: 20px !important;
  border: 0px;
  opacity: 1;
  height: 126px !important;

  label {
    cursor: pointer;
    flex-direction: column !important;
    background-color: transparent;
  }

  input {
    display: none;
  }
}

// }

mat-icon {
  position: relative;
  float: right;
  top: -3px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.54);
}

.mat-datepicker-input {
  width: 85% !important;
}

.cdk-overlay-container {
  z-index: 1200 !important;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: $scroll-bar-box-shadow;
  box-shadow: $scroll-bar-box-shadow;
  border-radius: 10px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar {
  width: 5px;
  background-color: #f5f5f5;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: $scroll-bar-box-shadow;
  box-shadow: $scroll-bar-box-shadow;
  background-color: $scroll-bar-color;
}

.notify-alert {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.23), 0 3px 10px rgba(0, 0, 0, 0.16);
  border: 0 !important;
  max-width: 400px;
  color: #fff !important;

  &.alert-success {
    background-color: #4caf50;
    color: #fff !important;
  }

  &.alert-info {
    background-color: #689ab5;
    color: #fff !important;
  }

  &.alert-warning {
    background-color: #ffad33;
    color: #fff !important;
  }

  &.alert-danger {
    background-color: #f55a4e;
    color: #fff !important;
  }

  button[data-notify="dismiss"] {
    margin-left: 5px;
    outline: none !important;
    background: transparent;
    border: 0px;
    color: #fff;
    font-size: 23px;
    line-height: 14px;
    padding-top: 8px;
    display: none !important;
  }
}

.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;

  .pace-progress {
    background: $primary;
    position: fixed;
    z-index: 2000;
    top: 0;
    right: 100%;
    width: 100%;
    height: 2px;
  }

  .pace-progress-inner {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px $primary, 0 0 5px $primary;
    opacity: 1;
    -webkit-transform: rotate(3deg) translate(0px, -4px);
    -moz-transform: rotate(3deg) translate(0px, -4px);
    -ms-transform: rotate(3deg) translate(0px, -4px);
    -o-transform: rotate(3deg) translate(0px, -4px);
    transform: rotate(3deg) translate(0px, -4px);
  }

  .pace-activity {
    display: block;
    position: fixed;
    z-index: 2000;
    top: 15px;
    right: 15px;
    width: 14px;
    height: 14px;
    border: solid 2px transparent;
    border-top-color: $primary;
    border-left-color: $primary;
    border-radius: 10px;
    -webkit-animation: pace-spinner 400ms linear infinite;
    -moz-animation: pace-spinner 400ms linear infinite;
    -ms-animation: pace-spinner 400ms linear infinite;
    -o-animation: pace-spinner 400ms linear infinite;
    animation: pace-spinner 400ms linear infinite;
  }
}

.pace-inactive {
  display: none;
}

.table {
  max-width: 100% !important;
}

table.dataTable {
  max-width: 100% !important;
}

table.dataTable.no-footer {
  border-bottom: 0px solid #f6f6f6;
  border-color: #f6f6f6;
}

.dataTables_wrapper .dataTables_length select {
  padding: 0.375rem 2.25rem 0.375rem 0.75rem !important;
}

table.table-bordered.dataTable thead tr:first-child th,
table.table-bordered.dataTable thead tr:first-child td {
  background-color: #d3d7db;
  font-size: 14px;
  color: #000;
  font-weight: 600;
  border: 0px !important;
}

table.dataTable thead th,
table.dataTable thead td {
  padding: 15px;
  border-bottom: 1px solid #f6f6f6;
}

table.dataTable tbody th,
table.dataTable tbody td {
  padding: 0px 15px 0px 15px;
  font-size: 13px;
  vertical-align: middle;
  padding-top: 5px !important;
  padding-bottom: 5px !important;
  border-right: 0px;
  border-left: 0px;
  font-weight: 500;

  .detail-page-link {
    color: $primary;
    font-weight: 500;
  }
}

table.dataTable thead .sorting {
  background-image: none !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border: 0px;
  padding: 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  border: 0px;
  background-color: transparent;
  background: transparent;
}

div.dataTables_wrapper div.dataTables_filter input {
  height: 45px;
  border-radius: 0px;
  border: 1px solid #ced4da;
  width: 250px;
  padding: 10px 15px;
}

div.dataTables_wrapper div.dataTables_length label {
  font-weight: 500;
  font-size: 16px;
}

.dataTables_wrapper .dataTables_info {
  font-weight: 500;
  font-size: 16px;
}

.dataTables_wrapper .col-sm-12,
.dataTables_wrapper .col-md-6 {
  padding: 0px !important;
}

.server-side-table {
  &.has-records {
    table.dataTable td.dataTables_empty {
      display: none;
    }
  }
}

.site-custom-table {

  thead th,
  thead td {
    padding: 15px;
    border-bottom: 1px solid #f6f6f6;
    background-color: $secondary;
    font-size: 14px;
    color: #000;
    font-weight: 500;
  }

  tbody th,
  tbody td {
    padding: 15px;
    font-size: 14px;
    vertical-align: middle;
    padding-top: 23px !important;
    padding-bottom: 23px !important;
    font-weight: 600;

    .detail-page-link {
      color: $primary;
      font-weight: 500;
    }
  }
}

.page-link {
  color: $primary !important;
}

.page-item.active .page-link {
  background-color: $primary;
  border-color: $primary;
  color: #fff !important;
}

.page-link:focus,
.dataTables_wrapper .form-select:focus,
.dataTables_wrapper .form-control:focus {
  box-shadow: none !important;
}

.detail-page {
  .form-control[readonly] {
    background-color: #fff !important;
    box-shadow: none !important;
    border: 1px solid $secondary !important;
  }
}

@-webkit-keyframes pace-spinner {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes pace-spinner {
  0% {
    -moz-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-o-keyframes pace-spinner {
  0% {
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-ms-keyframes pace-spinner {
  0% {
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes pace-spinner {
  0% {
    transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

ngb-typeahead-window {
  display: block !important;
  width: 100%;
  border-top: 0px !important;
  border-radius: 0px !important;
  padding-bottom: 0px !important;
  font-family: "Blinker", sans-serif !important;
  font-size: 14px !important;
  padding-top: 0px !important;

  .dropdown-item {
    padding: 10px 25px !important;
  }
}


.title {
  font-size: 18px !important;
}

// .site-customer-main-container{
//   .custom-input-group {

//     position: relative;

//     max-width: 300px;

//     width: 100%;

//     margin-right: 10px;

//     float: left;

//     .search-form-control {

//         padding-left: 40px;

//         font-size: 12px !important;

//         height: 52px;

//         box-shadow: none !important;

//     }

//     .bi-search {

//         position: absolute;

//         left: 15px;

//         top: 16px;

//     }
//   }

// }
.code {
  font-size: 24px;
  font-weight: 600;
  margin-top: 8px;
  white-space: nowrap;
  cursor: pointer;

}

.code:hover {
  color: #1681ff;
}

.mat-form-field-invalid {
  .mat-form-field-flex {
    border: 1px solid #dc3545 !important;
  }
}

/* Importing Bootstrap SCSS file. */
@import "~bootstrap/scss/bootstrap";