import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminInvitedService } from './program-admin-invited-user.service';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminInvitedManager extends BaseManager {

    constructor(protected programAdminInvitedService: ProgramAdminInvitedService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(programAdminInvitedService, loadingService, toastService);
    }
}
