<div data-aos="zoom-in"  data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
  <div class="dashboard-content-container add-training">
    <form #categoryForm="ngForm" novalidate="novalidate">
      <div class="row">
        <div class="col-12 col-md-6 col-lg-6 col-xl-4">
          <div class="form-floating">
            <div class="mb-4 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':trainingCategoryId.invalid && onClickValidation}">
			  <ng-multiselect-dropdown
				placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
				name="trainingCategoryId" [settings]="categoryDropdownSettings"
				[data]="categories" (onSelect)="onCategorySelect($event)" (onDeSelect)="onCategoryDeSelect($event)"
				class="custom-multiselect form-control padding-bottom-8" name="categories"
				[(ngModel)]="training.selectedCategories" #trainingCategoryId="ngModel">
			  </ng-multiselect-dropdown>
            </div>
            <label for="language">{{"Training.categoryId" | translate}}</label>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-6 col-xl-4">
          <div class="form-floating">
            <div class="mb-4 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':trainingSubCategoryId.invalid && onClickValidation}">
			  <ng-multiselect-dropdown
				placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
				name="trainingSubCategoryId" [settings]="subCategoryDropdownSettings"
				[data]="subCategories" (onSelect)="onSubCategorySelect($event)" (onDeSelect)="onSubCategoryDeSelect($event)"
				class="custom-multiselect form-control padding-bottom-8"
				[(ngModel)]="training.selectedSubCategories" #trainingSubCategoryId="ngModel">
			  </ng-multiselect-dropdown>
            </div>
            <label for="language">{{"Training.subCategoryId" | translate}}</label>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-6 col-xl-4">
          <div class="form-floating mt-2 mb-4 w-100 category-language">
            <input [ngClass]="{'is-invalid':!videoCredit.valid && onClickValidation}"
              class="form-control video-credit-height" type="text" name="videoCredit" #videoCredit="ngModel"
              [(ngModel)]="training.videoCredit"
              placeholder="{{'Training.videoCredit' | translate}}">
            <label for="floatingInput">{{"Training.videoCredit" | translate}}</label>
          </div>
        </div>
        <div *ngFor="let training of trainingFormInputs;" class="col-12 col-md-6 col-lg-6 col-xl-4">

          <div class="training-section">
            <div class="category-language w-100 mb-4 mt-3 p-3 bg-secondary d-flex justify-content-between">
              <h6 class=" program-heading lh-lg text-light">{{training.languageName}}</h6>
              <img [src]="getImageFetchByLanguageName(training.languageName)" class="img-fluid me-2" alt="">
            </div>
            <div class="video-title">
              <div class="form-floating mb-4 w-100 category-language">
                <input (ngModelChange)="training.languageName == MY_CONSTANT.languages.English ? addTrainingTitleToRemainingLanguages($event) : null" [ngClass]="{'is-invalid':!videoTitle.valid && onClickValidation}" class="form-control"
                  type="text" [name]="training.languageName" #videoTitle="ngModel" [(ngModel)]="training.videoTitle"
                  required="required" placeholder="{{'Training.title' | translate}}">
                <label for="floatingInput">{{"Training.title" | translate}}</label>
              </div>
              <div class="form-floating form-floating-textarea mb-4 w-100 category-language">
                <textarea (ngModelChange)="training.languageName == MY_CONSTANT.languages.English ? addDescriptionToRemainingLanguages($event) : null" [ngClass]="{'is-invalid':!Description.valid && onClickValidation}"
                  class="form-control form-description" [name]="training.languageName+'Description'"
                  #Description="ngModel" [(ngModel)]="training.description" required="required"
                  placeholder="Description" id="floatingTextarea2"></textarea>
                <label for="floatingInput">{{"Training.description" | translate}}</label>
              </div>
              <div class="upload-video-section mb-4"
                [ngClass]="{'is-invalid form-control': !training.videoUrl && onClickValidation}">
                <label *ngIf="training.videoUrl ==''"
                  [ngClass]="{'d-flex align-items-center': languageIds.length > 0 && languageIds.includes(training.languageId)}"
                  class="fw-bold" [for]="'file-input'+training.languageName">
                  <img src="/assets/images/icons/menu/video_icon.svg" class="img-fluid me-2" alt="" /> {{languageIds.length > 0 && languageIds.includes(training.languageId) ? 'UPLOADING...' : 'UPLOAD VIDEO HERE' }}
                  <div *ngIf="languageIds.length > 0 && languageIds.includes(training.languageId)"
                    class="spinner-border ms-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </label>
                <label *ngIf="training.videoUrl && !loadingVideoIds.includes(training.languageId)"
                  class="fw-bold d-flex align-items-center">
                  <img src="/assets/images/icons/menu/video_icon.svg" class="img-fluid me-2" alt="" />LOADIND VIDEO...
                </label>
                <div *ngIf="training.videoUrl" class="video-wrapper"
                  [ngStyle]="{'display': loadingVideoIds.length > 0 && loadingVideoIds.includes(training.languageId) ? 'block' : 'none' }">
                  <div class="video-container" id="video-container">
                    <div class="play-button-wrapper">
                      <div (click)="playVideoFromPlayIcon(training.languageId)" title="Play video"
                        class="play-gif circle-play-b-cls" [id]="'circle-play-b'+training.languageId">
                        <!-- SVG Play Button -->
                        <svg *ngIf="!videoPlayIds.includes(training.languageId)" xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 80 80">
                          <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                        </svg>
                      </div>
                    </div>
                    <div class="position-absolute delete-video-container" (click)="removeFile(training.languageId)">
                      <i class="bi bi-x"></i>
                    </div>
                    <video playsinline class="mw-100" [id]="'video'+training.languageId" controlslist="nodownload">
                      Your browser does not support HTML video.
                    </video>
                  </div>
                </div>
                <input *ngIf="training.videoUrl =='' && !languageIds.includes(training.languageId) " [name]="training.languageName+'Video'"
                  (change)="uploadVideo(training.languageId,$event)" [id]="'file-input'+training.languageName" type="file" accept="video/*" />
              </div>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-12 col-lg-12 col-xl-12">
          <div class="training-feed">
            <!-- <a href="" class="fw-bold"><img src="/assets/images/icons/menu/tick.svg" class="img-fluid me-2"
								alt="">{{"Training.publishedForTrainingFeed" | translate}}</a> -->
            <!-- <div class="material-switch">
							<input id="trainingPublishedForTrainingFeedId" name="trainingPublishedForTrainingFeed"
								type="checkbox" [(ngModel)]="training.publishedForTrainingFeed" />
							<label for="trainingPublishedForTrainingFeedId" class="label-primary"></label>
						</div> -->
            <div class="form-check publish-training-check-cls">
              <input class="form-check-input" type="checkbox" name="publishedForTrainingFeed" value=""
                id="flexCheckDefault" [(ngModel)]="training.publishedForTrainingFeed">
              <label class="form-check-label" for="flexCheckDefault">
                {{"Training.publishedForTrainingFeed" | translate}}
              </label>
            </div>
          </div>
        </div>
        <div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
          <button class="btn btn-primary site-button btn-sm large-button save-button rounded-3" type="button"
            (click)="save(categoryForm.form)" *ngIf="authService.isAccessible('CATEGORY','AddButton')"
            [disabled]="authService.isDisabled('CATEGORY','AddButton')">
            Save
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!--
<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Training Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/training']">{{'Training.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'Training.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{training.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div> -->
<!-- <div class="clearfix"></div>
<div class="site-page-container">
	<div class="site-card">
		<form #trainingForm="ngForm" novalidate="novalidate">
			<div class="row justify-content-start">
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.categoryId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="categories" bindLabel="title" bindValue="id" name="trainingCategoryId"
								#trainingCategoryId="ngModel" [(ngModel)]="training.categoryId" required="required"
								#CategoryId="ngModel"
								[ngClass]="{'invalid-field':trainingCategoryId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} category">
							</ng-select> -->
<!-- <span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('subCategoryCategoryIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span> -->
<!-- <div class="form-floating">
								<div class="mb-4 form-control select-width ng-select-main-container"
									[ngClass]="{'is-invalid':trainingCategoryId.invalid && onClickValidation}">
									<ng-select [items]="categories" bindLabel="title" bindValue="id"
										name="trainingCategoryId" #trainingCategoryId="ngModel"
										[(ngModel)]="training.categoryId" #CategoryId="ngModel" required="required"
										placeholder="{{'COMMON.SELECT_OPTION' | translate}}">
									</ng-select>
								</div>
							</div>

						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.subCategoryId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="subCategories" bindLabel="title" bindValue="id"
								name="trainingSubCategoryId" #trainingSubCategoryId="ngModel"
								[(ngModel)]="training.subCategoryId" required="required" #SubCategoryId="ngModel"
								[ngClass]="{'invalid-field':trainingSubCategoryId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} subCategory">
							</ng-select>
							<span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('trainingSubCategoryIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.languageId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="languages" bindLabel="name" bindValue="id" name="trainingLanguageId"
								#trainingLanguageId="ngModel" [(ngModel)]="training.languageId" required="required"
								#LanguageId="ngModel"
								[ngClass]="{'invalid-field':trainingLanguageId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} language">
							</ng-select>
							<span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('trainingLanguageIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.videoTitle" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255"
								name="trainingVideoTitle" [(ngModel)]="training.videoTitle" #VideoTitle="ngModel">
						</div>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.videoUrl" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="MAX"
								name="trainingVideoUrl" [(ngModel)]="training.videoUrl" #VideoUrl="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.publishedForTrainingFeed" | translate}}
						</label>
						<div class="material-switch">
							<input id="trainingPublishedForTrainingFeedId" name="trainingPublishedForTrainingFeed"
								type="checkbox" [(ngModel)]="training.publishedForTrainingFeed" />
							<label for="trainingPublishedForTrainingFeedId" class="label-primary"></label>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.commonVideoTitle" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="MAX"
								name="trainingCommonVideoTitle" [(ngModel)]="training.commonVideoTitle"
								#CommonVideoTitle="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.groupCode" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255"
								name="trainingGroupCode" [(ngModel)]="training.groupCode" #GroupCode="ngModel">
						</div>
					</div>
				</div>
			</div>
		</form>
		<div class="clearfix"></div>
		<div class="col-md-12 no-padding text-right">
			<button title="Save" class="btn btn-primary site-button" type="button" (click)="save(trainingForm.form)"
				*ngIf="authService.isAccessible('TRAINING','AddButton')"
				[disabled]="authService.isDisabled('TRAINING','AddButton')">
				{{"COMMON.SAVE" | translate}}
			</button>
			<button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button"
				(click)="navigate()">
				{{"COMMON.CANCEL" | translate}}
			</button>
			<div class="clearfix"></div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="modal fade nav-scroll" id="trainingCategoryIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-category [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-category>
			</div>
		</div>
	</div>
</div>
<div class="modal fade nav-scroll" id="trainingSubCategoryIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-subcategory [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-subcategory>
			</div>
		</div>
	</div>
</div>
<div class="modal fade nav-scroll" id="trainingLanguageIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-language [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-language>
			</div>
		</div>
	</div>
</div> -->
