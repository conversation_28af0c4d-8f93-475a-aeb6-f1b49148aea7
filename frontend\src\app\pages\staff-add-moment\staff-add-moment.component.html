<div data-aos="fade-up" data-aos-duration="1000" class="staff-add-moment container">
  <form #momentForm="ngForm" class="row staff-add-moment-form">
    <div class="col-12 mb-4">
      <div class="form-floating">
        <div class="form-control select-width ng-select-main-container"
          [ngClass]="{'is-invalid':farmId.invalid && onClickValidation}">
          <ng-select [items]="farms" bindLabel="name" bindValue="id" [name]="farmId" [(ngModel)]="moment.farmId"
            #farmId="ngModel" required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}}">
          </ng-select>
        </div>
        <label for="language">{{"Moment.selectSite" | translate}}</label>
      </div>
    </div>
    <!-- <div class="col-12 mb-4">
      <div class="form-floating">
        <select class="form-select" id="floatingSelectGrid" aria-label="Floating label select example">
          <option selected>Select Sites</option>
          <option value="1">One</option>
          <option value="2">Two</option>
          <option value="3">Three</option>
        </select>
        <label for="floatingSelectGrid">Select Site Worker</label>
      </div>
    </div> -->
    <div class="col-12 mb-4">
      <div class="form-floating">
        <select class="form-select form-control" name="type" aria-label="Please Select Type" [(ngModel)]="moment.type"
          [ngClass]="{'is-invalid':!type.valid && onClickValidation}" required="required" #type="ngModel">
          <option [ngValue]="undefined" selected disabled>Select Option</option>
          <option value="Physical">Physical</option>
          <option value="Procedural">Procedural</option>

        </select>
        <label for="type">{{"Moment.SelectType" | translate}}</label>
      </div>
    </div>
    <div class="col-12 mb-4">
      <div class="form-floating form-floating-textarea">
        <textarea [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
          class="form-control form-description" name="description" #description="ngModel"
          [(ngModel)]="moment.description" required="required" placeholder="Description"
          id="floatingTextarea2"></textarea>
        <label for="floatingInput">{{"Moment.description" | translate}}</label>
      </div>
    </div>
    <div class="col-12 mb-4">
      <div *ngIf="!moment.mediaUrl" class="form-control upload-img-button"
        [ngClass]="{'is-invalid': !moment.mediaUrl && onClickValidation}">
        <label id="file-input" class="cursor-pointer"
          [ngClass]="{'d-flex align-items-center justify-content-center': isLoader}"><img
            src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">{{!isLoader ? 'UPLOAD
          MOMENT VIDEO/IMAGE' : 'UPLOADING..' }} <div *ngIf="isLoader" class="spinner-border ms-2" role="status"
            style="width: 1.7rem; height: 1.7rem">
            <span class="visually-hidden">Loading...</span>
          </div>
          <input *ngIf="!isLoader" name="mediaUrl" class="d-none" (change)="uploadMomentVideoOrImage($event)"
            id="file-input" type="file" accept="audio/*, image/png, image/jpg, image/jpeg, video/*" />
        </label>
      </div>
      <div *ngIf="loadingVideo" class="col-12 form-control upload-img-button d-flex justify-content-center">
        <label class="cursor-pointer"><img src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon"
            alt="">LOADING VIDEO...
        </label>
      </div>
      <div *ngIf="mediaType == 'video' && moment.mediaUrl" [ngStyle]="{'display': !loadingVideo ? 'block': 'none'}"
        class="video-wrapper moment-video-width">
        <div class="video-container" id="video-container">
          <div class="play-button-wrapper">
            <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
              id="circle-play-b">
              <!-- SVG Play Button -->
              <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
              </svg>
            </div>
          </div>
          <div class="position-absolute delete-video-container" (click)="removeFile()">
            <i class="bi bi-x"></i>
          </div>
          <video playsinline class="mw-100" id="videoId" controlslist="nodownload">
          </video>
        </div>
      </div>
      <div *ngIf="mediaType == 'image' && moment.mediaUrl" class="moment-image-container">
        <img [src]="moment.mediaUrl" />
        <div class="position-absolute delete-video-container" (click)="removeFile()">
          <i class="bi bi-x"></i>
        </div>
      </div>
      <!-- <div class="form-check publish-training-check-cls mt-3">
        <input class="form-check-input" type="checkbox" name="recurringMoment" value=""
          id="flexCheckDefault" [(ngModel)]="moment.isRecurring">
        <label class="form-check-label" for="flexCheckDefault">
          {{"Moment.momentRecurring" | translate}}
        </label>
      </div> -->
    </div>
    <div class="col-12">
      <button (click)="save(momentForm.form)" class="btn add-moment-button bg-secondary btn-lg">ADD MOMENT</button>
    </div>
  </form>
</div>
