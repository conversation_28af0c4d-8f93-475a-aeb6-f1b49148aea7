import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { ManageCourseManager } from '../manage-course/manage-course-manager';
import { ToastService } from 'src/app/shared/toast.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonUtil } from 'src/app/shared/common.util';
import { ManageCourseService } from '../manage-course/manage-course-service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CourseTrainingsService } from './course-trainings-service';
import { CourseTrainingsManager } from './course-trainings-manager';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import AOS from 'aos';
import { Content } from 'src/app/models/content';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { FilterParam } from 'src/app/models/filterparam';
import { BaseModel } from 'src/app/config/base.model';
import { RouteDataService } from 'src/app/shared/title.service';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-course-trainings',
  templateUrl: './course-trainings.component.html',
  styleUrls: ['./course-trainings.component.scss']
})
export class CourseTrainingsComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  moment: any = moment;
  dropdownSettings = {};
  dropdownSettingsUsers = {};
  searchTraining: any;
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  trainings: CourseTrainings[];
  contentTypes: Content[];
  learningSeries: CourseLearningSeries[];
  trainingFilterData: RestResponse;
  videos: any[] = [];
  isViewAllTableVisible: boolean = false;
  filterTrainingModal: any;
  records: CourseTrainings[];
  username: any;

  constructor(protected courseTrainingsManager: CourseTrainingsManager, private courseTrainingsService: CourseTrainingsService, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService, protected route: ActivatedRoute,
    protected router: Router, public commonUtil: CommonUtil, private manageCourseService: ManageCourseService, public routeDataService: RouteDataService) {
    super(courseTrainingsManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.setDropdownSettings();
    this.setDropdownSettingsUsers();
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseTrainings>();
    this.contentTypes = new Array<Content>();
    this.filterParam.sortBy = true;
    this.learningSeries = new Array<CourseLearningSeries>();
    this.init();
    this.fetchAllTrainings()
    this.fetchAssociatedData();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('filterTrainingModal');
      if (modalElement) {
        this.filterTrainingModal = new bootstrap.Modal(modalElement);
      }

      // Initialize Bootstrap tooltips
      document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((tooltip: HTMLElement) => {
        new bootstrap.Tooltip(tooltip);
      });
    }, 0);
  }


  onFetchCompleted(): void {
    this.videos = [...this.records]
    // this.videos = [...this.duplicateArray(this.videos, 20)]
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  async fetchAssociatedData() {
    this.trainingFilterData = await this.courseTrainingsService.getTrainingFilterData(null);

    // this.learningSeries=  new Array<CourseLearningSeries>();
    // this.trainingFilterData.data.forEach(element => {
    //   this.learningSeries.push(element.learningSeriesDetail);
    // });
    // console.log(this.learningSeries);

  }


  updateStatus(data: any, recordData: any) {
    let status = JSON.parse(JSON.stringify(data.currentTarget.checked));
    if (!data.currentTarget.checked) {
      data.currentTarget.checked = true;
    }
    this.commonService.confirmation('Would you like to change the status of Training?', this.updateStatusCallback.bind(this), { id: recordData.id, isPublish: status }, null, null, this.cancelUpdateStatusCallback.bind(this));
  }

  onpublishChange(event) {
    event.target.checked = true;
  }

  //Accssibility update status
  getTooltipText(accessibility: string): string {
    return accessibility === 'Public' ? 'Public' : 'Private';
  }

  updateStatusAccssibility(record: any, index) {
    this.commonService.confirmation('Would you like to change the Accessibility Mode?', this.updateAccessibilityCallback.bind(this), { id: record.id, accessibility: record.accessibility }, null, null, this.cancelUpdateStatusCallback.bind(this));
  }

  // onCancelAccessibility(record, index) {
  //   this.records[index] = record;
  // }

  updateStatusAccssibilityMethod(record: any, index) {
    if (record.accessibility === "Public") {
      this.records[index].accessibility = "Private";
    }
    else {
      this.records[index].accessibility = "Public";
    }
    const data = {
      id: record.id,
      accessibility: record.accessibility
    };
    this.updateAccessibilityCallback(data);
  }

  cancelUpdateStatusCallback() {
    this.onCancel();
  }

  async updateAccessibilityCallback(data) {
    data.accessibility = data.accessibility === "Public" ? "Private" : "Public";
    try {
      this.loadingService.show();
      console.log(data)
      const response: RestResponse = await this.courseTrainingsManager.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  //Accssibility update status end

  async updateStatusCallback(data: any) {

    try {
      this.loadingService.show();
      const response: RestResponse = await this.courseTrainingsManager.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }


  openTrainingDetailPage(record: any) {
    this.router.navigate(
      ['/dashboard/program-admin/training/detail/' + record.id],
    )
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  setDropdownSettingsUsers() {
    this.dropdownSettingsUsers = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/program-admin/trainings/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/program-admin/trainings/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  removeSuccess() {
    this.onCancel();
  }
  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/trainings/edit/' + id])

  }
  @ViewChild('tableContainer', { static: false }) tableContainer: ElementRef;


  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.videos, event.previousIndex, event.currentIndex);
    this.updateSequences()

    const payload = this.generatePayload();

    this.updateTrainingOrder(payload);
  }

  dragMoved(event: any) {
    const container = this.tableContainer.nativeElement;
    const scrollIncrement = 20;

    if (event.pointerPosition.y > container.clientHeight + container.scrollTop - 100) {
      container.scrollTop += scrollIncrement;
    } else if (event.pointerPosition.y < container.scrollTop + 100) {
      container.scrollTop -= scrollIncrement;
    }

  }

  duplicateArray(array, times) {
    if (times < 1) {
      console.log("Number of times should be at least 1");
      return [];
    }

    let result = [];

    for (let i = 0; i < times; i++) {
      result = result.concat(array);
    }
    console.log(result)

    return result;
  }

  async fetchAllTrainings() {
    try {
      const filterParam: FilterParam = new FilterParam();
      filterParam.sortBy = true;
      this.loadingService.show();
      const response: RestResponse = await this.courseTrainingsService.getAllTrainings(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.videos = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  toggleDropdown() {
    this.isViewAllTableVisible = !this.isViewAllTableVisible;
  }

  // Update sequence values
  updateSequences() {
    this.videos.forEach((training, index) => {
      training.sequence = (index + 1); // Update the sequence based on the index
    });
  }

  generatePayload(): any[] {
    return this.videos.map(training => ({
      id: training.id,
      sequence: training.sequence
    }));
  }

  async updateTrainingOrder(orderData: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseService.changeOrder(orderData);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.fetchAllTrainings();
      this.refreshRecord()
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // filter listing data

  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      delete this.filterParam.searchCommonTitle;
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  onClickTrainingFilter(valid) {
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  resetFilter() {
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.searchText;
    delete this.filterParam.isPublish;
    delete this.filterParam.accessibility;
    this.filterTrainingModal.hide();
    this.onCancel();
  }

}
