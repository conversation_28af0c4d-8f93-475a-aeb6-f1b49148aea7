.vertical-line {
    border-left: 1px solid #000;
    height: 34px;
    margin: 10px 15px;
    width: 1px;
}

table.dataTable tbody th,
table.dataTable tbody td {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
}

.site-customer-main-container .add-button {
    max-width: 155px;
}

// 
.btn-close-dark {
    color: #000;
    border: none;
    opacity: 1;
}

// 
.b-r-8 {
    border-radius: 8px !important;
}

.site-customer-main-container .dashboard-content-container {
    min-height: auto !important;
}

// 

.accordion {
    --bs-accordion-bg: transparent !important;
}

.accordion-button {
    background-color: #1681FFAB;
    color: white;
    border-radius: 0px !important;
}

.accordion-button:focus {
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    color: white;
    background-color: #1681FFAB;
    box-shadow: 0px 3px 6px #00000029;
    opacity: 1;
}

.accordion-body .d-flex {
    // border-top: 1px solid #ddd;
    padding: 8px 0;
}

.accordion-body .d-flex:first-child {
    border-top: none;
}

.accordion-item {
    border: 0px;
    border-radius: 0px;
}

.accordion-button::after {
    flex-shrink: 0;
    width: var(--bs-accordion-btn-icon-width);
    height: var(--bs-accordion-btn-icon-width);
    margin-left: auto;
    content: "";
    background-image: var(--bs-accordion-btn-icon);
    background-position: center;
    border-radius: 50px;
    padding: 20px;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: var(--bs-accordion-btn-icon-width);
    transition: var(--bs-accordion-btn-icon-transition);
}

.course-description {
    max-width: 92%;
    padding: 0px 23px;
}

.line {
    margin: 15px 0;
    width: 96%;
    color: #e3e7ea;
}

.info-part {
    z-index: 20;
    position: absolute;
    right: 102px;
    font-size: 30px;
    top: 50%;
    transform: translateY(-50%);
    border: none !important;
    color: #fff !important;
}

.modal-title {
    font-size: 14px;
    font-weight: 600;
    margin-top: 0;
    border-radius: 1px;
    color: #00000029;
}


.code {
    font-weight: 600;
    cursor: pointer;
}

.code:hover {
    color: #1681ff;
}

.courseRate {
    width: 150px;
    /* Set a fixed width */
    height: 50px;
    /* Set a fixed height */
    display: flex;
    /* Use flexbox for alignment */
    align-items: center;
    /* Vertically center the content */
    justify-content: center;
    /* Horizontally center the content */
    border: 1px solid #000;
    /* Add a border for consistent appearance */
    font-size: 16px;
    /* Adjust font size */
    text-align: center;
    /* Center align the text */
    margin: 10px 0;
    /* Add spacing between the elements */
    box-sizing: border-box;
    /* Ensure padding/border doesn't affect dimensions */
}

.complete-previous-step {
    width: 36px;
}

.no-records-container {
    text-align: center;
    color: #000;
    font-size: 1rem;
    padding: 10px 0;
    font-weight: 400;
    background-color: #fff;
    border-radius: 4px;
    border: 0px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 10px 40px;
}