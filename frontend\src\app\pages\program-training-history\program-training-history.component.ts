import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Constant } from 'src/app/config/constants';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from 'src/app/shared/auth.services';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Users } from 'src/app/models/users';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';
import AOS from 'aos';
import { saveAs } from 'file-saver';
import { ProgramTrainingHistoryManager } from './program-training-history.manager';
import { ProgramTrainingHistoryService } from './program-training-history.service';
import { CourseTrainingsService } from '../course-trainings/course-trainings-service';
import { LayoutComponent } from '../layout/layout.component';
import { RouteDataService } from 'src/app/shared/title.service';
import { UsersService } from 'src/app/services/users.service';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-program-training-history',
  templateUrl: './program-training-history.component.html',
  styleUrls: ['./program-training-history.component.scss']
})
export class ProgramTrainingHistoryComponent extends BaseListServerSideComponent implements OnInit {
  @Input() assignVideoFromMoment: boolean | false;
  @Output() assignTrainingIdOutput = new EventEmitter<string>();

  readonly MY_CONSTANT = Constant;
  strUserId: string;
  loadingVideo: boolean;
  programUserTrainingVideoModal: any;
  moment: any = moment;
  searchTraining: any;
  isTrainingCsvExport: boolean = false
  filterTrainingModal: any;
  trainingFilterData: RestResponse;
  username: any;
  user: Users;
  @Input() userId?: string;


  constructor(private route: ActivatedRoute, protected programTrainingHistoryManager: ProgramTrainingHistoryManager,private usersService: UsersService, protected commonService: CommonService,
    protected toastService: ToastService, protected loadingService: LoadingService,private layoutComponent: LayoutComponent,
    protected router: Router, private programTrainingHistoryService: ProgramTrainingHistoryService, private courseTrainingsService: CourseTrainingsService, public routeDataService: RouteDataService,
    private commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, public authService: AuthService, private http: HttpClient) {
    super(programTrainingHistoryManager, commonService, toastService, loadingService, router);
  }

  async ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    this.records = [] as Users[];
    this.strUserId = this.route.snapshot.paramMap.get('id');
    this.programTrainingHistoryService.getUserId(this.strUserId);
    this.filterParam.strUserId = this.strUserId;
    // this.getTrainingFilterData();
    this.init();
    this.fetchUserDetail();

  }

  // async getTrainingFilterData() {
  //   this.trainingFilterData = await this.courseTrainingsService.getTrainingFilterData(null);
  //   this.trainingFilterData.data[0].trainingStatusDetail = this.trainingFilterData.data[0].trainingStatusDetail.map(data => {
  //     return {
  //       id: this.commonUtil.trainingFilterIdData[data.status],
  //       status: this.commonUtil.getTitleStatus(data.status)
  //     }
  //   })
  // }

  // async fetchRecords(param, callBack) {
  //   try {
  //     this.hasDataLoad = false;
  //     this.setParam(param);
  //     this.loadingService.show();
  //     const response: RestResponse = await this.programTrainingHistoryManager.fetch(this.strUserId);
  //     this.loadingService.hide();
  //     if (!response.status) {
  //       this.toastService.error(response.message);
  //       return;
  //     }
  //     this.records = response.data;
  //     this.onFetchCompleted();
  //     callBack({ recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length, recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length, data: [] });
  //   } catch (error) {
  //     this.loadingService.hide();
  //     this.toastService.error(error.message);
  //   }
  // }

  ngAfterViewInit() {
    // setTimeout(() => {
    //   this.filterTrainingModal = new bootstrap.Modal(
    //     document.getElementById('filterTrainingModal')
    //   );
    // }, 0);
    setTimeout(() => {
      this.programUserTrainingVideoModal = new bootstrap.Modal(
        document.getElementById('programUserTrainingVideoModal')
      );
    }, 0);
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  resetFilter() {
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.searchText;
    delete this.filterParam.status;
    this.filterTrainingModal.hide();
    this.onCancel();
  }

  onClickTrainingFilter() {
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  approveOrRejectTraining(id: string, status: string) {
    let data = {
      id: id,
      userId: this.strUserId,
      status: status
    }
    const statusText = status == "APPROVED" ? 'approve' : 'reject';
    const confirmatiomMessage = 'Would you like to ' + statusText + ' this training?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectTrainingCallback.bind(this), data);
  }

  async approveOrRejectTrainingCallback(data: boolean) {
    try {
      const response: RestResponse = await this.programTrainingHistoryService.approveOrRejectTraining(data)
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

  exportTrainingHistroyCsv() {
    this.isTrainingCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], this.username + '_My Training Report_' + moment().format('DD-MM-YYYY') + '.xlsx', { type: 'application/vnd.ms.excel' });

        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isTrainingCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isTrainingCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isTrainingCsvExport = false;
      return;
    }
  }

  download(): Observable<Blob> {
    const strUserId = this.route.snapshot.paramMap.get('id');
    return this.http.post(environment.BaseApiUrl + '/api/trainings/' + 'userid' + '/export',
      this.filterParam, { responseType: 'blob' }).pipe(catchError(this.parseErrorBlob));
  }

  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isTrainingCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }

  watchVideo(record: any) {
    this.loadingVideo = true
    this.programUserTrainingVideoModal.show();
    AOS.init({ disable: true });
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.videoUrl);
      this.loadingVideo = false;
    }, 0)
  }

  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  openTrainingDetailPage(record: any) {
    window.open(
      '/dashboard/program-admin/course/training/detail/' + record.trainingDetail.id,
      '_blank'
    );
  }

  assignTrainingVideoCallback(id: string) {
    this.assignTrainingIdOutput.emit(id);
  }
  assignTrainingVideo(id: string) {
    this.commonService.confirmation('Would you like to assign this training video?', this.assignTrainingVideoCallback.bind(this), id, null, null, null);
  }
  //cours detail page linking
  openCourseDetailPage(record: any) {
    window.open(
      '/dashboard/program-admin/course/detail/' + record.courseDetail.id,
      '_blank'
    );
  }

  //Breadcrumb set
  onFetchCompleted() {
    let obj: any = {};
    obj.type = "TEXT_REPLACE";
    obj.title = this.username;
    // this.commonService.updateData(obj);
    // this.routeDataService.setData(this.router.url, this.username);

  }

  async fetchUserDetail() {
    try {
      const id=this.route.snapshot.paramMap.get('id')
      const response: RestResponse = await this.usersService.fetch(id).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.request.isNewRecord = false;
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      this.setbrreadCrumbTitle();
      // this.setSelectedFarmsUserEdit(userFarmMapping);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }


  setbrreadCrumbTitle(){
    // this.layoutComponent.breadcrumbs.forEach((data, index) => {
    //   if (index == this.layoutComponent.breadcrumbs.length - 1) {
    //     data.title = this.user.fullName;
    //   }
    // })
    // this.layoutComponent.breadcrumbs = this.layoutComponent.breadcrumbs
    // this.routeDataService.setData(this.router.url, this.user.fullName);
        this.routeDataService.setData(this.router.url, this.user.fullName);

  }

}
