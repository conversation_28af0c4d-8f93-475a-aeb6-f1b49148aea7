import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { FarmAdminService } from '../farm-admin.service';

@Injectable({ providedIn: 'root' })
export class FarmAdminEditResolver implements Resolve<any> {

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const farmAdminId: any | null = route.paramMap.get("id");

        return of(
            farmAdminId == 0 ? "New Company Admin" : "Edit Company Admin"
        )
    }
}
