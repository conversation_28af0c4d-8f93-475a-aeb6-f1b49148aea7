import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Training } from '../../../models/training-old 08-01-2024';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { TrainingManager } from '../training.manager';

import { CategoryManager } from '../../category/category.manager';
import { Category } from '../../../models/category';
import { SubCategoryManager } from '../../subcategory/subcategory.manager';
import { SubCategory } from '../../../models/subcategory';
import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from 'src/app/config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
import { environment } from 'src/environments/environment';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { lang } from 'moment';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { TrainingCategoryMapping } from 'src/app/models/trainingcategorymapping';
import { TrainingSubCategoryMapping } from 'src/app/models/trainingsubcategorymapping';
declare const $: any;

@Component({
  selector: 'app-training-edit',
  templateUrl: './training-edit.component.html',
  styleUrls: ['./training-edit.component.scss']
})

export class TrainingEditComponent extends BaseEditComponent implements OnInit, OnDestroy {
  public training: Training;
  public categories: Category[];
  public subCategories: SubCategory[];
  public languages: Language[];
  public trainingFormInputs: any[]
  public languageIds: any = [];
  public videoPlayIds: any = [];
  public loadingVideoIds: any = [];
  readonly MY_CONSTANT = Constant;
  private fileData: any;
  languageId: string;
  categoryDropdownSettings = {};
  subCategoryDropdownSettings = {};

  constructor(protected route: ActivatedRoute, protected trainingManager: TrainingManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService
    , private categoryManager: CategoryManager, private subcategoryManager: SubCategoryManager, private languageManager: LanguageManager
    , public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(trainingManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.training = new Training();
    this.training.isActive = true;
    this.setRecord(this.training);
    this.setCategoryDropdownSettings();
    this.setSubCategoryDropdownSettings();

    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.categories = new Array<Category>();
    this.subCategories = new Array<SubCategory>();
    this.languages = new Array<Language>();
    this.init();
  }

  ngOnDestroy() {

  }

  onFetchCompleted() {
    this.training = Training.fromResponse(this.record);
    this.setRecord(this.training);
  }

  async selectCategory(event: any) {
    const response: RestResponse = await this.categoryManager.fetch(event.id);
    this.trainingFormInputs.map(training => {
      const exist = response.data.find(category => category.languageId == training.languageId);
      if (exist) {
        training.categoryId = exist.id;
      }
      return training;
    })
  }

  async selectSubCategory(event: any) {
    const response: RestResponse = await this.subcategoryManager.fetch(event.id);
    this.trainingFormInputs.map(training => {
      const exist = response.data.find(subCategory => subCategory.languageId == training.languageId);
      // console.log(exist, 'exist', training.languageId)
      if (exist) {
        training.subCategoryId = exist.id;
      }
      return training;
    });
  }

  uploadVideo(languageId: string, event) {
    const file = event.target.files[0];
    if (languageId) {
      this.languageId = languageId;
      this.languageIds.push(languageId);
    }
    if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg"
      && event.target.files[0].type != "image/png") {
      this.commonService.convertVideoFormat(file).then(res => {
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    }
    else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  async fetchAssociatedData() {
    this.filterParam.isOnlyEnglishLanguage = true;
    this.categories = await this.categoryManager.fetchAllData(this.filterParam);
    this.subCategories = await this.subcategoryManager.fetchAllData(this.filterParam);
    this.languages = await this.languageManager.fetchAllData(null);
    this.afterFetchAssociatedCompleted();
    this.setTrainingInputsOnAdd(this.languages);
  }

  setTrainingInputsOnAdd(languages: any) {
    if (this.request.recordId == 0) {
      this.trainingFormInputs = languages.map(language => ({
        languageId: language.id,
        languageName: language.name,
        uploader: this.initializeUploaderTraining(null, 'mp4,mkv', null, null, this.toastService, "Only Mkv, Mp4 files are allowed", null, language.id),
        title: "",
        categoryId: "",
        subCategoryId: "",
        videoUrl: "",
        description: "",
        isActive: true,
        isDeleted: false,
        publishedForTrainingFeed: false,
        videoCredit: ""

      })).sort((a, b) => {
        return a.languageName.localeCompare(b.languageName);
      });
    }
  }

  addDescriptionToRemainingLanguages(data: any) {
    this.trainingFormInputs.map(input => {
      if (input.languageName !== Constant.languages.English) {
        input.description = data;
      }
      return input
    })
  }

  addTrainingTitleToRemainingLanguages(data: any) {
    this.trainingFormInputs.map(input => {
      if (input.languageName !== Constant.languages.English) {
        input.videoTitle = data;
      }
      return input
    })
  }

  getImageFetchByLanguageName(name: any) {
    switch (name) {
      case Constant.languages.English:
        return "/assets/images/icons/menu/united.svg";
      case Constant.languages.Vietnamese:
        return "/assets/images/icons/menu/Vietnam.svg";
      case Constant.languages.Bahasa:
        return "/assets/images/icons/menu/indonesia.svg"
      default:
        return "/assets/images/icons/menu/united.svg";
    }
  }

  afterFetchAssociatedCompleted() {
    const categoryIdId: string = this.route.snapshot.queryParamMap.get('Category');
    if (categoryIdId) {
      this.onAssociatedValueSelected({ "id": categoryIdId }, 'trainingCategoryIdSelect');
    }
    const subCategoryIdId: string = this.route.snapshot.queryParamMap.get('SubCategory');
    if (subCategoryIdId) {
      this.onAssociatedValueSelected({ "id": subCategoryIdId }, 'trainingSubCategoryIdSelect');
    }
    const languageIdId: string = this.route.snapshot.queryParamMap.get('Language');
    if (languageIdId) {
      this.onAssociatedValueSelected({ "id": languageIdId }, 'trainingLanguageIdSelect');
    }
  }

  onSaveSuccess(data: any) {
    this.navigate('/dashboard/training');
  }

  onUploadSuccessTraining(file: any, files: any, languageId: string) {
    this.trainingFormInputs.map(training => {
      if (languageId == training.languageId) {
        training.videoUrl = file.path;
      }
      return training
    })

    this.loadVideoFromUrl.UrlToBlobUrl(file.path)
      .then(blobUrl => { // now it's loaded
        document.body.className = 'loaded';
        setTimeout(() => {
          let vid = document.getElementById('video' + languageId) as HTMLVideoElement;
          this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
          vid.addEventListener('canplaythrough', (event) => {
            this.loadingVideoIds.push(languageId)
          })
        }, 0);
      }).catch((err) => console.log(err));

    this.languageIds = this.languageIds.filter(languageDataId => languageDataId !== languageId)
  }

  fileValidationErrorTraining(data: string, toastService: any, languageId: string) {
    this.languageIds = this.languageIds.filter(languageDataId => languageDataId !== languageId)
    toastService.error(data);
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string, languageId: string) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService, languageId);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService, languageId);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessTraining(file, files, languageId);
        }, 200);
      }
    };

    uploader.onErrorItem = (fileItem, response) => {
      this.languageIds = this.languageIds.filter(languageDataId => languageDataId !== languageId)
      toastService.error('Something error occurred please try again later');
    }

    return uploader;
  }

  playVideoFromPlayIcon(languageId: string) {
    var videoId = document.getElementById("video" + languageId) as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      videoId.controls = true;
      this.videoPlayIds.push(languageId);
    });
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlayIds = this.videoPlayIds.filter(id => id != languageId)

    });
  }
  async fetchExistingRecord() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      //let trainings = JSON.parse(JSON.stringify(response.data));
      this.setTrainingInputsOnEdit(response.data)

      this.request.isNewRecord = false;
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  setTrainingInputsOnEdit(allData: any) {
    this.trainingFormInputs = allData.map(data => ({
      id: data?.id,
      languageId: data?.languageId,
      languageName: data?.languageIdDetail.name,
      uploader: this.initializeUploaderTraining(null, 'mp4,mkv', null, null, this.toastService, "Only Mkv, Mp4 files are allowed", null, data?.languageId),
      videoTitle: data?.videoTitle,
      categoryId: data?.categoryIdDetail?.id,
      subCategoryId: data?.subCategoryIdDetail?.id,
      videoUrl: data?.videoUrl,
      description: data?.description,
      isActive: data?.isActive,
      isDeleted: data?.isDeleted,
      publishedForTrainingFeed: data?.publishedForTrainingFeed,
      videoCredit: data?.videoCredit,
      trainingCategoryMappingList: data.trainingCategoryMappingList,
      trainingSubCategoryMappingList: data.trainingSubCategoryMappingList
    })).sort((a, b) => {
      return a.languageName.localeCompare(b.languageName);
    });

    allData.forEach(element => {
      this.loadVideoFromUrl.UrlToBlobUrl(element.videoUrl)
        .then(blobUrl => { // now it's loaded
          document.body.className = 'loaded';
          setTimeout(() => {
            let vid = document.getElementById('video' + element.languageId) as HTMLVideoElement;
            this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
            vid.addEventListener('canplaythrough', (event) => {
              this.loadingVideoIds.push(element.languageId);
            })
          }, 0)
        }).catch((err) => console.log(err));
    });

    this.training.videoCredit = this.trainingFormInputs[0].videoCredit;
    this.training.publishedForTrainingFeed = this.trainingFormInputs[0].publishedForTrainingFeed;

    const englishDataTraining = this.trainingFormInputs.find(training => training.languageName == Constant.languages.English);
    if (!englishDataTraining.selectedCategories) {
      englishDataTraining.selectedCategories = new Array<Category>();
    }
    if (!this.isNullOrUndefined(englishDataTraining.trainingCategoryMappingList) && englishDataTraining.trainingCategoryMappingList.length > 0) {
      englishDataTraining.trainingCategoryMappingList.forEach(item => {
        let selectedCategory = new Category();
        selectedCategory.id = item.categoryId;
        selectedCategory.title = item.categoryName;
        englishDataTraining.selectedCategories.push(selectedCategory);
      });
    }
    this.training.selectedCategories = englishDataTraining.selectedCategories;

    if (!englishDataTraining.selectedSubCategories) {
      englishDataTraining.selectedSubCategories = new Array<SubCategory>();
    }
    if (!this.isNullOrUndefined(englishDataTraining.trainingSubCategoryMappingList) && englishDataTraining.trainingSubCategoryMappingList.length > 0) {
      englishDataTraining.trainingSubCategoryMappingList.forEach(item => {
        let selectedSubCategory = new SubCategory();
        selectedSubCategory.id = item.subCategoryId;
        selectedSubCategory.title = item.subCategoryName;
        englishDataTraining.selectedSubCategories.push(selectedSubCategory);
      });
    }
    this.training.selectedSubCategories = englishDataTraining.selectedSubCategories;
    // console.log(this.trainingFormInputs)
  }


  removeFile(data) {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), data);
  }

  removeFileCallback(data) {
    this.trainingFormInputs.map(training => {
      if (training.languageId == data) {
        training.videoUrl = '';
      }
      return training
    })
    this.loadingVideoIds = this.loadingVideoIds.filter(id => id != data);
  }

  checkConditionToReload(records: BaseModel[], selectedRecord: any) {
    if (!records.some(x => x.id === selectedRecord.id)) {
      this.fetchAssociatedData();
    }
  }

  onAssociatedValueSelected(selectedRecord: any, selectedField: any) {
    if (this.request.popupId) {
      $('#' + this.request.popupId).appendTo('body').modal('hide');
    }
    if ((!this.isNullOrUndefined(selectedField) && selectedField === 'trainingCategoryIdSelect') || this.request.popupId === 'trainingCategoryIdPopup') {
      this.training.categoryId = selectedRecord.id;
      this.checkConditionToReload(this.categories, selectedRecord);
      return;
    }
    if ((!this.isNullOrUndefined(selectedField) && selectedField === 'trainingSubCategoryIdSelect') || this.request.popupId === 'trainingSubCategoryIdPopup') {
      this.training.subCategoryId = selectedRecord.id;
      this.checkConditionToReload(this.subCategories, selectedRecord);
      return;
    }
    if ((!this.isNullOrUndefined(selectedField) && selectedField === 'trainingLanguageIdSelect') || this.request.popupId === 'trainingLanguageIdPopup') {
      this.training.languageId = selectedRecord.id;
      this.checkConditionToReload(this.languages, selectedRecord);
      return;
    }

  }

  async save(form: any) {
    const trainingFormInputsUpdatedData = this.trainingFormInputs.map(({ uploader, languageName, videoCredit, publishedForTrainingFeed, ...training }) => ({ videoCredit: this.training.videoCredit, publishedForTrainingFeed: this.training.publishedForTrainingFeed, ...training }));

    // let isvideoUrlExist = true;
    // trainingFormInputsUpdatedData.forEach(element => {
    //   if (!element.videoUrl) {
    //     isvideoUrlExist = false;
    //   }
    // })
    // if (!isvideoUrlExist) {
    //   this.onClickValidation = true;
    //   return
    // }
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    trainingFormInputsUpdatedData.forEach(x => {
      delete x.categoryId;
      delete x.subCategoryId;
    });

    // trainingFormInputsUpdatedData.forEach((x: Training) => {
    //   // training category mapping
    //   let selectedCategory = new TrainingCategoryMapping();
    //   if (!x.trainingCategoryMappingList) {
    //     x.trainingCategoryMappingList = new Array<TrainingCategoryMapping>();
    //   }
    //   this.selectedCategories.forEach(category => {
    //     let index = x.trainingCategoryMappingList.findIndex(y => y.categoryId == category.id);
    //     if (index === -1) {
    //       selectedCategory.categoryId = category.id;
    //       x.trainingCategoryMappingList.push(selectedCategory);
    //     }
    //   });
    //   x.trainingCategoryMappingList.forEach(item => {
    //     if (item.id != null) {
    //       let index = this.selectedCategories.findIndex(x => x.id == item.categoryId);
    //       if (index === -1) {
    //         item.isDeleted = true;
    //       }
    //     }
    //   });

    //   // training subcategory mapping
    //   let selectedSubCategory = new TrainingSubCategoryMapping();
    //   if (!x.trainingSubCategoryMappingList) {
    //     x.trainingSubCategoryMappingList = new Array<TrainingSubCategoryMapping>();
    //   }
    //   this.selectedCategories.forEach(subCategory => {
    //     let index = x.trainingSubCategoryMappingList.findIndex(y => y.subCategoryId == subCategory.id);
    //     if (index === -1) {
    //       selectedSubCategory.subCategoryId = subCategory.id;
    //       x.trainingSubCategoryMappingList.push(selectedSubCategory);
    //     }
    //   });
    //   x.trainingSubCategoryMappingList.forEach(item => {
    //     if (item.id != null) {
    //       let index = this.selectedCategories.findIndex(x => x.id == item.subCategoryId);
    //       if (index === -1) {
    //         item.isDeleted = true;
    //       }
    //     }
    //   });

    // });

    // if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
    //   return;
    // }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.manager[method](trainingFormInputsUpdatedData);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.data);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async onFileProcessingCompleted(files: any) {
    let index = this.trainingFormInputs.findIndex(x => x.languageId == this.languageId);
    this.trainingFormInputs[index].uploader.addToQueue(files);
    this.trainingFormInputs[index].uploader.uploadAll();
  }

  setCategoryDropdownSettings() {
    this.categoryDropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'title',
      allowSearchFilter: true
    };
  }

  setSubCategoryDropdownSettings() {
    this.subCategoryDropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'title',
      allowSearchFilter: true
    };
  }

  async onCategorySelect(event: any) {
    const response: RestResponse = await this.categoryManager.fetch(event.id);
    this.trainingFormInputs.forEach(training => {
      if (!training.trainingCategoryMappingList) {
        training.trainingCategoryMappingList = new Array<TrainingCategoryMapping>();
      }
      const exist = response.data.find(category => category.languageId == training.languageId);
      if (exist) {
        let selectCategory = new TrainingCategoryMapping();
        selectCategory.categoryId = exist.id;
        training.trainingCategoryMappingList.push(selectCategory);
      }
    });
  }

  async onCategoryDeSelect(event) {
    let commonTitle = this.categories.find(x=> x.id === event.id).commonTitle;
    this.trainingFormInputs.forEach(training => {
      let index = training.trainingCategoryMappingList.findIndex(x=> x.categoryCommmonTitle === commonTitle);
      if (index !== -1) {
        if(training.trainingCategoryMappingList[index].id != null) {
          training.trainingCategoryMappingList[index].isDeleted = true;
        }
        else {
          training.trainingCategoryMappingList.splice(index, 1);
        }
      }
    });
  }

  async onSubCategorySelect(event: any) {
    const response: RestResponse = await this.subcategoryManager.fetch(event.id);
    this.trainingFormInputs.forEach(training => {
      if (!training.trainingSubCategoryMappingList) {
        training.trainingSubCategoryMappingList = new Array<TrainingSubCategoryMapping>();
      }
      const exist = response.data.find(subCategory => subCategory.languageId == training.languageId);
      if (exist) {
        let selectSubCategory = new TrainingSubCategoryMapping();
        selectSubCategory.subCategoryId = exist.id;
        training.trainingSubCategoryMappingList.push(selectSubCategory);
      }
    });
  }

  async onSubCategoryDeSelect(event) {
    let commonTitle = this.subCategories.find(x=> x.id === event.id).commonTitle;
    this.trainingFormInputs.forEach(training => {
      let index = training.trainingSubCategoryMappingList.findIndex(x=> x.subCategoryCommmonTitle === commonTitle);
      if(index !== -1) {
        if(training.trainingSubCategoryMappingList[index].id != null) {
          training.trainingSubCategoryMappingList[index].isDeleted = true;
        }
        else {
          training.trainingSubCategoryMappingList.splice(index, 1);
        }
      }
    });
  }
}
