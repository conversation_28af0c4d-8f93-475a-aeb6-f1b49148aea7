import { HttpHeaders } from '@angular/common/http';

const host : any = window.location.protocol + "//" + window.location.host;

export const environment = {
  production: true,
  BaseApiUrl: 'https://integrax-dev.iotasolstaging.com',
  // BaseApiUrl: 'https://integrax-staging.iotasolstaging.com',
  // BaseApiUrl: 'https://integrax-stg.azurewebsites.net',
  // BaseApiUrl:host,
  //BaseApiUrl:'https://integrax-prod.azurewebsites.net',
  AppHeaders: new HttpHeaders({
    'Content-Type': 'application/json',
    Accept: 'application/json'
  })
};
