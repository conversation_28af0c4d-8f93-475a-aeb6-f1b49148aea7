import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class MomentEditTitle implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const momentId: any | null = route.paramMap.get("id");
        return of(
            momentId == 0 ? "New Moment" : "Edit Moment"
        )
    }
} 