<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Language Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Language.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('LANGUAGE','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('LANGUAGE','AddButton')">
            <span class="hidden-xs">{{'Language.ADD_NEW_LANGUAGE' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Language.name' | translate}}</th>
					    		<th>{{'Language.country' | translate}}</th>
					    		<th>{{'Language.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        			<td>{{record.name}}</td>
					        			<td>{{record.country}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('LANGUAGE','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('LANGUAGE','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('LANGUAGE','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('LANGUAGE','EditButton')"
                           [routerLink]="['/dashboard/language/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('LANGUAGE','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('LANGUAGE','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-language-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-language-edit>

<div class="modal fade site-detail-modal right" id="languageDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="languageDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-language-detail [recordId]="selectedId"></app-language-detail>
            </div>
        </div>
    </div>
</div>

