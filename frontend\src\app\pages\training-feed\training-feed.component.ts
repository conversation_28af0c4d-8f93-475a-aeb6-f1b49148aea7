import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { TrainingFeed } from 'src/app/models/trainingfeed';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { TrainingFeedManager } from './trainingfeed.manager';
import AOS from 'aos';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-training-feed',
  templateUrl: './training-feed.component.html',
  styleUrls: ['./training-feed.component.scss']
})
export class TrainingFeedComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  filterTrainingFeedModal: any;
  fromDate: string;
  searchTraining: any;
  toDate: string;
  constructor(protected trainingFeedManager: TrainingFeedManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil) {
    super(trainingFeedManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
  }

  ngOnInit(){
    this.filterParam.isPublish = true;
    this.records = new Array<TrainingFeed>();
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.filterTrainingFeedModal = new bootstrap.Modal(
        document.getElementById('filterTrainingFeedModal')
      );
    }, 0)
  }

  openFilterTrainingModal(){
    if (this.searchTraining) {
      this.searchTraining = "";
      delete this.filterParam.searchCommonTitle;
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingFeedModal.show();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  onClickTrainingFilter(valid) {
    this.filterTrainingFeedModal.hide()
    this.onCancel();
  }

  resetFilter() {
    this.fromDate = null;
    this.toDate = null;
    delete this.filterParam.startDate
    delete this.filterParam.endDate;
    delete this.filterParam.categorySearch;
    delete this.filterParam.subCategorySearch;
    delete this.filterParam.searchCommonTitle;
    this.filterTrainingFeedModal.hide();
    this.onCancel();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchCommonTitle = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  editRecord(id: any) {
		this.router.navigate(['/dashboard/training/edit/' + id])
	}

  removeSuccess() {
		this.onCancel();
	}

  onCancel() {
		this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
	}

}
