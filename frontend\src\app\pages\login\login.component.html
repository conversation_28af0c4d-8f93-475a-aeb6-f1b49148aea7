<div class="site-login-page">
  <div class="row g-0">
    <div  data-aos="flip-up" data-aos-duration="1000" class="col-12 col-lg-5 col-xl-3 col-xxl-3 offset-lg-1 offset-xl-1 offset-xxl-2 login-left-side-section">
      <div class="login-form-section">
        <div class="site-logo d-sm-block d-lg-none text-right">
          <img src="/assets/images/banner.png">
        </div>
        <div [routerLink]="['/']" class="margin-bottom-40 cursor-pointer">
          <img src="/assets/images/logo.svg" class="img-fluid" width="200">
        </div>
        <h2>Welcome to integrax</h2>
        <p>Login to continue</p>
        <div class="clearfix"></div>
        <form autocomplete="off" novalidate="novalidate" #loginForm="ngForm">
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input class="form-control" type="text" name="useUserName" #useUserName="ngModel"
                [(ngModel)]="data.userName" required="required" placeholder="{{'LOGIN.EMAIL' | translate}}"
                [ngClass]="{'is-invalid':!useUserName.valid && onClickValidation}">
              <label for="useUserName"> {{"LOGIN.MOBILE_OR_EMAIL"   | translate }}</label>
            </div>
            <app-validation-message [field]="useUserName" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input class="form-control" [type]="passwordFieldType" name="userPassword" #userPassword="ngModel"
                [(ngModel)]="data.password" required="required" placeholder="{{'LOGIN.PASSWORD' | translate}}"
                [ngClass]="{'is-invalid':!userPassword.valid && onClickValidation}">
              <label for="userPassword">{{"LOGIN.PASSWORD" | translate}}</label>
              <i (click)="eyePassword()" *ngIf="passwordFieldType == 'text'" class="bi bi-eye"></i>
              <i  (click)="eyePassword()" class="bi bi-eye-slash" *ngIf="passwordFieldType == 'password'"></i>
            </div>
            <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation">
            </app-validation-message>
            <div class="text-end justify-content-end pt-4">
              <a class="forgot-password cursor-pointer" (click)="gotoForgotPassword()">Forgot Password</a>
            </div>
          </div>
          <div class="col-12 margin-bottom-40">
            <button [disabled]="loginInProcess ? true : false" class="btn btn-secondary site-button large-button full-width uppercase-text"
              (click)="login(loginForm.form)">
              {{loginInProcess ? 'Please Wait...' : 'Login'}}
            </button>
          </div>
          <p class="term-condition-text">Don`t have an account? <a
              [routerLink]="['/program-register']">Create</a></p>
          <p class="term-condition-text">
            By signing in, creating an account, you are agreeing to our <br><a href="/terms-and-conditions">Terms of Use</a> and our
            <a href="/privacy-policy">Privacy Policy</a>
          </p>
        </form>
      </div>
    </div>
    <div  data-aos="flip-up" data-aos-duration="1000" class="col-12 col-lg-6 col-xl-8 col-xxl-6 d-md-none d-lg-block login-right-side-section">
    </div>
  </div>
</div>

<!-- <div class="site-login-page">
  <div class="row g-0">
    <div class="col-12 col-lg-6">
      <div class="login-right-side-section">
        <img src="/assets/images/logo.png" class="img-fluid" width="200" [routerLink]="['/']">
        <div class="clearfix"></div>
        <div class="text-center right-side-image-section">
          <img src="/assets/images/banner.png" class="img-fluid right-section-image">
          <h1>Landmark Inspections</h1>
          <p>Most comprehensive, highly rated and affordable building and property inspections</p>
        </div>
      </div>

    </div>
    <div class="col-12 col-lg-6 login-left-side-section">
      <div class="login-form-section">
        <h2>WELCOME!</h2>
        <p>Login to contiune</p>
        <div class="clearfix"></div>
        <form autocomplete="off" novalidate="novalidate" #loginForm="ngForm">
          <div class="col-12 margin-bottom-40">
            <div class="form-floating only-bottom-border">
              <input class="form-control" type="text" name="useUserName" #useUserName="ngModel"
                [(ngModel)]="data.userName" required="required" placeholder="{{'LOGIN.EMAIL' | translate}}"
                [ngClass]="{'is-invalid':!useUserName.valid && onClickValidation}" pattern="^[0-9]{7,15}$">
              <label for="useUserName"> {{"LOGIN.MOBILE_NUMBER" | translate}}</label>
            </div>
            <app-validation-message [field]="useUserName" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
          <div class="col-12 margin-bottom-40">
            <div class="form-floating only-bottom-border">
              <input class="form-control" type="password" name="userPassword" #userPassword="ngModel"
                [(ngModel)]="data.password" required="required" placeholder="{{'LOGIN.PASSWORD' | translate}}"
                [ngClass]="{'is-invalid':!userPassword.valid && onClickValidation}">
              <label for="userPassword">{{"LOGIN.PASSWORD" | translate}}</label>
            </div>
            <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
          <div class="text-right margin-bottom-60">
            <a class="forgot-password" (click)="gotoForgotPassword()">Forgot Password?</a>
          </div>
          <div class="col-12 margin-bottom-40">
            <button class="btn btn-primary site-button large-button full-width uppercase-text"
              (click)="login(loginForm.form)">
              Sign In &amp; Continue
            </button>
          </div>
          <p class="term-condition-text">
            By signing in, creating an account, you are agreeing to our <a href="#">Terms of Use</a> and our <a
              href="#">Privacy Policy</a>
          </p>
        </form>
      </div>
    </div>
  </div>
</div> -->
