import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class TrainingService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/training', '/api/trainings');
    }

    assignTraining(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/assign-training', data);
    }

    getLearningSeriesByContentTypeId(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/LearningSeries/' + filterParam.strCategoryId, null);
    }

    getTrainingFilterData(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/training/filter/data', null);
    }

    fetchPrerequisitesTraining(id: string): Observable<RestResponse> {
        return this.getRecord('/api/trainings/' + id);
    }

    fetchLearningSeriesAuthorBased(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/author/learningseries', filterParam);
    }

}

