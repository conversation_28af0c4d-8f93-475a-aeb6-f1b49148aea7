<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-4 text-start">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="" appDelayedInput (delayedInput)="search($event)"
            [delayTime]="1000">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div class="col-12 col-sm-4">
      </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th>{{'UsersContactList.fullName' | translate}}</th>
            <th>{{'UsersContactList.email' | translate}}</th>
            <th>{{'UsersContactList.countryCode' | translate}}</th>
            <th>{{'UsersContactList.phoneNumber' | translate}}</th>
            <th>{{'UsersContactList.message' | translate}}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;">
            <td>{{record.firstName + ' ' + record.lastName}}</td>
            <td>
              {{record.Email}}
            </td>
            <td>{{record.countryCode}}</td>
            <td>
              {{record.PhoneNumber}}
            </td>
            <td>
              {{record.Message}}
            </td>
            <td class="text-center custom-action-button text-right">
              <div class="d-flex justify-content-end mb-3">
                <button title="Delete" class="btn btn-primary action-button" (click)="remove(record.id)">
                  <i class="bi bi-trash"></i> DELETE
                </button>
              </div>
            </td>
          </tr>
          <!-- <tr *ngIf="records.length===0">
						<td class="text-center" colspan="5">
							{{"COMMON.NORECORDS" | translate}}
						</td>
					</tr> -->
        </tbody>
      </table>
    </div>
  </div>
</div>
