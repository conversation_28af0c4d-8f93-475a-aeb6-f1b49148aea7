import { TranslateService } from "@ngx-translate/core";
import { BaseModel } from "../config/base.model";
import { ToastService } from "../shared/toast.service";

export class UserFarmDetail extends BaseModel {
    userId: string;
    farmId: string;
    farmName: string;
    farmCode: string;

    constructor() {
        super();
    }

    forRequest() {
        this.farmName = this.farmName.trim();
        this.farmCode = this.farmCode.trim();
        this.userId = this.userId.trim();
        this.farmId = this.farmId.trim();

        return this;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    static fromResponse(data: any): UserFarmDetail {
        const userFarmDetail = new UserFarmDetail();
        userFarmDetail.id = data.id;
        userFarmDetail.userId = data.userId;
        userFarmDetail.farmId = data.farmId;
        userFarmDetail.farmName = data.farmName;
        userFarmDetail.farmCode = data.farmCode;

        return userFarmDetail;
    }
}
