import { TranslateService } from "@ngx-translate/core";
import { BaseModel } from "../config/base.model";
import { ToastService } from "../shared/toast.service";
import { Farm } from "./farm";
import { Users } from "./users";

export class SubCategoryAssignedUsers extends BaseModel {

    userId: string;
    farmId: string;
    userDetail: Users;
    farmDetail: Farm;

    constructor() {
        super();
        this.userDetail = new Users();
        this.farmDetail = new Farm();
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        throw new Error("Method not implemented.");
    }
    forRequest() {
        throw new Error("Method not implemented.");
    }

}