import { Component, OnInit } from '@angular/core';
import { BaseDetailComponent } from '../../../config/base.detail.component';
import { Category } from '../../../models/category';
import { ActivatedRoute, Router } from '@angular/router';
import { ManageContentManager } from '../manage-content.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';

@Component({
	selector: 'app-manage-content-detail',
	templateUrl: './manage-content-detail.component.html',
	styleUrls: ['./manage-content-detail.component.scss']
})
export class ManageContentDetailComponent extends BaseDetailComponent implements OnInit {

	constructor(protected route: ActivatedRoute, protected manageContentManager: ManageContentManager, protected toastService: ToastService,
		protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
		protected translateService: TranslateService, public commonUtil: CommonUtil) {
		super(manageContentManager, commonService, toastService, loadingService, route, router, translateService);

	}

	ngOnInit() {
		this.record = new Category();
		this.isDetailPage = true;
		this.init();
	}

	onFetchCompleted() {
		super.onFetchCompleted();
		this.filterParam.relationTable = "Category";
		this.filterParam.relationId = this.record.id;
	}

}
