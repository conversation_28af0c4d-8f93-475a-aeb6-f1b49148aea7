<div class="breadcrumb-container">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Account Settings</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">Account Settings</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
        <form #accountSettingForm="ngForm" novalidate="novalidate" class="form-item">
            <div class="row mb-20">
                <div class="col-md-6 form-group"
                     [ngClass]="{'has-error':!firstName.valid && onClickValidation}">
                    <h5>{{"FIRST NAME" | translate}}</h5>
                    <input class="form-control" type="text" name="firstName" #firstName="ngModel"
                           [(ngModel)]="user.firstName" required="required" placeholder="{{'FIRST NAME' | translate}}">
                </div>
                <div class="col-md-6 form-group"
                     [ngClass]="{'has-error':!lastName.valid && onClickValidation}">
                    <h5>{{"LAST NAME" | translate}}</h5>
                    <input class="form-control" type="text" name="lastName" #lastName="ngModel"
                           [(ngModel)]="user.lastName" required="required" placeholder="{{'LAST NAME' | translate}}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 mb-20 form-group"
                     [ngClass]="{'has-error':!userName.valid && onClickValidation}">
                    <h5>{{"EMAIL" | translate}}</h5>
                    <input class="form-control" type="text" name="userName" #userName="ngModel"
                           [(ngModel)]="user.userName" required="required" readonly email
                           placeholder="{{'EMAIL' | translate}}">
                </div>
                <div class="clearfix"></div>
                <div class="col-md-12 mb-20 form-group"
                     [ngClass]="{'has-error':!phoneNumber.valid && onClickValidation}">
                    <h5>{{"MOBILE NO" | translate}}</h5>
                    <input class="form-control" type="text" name="phoneNumber" #phoneNumber="ngModel"
                           [(ngModel)]="user.phoneNumber" required="required" placeholder="{{'MOBILE NO' | translate}}">
                </div>
            </div>
            <div class="pull-right">
                <button class="btn btn-primary site-button"
                        (click)="update(accountSettingForm.form)">{{"SAVE" | translate}}</button>
            </div>
        </form>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
