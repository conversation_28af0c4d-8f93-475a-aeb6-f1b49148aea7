import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Category } from './category';
import { Language } from './language';
export class MostCompletedTrainings extends BaseModel {

  tenantId: number;
  title: string;
  accessibility: string;
  uploadRequired: boolean;
  watchRequired: boolean;

  constructor() {
    super();
    this.isDeleted = false;
    this.isActive = true;
  }

  isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
    if (this.isNullOrUndefinedAndEmpty(this.title)) {
      form.controls.title.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  forRequest() {
    this.title = this.trimMe(this.title);
    return this;
  }
}
