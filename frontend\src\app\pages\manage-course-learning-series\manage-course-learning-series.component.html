<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
    <!-- <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}"> -->
    <div class="row">
        <div class="col-12 col-sm-4 text-start">
            <div class="custom-input-group">
                <input class="form-control search-form-control" placeholder="Search" appDelayedInput
                    (delayedInput)="search($event)" [delayTime]="1000">
                <i class="bi bi-search pe-3"></i>
            </div>
        </div>
        <div class="col-12 col-sm-4">
        </div>
        <div class="col-12 col-sm-4 text-end pe-0 mb-2">
            <button type="button" class="btn add-button btn-primary btn-lg font-15px add-button-content"
                (click)="onNewRecord()" *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','AddButton')">
                <img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-15px" alt="">Add</button>
        </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
        <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            <tbody>
                <tr *ngFor="let record of records;">
                    <td>
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <img src="/assets/images/icons/menu/learning-series-logo.svg"
                                            class="ms-2 width-30px">
                                        <h5 class="card-title mb-0 ms-2">
                                            {{record.title}}
                                        </h5>
                                        <div class="vertical-line" style="border:1px solid #1681FF">
                                        </div>
                                        <div class="text-muted ms-2">
                                            <div class="title">{{moment(record.updatedOn).format('DD-MM-YYYY')}}</div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end mb-3 me-2">
                                        <i class="bi bi-pencil font-21px me-3 cursor-pointer"
                                            *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','EditButton') && !isDetailPage && !isPlusButton"
                                            [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','EditButton')"
                                            title="Edit" (click)="editRecord(record.id)">
                                        </i>
                                        <i class="bi bi-trash cursor-pointer font-21px"
                                            *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','DeleteButton') && !isPlusButton"
                                            [class.disabled]="authService.isDisabled('MANAGE_COURSE_TRAINING','DeleteButton')"
                                            title="Delete" (click)="remove(record.id)"></i>
                                    </div>
                                </div>
                                <div>
                                    <hr class="line">
                                </div>
                                <p class="card-text ms-2 mb-3">{{record.description}}</p>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>