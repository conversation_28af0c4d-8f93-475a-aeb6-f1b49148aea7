import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { SubCategory } from '../../../models/subcategory';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { SubCategoryManager } from '../subcategory.manager';

import { CategoryManager } from '../../category/category.manager';
import { Category } from '../../../models/category';
import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Constant } from 'src/app/config/constants';
import { CategorySubCategoryMapping } from 'src/app/models/categorysubcategorymapping';
declare const $: any;

@Component({
	selector: 'app-subcategory-edit',
	templateUrl: './subcategory-edit.component.html',
	styleUrls: ['./subcategory-edit.component.scss']
})

export class SubCategoryEditComponent extends BaseEditComponent implements OnInit {
	public subCategory: SubCategory;
	public categories: Category[];
	public languages: Language[];
	readonly MY_CONSTANT = Constant;
	public subcategoryFormInputs: any[]
	public allLanguagesCategoryData: any | undefined;

	categoryIds: string[];
	dropdownSettings = {};
	isCategoryRequired: boolean;

	constructor(protected route: ActivatedRoute, protected subcategoryManager: SubCategoryManager,
		protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
		protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService
		, private categoryManager: CategoryManager, private languageManager: LanguageManager
		, public commonUtil: CommonUtil) {
		super(subcategoryManager, commonService, toastService, loadingService, route, router, translateService);
	}

	ngOnInit() {
		this.subCategory = new SubCategory();
		this.subCategory.isActive = true;
		this.setRecord(this.subCategory);
		this.setDropdownSettings();

		this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
		this.categories = new Array<Category>();
		this.languages = new Array<Language>();
		this.categoryIds = new Array<string>();
		this.init();
	}

	onFetchCompleted() {
		this.subCategory = SubCategory.fromResponse(this.record);
		this.setRecord(this.subCategory);
	}

	async fetchExistingRecord() {
		try {
			const response: RestResponse = await this.manager.fetch(this.request.recordId);
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			let subCategories = JSON.parse(JSON.stringify(response.data));
			subCategories.forEach((subCategory: SubCategory) => {
				if (!subCategory.selectedCategories) {
					subCategory.selectedCategories = new Array<Category>();
				}
				if (!this.isNullOrUndefined(subCategory.categorySubCategoryMappingList) && subCategory.categorySubCategoryMappingList.length > 0) {
					subCategory.categorySubCategoryMappingList.forEach(item => {
						let selectedCategory = new Category();
						selectedCategory.id = item.categoryId;
						selectedCategory.title = item.categoryName;
						subCategory.selectedCategories.push(selectedCategory);
					});
				}
			});
			this.setSubCategoryInputsOnEdit(subCategories);
			this.request.isNewRecord = false;
		} catch (error) {
			this.toastService.error(error.message);
		}
	}



	async fetchAssociatedData() {
		this.categories = await this.categoryManager.fetchAllData(null);
		this.languages = await this.languageManager.fetchAllData(null);
		let English = this.categories.filter(category => category.languageIdDetail.name == Constant.languages.English);
		let Bahasa = this.categories.filter(category => category.languageIdDetail.name == Constant.languages.Bahasa);
		let Vietnamese = this.categories.filter(category => category.languageIdDetail.name == Constant.languages.Vietnamese)
		this.allLanguagesCategoryData = { English, Bahasa, Vietnamese }
		this.setSubCategoryInputsOnAdd(this.languages);
		// this.afterFetchAssociatedCompleted();
	}

	setSubCategoryInputsOnAdd(languages: any) {
		if (this.request.recordId == 0) {
			this.subcategoryFormInputs = languages.map(language => ({
				languageId: language.id,
				languageName: language.name,
				title: "",
				description: "",
				isActive: true,
				isDeleted: false,

			})).sort((a, b) => {
				return a.languageName.localeCompare(b.languageName);
			});
		}
	}

	setSubCategoryInputsOnEdit(allData: any) {
		this.subcategoryFormInputs = allData.map(data => ({
			id: data?.id,
			title: data?.title,
			description: data?.description,
			isActive: data?.isActive,
			isDeleted: data?.isDeleted,
			languageId: data?.languageId,
			categoryId: data?.categoryId,
			languageName: data?.languageIdDetail?.name,
			categorySubCategoryMappingList: data.categorySubCategoryMappingList,
			selectedCategories: data.selectedCategories
		})).sort((a, b) => {
			return a.languageName.localeCompare(b.languageName);
		});
	}

	addDescriptionToRemainingLanguages(data: any) {
		this.subcategoryFormInputs.map(input => {
			if (input.languageName !== Constant.languages.English) {
				input.description = data;
			}
			return input
		})
	}

	addSubCategoryTitleToRemainingLanguages(data: any) {
		this.subcategoryFormInputs.map(input => {
			if (input.languageName !== Constant.languages.English) {
				input.title = data;
			}
			return input
		})
	}

	setCategoryDropdownByLanguageName(name: any) {
		switch (name) {
			case Constant.languages.English:
				return this.allLanguagesCategoryData.English;
			case Constant.languages.Vietnamese:
				return this.allLanguagesCategoryData.Vietnamese;
			case Constant.languages.Bahasa:
				return this.allLanguagesCategoryData.Bahasa;
			default:
				return this.allLanguagesCategoryData.English;
		}
	}

	getImageFetchByLanguageName(name: any) {
		switch (name) {
			case Constant.languages.English:
				return "/assets/images/icons/menu/united.svg";
			case Constant.languages.Vietnamese:
				return "/assets/images/icons/menu/Vietnam.svg";
			case Constant.languages.Bahasa:
				return "/assets/images/icons/menu/indonesia.svg"
			default:
				return "/assets/images/icons/menu/united.svg";
		}
	}

	afterFetchAssociatedCompleted() {
		const categoryIdId: string = this.route.snapshot.queryParamMap.get('Category');
		if (categoryIdId) {
			this.onAssociatedValueSelected({ "id": categoryIdId }, 'subCategoryCategoryIdSelect');
		}
		const languageIdId: string = this.route.snapshot.queryParamMap.get('Language');
		if (languageIdId) {
			this.onAssociatedValueSelected({ "id": languageIdId }, 'subCategoryLanguageIdSelect');
		}
	}

	onSaveSuccess(data: any) {
		this.navigate('/dashboard/sub-category');
	}


	checkConditionToReload(records: BaseModel[], selectedRecord: any) {
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}

	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {
		if (this.request.popupId) {
			$('#' + this.request.popupId).appendTo('body').modal('hide');
		}
		if ((!this.isNullOrUndefined(selectedField) && selectedField === 'subCategoryCategoryIdSelect') || this.request.popupId === 'subCategoryCategoryIdPopup') {
			this.subCategory.categoryId = selectedRecord.id;
			this.checkConditionToReload(this.categories, selectedRecord);
			return;
		}
		if ((!this.isNullOrUndefined(selectedField) && selectedField === 'subCategoryLanguageIdSelect') || this.request.popupId === 'subCategoryLanguageIdPopup') {
			this.subCategory.languageId = selectedRecord.id;
			this.checkConditionToReload(this.languages, selectedRecord);
			return;
		}

	}


	async save(form: any) {

		let updatedSubCategoryFormInputsData = this.subcategoryFormInputs.map(({ languageName, ...rest }) => rest)
		this.onClickValidation = !form.valid;
		if (!form.valid) {
			return;
		}

		updatedSubCategoryFormInputsData.forEach(x => {
			if (!x.categorySubCategoryMappingList) {
				x.categorySubCategoryMappingList = new Array<CategorySubCategoryMapping>();
			}
			x.selectedCategories.forEach(category => {
				let index = x.categorySubCategoryMappingList.findIndex(y => y.categoryId == category.id);
				if (index === -1) {
					let selectedCategory = new CategorySubCategoryMapping();
					selectedCategory.categoryId = category.id;
					x.categorySubCategoryMappingList.push(selectedCategory);
				}
			});
			x.categorySubCategoryMappingList.forEach(item => {
				if (item.id != null) {
					let index = x.selectedCategories.findIndex(x => x.id == item.categoryId);
					if (index === -1) {
						item.isDeleted = true;
					}
				}
			});
			delete x.selectedCategories;
			delete x.categoryId;
		});
		// if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
		// 	return;
		// }
		try {
			this.loadingService.show();
			const method = this.request.isNewRecord ? 'save' : 'update';
			const response: RestResponse = await this.manager[method](updatedSubCategoryFormInputsData);
			this.loadingService.hide();
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.onSaveSuccess(response.data);
		} catch (error) {
			this.loadingService.hide();
			this.toastService.error(error.message);
		}
	}

	setDropdownSettings() {
		this.dropdownSettings = {
			singleSelection: false,
			idField: 'id',
			enableCheckAll: false,
			textField: 'title',
			// itemsShowLimit: 3,
			allowSearchFilter: true
		};
	}

	addCategoriesToRemainingLanguages(event) {
		let commonTitle = this.setCategoryDropdownByLanguageName(Constant.languages.English).find(x => x.id === event.id).commonTitle;
		let categoriesByCommonTitle = this.categories.filter(x => x.commonTitle === commonTitle);
		this.subcategoryFormInputs.forEach(input => {
			if (input.languageName !== Constant.languages.English) {
				if (!input.selectedCategories) {
					input.selectedCategories = new Array<Category>();
				}
				input.selectedCategories = input.selectedCategories.filter(x => x.selectedForAnotherLanguages == false);
				let categoryIndex = categoriesByCommonTitle.findIndex(x => x.languageId === input.languageId);
				if (categoryIndex !== -1) {
					let selectedCategory = new Category();
					selectedCategory.id = categoriesByCommonTitle[categoryIndex].id;
					selectedCategory.title = categoriesByCommonTitle[categoryIndex].title
					input.selectedCategories.push(selectedCategory);
				}
				input.selectedCategories = [...input.selectedCategories];
			}
		});
	}

	removeCategoriesToRemainingLanguages(event) {
		let commonTitle = this.setCategoryDropdownByLanguageName(Constant.languages.English).find(x => x.id === event.id).commonTitle;
		let categoriesByCommonTitle = this.categories.filter(x => x.commonTitle === commonTitle);
		this.subcategoryFormInputs.forEach(input => {
			categoriesByCommonTitle.forEach(category => {
				let index = input.selectedCategories.findIndex(x => x.id == category.id);
				if (index !== -1) {
					input.selectedCategories.splice(index, 1);
				}
				input.selectedCategories = [...input.selectedCategories];
			});
		});
	}
}
