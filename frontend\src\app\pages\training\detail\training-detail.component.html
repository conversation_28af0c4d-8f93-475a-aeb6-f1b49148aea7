<div class="site-customer-main-container training-detail" id="userEditComponent">
  <div class="dashboard-content-container training-detail-main-cls container">
    <div *ngIf="training?.id" class="row">
      <div class="col-12 col-lg-6">
        <div class="text-start">
          <div class="d-flex">
            <h2>{{training?.title}}</h2>
          </div>
          <hr class="mt-4 opacity-50">
          <div class="row d-flex text-start justify-content-between">
            <!-- <div class="col-12 col-lg-6 pe-0 ps-5">
              <h6 class="mt-5 semi-bold">Private</h6>
              <img
                [src]="training.isPrivate == true ? '/assets/images/icons/menu/green-tick.svg' : '/assets/images/icons/menu/red-cross.svg'"
                class="width-25-px" />
            </div> -->
          </div>
          <h6 class="mt-5 semi-bold mx-2">Description</h6>
          <p class="mx-2" [innerHTML]="training?.description"></p>
          <hr class="mt-5">
          <div class="row d-flex text-start mt-4 justify-content-between">
            <div class="col-12 col-lg-6">
              <h6 class="semi-bold">Content Type</h6>
              <div>{{training.contentTypeDetail.title}}</div>
            </div>
            <div class="col-12 col-lg-6 pe-0 ps-5">
              <h6 class="semi-bold">Learning Series</h6>
              <div class="text-start">{{training.learningSeriesDetail.title}}</div>
            </div>
          </div>
          <hr class="mt-5">
          <div class="row d-flex text-start mt-4 justify-content-between">
            <div class="col-12 col-lg-6">
              <h6 class="semi-bold">Private/Public</h6>
              <!-- <img [title]="commonUtil.getAccessbilitytitle(training.accessibility)"
                [src]="commonUtil.getAccessbilityIcon(training.accessibility)" class="width-27px mb-3" /> -->
              <!-- <img
                [src]="record?.accessibility == 'Public' ? '/assets/images/icons/menu/users-green.svg' :'/assets/images/icons/menu/private-red.svg'"
                class="width-27px mb-3" /> -->
              <div class="form-check form-switch">
                <input data-bs-toggle="tooltip" data-bs-placement="top"
                  class="form-check-input accessibility-switch toggle-width" type="checkbox"
                  id="flexSwitchCheckCheckedAccssibility" [checked]="training?.accessibility === 'Public'"
                  [ngModelOptions]="{standalone: true}" disabled>
              </div>
            </div>
            <div class="col-12 col-lg-6 pe-0 ps-5">
              <h6 class="semi-bold">Status</h6>
              <!-- <p>{{training.isPublish == true ? "Publish" : "Draft"}}
              </p> -->
              <div class="form-check form-switch">
                <input class="form-check-input toggle-width" type="checkbox" id="flexSwitchCheckChecked"
                  [(ngModel)]="training.isPublish" [ngModelOptions]="{standalone: true}" disabled>
                <label class="form-check-label" for="flexSwitchCheckChecked45545"></label>
              </div>
            </div>
            <hr class="mt-5">
            <div class="col-12 col-lg-6 mt-3">
              <h6 class="semi-bold">Created By</h6>
              <p>
                {{training?.userDetails?.authorName}}
              </p>
            </div>
            <!-- <div class="col-12 col-lg-6 pe-0 ps-5">
              <h6 class="semi-bold">{{'Training.TrainingRequired' | translate}}</h6>
              <h6><span *ngIf="training.uploadRequired" class="badge integrax-tags integrax-font">Uploading
                  Required</span></h6>
              <h6><span *ngIf="training.watchRequired" class="badge integrax-tags integrax-font integrax-black">Watching
                  Required</span></h6>
            </div> -->
          </div>
        </div>
      </div>
      <div class="col-12 col-lg-6">
        <div class="ms-4">
          <h6 class="mt-3 semi-bold">Video</h6>
        </div>
        <div class="video-wrapper mt-3">
          <div class="video-container" id="video-container">
            <div class="play-button-wrapper">
              <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
                id="circle-play-b">
                <!-- SVG Play Button -->
                <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                  <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                </svg>
              </div>
            </div>
            <video playsinline [src]="training.url" class="mw-100" id="training_video" controlslist="nodownload">
              Your browser does not support HTML video.
            </video>
          </div>
        </div>
        <div class="ms-4">
          <h6 class="mt-3 semi-bold">Thumbnail Image</h6>
        </div>
        <div class="moment-image-container max-width-none mt-4">
          <img [src]="training.thumbnailImageUrl" />
        </div>
      </div>
    </div>
  </div>
</div>
