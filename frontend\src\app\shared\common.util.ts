import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class CommonUtil {
    titleStatus: any = {
        "INPROGRESS": "IN PROGRESS",
        "UPLOADED": "UPLOADED",
        "REJECTED": "REJECTED",
        "WAITING_FOR_APPROVAL": "WAITING FOR APPROVAL",
        "COMPLETED": "COMPLETED",
        "WATCH_TO_COMPLETE": "WATCH TO COMPLETE",
        "WATCHED": "WATCHED",
        "APPROVED": "APPROVED",
    }
    trainingFilterIdData: any = {
        "INPROGRESS": "INPROGRESS",
        "REJECTED": "REJECTED",
        "WAITING_FOR_APPROVAL": "UPLOADED",
        "COMPLETED": "APPROVED",
    }
    titleStatusImg: any = {
        "INPROGRESS": "/assets/images/icons/menu/processing-time.svg",
        "REJECTED": "/assets/images/icons/menu/rejected.svg",
        "APPROVED": "/assets/images/icons/menu/completed.svg",
        "COMPLETED": "/assets/images/icons/menu/completed.svg",
        "WAITING_FOR_APPROVAL": "/assets/images/icons/menu/waiting_for_approval.svg",
    }

    titleStatusColor: any = {
        "INPROGRESS": "in-progress-color",
        "WAITING_FOR_APPROVAL": "waiting-for-approval-color",
        "REJECTED": "rejected-color",
        "APPROVED": "completed-color",
        "COMPLETED": "completed-color",
    }
    accessbilityIcon: any = {
        "RESTRICTED": "/assets/images/icons/menu/users-red.svg",
        "PUBLIC": "/assets/images/icons/menu/users-green.svg",
        "PRIVATE": "/assets/images/icons/menu/private-red.svg",
        "isPUBLIC": "/assets/images/icons/menu/publish-logo.svg",
        "saveAsDraft": "/assets/images/icons/menu/isDraft-logo.svg",
        
    }
    accessibilityTitle: any = {
        "RESTRICTED": "Restricted",
        "PUBLIC": "Public",
        "PRIVATE": "Private",
    }
    logging(type, message, functionName, fileName) {
        if (!environment.production) {
            console.log('Type:' + type + ' In Function:' + functionName + ' In File: ' + fileName);
            console.log(message);
        }
    }

    toggleMenu() {
        if ($('#sidebar').hasClass('active')) {
            $('#sidebar').addClass('content-body-active');
            $('#sidebar').removeClass('active');
            $('#content').addClass('window-content-body');
            $('#content').removeClass('mobile-content-body');
        } else {
            $('#sidebar').addClass('active');
            $('#sidebar').removeClass('content-body-active');
            $('#content').removeClass('window-content-body');
            $('#content').addClass('mobile-content-body');
        }
    }

    static isNullOrUndefined(value) {
        return value === undefined || value === null;
    }

    getTitleStatus(status: string) {
        return this.titleStatus[status]
    }
    getTrainingFilterIdData(status: string) {
        return this.trainingFilterIdData[status]
    }

    getTitleStatusImg(status: string) {
        return this.titleStatusImg[status]
    }
    getTitleStatusColor(status: string) {
        return this.titleStatusColor[status]
    }

    getAccessbilityIcon(name: string) {
        return this.accessbilityIcon[name];
    }

    getAccessbilitytitle(name: string) {
        return this.accessibilityTitle[name];
    }

    loadVideo = file => new Promise((resolve, reject) => {
        try {
            let video = document.createElement('video')
            video.preload = 'metadata'

            video.onloadedmetadata = function () {
                resolve(this)
            }

            video.onerror = function () {
                reject("Invalid video. Please select a video file.")
            }

            video.src = window.URL.createObjectURL(file)
        } catch (e) {
            reject(e)
        }
    })
}
