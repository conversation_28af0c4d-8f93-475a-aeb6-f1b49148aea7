import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { AuthService } from 'src/app/shared/auth.services';

@Injectable({
    providedIn: 'root'
})
export class UsersBreadcrumbs implements Resolve<any> {
    constructor(private authService: AuthService) {}

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const user = this.authService.getUser();
        const breadcrumbs = [];
        
        // Only show Dashboard link if user is not ROLE_FARM_ADMIN
        if (user && user.roles && !user.roles.includes('ROLE_FARM_ADMIN')) {
            breadcrumbs.push({
                title: "Dashboard", 
                link: "/dashboard", 
                active: false
            });
        }
        
        breadcrumbs.push({
            title: "Manage Seekers", 
            link: "/dashboard/users", 
            active: true
        });

        return of(breadcrumbs);
    }
} 