<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="m-t-20">
    <div class="dashboard-content-container">
        <form *ngIf="user.id || request.recordId == 0" #recordForm="ngForm" novalidate="novalidate">
            <div class="row site-form-container">
                <div class="col-12 col-md-12 mb-3">
                    <div class="profile-image-container position-relative"
                        [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
                        <img *ngIf="!profileImageLoader"
                            [src]="user.profileImageUrl ? user.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
                        <div *ngIf="profileImageLoader" class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div *ngIf="!profileImageLoader && !authService.isProgramAdmin()"
                            class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
                            <label for="file-input">
                                <i class="bi bi-pencil-fill"></i>
                                <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect
                                    [uploader]="uploader" id="file-input" type="file"
                                    accept="image/png, image/jpg, image/jpeg" />
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 mb-4">
                    <div class="form-floating">
                        <input class="form-control rounded" type="text" name="firstName" #firstName="ngModel"
                            [(ngModel)]="user.firstName" required="required" placeholder="First Name"
                            [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}"
                            [disabled]="authService.isProgramAdmin()">
                        <label for="floatingInput">First Name</label>
                    </div>
                </div>
                <div class="col-12 col-md-6 mb-4">
                    <div class="form-floating">
                        <input class="form-control rounded" name="lastname" #lastname="ngModel"
                            [(ngModel)]="user.lastName" required="required" placeholder="Last Name"
                            [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}"
                            [disabled]="authService.isProgramAdmin()">
                        <label for="floatingInput">Last Name</label>
                    </div>
                </div>
                <div class="col-12 col-md-6 mb-4">
                    <div class="form-floating">
                        <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control"
                            type="email" name="email" #email="ngModel" [(ngModel)]="user.email" required="required"
                            placeholder="Email" pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$"
                            [disabled]="authService.isProgramAdmin()">
                        <label for="floatingInput">{{"USERS.Email" | translate}}</label>
                    </div>
                </div>
                <div class="col-12 col-md-6 mb-4">
                    <div class="form-floating">
                        <input autocomplete="off" class="form-control phone_number" type="text" name="phone" ng2TelInput
                            (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                            [disabled]="authService.isProgramAdmin()" (ng2TelOutput)="getNumber($event)"
                            #phone="ngModel" [(ngModel)]="user.phoneNumber"
                            [ngClass]="{'is-invalid':!phone.valid && onClickValidation}" required="required"
                            placeholder="Phone Number" minLength="7" maxlength="12"
                            (countryChange)="onCountryChange($event)">
                    </div>
                </div>
                <!-- <div class="col-12 col-md-6 mb-4">
                    <div class="form-floating">
                        <input class="form-control rounded" name="programName" #programName="ngModel"
                            [(ngModel)]="user.programName" required="required" placeholder="Program Name"
                            [ngClass]="{'is-invalid':!programName.valid && onClickValidation}">
                        <label for="floatingInput">Program Name</label>
                    </div>
                </div>
                <div class="col-12 col-md-12">
                    <div class="form-floating form-floating-textarea mb-4 w-100">
                        <textarea maxlength="250" [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
                            class="form-control form-description" name="description" #description="ngModel"
                            [(ngModel)]="user.about" required="required" placeholder="Description"
                            id="floatingTextarea2"></textarea>
                        <label for="floatingInput">About</label>
                    </div>
                </div> -->
                <div class="col-12 d-flex justify-content-end"
                    *ngIf="!authService.isSuperAdmin() && !authService.isProgramAdmin()">
                    <button class=" btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                        type="button" (click)="save(recordForm.form.valid)" [disabled]="request.isRequested">
                        {{request.recordId ==
                        0 ? 'SAVE' : 'UPDATE'}}
                    </button>
                </div>
            </div>
        </form>
        <div class="clearfix"></div>
    </div>
</div>
