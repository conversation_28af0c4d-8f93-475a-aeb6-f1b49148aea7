import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { FilterParam } from 'src/app/models/filterparam';
import { Users } from 'src/app/models/users';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramUserService } from './program-user.service';
import { ProgramUserManager } from './program-user.manager';
import AOS from 'aos';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { environment } from 'src/environments/environment';
import { RouteDataService } from 'src/app/shared/title.service';
import { LayoutComponent } from '../layout/layout.component'

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-program-user-edit',
  templateUrl: './program-user-edit.component.html',
  styleUrls: ['./program-user-edit.component.scss']
})
export class ProgramUserEditComponent extends BaseEditComponent implements OnInit {
  @Input() userDetails?: boolean;
  @Input() userId?: string;
  user: Users;
  onClickValidation: boolean;
  farms: any[];
  request: any;
  selectedFarms: any = [];
  userDeletedFarms: any = [];
  filterParam: FilterParam;
  uploader: any;
  teams: Users[];
  dropdownSettings = {};
  transferUser: Users;
  adminContentTransferModal: any;
  profileImageLoader: boolean = false;
  uploadingProgramImage: boolean = false;
  programImage: boolean;
  programProfileImg: boolean = false;
  fileUploadType: any;


  constructor(protected route: ActivatedRoute, private programUserService: ProgramUserService, protected programUserManager: ProgramUserManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService,
    public commonUtil: CommonUtil, public routeDataService: RouteDataService, private layoutComponent: LayoutComponent) {
    super(programUserManager, commonService, toastService, loadingService, route, router, translateService,);
  }

  ngOnInit() {
    this.loadingService.show();
    // await this.fetchAssociatedData()
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)
    this.fileUploadType = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)

    if (this.request.recordId <= 0 && !this.userId) {
      this.loadingService.hide();
      return;
    }
    this.user = new Users();
    this.user.isActive = true;

    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.setDropdownSettings()
    this.init();
    if (this.request.recordId != 0) {
      this.fetchUserDetail();
      this.request.isNewRecord = true;
    };
    this.loadingService.hide();


  }

  onlyAllowNumbers(event: KeyboardEvent): void {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.adminContentTransferModal = new bootstrap.Modal(
        document.getElementById('adminContentTransferModal')
      );
    }, 0)
  }


  uploadProfilePhoto(event: any) {
    if (event && event.target.files.length > 0) {
      this.profileImageLoader = true;
    }
    this.programImage = false;
  }

  fileValidationError(data: string, toastService: any) {
    this.profileImageLoader = false;
    this.programProfileImg = false;
    toastService.error(data);
  }

  initializeUploader(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationError("Image size to too large", this.toastService);
          }, 200);

          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationError("only jpg,png,jpeg files are allowed", this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    // Program Profile Image Upload
    // uploadProgramImageUrl(event: any) {
    //   if (event && event.target.files.length > 0) {
    //     this.uploadingProgramImage = true;
    //     const file = event.target.files[0];
    //     this.fileUploadType.addToQueue([file]);
    //     this.fileUploadType.onSuccessItem = (fileItem, response) => {
    //       const uploadResponse = JSON.parse(response);
    //       if (uploadResponse.length > 0) {
    //         this.user.programProfileImage = uploadResponse[0].path;
    //         this.uploadingProgramImage = false;
    //       }
    //     };
    //   }
    //   this.programImage = true;
    // }

    // removeProgramImage(fileUrl: string) {
    //   this.commonService.confirmation('Would you like to delete?', this.removeProgramImageCallback.bind(this), fileUrl);
    // }

    // removeProgramImageCallback(fileUrl: string) {
    //   if (fileUrl === this.user.programProfileImage) {
    //     this.user.programProfileImage = null;
    //   }
    // }

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const image = uploadResponse[0];
        image.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(image);
        setTimeout(() => {
          this.onUploadSuccess(image);
        }, 200);
      }
    };

    return uploader;
  }

  onUploadSuccess(data: any) {
    if (!this.programImage) {
      this.user.profileImageUrl = data.path;
      this.profileImageLoader = false;
    } else {
      this.user.programProfileImage = data.path;
      this.programProfileImg = false;
    }
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  async fetchUserDetail() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.user = response.data;
      this.onFetchCompleted();
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  onFetchCompleted() {
    let obj: any = {};
    obj.type = "TEXT_REPLACE";
    obj.title = this.user.fullName;
    this.commonService.updateData(obj);
    // this.routeDataService.setData(this.router.url, this.user.fullName);
  }


  telInputObject(event: any) {
    if (this.request.recordId == 0) {
      event.setCountry('sg');
      return
    }
    if (this.user.countryCode && this.user.phoneNumber) {
      event.setNumber('+' + this.user.countryCode + this.user.phoneNumber);
      return
    } else {
      event.setCountry('sg')
    }
  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  hasError(event: any) {
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  async save(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.programUserManager[method](this.user);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/program-admin-list');
  }

  openContentTransferModal() {
    AOS.init({ disable: true });
    this.transferUser = new Users();
    this.adminContentTransferModal.show();
  }
  closeContentTransferModal() {
    this.adminContentTransferModal.hide();
    // this.user = new Users();
  }

  async SendContentTransfer(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    // let data: any;
    // if (this.selectedProgramAdmin === this.MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXISTING_PROGRAM_ADMIN) {
    //   // Data for existing program admin
    //   data = {
    //     user: this.usersSelectedId,
    //     isActive: this.transferUser.isActive,
    //     isDeleted: this.transferUser.isDeleted,
    //   };
    // } else if (this.selectedProgramAdmin === this.MY_CONSTANT.CONTENT_TRANSFER_REQUIRED.EXTERNAL_PROGRAM_ADMIN) {
    //   // Data for external program admin
    //   data = {
    //     firstName: this.transferUser.firstName,
    //     lastName: this.transferUser.lastName,
    //     email: this.transferUser.email,
    //     isActive: this.transferUser.isActive,
    //     isDeleted: this.transferUser.isDeleted,
    //   };
    // }
    this.transferUser.fromUser = this.router.url.substring(this.router.url.lastIndexOf('/') + 1).split('?')[0];
    this.loadingService.show();
    try {
      const response: RestResponse = await this.programUserService.saveContentTransfer(this.transferUser);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      $("#adminContentTransferModal").modal("hide");
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
