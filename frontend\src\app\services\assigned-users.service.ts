import { Injectable } from '@angular/core';
import { HttpServiceRequests } from '../shared/http.service';
import { IResourceWithId, RestResponse } from '../shared/auth.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseService } from '../config/base.service';
import { FilterParam } from '../models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class AssignedUsersService extends BaseService {
    constructor(public http: HttpClient) {
        super(http, '/api/account/user', '/api/account/users');
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/GetFarmUser', filterParam);
    }

    update(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/delete/userfarm', data);
    }
}
