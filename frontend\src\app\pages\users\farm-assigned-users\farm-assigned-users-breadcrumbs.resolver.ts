import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FarmAssignedUsersBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const farmId: string | null = route.paramMap.get("id");

    return of([
      {
        title: "Dashboard", link: "/dashboard", active: false
      },
      {
        title: "Manage Farms", link: "/dashboard/farms", active: false
      },
      {
        title: "Assigned Users", link: "/dashboard/assigned-users/" + farmId, active: true
      }
    ])
  }



}
