import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { UserTrainingStatusService } from './usertrainingstatus.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class UserTrainingStatusManager extends BaseManager {

    constructor(private userTrainingStatusService: UserTrainingStatusService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userTrainingStatusService, loadingService, toastService);
    }
}
