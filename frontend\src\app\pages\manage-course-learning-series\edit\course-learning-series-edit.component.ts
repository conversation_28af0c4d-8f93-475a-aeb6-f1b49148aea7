import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageCourseLearningSeriesService } from '../manage-course-learning-series.service';
import { ManageCourseLearningSeriesManager } from '../manage-course-learning-series.manager';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { Constant } from 'src/app/config/constants';
import { BaseModel } from 'src/app/config/base.model';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
declare const $: any;
@Component({
  selector: 'app-course-learning-series-edit',
  templateUrl: './course-learning-series-edit.component.html',
  styleUrls: ['./course-learning-series-edit.component.scss']
})
export class CourseLearningSeriesEditComponent extends BaseEditComponent implements OnInit {
  public courseLearningSeries: CourseLearningSeries;
  readonly MY_CONSTANT = Constant;

  constructor(protected route: ActivatedRoute, protected manageCourseLearningSeriesService: ManageCourseLearningSeriesService, private manageCourseLearningSeriesManager: ManageCourseLearningSeriesManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService,
    public commonUtil: CommonUtil) {
    super(manageCourseLearningSeriesManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.courseLearningSeries = new CourseLearningSeries();
    this.courseLearningSeries.isActive = true;
    this.setRecord(this.courseLearningSeries);
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.init();
  }

  onFetchCompleted() {
    this.courseLearningSeries = CourseLearningSeries.fromResponse(this.record);
    this.setRecord(this.courseLearningSeries);
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/program-admin/manage-course-learning-series');
  }

  checkConditionToReload(records: BaseModel[], selectedRecord: any) {
    if (!records.some(x => x.id === selectedRecord.id)) {
      this.fetchAssociatedData();
    }
  }

  async save(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.manageCourseLearningSeriesManager[method](this.courseLearningSeries);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
