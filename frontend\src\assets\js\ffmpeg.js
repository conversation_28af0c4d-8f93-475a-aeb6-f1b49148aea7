!function (e, t) {
    "object" == typeof exports && "object" == typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports ? exports.FFmpeg = t() : e.FFmpeg = t();
}(window, function () {
    return function (e) {
        var t = {};
        function r(n) {
            if (t[n]) return t[n].exports;
            var o = t[n] = {
                i: n,
                l: !1,
                exports: {}
            };
            return e[n].call(o.exports, o, o.exports, r), o.l = !0, o.exports;
        }
        return r.m = e, r.c = t, r.d = function (e, t, n) {
            r.o(e, t) || Object.defineProperty(e, t, {
                enumerable: !0,
                get: n
            });
        }, r.r = function (e) {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
                value: "Module"
            }), Object.defineProperty(e, "__esModule", {
                value: !0
            });
        }, r.t = function (e, t) {
            if (1 & t && (e = r(e)), 8 & t) return e;
            if (4 & t && "object" == typeof e && e && e.__esModule) return e;
            var n = Object.create(null);
            if (r.r(n), Object.defineProperty(n, "default", {
                enumerable: !0,
                value: e
            }), 2 & t && "string" != typeof e) for (var o in e) r.d(n, o, function (t) {
                return e[t];
            }.bind(null, o));
            return n;
        }, r.n = function (e) {
            var t = e && e.__esModule ? function () {
                return e.default;
            } : function () {
                return e;
            };
            return r.d(t, "a", t), t;
        }, r.o = function (e, t) {
            return Object.prototype.hasOwnProperty.call(e, t);
        }, r.p = "", r(r.s = 3);
    }([function (e, t) {
        var r, n, o = e.exports = {};
        function i() {
            throw new Error("setTimeout has not been defined");
        }
        function a() {
            throw new Error("clearTimeout has not been defined");
        }
        function c(e) {
            if (r === setTimeout) return setTimeout(e, 0);
            if ((r === i || !r) && setTimeout) return r = setTimeout, setTimeout(e, 0);
            try {
                return r(e, 0);
            } catch (t) {
                try {
                    return r.call(null, e, 0);
                } catch (t) {
                    return r.call(this, e, 0);
                }
            }
        }
        !function () {
            try {
                r = "function" == typeof setTimeout ? setTimeout : i;
            } catch (e) {
                r = i;
            }
            try {
                n = "function" == typeof clearTimeout ? clearTimeout : a;
            } catch (e) {
                n = a;
            }
        }();
        var u, s = [], f = !1, l = -1;
        function p() {
            f && u && (f = !1, u.length ? s = u.concat(s) : l = -1, s.length && d());
        }
        function d() {
            if (!f) {
                var e = c(p);
                f = !0;
                for (var t = s.length; t;) {
                    for (u = s, s = []; ++l < t;) u && u[l].run();
                    l = -1, t = s.length;
                }
                u = null, f = !1, function (e) {
                    if (n === clearTimeout) return clearTimeout(e);
                    if ((n === a || !n) && clearTimeout) return n = clearTimeout, clearTimeout(e);
                    try {
                        n(e);
                    } catch (t) {
                        try {
                            return n.call(null, e);
                        } catch (t) {
                            return n.call(this, e);
                        }
                    }
                }(e);
            }
        }
        function h(e, t) {
            this.fun = e, this.array = t;
        }
        function m() { }
        o.nextTick = function (e) {
            var t = new Array(arguments.length - 1);
            if (arguments.length > 1) for (var r = 1; r < arguments.length; r++) t[r - 1] = arguments[r];
            s.push(new h(e, t)), 1 !== s.length || f || c(d);
        }, h.prototype.run = function () {
            this.fun.apply(null, this.array);
        }, o.title = "browser", o.browser = !0, o.env = {}, o.argv = [], o.version = "",
            o.versions = {}, o.on = m, o.addListener = m, o.once = m, o.off = m, o.removeListener = m,
            o.removeAllListeners = m, o.emit = m, o.prependListener = m, o.prependOnceListener = m,
            o.listeners = function (e) {
                return [];
            }, o.binding = function (e) {
                throw new Error("process.binding is not supported");
            }, o.cwd = function () {
                return "/";
            }, o.chdir = function (e) {
                throw new Error("process.chdir is not supported");
            }, o.umask = function () {
                return 0;
            };
    }, function (e, t, r) {
        var n, o;
        void 0 === (o = "function" == typeof (n = function () {
            return function () {
                var e = arguments.length;
                if (0 === e) throw new Error("resolveUrl requires at least one argument; got none.");
                var t = document.createElement("base");
                if (t.href = arguments[0], 1 === e) return t.href;
                var r = document.getElementsByTagName("head")[0];
                r.insertBefore(t, r.firstChild);
                for (var n, o = document.createElement("a"), i = 1; i < e; i++) o.href = arguments[i],
                    n = o.href, t.href = n;
                return r.removeChild(t), n;
            };
        }) ? n.call(t, r, t, e) : n) || (e.exports = o);
    }, function (e, t) {
        var r = !1;
        t.logging = r, t.setLogging = function (e) {
            r = e;
        }, t.log = function (e, t) {
            return r ? console.log("[".concat(e, "] ").concat(t)) : null;
        };
    }, function (e, t, r) {
        r(4);
        var n = r(5);
        e.exports = {
            createFFmpeg: n
        };
    }, function (e, t, r) {
        var n = function (e) {
            "use strict";
            var t = Object.prototype, r = t.hasOwnProperty, n = "function" == typeof Symbol ? Symbol : {}, o = n.iterator || "@@iterator", i = n.asyncIterator || "@@asyncIterator", a = n.toStringTag || "@@toStringTag";
            function c(e, t, r, n) {
                var o = t && t.prototype instanceof f ? t : f, i = Object.create(o.prototype), a = new O(n || []);
                return i._invoke = function (e, t, r) {
                    var n = "suspendedStart";
                    return function (o, i) {
                        if ("executing" === n) throw new Error("Generator is already running");
                        if ("completed" === n) {
                            if ("throw" === o) throw i;
                            return E();
                        }
                        for (r.method = o, r.arg = i; ;) {
                            var a = r.delegate;
                            if (a) {
                                var c = b(a, r);
                                if (c) {
                                    if (c === s) continue;
                                    return c;
                                }
                            }
                            if ("next" === r.method) r.sent = r._sent = r.arg; else if ("throw" === r.method) {
                                if ("suspendedStart" === n) throw n = "completed", r.arg;
                                r.dispatchException(r.arg);
                            } else "return" === r.method && r.abrupt("return", r.arg);
                            n = "executing";
                            var f = u(e, t, r);
                            if ("normal" === f.type) {
                                if (n = r.done ? "completed" : "suspendedYield", f.arg === s) continue;
                                return {
                                    value: f.arg,
                                    done: r.done
                                };
                            }
                            "throw" === f.type && (n = "completed", r.method = "throw", r.arg = f.arg);
                        }
                    };
                }(e, r, a), i;
            }
            function u(e, t, r) {
                try {
                    return {
                        type: "normal",
                        arg: e.call(t, r)
                    };
                } catch (e) {
                    return {
                        type: "throw",
                        arg: e
                    };
                }
            }
            e.wrap = c;
            var s = {};
            function f() { }
            function l() { }
            function p() { }
            var d = {};
            d[o] = function () {
                return this;
            };
            var h = Object.getPrototypeOf, m = h && h(h(j([])));
            m && m !== t && r.call(m, o) && (d = m);
            var y = p.prototype = f.prototype = Object.create(d);
            function g(e) {
                ["next", "throw", "return"].forEach(function (t) {
                    e[t] = function (e) {
                        return this._invoke(t, e);
                    };
                });
            }
            function v(e, t) {
                var n;
                this._invoke = function (o, i) {
                    function a() {
                        return new t(function (n, a) {
                            !function n(o, i, a, c) {
                                var s = u(e[o], e, i);
                                if ("throw" !== s.type) {
                                    var f = s.arg, l = f.value;
                                    return l && "object" == typeof l && r.call(l, "__await") ? t.resolve(l.__await).then(function (e) {
                                        n("next", e, a, c);
                                    }, function (e) {
                                        n("throw", e, a, c);
                                    }) : t.resolve(l).then(function (e) {
                                        f.value = e, a(f);
                                    }, function (e) {
                                        return n("throw", e, a, c);
                                    });
                                }
                                c(s.arg);
                            }(o, i, n, a);
                        });
                    }
                    return n = n ? n.then(a, a) : a();
                };
            }
            function b(e, t) {
                var r = e.iterator[t.method];
                if (void 0 === r) {
                    if (t.delegate = null, "throw" === t.method) {
                        if (e.iterator.return && (t.method = "return", t.arg = void 0, b(e, t), "throw" === t.method)) return s;
                        t.method = "throw", t.arg = new TypeError("The iterator does not provide a 'throw' method");
                    }
                    return s;
                }
                var n = u(r, e.iterator, t.arg);
                if ("throw" === n.type) return t.method = "throw", t.arg = n.arg, t.delegate = null,
                    s;
                var o = n.arg;
                return o ? o.done ? (t[e.resultName] = o.value, t.next = e.nextLoc, "return" !== t.method && (t.method = "next",
                    t.arg = void 0), t.delegate = null, s) : o : (t.method = "throw", t.arg = new TypeError("iterator result is not an object"),
                        t.delegate = null, s);
            }
            function w(e) {
                var t = {
                    tryLoc: e[0]
                };
                1 in e && (t.catchLoc = e[1]), 2 in e && (t.finallyLoc = e[2], t.afterLoc = e[3]),
                    this.tryEntries.push(t);
            }
            function x(e) {
                var t = e.completion || {};
                t.type = "normal", delete t.arg, e.completion = t;
            }
            function O(e) {
                this.tryEntries = [{
                    tryLoc: "root"
                }], e.forEach(w, this), this.reset(!0);
            }
            function j(e) {
                if (e) {
                    var t = e[o];
                    if (t) return t.call(e);
                    if ("function" == typeof e.next) return e;
                    if (!isNaN(e.length)) {
                        var n = -1, i = function t() {
                            for (; ++n < e.length;) if (r.call(e, n)) return t.value = e[n], t.done = !1, t;
                            return t.value = void 0, t.done = !0, t;
                        };
                        return i.next = i;
                    }
                }
                return {
                    next: E
                };
            }
            function E() {
                return {
                    value: void 0,
                    done: !0
                };
            }
            return l.prototype = y.constructor = p, p.constructor = l, p[a] = l.displayName = "GeneratorFunction",
                e.isGeneratorFunction = function (e) {
                    var t = "function" == typeof e && e.constructor;
                    return !!t && (t === l || "GeneratorFunction" === (t.displayName || t.name));
                }, e.mark = function (e) {
                    return Object.setPrototypeOf ? Object.setPrototypeOf(e, p) : (e.__proto__ = p, a in e || (e[a] = "GeneratorFunction")),
                        e.prototype = Object.create(y), e;
                }, e.awrap = function (e) {
                    return {
                        __await: e
                    };
                }, g(v.prototype), v.prototype[i] = function () {
                    return this;
                }, e.AsyncIterator = v, e.async = function (t, r, n, o, i) {
                    void 0 === i && (i = Promise);
                    var a = new v(c(t, r, n, o), i);
                    return e.isGeneratorFunction(r) ? a : a.next().then(function (e) {
                        return e.done ? e.value : a.next();
                    });
                }, g(y), y[a] = "Generator", y[o] = function () {
                    return this;
                }, y.toString = function () {
                    return "[object Generator]";
                }, e.keys = function (e) {
                    var t = [];
                    for (var r in e) t.push(r);
                    return t.reverse(), function r() {
                        for (; t.length;) {
                            var n = t.pop();
                            if (n in e) return r.value = n, r.done = !1, r;
                        }
                        return r.done = !0, r;
                    };
                }, e.values = j, O.prototype = {
                    constructor: O,
                    reset: function (e) {
                        if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1,
                            this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(x),
                            !e) for (var t in this) "t" === t.charAt(0) && r.call(this, t) && !isNaN(+t.slice(1)) && (this[t] = void 0);
                    },
                    stop: function () {
                        this.done = !0;
                        var e = this.tryEntries[0].completion;
                        if ("throw" === e.type) throw e.arg;
                        return this.rval;
                    },
                    dispatchException: function (e) {
                        if (this.done) throw e;
                        var t = this;
                        function n(r, n) {
                            return a.type = "throw", a.arg = e, t.next = r, n && (t.method = "next", t.arg = void 0),
                                !!n;
                        }
                        for (var o = this.tryEntries.length - 1; o >= 0; --o) {
                            var i = this.tryEntries[o], a = i.completion;
                            if ("root" === i.tryLoc) return n("end");
                            if (i.tryLoc <= this.prev) {
                                var c = r.call(i, "catchLoc"), u = r.call(i, "finallyLoc");
                                if (c && u) {
                                    if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
                                    if (this.prev < i.finallyLoc) return n(i.finallyLoc);
                                } else if (c) {
                                    if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
                                } else {
                                    if (!u) throw new Error("try statement without catch or finally");
                                    if (this.prev < i.finallyLoc) return n(i.finallyLoc);
                                }
                            }
                        }
                    },
                    abrupt: function (e, t) {
                        for (var n = this.tryEntries.length - 1; n >= 0; --n) {
                            var o = this.tryEntries[n];
                            if (o.tryLoc <= this.prev && r.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
                                var i = o;
                                break;
                            }
                        }
                        i && ("break" === e || "continue" === e) && i.tryLoc <= t && t <= i.finallyLoc && (i = null);
                        var a = i ? i.completion : {};
                        return a.type = e, a.arg = t, i ? (this.method = "next", this.next = i.finallyLoc,
                            s) : this.complete(a);
                    },
                    complete: function (e, t) {
                        if ("throw" === e.type) throw e.arg;
                        return "break" === e.type || "continue" === e.type ? this.next = e.arg : "return" === e.type ? (this.rval = this.arg = e.arg,
                            this.method = "return", this.next = "end") : "normal" === e.type && t && (this.next = t),
                            s;
                    },
                    finish: function (e) {
                        for (var t = this.tryEntries.length - 1; t >= 0; --t) {
                            var r = this.tryEntries[t];
                            if (r.finallyLoc === e) return this.complete(r.completion, r.afterLoc), x(r), s;
                        }
                    },
                    "catch": function (e) {
                        for (var t = this.tryEntries.length - 1; t >= 0; --t) {
                            var r = this.tryEntries[t];
                            if (r.tryLoc === e) {
                                var n = r.completion;
                                if ("throw" === n.type) {
                                    var o = n.arg;
                                    x(r);
                                }
                                return o;
                            }
                        }
                        throw new Error("illegal catch attempt");
                    },
                    delegateYield: function (e, t, r) {
                        return this.delegate = {
                            iterator: j(e),
                            resultName: t,
                            nextLoc: r
                        }, "next" === this.method && (this.arg = void 0), s;
                    }
                }, e;
        }(e.exports);
        try {
            regeneratorRuntime = n;
        } catch (e) {
            Function("r", "regeneratorRuntime = r")(n);
        }
    }, function (e, t, r) {
        function n(e) {
            return function (e) {
                if (Array.isArray(e)) return o(e);
            }(e) || function (e) {
                if ("undefined" != typeof Symbol && Symbol.iterator in Object(e)) return Array.from(e);
            }(e) || function (e, t) {
                if (!e) return;
                if ("string" == typeof e) return o(e, t);
                var r = Object.prototype.toString.call(e).slice(8, -1);
                "Object" === r && e.constructor && (r = e.constructor.name);
                if ("Map" === r || "Set" === r) return Array.from(r);
                if ("Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)) return o(e, t);
            }(e) || function () {
                throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }();
        }
        function o(e, t) {
            (null == t || t > e.length) && (t = e.length);
            for (var r = 0, n = new Array(t); r < t; r++) n[r] = e[r];
            return n;
        }
        function i(e, t, r, n, o, i, a) {
            try {
                var c = e[i](a), u = c.value;
            } catch (e) {
                return void r(e);
            }
            c.done ? t(u) : Promise.resolve(u).then(n, o);
        }
        function a(e) {
            return function () {
                var t = this, r = arguments;
                return new Promise(function (n, o) {
                    var a = e.apply(t, r);
                    function c(e) {
                        i(a, n, o, c, u, "next", e);
                    }
                    function u(e) {
                        i(a, n, o, c, u, "throw", e);
                    }
                    c(void 0);
                });
            };
        }
        function c(e, t) {
            var r = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var n = Object.getOwnPropertySymbols(e);
                t && (n = n.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable;
                })), r.push.apply(r, n);
            }
            return r;
        }
        function u(e) {
            for (var t = 1; t < arguments.length; t++) {
                var r = null != arguments[t] ? arguments[t] : {};
                t % 2 ? c(Object(r), !0).forEach(function (t) {
                    s(e, t, r[t]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : c(Object(r)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
                });
            }
            return e;
        }
        function s(e, t, r) {
            return t in e ? Object.defineProperty(e, t, {
                value: r,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : e[t] = r, e;
        }
        function f(e, t) {
            if (null == e) return {};
            var r, n, o = function (e, t) {
                if (null == e) return {};
                var r, n, o = {}, i = Object.keys(e);
                for (n = 0; n < i.length; n++) r = i[n], t.indexOf(r) >= 0 || (o[r] = e[r]);
                return o;
            }(e, t);
            if (Object.getOwnPropertySymbols) {
                var i = Object.getOwnPropertySymbols(e);
                for (n = 0; n < i.length; n++) r = i[n], t.indexOf(r) >= 0 || Object.prototype.propertyIsEnumerable.call(e, r) && (o[r] = e[r]);
            }
            return o;
        }
        var l = r(6), p = r(2), d = p.setLogging, h = p.log, m = r(7), y = r(10), g = r(11), v = r(13), b = r(14), w = b.defaultOptions, x = b.getModule, O = b.fetchFile, j = Error("FFmpeg.js is not ready, make sure you have completed load()."), E = Error("FFmpeg.js can only run one command at a time"), P = null, S = null;
        e.exports = function () {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = null, r = !1, o = m(u({}, w, {}, e)), i = o.log, c = o.logger, s = o.progress, p = f(o, ["log", "logger", "progress"]), b = function (e) {
                var n = e.message;
                "ffmpeg-stdout" === e.type && "FFMPEG_END" === n && null !== t && (t(), t = null,
                    r = !1);
            };
            d(i);
            var L = function () {
                var e = a(regeneratorRuntime.mark(function e() {
                    return regeneratorRuntime.wrap(function (e) {
                        for (; ;) switch (e.prev = e.next) {
                            case 0:
                                if (null !== P) {
                                    e.next = 8;
                                    break;
                                }
                                return h("info", "load ffmpeg-core"), e.next = 4, x(p);

                            case 4:
                                (P = e.sent).setLogger(function (e) {
                                    b(e), y(e, s), c(e), h(e.type, e.message);
                                }), null === S && (S = P.cwrap("proxy_main", "number", ["number", "number"])),
                                    h("info", "ffmpeg-core loaded");

                            case 8:
                            case "end":
                                return e.stop();
                        }
                    }, e);
                }));
                return function () {
                    return e.apply(this, arguments);
                };
            }(), _ = function (e, t) {
                if (null === P) throw j;
                var r;
                return h("info", "FS.".concat(e, " ").concat(t[0])), (r = P.FS)[e].apply(r, n(t));
            }, k = function () {
                var e = a(regeneratorRuntime.mark(function e(t, r) {
                    return regeneratorRuntime.wrap(function (e) {
                        for (; ;) switch (e.prev = e.next) {
                            case 0:
                                return e.t0 = _, e.t1 = t, e.next = 4, O(r);

                            case 4:
                                return e.t2 = e.sent, e.t3 = [e.t1, e.t2], e.abrupt("return", (0, e.t0)("writeFile", e.t3));

                            case 7:
                            case "end":
                                return e.stop();
                        }
                    }, e);
                }));
                return function (t, r) {
                    return e.apply(this, arguments);
                };
            }(), F = function (e, t) {
                return _("writeFile", [e, t]);
            }, T = function (e) {
                return _("readFile", [e]);
            }, A = function (e) {
                return _("unlink", [e]);
            }, R = function (e) {
                return _("readdir", [e]);
            }, M = function (e) {
                if (null === S) throw j;
                if (r) throw E;
                return r = !0, new Promise(function (r) {
                    var o = [].concat(n(l), n(v(e))).filter(function (e) {
                        return 0 !== e.length;
                    });
                    h("info", "ffmpeg command: ".concat(o.join(" "))), t = r, S(o.length, g(P, o));
                });
            }, D = function (e, t) {
                var r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "";
                return M("-i ".concat(e, " ").concat(r, " ").concat(t));
            }, N = function (e, t, r, n) {
                var o = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : "";
                return M("-i ".concat(e, " -ss ").concat(r, " -to ").concat(n, " ").concat(o, " ").concat(t));
            }, I = function (e, t) {
                var r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "", n = e.reduce(function (e, t) {
                    return "".concat(e, "\nfile ").concat(t);
                }, "");
                return F("concat_list.txt", n), M("-f concat -safe 0 -i concat_list.txt ".concat(r, " ").concat(t));
            };
            return {
                load: L,
                FS: _,
                write: k,
                writeText: F,
                read: T,
                remove: A,
                ls: R,
                run: M,
                transcode: D,
                trim: N,
                concatDemuxer: I
            };
        };
    }, function (e, t) {
        e.exports = ["./ffmpeg", "-nostdin", "-hide_banner"];
    }, function (e, t, r) {
        function n(e, t) {
            var r = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var n = Object.getOwnPropertySymbols(e);
                t && (n = n.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable;
                })), r.push.apply(r, n);
            }
            return r;
        }
        function o(e, t, r) {
            return t in e ? Object.defineProperty(e, t, {
                value: r,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : e[t] = r, e;
        }
        var i = "browser" === r(8)("type") ? r(1) : function (e) {
            return e;
        };
        e.exports = function (e) {
            var t = function (e) {
                for (var t = 1; t < arguments.length; t++) {
                    var r = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? n(Object(r), !0).forEach(function (t) {
                        o(e, t, r[t]);
                    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : n(Object(r)).forEach(function (t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
                    });
                }
                return e;
            }({}, e);
            return ["corePath"].forEach(function (r) {
                void 0 !== e[r] && (t[r] = i(t[r]));
            }), t;
        };
    }, function (e, t, r) {
        (function (t) {
            function n(e) {
                return (n = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) {
                    return typeof e;
                } : function (e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e;
                })(e);
            }
            var o = r(9);
            e.exports = function (e) {
                var r = {};
                return o() ? r.type = "electron" : "object" === ("undefined" == typeof window ? "undefined" : n(window)) ? r.type = "browser" : "function" == typeof importScripts ? r.type = "webworker" : "object" === (void 0 === t ? "undefined" : n(t)) && (r.type = "node"),
                    void 0 === e ? r : r[e];
            };
        }).call(this, r(0));
    }, function (e, t, r) {
        (function (t) {
            e.exports = function () {
                return "undefined" != typeof window && "object" == typeof window.process && "renderer" === window.process.type || (!(void 0 === t || "object" != typeof t.versions || !t.versions.electron) || "object" == typeof navigator && "string" == typeof navigator.userAgent && navigator.userAgent.indexOf("Electron") >= 0);
            };
        }).call(this, r(0));
    }, function (e, t) {
        function r(e, t) {
            return function (e) {
                if (Array.isArray(e)) return e;
            }(e) || function (e, t) {
                if ("undefined" == typeof Symbol || !(Symbol.iterator in Object(e))) return;
                var r = [], n = !0, o = !1, i = void 0;
                try {
                    for (var a, c = e[Symbol.iterator](); !(n = (a = c.next()).done) && (r.push(a.value),
                        !t || r.length !== t); n = !0);
                } catch (e) {
                    o = !0, i = e;
                } finally {
                    try {
                        n || null == c.return || c.return();
                    } finally {
                        if (o) throw i;
                    }
                }
                return r;
            }(e, t) || function (e, t) {
                if (!e) return;
                if ("string" == typeof e) return n(e, t);
                var r = Object.prototype.toString.call(e).slice(8, -1);
                "Object" === r && e.constructor && (r = e.constructor.name);
                if ("Map" === r || "Set" === r) return Array.from(r);
                if ("Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)) return n(e, t);
            }(e, t) || function () {
                throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }();
        }
        function n(e, t) {
            (null == t || t > e.length) && (t = e.length);
            for (var r = 0, n = new Array(t); r < t; r++) n[r] = e[r];
            return n;
        }
        var o = 0, i = function (e) {
            var t = r(e.split(":"), 3), n = t[0], o = t[1], i = t[2];
            return 60 * parseFloat(n) * 60 + 60 * parseFloat(o) + parseFloat(i);
        };
        e.exports = function (e, t) {
            var r = e.message;
            if ("string" == typeof r) if (r.startsWith("  Duration")) {
                var n = r.split(", ")[0].split(": ")[1], a = i(n);
                (0 === o || o > a) && (o = a);
            } else if (r.startsWith("frame")) {
                var c = r.split("time=")[1].split(" ")[0];
                t({
                    ratio: i(c) / o
                });
            } else r.startsWith("video:") && t({
                ratio: 1
            });
        };
    }, function (e, t, r) {
        var n = r(12);
        e.exports = function (e, t) {
            var r = e._malloc(t.length * Uint32Array.BYTES_PER_ELEMENT);
            return t.forEach(function (t, o) {
                var i = n(e, t);
                e.setValue(r + 4 * o, i, "i32");
            }), r;
        };
    }, function (e, t) {
        e.exports = function (e, t) {
            for (var r = e._malloc((t.length + 1) * Uint8Array.BYTES_PER_ELEMENT), n = 0; n < t.length; n += 1) e.setValue(r + n, t.charCodeAt(n), "i8");
            return e.setValue(r + t.length, 0, "i8"), r;
        };
    }, function (e, t) {
        e.exports = function (e) {
            for (var t = [], r = 0, n = 0; (r = e.indexOf(" ", n)) >= 0;) {
                var o = e.substring(n, r), i = o.indexOf("'"), a = o.indexOf('"');
                if (0 === i || 0 === a) {
                    var c = o[0], u = e.indexOf(c, n + 1);
                    if (u < 0) throw new Error("Bad command escape sequence ".concat(c, " near ").concat(r));
                    o = e.substring(n + 1, u), n = u + 2, t.push(o);
                } else if (i > 0 || a > 0) {
                    -1 === i && (i = 1 / 0), -1 === a && (a = 1 / 0);
                    var s = i < a ? "'" : '"', f = Math.min(i, a), l = e.indexOf(s, n + f + 1);
                    if (l < 0) throw new Error("Bad command escape sequence ".concat(s, " near ").concat(r));
                    o = e.substring(n, l + 1), n = l + 2, t.push(o);
                } else "" !== o ? (t.push(o), n = r + 1) : n = r + 1;
            }
            return n !== e.length && t.push(e.substring(n)), t;
        };
    }, function (e, t, r) {
        var n = r(15), o = r(18), i = r(19);
        e.exports = {
            defaultOptions: n,
            getModule: o,
            fetchFile: i
        };
    }, function (e, t, r) {
        (function (t) {
            function n(e, t) {
                var r = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var n = Object.getOwnPropertySymbols(e);
                    t && (n = n.filter(function (t) {
                        return Object.getOwnPropertyDescriptor(e, t).enumerable;
                    })), r.push.apply(r, n);
                }
                return r;
            }
            function o(e, t, r) {
                return t in e ? Object.defineProperty(e, t, {
                    value: r,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = r, e;
            }
            var i = r(1), a = r(16).dependencies, c = r(17);
            e.exports = function (e) {
                for (var t = 1; t < arguments.length; t++) {
                    var r = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? n(Object(r), !0).forEach(function (t) {
                        o(e, t, r[t]);
                    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : n(Object(r)).forEach(function (t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
                    });
                }
                return e;
            }({}, c, {
                corePath: void 0 !== t && "development" === t.env.FFMPEG_ENV ? i("/node_modules/@ffmpeg/core/ffmpeg-core.js") : "https://unpkg.com/@ffmpeg/core@v".concat(a["@ffmpeg/core"].substring(1), "/ffmpeg-core.js")
            });
        }).call(this, r(0));
    }, function (e) {
        e.exports = JSON.parse('{"name":"@ffmpeg/ffmpeg","version":"0.8.3","description":"FFmpeg WebAssembly version","main":"src/index.js","directories":{"example":"examples"},"scripts":{"start":"node scripts/server.js","build":"rimraf dist && webpack --config scripts/webpack.config.prod.js","prepublishOnly":"npm run build","lint":"eslint src","wait":"rimraf dist && wait-on http://localhost:3000/dist/ffmpeg.dev.js","test":"npm-run-all -p -r start test:all","test:all":"npm-run-all wait test:browser:ffmpeg test:node:all","test:node":"node --experimental-wasm-threads --experimental-wasm-bulk-memory node_modules/.bin/_mocha --exit --bail --require ./scripts/test-helper.js","test:node:all":"npm run test:node -- ./tests/*.test.js","test:browser":"mocha-headless-chrome -a allow-file-access-from-files -a incognito -a no-sandbox -a disable-setuid-sandbox -a disable-logging -t 300000","test:browser:ffmpeg":"npm run test:browser -- -f ./tests/ffmpeg.test.html"},"browser":{"./src/node/index.js":"./src/browser/index.js"},"repository":{"type":"git","url":"git+https://github.com/ffmpegwasm/ffmpeg.wasm.git"},"keywords":["ffmpeg","WebAssembly","video"],"author":"Jerome Wu <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/ffmpegwasm/ffmpeg.wasm/issues"},"engines":{"node":">=12.16.1"},"homepage":"https://github.com/ffmpegwasm/ffmpeg.wasm#readme","dependencies":{"@ffmpeg/core":"^0.7.1","idb":"^4.0.5","is-electron":"^2.2.0","is-url":"^1.2.4","node-fetch":"^2.6.0","regenerator-runtime":"^0.13.5","resolve-url":"^0.2.1"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.0","babel-loader":"^8.1.0","cors":"^2.8.5","eslint":"^6.8.0","eslint-config-airbnb-base":"^14.1.0","eslint-plugin-import":"^2.20.1","expect.js":"^0.3.1","express":"^4.17.1","mocha":"^5.2.0","mocha-headless-chrome":"^2.0.3","npm-run-all":"^4.1.5","nyc":"^14.1.1","wait-on":"^3.3.0","webpack":"^4.42.0","webpack-cli":"^3.3.11","webpack-dev-middleware":"^3.7.2"}}');
    }, function (e, t) {
        e.exports = {
            log: !1,
            logger: function () { },
            progress: function () { }
        };
    }, function (e, t, r) {
        function n(e, t, r, n, o, i, a) {
            try {
                var c = e[i](a), u = c.value;
            } catch (e) {
                return void r(e);
            }
            c.done ? t(u) : Promise.resolve(u).then(n, o);
        }
        var o = r(2).log;
        e.exports = function () {
            var e, t = (e = regeneratorRuntime.mark(function e(t) {
                var r, n;
                return regeneratorRuntime.wrap(function (e) {
                    for (; ;) switch (e.prev = e.next) {
                        case 0:
                            if (r = t.corePath, void 0 !== window.Module) {
                                e.next = 12;
                                break;
                            }
                            return o("info", "fetch ffmpeg-core.worker.js script"), e.next = 5, fetch(r.replace("ffmpeg-core.js", "ffmpeg-core.worker.js"));

                        case 5:
                            return e.next = 7, e.sent.blob();

                        case 7:
                            return n = e.sent, window.FFMPEG_CORE_WORKER_SCRIPT = URL.createObjectURL(n), o("info", "worker object URL=".concat(window.FFMPEG_CORE_WORKER_SCRIPT)),
                                o("info", "download ffmpeg-core script (~25 MB) from ".concat(r)), e.abrupt("return", new Promise(function (e) {
                                    var t = document.createElement("script");
                                    t.src = r, t.type = "text/javascript", t.addEventListener("load", function r() {
                                        t.removeEventListener("load", r), o("info", "initialize ffmpeg-core"), window.Module.onRuntimeInitialized = function () {
                                            o("info", "ffmpeg-core initialized"), e(window.Module);
                                        };
                                    }), document.getElementsByTagName("head")[0].appendChild(t);
                                }));

                        case 12:
                            return o("info", "ffmpeg-core is loaded already"), e.abrupt("return", Promise.resolve(window.Module));

                        case 14:
                        case "end":
                            return e.stop();
                    }
                }, e);
            }), function () {
                var t = this, r = arguments;
                return new Promise(function (o, i) {
                    var a = e.apply(t, r);
                    function c(e) {
                        n(a, o, i, c, u, "next", e);
                    }
                    function u(e) {
                        n(a, o, i, c, u, "throw", e);
                    }
                    c(void 0);
                });
            });
            return function (e) {
                return t.apply(this, arguments);
            };
        }();
    }, function (e, t, r) {
        function n(e, t, r, n, o, i, a) {
            try {
                var c = e[i](a), u = c.value;
            } catch (e) {
                return void r(e);
            }
            c.done ? t(u) : Promise.resolve(u).then(n, o);
        }
        var o = r(1), i = function (e) {
            return new Promise(function (t, r) {
                var n = new FileReader();
                n.onload = function () {
                    t(n.result);
                }, n.onerror = function (e) {
                    var t = e.target.error.code;
                    r(Error("File could not be read! Code=".concat(t)));
                }, n.readAsArrayBuffer(e);
            });
        };
        e.exports = function () {
            var e, t = (e = regeneratorRuntime.mark(function e(t) {
                var r, n;
                return regeneratorRuntime.wrap(function (e) {
                    for (; ;) switch (e.prev = e.next) {
                        case 0:
                            if (r = t, void 0 !== t) {
                                e.next = 3;
                                break;
                            }
                            return e.abrupt("return", "undefined");

                        case 3:
                            if ("string" != typeof t) {
                                e.next = 16;
                                break;
                            }
                            if (!/data:_data\/([a-zA-Z]*);base64,([^"]*)/.test(t)) {
                                e.next = 8;
                                break;
                            }
                            r = atob(t.split(",")[1]).split("").map(function (e) {
                                return e.charCodeAt(0);
                            }), e.next = 14;
                            break;

                        case 8:
                            return e.next = 10, fetch(o(t));

                        case 10:
                            return n = e.sent, e.next = 13, n.arrayBuffer();

                        case 13:
                            r = e.sent;

                        case 14:
                            e.next = 20;
                            break;

                        case 16:
                            if (!(t instanceof File || t instanceof Blob)) {
                                e.next = 20;
                                break;
                            }
                            return e.next = 19, i(t);

                        case 19:
                            r = e.sent;

                        case 20:
                            return e.abrupt("return", new Uint8Array(r));

                        case 21:
                        case "end":
                            return e.stop();
                    }
                }, e);
            }), function () {
                var t = this, r = arguments;
                return new Promise(function (o, i) {
                    var a = e.apply(t, r);
                    function c(e) {
                        n(a, o, i, c, u, "next", e);
                    }
                    function u(e) {
                        n(a, o, i, c, u, "throw", e);
                    }
                    c(void 0);
                });
            });
            return function (e) {
                return t.apply(this, arguments);
            };
        }();
    }]);
});