import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class NewsFeedService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/newsfeed', '/api/Newsfeeds');
    }

    assignTraining(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/assign-training', data);
    }

    getLearningSeriesByContentTypeId(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/LearningSeries/' + filterParam.strCategoryId, null);
    }
}

