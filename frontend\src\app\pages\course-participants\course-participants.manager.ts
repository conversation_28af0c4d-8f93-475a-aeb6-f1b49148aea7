import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { CourseParticipantsService } from './course-participants.service';

@Injectable({
    providedIn: 'root'
})
export class CourseParticipantsManager extends BaseManager {

    constructor(protected courseParticipantsService: CourseParticipantsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(courseParticipantsService, loadingService, toastService);
    }
}
