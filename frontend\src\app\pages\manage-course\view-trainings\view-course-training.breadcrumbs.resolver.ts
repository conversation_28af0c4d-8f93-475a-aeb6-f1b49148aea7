import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { from, Observable, of } from 'rxjs';
import { CourseTrainingsService } from '../../course-trainings/course-trainings-service';
import { RestResponse } from 'src/app/shared/auth.model';
import { switchMap } from 'rxjs/operators';
import { RouteDataService } from 'src/app/shared/title.service';

@Injectable({
  providedIn: 'root'
})
export class ViewCourseTrainingBreadcrumbs implements Resolve<any> {
  constructor(protected courseTrainingsService: CourseTrainingsService,public routeDataService:RouteDataService) {

  }
  title: string;
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const trainingId: string | null = route.paramMap.get("id");
    return from(this.courseTrainingsService.getcourseTitle(trainingId)).pipe(
      switchMap(response => {
        const title = response?.data?.title || 'Manage Course Training';
        this.routeDataService.setData('title', title);
        return of([
          { title: "Dashboard", link: "/dashboard/program-admin", active: false },
          { title: 'Manage Courses', link: "/dashboard/program-admin/courses", active: false },
          { title: "Manage Course Training", link: "/dashboard/program-admin/course/trainings/" + trainingId, active: true },
        ])
      })
    );
  }

}

