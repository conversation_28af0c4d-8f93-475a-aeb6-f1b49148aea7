import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Users } from 'src/app/models/users';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { SupplierChainManager } from './supplier-chain-manager.manager';

@Component({
	selector: 'app-supplier-chain-manager',
	templateUrl: './supplier-chain-manager.component.html',
	styleUrls: ['./supplier-chain-manager.component.scss']
})
export class SupplierChainManagerComponent extends BaseListServerSideComponent implements OnInit {

	constructor(protected supplierChainManager: SupplierChainManager, protected toastService: ToastService,
		protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
		protected router: Router, public commonUtil: CommonUtil) {
		super(supplierChainManager, commonService, toastService, loadingService, router);
	}

	ngOnInit() {
		this.request.loadEditPage = false;
		this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
		this.records = new Array<Users>();
		this.init();
	}

	onCancel() {
		this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
	}

	onNewRecord() {
		if (!this.isPlusButton) {
			if (this.filterParam) {
				this.router.navigate(['/dashboard/team/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
			} else {
				this.router.navigate(['/dashboard/team/edit/0']);
			}
			return;
		}
		this.request.loadEditPage = true;
	}

	removeSuccess() {
		this.onCancel();
	}

	search($event) {
		const value = ($event.target as HTMLInputElement).value;
		this.filterParam.searchText = (value && value != '') ? value.trim() : null;
		this.refreshRecord();
	}

}
