import { Component, OnInit } from '@angular/core';
import {BaseDetailComponent} from '../../../config/base.detail.component';
import { UserTrainingStatus } from '../../../models/usertrainingstatus';
import { ActivatedRoute, Router } from '@angular/router';
import { UserTrainingStatusManager } from '../usertrainingstatus.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';

@Component({
  selector: 'app-usertrainingstatus-detail',
  templateUrl: './usertrainingstatus-detail.component.html',
  styleUrls: ['./usertrainingstatus-detail.component.scss']
})
export class UserTrainingStatusDetailComponent extends BaseDetailComponent implements OnInit {

	  constructor(protected route: ActivatedRoute, protected userTrainingStatusManager: UserTrainingStatusManager, protected toastService: ToastService,
	    		  protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService, 
	    		  protected translateService: TranslateService,public commonUtil: CommonUtil ) {
	    	super(userTrainingStatusManager, commonService, toastService, loadingService, route, router, translateService);
	    	
	  }

	  ngOnInit() {
	  	this.record = new UserTrainingStatus();    
	    this.isDetailPage =  true;
	    this.init();
	  }
  
	  onFetchCompleted() { 
	    super.onFetchCompleted();
	    this.filterParam.relationTable = "UserTrainingStatus";
	    this.filterParam.relationId = this.record.id;
	  }
	  
}
