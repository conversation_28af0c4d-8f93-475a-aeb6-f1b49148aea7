import { Component, OnInit } from '@angular/core';
import { LocalStorageService } from 'angular-2-local-storage';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { Users } from 'src/app/models/users';
import { AccountService } from 'src/app/services/account.service';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';
import { FarmService } from '../farm/farm.service';
import { FilterParam } from 'src/app/models/filterparam';

declare const $: any;

@Component({
  selector: 'app-admin-setting',
  templateUrl: './admin-setting.component.html',
  styleUrls: ['./admin-setting.component.scss']
})
export class AdminSettingComponent implements OnInit {
  user: Users
  onClickValidation: boolean = false;
  profileImageLoader: boolean = false;
  uploader: any;
  farms: any[] = [];
  selectedFarms: any[] = [];
  constructor(private accountService: AccountService, private toastService: ToastService, private authService: AuthService, private localStorageService: LocalStorageService, private loadingService: LoadingService, private farmService: FarmService) {
    this.user = new Users();
  }

  ngOnInit() {
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)
    this.fetchProfileData();
  }

  async fetchProfileData() {
    try {
      const response: RestResponse = await this.accountService.fetchMe().toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.user = response.data;
      console.log(this.user, 'user');
      if (this.authService.isFarmAdmin()) {
        // Fetch all available farms
        const farmsResp: RestResponse = await this.farmService.fetchAllAvailableFarms(new FilterParam());
        console.log(farmsResp, 'farmsResp');
        if (farmsResp.status && farmsResp.data && Array.isArray(farmsResp.data)) {
          this.farms = farmsResp.data.map(farm => ({
            id: farm.id,
            farmName: farm.name,
            ...farm
          }));
          // Select only those farms that are in userFarmDetail
          this.selectedFarms = this.user.userFarmDetail ? this.user.userFarmDetail.map(ufd => ufd.farmId) : [];
        } else {
          this.farms = [];
          this.selectedFarms = [];
        }
      }
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  onlyAllowNumbers(event: KeyboardEvent): void {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }

  initializeUploader(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationError("Image size to too large", this.toastService);
          }, 200);

          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationError("only jpg,png,jpeg files are allowed", this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const image = uploadResponse[0];
        image.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(image);
        setTimeout(() => {
          this.onUploadSuccess(image);
        }, 200);
      }
    };

    return uploader;
  }

  uploadProfilePhoto(event: any) {
    if (event && event.target.files.length > 0) {
      this.profileImageLoader = true;
    }
  }

  fileValidationError(data: string, toastService: any) {
    this.profileImageLoader = false;
    toastService.error(data);
  }

  onUploadSuccess(data: any) {
    this.user.profileImageUrl = data.path;
    this.profileImageLoader = false;
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  telInputObject(event: any) {
    if (this.user.countryCode && this.user.phoneNumber) {
      event.setNumber('+' + this.user.countryCode + this.user.phoneNumber);
      return
    } else {
      event.setCountry('sg')
    }

  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  hasError(event: any) {
  }

  onFarmsChange(selectedFarms: any[]) {
    this.selectedFarms = selectedFarms;
  }

  async save(valid) {
    this.onClickValidation = true;
    if (!valid) {
      return;
    }
    // Handle company (farm) selection for farm admins
    if (this.authService.isFarmAdmin()) {
      if (!this.user.userFarmDetail) {
        this.user.userFarmDetail = [];
      }
      const prevFarmDetails = this.user.userFarmDetail || [];
      const prevFarmIds = prevFarmDetails.filter(fd => !fd.isDeleted).map(fd => fd.farmId);
      const selectedFarmIds = this.selectedFarms;
      // Farms removed by user
      const removedFarmIds = prevFarmIds.filter(id => !selectedFarmIds.includes(id));
      // Farms newly added by user
      const addedFarmIds = selectedFarmIds.filter(id => !prevFarmIds.includes(id));
      // Mark removed farms as isDeleted: true
      let updatedFarmDetails = prevFarmDetails.map(fd => {
        if (removedFarmIds.includes(fd.farmId)) {
          return { ...fd, isDeleted: true };
        }
        return fd;
      });
      // Add new farms
      addedFarmIds.forEach(id => {
        const farmObj = this.farms.find(f => f.id === id);
        updatedFarmDetails.push({
          farmId: id,
          farmName: farmObj ? farmObj.farmName : '',
          farmCode: farmObj ? farmObj.farmCode : '',
          isDeleted: false
        });
      });
      this.user.userFarmDetail = updatedFarmDetails;
    }
    const data: any = {
      id: this.user.id,
      firstName: this.user.firstName,
      lastName: this.user.lastName,
      phoneNumber: this.user.phoneNumber,
      countryCode: this.user.countryCode,
      email: this.user.email
    };

    if(!this.authService.isFarmAdmin()){
      data.profileImageUrl = this.user.profileImageUrl
    }

    if (this.authService.isFarmAdmin()) {
      data.userFarmDetail = this.user.userFarmDetail;
    }
    this.loadingService.show();
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.accountService['update'](data);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      this.loadingService.hide();
      this.toastService.success(response.message);
      this.updateUserDataLocalStorage();
    } catch (e) {
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  updateUserDataLocalStorage() {
    let newUserData = this.authService.getUser();
    const { firstName, lastName, profileImageUrl } = this.user;
    newUserData = { ...newUserData, fullName: firstName + ' ' + lastName, firstName, lastName, profileImageUrl }
    this.localStorageService.set('user', newUserData);

  }


}
