import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageCourseService } from './manage-course-service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Course } from 'src/app/models/course';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ManageCourseManager extends BaseManager {

    constructor(protected manageCourseService: ManageCourseService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageCourseService, loadingService, toastService);
    }

    updatePublish(data: Course): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageCourseService.updatePublish(data);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onSaveSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }

    getPublishUser(param: FilterParam): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageCourseService.getisPublish(param);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onSaveSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }

    getsCourseDetails(id: string): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageCourseService.getsCourseDetails(id).toPromise();
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onFetchSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }
}
