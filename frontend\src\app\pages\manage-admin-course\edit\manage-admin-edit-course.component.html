<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container mt-3">
    <!--CREATE COURSE First Accordion Item -->
    <div class="accordion" id="courseAccordion">
        <div class="accordion-item border-0">
            <div class="col-12 mb-2">
                <h2 class="accordion-header add-new-course mt-2" id="headingOne">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        [attr.aria-expanded]="!isCollapsedClosed" [class.collapsed]="isCollapsedClosed"
                        (click)="toggleCollapse()" [attr.data-bs-target]="'#collapseOne'" aria-controls="collapseOne">
                        <img src="/assets/images/icons/menu/course-logo.svg">
                        <span class="ms-2 accordion-title">
                            {{ request.recordId == 0 ? 'Add New Course ' : course?.code ? (course?.code + ' | ' +
                            course?.title) : 'Course loading...'}}
                        </span>
                        <span class="collapse-arrow" [ngClass]="{'collapsed': isCollapsedClosed}"></span>
                    </button>
                </h2>
            </div>
            <div id="collapseOne" class="accordion-collapse collapse" [ngClass]="{'show': !isCollapsedClosed}"
                aria-labelledby="headingOne" data-bs-parent="#courseAccordion">
                <div class="accordion-body p-0">
                    <div class="dashboard-content-container course-main p-1 d-block text-center">
                        <div class="offset-md-3 col-md-6 course-edit-section">
                            <form #courseForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
                                <div class="row">
                                    <div class="col-12 col-md-6 mb-2">
                                        <div class="form-floating mb-4 w-100">
                                            <input maxlength="80"
                                                [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                                                class="form-control" type="text" name="title" #title="ngModel"
                                                [(ngModel)]="course.title" required="required"
                                                placeholder="{{'Course.title' | translate}}">
                                            <label for="floatingInput">{{"Course.title" | translate}}</label>
                                            <app-validation-message [field]="title"
                                                [onClickValidation]="onClickValidation">
                                            </app-validation-message>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 mb-2">
                                        <div class="form-floating mb-4 w-100">
                                            <input minlength="3"
                                                [ngClass]="{'is-invalid':!code.valid && onClickValidation}"
                                                class="form-control" type="text" name="code" #code="ngModel"
                                                [(ngModel)]="course.code" required="required"
                                                placeholder="{{'Course.code' | translate}}">
                                            <label for="floatingInput">{{"Course.code" | translate}}</label>
                                            <app-validation-message [field]="code"
                                                [onClickValidation]="onClickValidation">
                                            </app-validation-message>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-12 mb-2 w-100">
                                        <div class="mb-4 w-100">
                                            <label class="color-dark-grey p-2"
                                                for="description">{{"Course.courseDescription"
                                                |translate}}</label>
                                            <ckeditor [editor]="Editor" id="description" name="description"
                                                #description="ngModel" [(ngModel)]="course.description"
                                                [config]="editorConfig" required>
                                            </ckeditor>
                                            <app-validation-message [field]="description"
                                                [onClickValidation]="onClickValidation">
                                            </app-validation-message>
                                        </div>
                                    </div>
                                    <div class="col-12 d-flex justify-content-end">
                                        <button
                                            class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                                            (click)="goToNextPanel(courseForm.form)">
                                            {{'Save and Continue'}}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Course Part Accordion Inside and Course Part Training Accordion -->
    <div class="accordion" id="coursePartAccordion" *ngIf="course.courseParts?.length > 0">
        <div *ngFor="let courseItem of course.courseParts;let i=index;">
            <div class="accordion-item border-0" id>
                <div class="col-12 mb-2">
                    <h2 class="accordion-header add-new-course mt-2" id="coursePart{{i}}"
                        (click)="toggleActiveIndex(i, $event)">
                        <button class="mt-20 accordion-button" type="button" data-bs-toggle="collapse"
                            [class.collapsed]="i==activeIndex" data-bs-target="#collapseCoursePart{{i}}"
                            aria-expanded="true" aria-controls="collapseCoursePart{{i}}">
                            <span class="accordion-title ms-2">
                                <img src="/assets/images/icons/menu/course-part-logo.svg">
                                <span *ngIf="courseItem.id" class="ms-2">Part {{ toRoman(i + 1) }} - </span>
                                <span>{{courseItem?.title && courseItem.id ? courseItem?.title: 'Add New Part'}}</span>
                            </span>
                        </button>
                        <button
                            *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM', 'AddButton') && !courseItem.id && (i === course.courseParts.length-1) && !isEditing"
                            class="btn btn-secondary site-button btn-sm large-button save-button rounded-3 me-2 border-0"
                            type="button" (click)="addNewCoursePart(0);startEditing(i);$event.stopPropagation()"
                            data-bs-toggle="tooltip" data-bs-placement="top" title="Add Course Part">
                            <img src="/assets/images/icons/menu/add_icon.svg" class="me-3 width-15px" alt="">Add
                        </button>
                        <button
                            *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','AddButton') && courseItem.id"
                            class="btn training-btn">
                            <img src="/assets/images/icons/menu/teaching.svg" (click)="showTrainingPart(i,$event);
                  " data-bs-toggle="tooltip" data-bs-placement="top" title="Add Course Part Training"
                                style="width: 26px;">
                        </button>
                        <i *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','EditButton') && courseItem.id"
                            [class.disabled]="authService.isDisabled('ADMIN_MANAGE_PROGRAM','EditButton')" title="Edit"
                            (click)="showCoursePart(i,$event);" class="bi bi-pencil font-21px me-2 cursor-pointer">
                        </i>
                        <i *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','DeleteButton') && !isPlusButton && courseItem.id"
                            [class.disabled]="authService.isDisabled('ADMIN_MANAGE_PROGRAM','DeleteButton')"
                            title="Delete" (click)="removePart(courseItem.id, i)"
                            class="bi bi-trash font-21px cursor-pointer me-2">
                        </i>
                        <span class="collapse-arrowg" [ngClass]="{'collapsed': i==activeIndex}"></span>
                    </h2>
                </div>
                <div id="collapseCoursePart{{i}}" [ngClass]="{'show': i == activeIndex}"
                    class="accordion-collapse collapse" aria-labelledby="coursePart{{i}}"
                    data-bs-parent="#coursePartAccordion">
                    <div class="accordion-body p-0">
                        <div class="dashboard-content-container course-main mt-2 p-1 d-block mh-0"
                            *ngIf="(courseItem?.showCourseMain && (courseItem?.trainings?.length>0 )) || courseItem?.showCoursePart || !courseItem.id">
                            <div id="coursePartEdit{{courseItem.id}}" class="offset-md-4 col-md-5 course-edit-section"
                                *ngIf="courseItem?.showCoursePart || !courseItem.id">
                                <form #coursePartForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
                                    <div class="mt-2 mb-3">
                                        <h4 class="fw-bold">Create a part</h4>
                                        <p class="user-edit-msg">Please make sure you fill all the fields before you
                                            click
                                            on
                                            {{'Save and Continue'}} button
                                        </p>
                                    </div>
                                    <div class="col-12 col-md-12 mb-4">
                                        <div class="form-floating mb-4 w-100">
                                            <input maxlength="80"
                                                [ngClass]="{'is-invalid':!parttitle.valid && onClickValidation}"
                                                class="form-control" type="text" name="parttitle" #parttitle="ngModel"
                                                [(ngModel)]="courseItem.title" required="required"
                                                placeholder="{{'Course.parttitle' | translate}}">
                                            <label for="floatingInput">{{"Course.parttitle" | translate}}</label>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-12 mb-4">
                                        <div class="mb-4 w-100">
                                            <label class="color-dark-grey p-2"
                                                for="courseItemDescription">{{"Course.description" |
                                                translate}}</label>
                                            <ckeditor [editor]="Editor" id="courseItemDescription{{i}}"
                                                name="courseItemdescription" #courseDescription="ngModel"
                                                [(ngModel)]="courseItem.description" [config]="editorConfig" required>
                                            </ckeditor>
                                            <app-validation-message [field]="courseDescription"
                                                [onClickValidation]="onClickValidation">
                                            </app-validation-message>
                                        </div>
                                    </div>
                                    <div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end gap-2">
                                        <button *ngIf="!courseItem.id"
                                            class="btn btn-danger site-button btn-sm large-button save-button rounded-3"
                                            (click)="activeIndex=-1;cancelEditing(i)">
                                            {{ 'Cancel' }}
                                        </button>
                                        <button
                                            class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                                            (click)="saveParts(coursePartForm.form,courseItem,i)">
                                            {{ 'Save and Continue'}}
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <!-- Course Part Training Accordion Item -->
                            <div class="accordion p-sm-2 p-md-3 p-lg-4 drag-container" id="coursePartAccordion{{i}}"
                                *ngIf="courseItem.showTrainingPart" cdkDropList (cdkDropListDropped)="drop($event, i)">
                                <div class="accordion-item border-0" id="courseTraining{{i}}"
                                    *ngFor="let courseTraining of courseItem.trainings; let j=index;" cdkDrag
                                    [cdkDragDisabled]="!courseTraining.id ||courseTraining.isLock || i+'_'+j == activeTraningIndex">
                                    <div class="col-12 mb-2">
                                        <h2 class="accordion-header add-new-course mt-2 bg-dark border-0"
                                            id="coursePartTraining{{i+'_'+j}}">
                                            <button class="training-button accordion-button text-light" type="button"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#collapseCoursePartTraining{{i+'_'+j}}"
                                                aria-expanded="true"
                                                aria-controls="collapseCoursePartTraining{{i+'_'+j}}"
                                                [class.collapsed]="i+'_'+j == activeTraningIndex">
                                                <span class="accordion-title">
                                                    <img src="/assets/images/icons/menu/course-training.svg">
                                                    <span class="ms-2" *ngIf="!courseTraining.id">Add New
                                                        Training</span>
                                                    <span class="ms-2"
                                                        *ngIf="courseTraining.id">{{courseTraining?.trainingLibraryDetail?.id
                                                        ?
                                                        courseTraining?.trainingLibraryDetail?.title :
                                                        courseTraining?.title}}</span>
                                                </span>
                                                <span class="collapse-arrow"></span>
                                            </button>
                                            <span class="d-flex align-items-center">
                                                <button *ngIf="courseTraining.id && courseTraining.isLock"
                                                    class="btn training-btn">
                                                    <img src="/assets/images/icons/menu/lock.svg"
                                                        data-bs-toggle="tooltip" data-bs-placement="top"
                                                        title="Add Course Part Training" style="width: 24px;">
                                                </button>
                                                <i *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','EditButton') && courseTraining.id"
                                                    [class.disabled]="authService.isDisabled('ADMIN_MANAGE_PROGRAM','EditButton')"
                                                    title="Edit"
                                                    (click)="editRecord(courseTraining.id); activeTraningIndex = activeTraningIndex == i+'_'+j ? -1 : i+'_'+j"
                                                    class="bi bi-pencil font-21px me-2 cursor-pointer text-light">
                                                </i>
                                                <i *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','DeleteButton') && !isPlusButton && courseTraining.id"
                                                    [class.disabled]="authService.isDisabled('ADMIN_MANAGE_PROGRAM','DeleteButton')"
                                                    title="Delete" (click)="removeIndex(courseTraining.id, i, j)"
                                                    class="bi bi-trash font-21px cursor-pointer me-2 text-light">
                                                </i>
                                            </span>
                                        </h2>
                                    </div>
                                    <div id="collapseCoursePartTraining{{i+'_'+j}}"
                                        class="accordion-collapse collapse show"
                                        aria-labelledby="coursePartTraining{{i+'_'+j}}"
                                        data-bs-parent="#coursePartAccordion{{i}}"
                                        [ngClass]="{'show': i+'_'+j == activeTraningIndex}">
                                        <div class="accordion-body p-0">
                                            <div
                                                class="dashboard-content-container mt-2 p-1 course-main d-block text-center">
                                                <div class="container course-training-section">
                                                    <form #courseTrainingForm="ngForm" novalidate="novalidate"
                                                        class="text-left d-inline-block"
                                                        *ngIf="!courseTraining?.trainingLibraryDetail?.id">
                                                        <div class="row">
                                                            <div class="col-12 col-md-6 mb-2">
                                                                <div class="form-floating mb-4 w-100">
                                                                    <div class="typeahead-container form-floating mb-4 w-100 text-left"
                                                                        #containerRef>
                                                                        <input type="text"
                                                                            (click)="$event.stopPropagation();showDropdown = true"
                                                                            #title="ngModel"
                                                                            [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                                                                            name="title"
                                                                            (input)="filterTrainingLibrary($event,i,j);courseTraining.title=$event.target.value"
                                                                            [(ngModel)]="courseTraining.title"
                                                                            class="typeahead-input form-control"
                                                                            required="required"
                                                                            placeholder="{{'Leverage Training' | translate}}" />
                                                                        <label for="trainingLibrary">{{'Leverage
                                                                            Training / Title' | translate}}</label>
                                                                        <ul *ngIf="showDropdown && filteredTraininglLibrary.length > 0"
                                                                            class="typeahead-dropdown">
                                                                            <li *ngFor="let training of filteredTraininglLibrary"
                                                                                (click)="selectTrainingLibrary(training,i,j);courseTraining.title=training.title"
                                                                                class="typeahead-item">
                                                                                {{ training.title }}
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6 mb-2">
                                                                <div class="form-floating">
                                                                    <div class="mb-4 form-control select-width ng-select-main-container"
                                                                        [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                                                                        <ng-select
                                                                            placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                                                            name="contentTypeId" [items]="contentTypes"
                                                                            bindLabel="title" bindValue="id"
                                                                            (change)="selectContentType($event,i,j)"
                                                                            required="required"
                                                                            class="custom-multiselect form-control padding-bottom-8"
                                                                            [(ngModel)]="courseTraining.contentType"
                                                                            #contentTypeId="ngModel"
                                                                            [searchable]="false" [disabled]="courseTraining.isProgramAdminTraining ||
                                                                             isSelectLeverageTraining">
                                                                        </ng-select>
                                                                    </div>
                                                                    <label for=" language">{{"Training.contentTypes" |
                                                                        translate}}</label>
                                                                </div>
                                                            </div>
                                                            <!-- <div class="col-12 col-md-3 mb-2">
                                  <div class="form-floating mb-4 w-100">
                                    <input [ngClass]="{'is-invalid':!sequence.valid && onClickValidation}"
                                      class="form-control" type="number" name="sequence" #sequence="ngModel"
                                      [(ngModel)]="courseTraining.sequence" required="required"
                                      [disabled]="courseTraining.isProgramAdminTraining"
                                      placeholder="{{'Training.sequence' | translate}}">
                                    <label for="floatingInput">{{"Training.sequence" |
                                      translate}}</label>
                                  </div>
                                </div> -->
                                                            <div class="col-12 col-md-6 mb-2">
                                                                <div class="w-100">
                                                                    <label class="color-dark-grey p-2"
                                                                        for="courseTraining">{{"Course.description" |
                                                                        translate}}</label>
                                                                    <ckeditor [editor]="Editor"
                                                                        *ngIf="i+'_'+j == activeTraningIndex"
                                                                        id="courseTraining{{i+'_'+j}}"
                                                                        name="courseTraining{{i+'_'+j}}"
                                                                        #courseTrainingEditor="ngModel"
                                                                        [(ngModel)]="courseTraining.description"
                                                                        [config]="editorConfig" required [disabled]="courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining">
                                                                    </ckeditor>
                                                                    <app-validation-message [field]="courseTraining"
                                                                        [onClickValidation]="onClickValidation">
                                                                    </app-validation-message>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6">
                                                                <div class="row">
                                                                    <div class="col-12 col-md-12 mb-2">
                                                                        <div class="form-floating">
                                                                            <div class="mb-4 form-control select-width ng-select-main-container"
                                                                                [ngClass]="{'is-invalid':!prerequisitesId.valid && onClickValidation}">
                                                                                <ng-select
                                                                                    placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                                                                    name="prerequisitesId"
                                                                                    [items]="filteredPreRequisitesTraining"
                                                                                    bindLabel="title" bindValue="id"
                                                                                    class="custom-multiselect form-control padding-bottom-8"
                                                                                    [disabled]="courseTraining.isProgramAdminTraining"
                                                                                    [(ngModel)]="courseTraining.prerequisitesTraining"
                                                                                    #prerequisitesId="ngModel"
                                                                                    [searchable]="false"
                                                                                    (ngModelChange)="courseTraining.isLock = !!$event">
                                                                                </ng-select>
                                                                            </div>
                                                                            <label
                                                                                for="language">{{"Training.prerequisites"
                                                                                |
                                                                                translate}}
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-5" [ngClass]="{'disabled': courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining }">
                                                                        <div *ngIf="!courseTraining.url"
                                                                            class="upload-video-container mb-4 form-control text-light bg-secondary p-3"
                                                                            [ngClass]="{'is-invalid': !courseTraining.url && onClickValidation}">
                                                                            <label *ngIf="!courseTraining.url"
                                                                                [ngClass]="{'d-flex align-items-center justify-content-center': !courseTraining.url, 'cursor-default': loading}"
                                                                                class="fw-bold" for="file-input">
                                                                                <img src="/assets/images/icons/menu/upload-video-icon.svg"
                                                                                    class="img-fluid me-2 mb-2 width-28-px text-center"
                                                                                    alt="" />
                                                                                {{ loading ? (fileUploadingMessage ||
                                                                                'Uploading...') : 'Upload Training
                                                                                Video' }}
                                                                                <div *ngIf="loading"
                                                                                    class="spinner-border ms-2"
                                                                                    role="status"
                                                                                    style="width: 1.5rem; height: 1.5rem">
                                                                                    <span
                                                                                        class="visually-hidden">Loading...</span>
                                                                                </div>
                                                                            </label>
                                                                            <input
                                                                                *ngIf="!courseTraining.url && !loading"
                                                                                name="trainingVideo" [disabled]="courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining" (change)="uploadVideo($event, courseTraining, i, j)"
                                                                                id="file-input" type="file"
                                                                                accept="video/*" />
                                                                        </div>
                                                                        <div *ngIf="courseTraining.url"
                                                                            class="video-wrapper mb-4" [ngClass]="{'disabled': courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining }" [ngStyle]="{'display': courseTraining.url ? 'block' : 'none' }"
                                                                            [disabled]="courseTraining.isProgramAdminTraining || isSelectLeverageTraining">
                                                                            <div class="video-container"
                                                                                id="video-container">
                                                                                <div class="play-button-wrapper">
                                                                                    <div (click)="playVideoFromPlayIcon()"
                                                                                        title="Play video"
                                                                                        class="play-gif circle-play-b-cls"
                                                                                        id="circle-play-b">
                                                                                        <!-- SVG Play Button -->
                                                                                        <svg *ngIf="!videoPlaying"
                                                                                            xmlns="http://www.w3.org/2000/svg"
                                                                                            viewBox="0 0 80 80">
                                                                                            <path
                                                                                                d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="position-absolute delete-video-container"
                                                                                    [ngClass]="{'disabled': courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining }" (click)="removeFileCustom(i,j,'videoUrl')"
                                                                                    [disabled]="courseTraining.isProgramAdminTraining || isSelectLeverageTraining">
                                                                                    <i class="bi bi-x"></i>
                                                                                </div>
                                                                                <video playsinline class="mw-100"
                                                                                    [src]="courseTraining.url"
                                                                                    id="training_video"
                                                                                    controlslist="nodownload">
                                                                                    Your browser does not support HTML
                                                                                    video.
                                                                                </video>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-5" [ngClass]="{'disabled': courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining }">
                                                                        <div *ngIf="!courseTraining.thumbnailImageUrl || courseTraining.thumbnailImageUrl==''"
                                                                            class="col-12 mb-4 form-control text-center border-2 border-dark upload-img-container"
                                                                            [ngClass]="{'is-invalid': !courseTraining.thumbnailImageUrl && onClickValidation}">
                                                                            <label id="file-input-thumbnail"
                                                                                class="upload-img-button cursor-pointer p-2"
                                                                                [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingThumbnail}"><img
                                                                                    src="/assets/images/icons/menu/image-thumbnail.svg"
                                                                                    class="me-2 mb-2 width-28-px upload-icon text-center"
                                                                                    alt="">
                                                                                <br>
                                                                                {{!uploadingThumbnail ? ('Add Thumbnail
                                                                                Image' |
                                                                                translate)
                                                                                :
                                                                                ('UPLOADIN THUMBNAIL..' |
                                                                                translate) }}
                                                                                <div *ngIf="uploadingThumbnail"
                                                                                    class="spinner-border ms-2"
                                                                                    role="status"
                                                                                    style="width: 1.5rem; height: 1.5rem; padding:10px;">
                                                                                    <span
                                                                                        class="visually-hidden">Loading...</span>
                                                                                </div>
                                                                                <input *ngIf="!uploadingThumbnail"
                                                                                    name="thumbnailUrl" class="d-none"
                                                                                    [disabled]="courseTraining.isProgramAdminTraining || isSelectLeverageTraining"
                                                                                    (change)="uploadThumbnail($event, courseTraining, i, j)"
                                                                                    id="file-input-thumbnail"
                                                                                    type="file"
                                                                                    accept="image/png, image/jpg, image/jpeg" />
                                                                            </label>
                                                                        </div>
                                                                        <div class="moment-image-container mb-4 max-width-none"
                                                                            *ngIf="courseTraining.thumbnailImageUrl && courseTraining.thumbnailImageUrl!=''">
                                                                            <img
                                                                                [src]="courseTraining.thumbnailImageUrl" />
                                                                            <div class="position-absolute delete-video-container"
                                                                                (click)="removeFileCustom(i,j,'thumbnailImageUrl')"
                                                                                [ngClass]="{'disabled': courseTraining.isProgramAdminTraining ||
                                        isSelectLeverageTraining }" [disabled]="courseTraining.isProgramAdminTraining || isSelectLeverageTraining">
                                                                                <i class="bi bi-x"></i>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6">
                                                                <div
                                                                    class="d-flex justify-content-start align-items-center">
                                                                    <span class="me-2 text-start">
                                                                        <label for="flexSwitchCheckChecked"
                                                                            class="form-label">Public</label>
                                                                    </span>
                                                                    <div class="form-check form-switch d-inline-block">
                                                                        <input
                                                                            (change)="selectAccessibility(i, j, $event)"
                                                                            [disabled]="courseTraining.isProgramAdminTraining || isSelectLeverageTraining"
                                                                            class="form-check-input toggle-width custom-switch"
                                                                            type="checkbox"
                                                                            id="flexSwitchCheckChecked{{i}}{{j}}"
                                                                            [checked]="course.courseParts[i].trainings[j].accessibility === 'Private'">
                                                                    </div>

                                                                    <span class="ms-2">
                                                                        <label for="flexSwitchCheckChecked"
                                                                            class="form-label">Private</label>
                                                                    </span>
                                                                </div>

                                                            </div>
                                                            <div class="col-12 col-md-6">
                                                                <div class="form-check">
                                                                    <input
                                                                        class="form-check-input custom-form-check ms-0"
                                                                        type="checkbox" name="isPublish" value=""
                                                                        [ngClass]="{'is-invalid':!isPublish.valid && onClickValidation}"
                                                                        id="flexCheckDefault"
                                                                        [(ngModel)]="courseTraining.isTermsConditions"
                                                                        [disabled]="courseTraining.isProgramAdminTraining || isSelectLeverageTraining"
                                                                        #isPublish="ngModel" required="required">
                                                                    <label class="form-check-label ms-3"
                                                                        for="flexCheckDefault">
                                                                        I have read and accept the
                                                                        <a href="https://integrax.co/terms-and-conditions"
                                                                            target="_blank">
                                                                            Terms & Conditions
                                                                        </a> <br>
                                                                        for uploading this video to <span
                                                                            class="fw-bold">Integrax</span>
                                                                    </label>

                                                                </div>
                                                            </div>
                                                            <div
                                                                class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
                                                                <button
                                                                    class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                                                                    (click)="saveCourseTrainingForm(courseTrainingForm.form,courseTraining,j,i)"
                                                                    [disabled]="!courseTraining.url && !courseTraining?.trainingLibraryDetail?.url">
                                                                    {{'Save and continue'}}
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </form>
                                                    <form #courseTrainingForm="ngForm" novalidate="novalidate"
                                                        class="text-left d-inline-block"
                                                        *ngIf="courseTraining?.trainingLibrary && courseTraining?.trainingLibraryDetail?.id">
                                                        <div class="row">
                                                            <div class="col-12 col-md-6 mb-2">
                                                                <div class="form-floating mb-4 w-100">
                                                                    <div class="typeahead-container form-floating mb-4 w-100 text-left"
                                                                        #containerRef>
                                                                        <input type="text"
                                                                            (click)="$event.stopPropagation();showDropdown = true"
                                                                            #title="ngModel"
                                                                            [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                                                                            name="title"
                                                                            (input)="filterTrainingLibrary($event,i,j);courseTraining.trainingLibraryDetail.title=$event.target.value"
                                                                            [(ngModel)]="courseTraining.trainingLibraryDetail.title"
                                                                            class="typeahead-input form-control"
                                                                            required="required"
                                                                            placeholder="{{'Leverage Training' | translate}}" />
                                                                        <label for="trainingLibrary">{{'Leverage
                                                                            Training / Title' | translate}}</label>
                                                                        <ul *ngIf="showDropdown && filteredTraininglLibrary.length > 0"
                                                                            class="typeahead-dropdown">
                                                                            <li *ngFor="let training of filteredTraininglLibrary"
                                                                                (click)="selectTrainingLibrary(training,i,j);courseTraining.trainingLibraryDetail.title=training.title"
                                                                                class="typeahead-item">
                                                                                {{ training.title }}
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6 mb-2">
                                                                <div class="form-floating">
                                                                    <div class="mb-4 form-control select-width ng-select-main-container"
                                                                        [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                                                                        <ng-select
                                                                            placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                                                            name="contentTypeId" [items]="contentTypes"
                                                                            bindLabel="title" bindValue="id"
                                                                            (change)="selectContentType($event,i,j)"
                                                                            required="required"
                                                                            class="custom-multiselect form-control padding-bottom-8"
                                                                            [(ngModel)]="courseTraining.trainingLibraryDetail.contentType"
                                                                            #contentTypeId="ngModel"
                                                                            [searchable]="false"
                                                                            [disabled]="courseTraining?.trainingLibraryDetail?.id">
                                                                        </ng-select>
                                                                    </div>
                                                                    <label for="language">{{"Training.contentTypes" |
                                                                        translate}}</label>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6 mb-2">
                                                                <div class="w-100">
                                                                    <label class="color-dark-grey p-2"
                                                                        for="trainingLibraryDetail">{{"Course.description"
                                                                        |
                                                                        translate}}</label>
                                                                    <ckeditor [editor]="Editor"
                                                                        id="trainingLibraryDetail"
                                                                        name="trainingLibraryDetail"
                                                                        #trainingLibraryDetail="ngModel"
                                                                        [(ngModel)]="courseTraining.trainingLibraryDetail.description"
                                                                        [config]="editorConfig"
                                                                        [disabled]="courseTraining.trainingLibraryDetail?.id"
                                                                        required>
                                                                    </ckeditor>
                                                                    <app-validation-message
                                                                        [field]="trainingLibraryDetail"
                                                                        [onClickValidation]="onClickValidation">
                                                                    </app-validation-message>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6">
                                                                <div class="row">
                                                                    <div class="col-12 col-md-12 mb-2">
                                                                        <div class="form-floating">
                                                                            <div class="mb-4 form-control select-width ng-select-main-container"
                                                                                [ngClass]="{'is-invalid':!prerequisitesId.valid && onClickValidation}">
                                                                                <ng-select
                                                                                    placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                                                                    name="prerequisitesId"
                                                                                    [items]="filteredPreRequisitesTraining"
                                                                                    bindLabel="title" bindValue="id"
                                                                                    class="custom-multiselect form-control padding-bottom-8"
                                                                                    [(ngModel)]="courseTraining.prerequisitesTraining"
                                                                                    #prerequisitesId="ngModel"
                                                                                    [searchable]="false"
                                                                                    (ngModelChange)="courseTraining.trainingLibraryDetail.isLock = !!$event">
                                                                                </ng-select>
                                                                            </div>
                                                                            <label
                                                                                for="language">{{"Training.prerequisites"
                                                                                |
                                                                                translate}}
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-5">
                                                                        <div *ngIf="!courseTraining.url"
                                                                            class="upload-video-container mb-4 form-control text-light bg-secondary p-3"
                                                                            [ngClass]="{'is-invalid': !courseTraining.trainingLibraryDetail.url && onClickValidation}">
                                                                            <label
                                                                                *ngIf="!courseTraining.trainingLibraryDetail.url"
                                                                                [ngClass]="{'d-flex align-items-center justify-content-center': !courseTraining.trainingLibraryDetail.url, 'cursor-default': loading}"
                                                                                class="fw-bold" for="file-input">
                                                                                <img src="/assets/images/icons/menu/upload-video-icon.svg"
                                                                                    class="img-fluid me-2 mb-2 width-28-px text-center"
                                                                                    alt="" />
                                                                                {{ loading ? (fileUploadingMessage ||
                                                                                'Uploading...') : 'Upload Training
                                                                                Video' }}
                                                                                <div *ngIf="loading"
                                                                                    class="spinner-border ms-2"
                                                                                    role="status"
                                                                                    style="width: 1.5rem; height: 1.5rem">
                                                                                    <span
                                                                                        class="visually-hidden">Loading...</span>
                                                                                </div>
                                                                            </label>
                                                                            <input
                                                                                *ngIf="!courseTraining.trainingLibraryDetail.url && !loading"
                                                                                name="trainingVideo"
                                                                                [disabled]="courseTraining.trainingLibraryDetail?.id"
                                                                                (change)="uploadVideo($event, courseTraining, i, j)"
                                                                                id="file-input" type="file"
                                                                                accept="video/*" />
                                                                        </div>
                                                                        <div *ngIf="courseTraining.url"
                                                                            class="video-wrapper mb-4"
                                                                            [ngStyle]="{'display': courseTraining.trainingLibraryDetail.url ? 'block' : 'none' }"
                                                                            [disabled]="courseTraining.trainingLibraryDetail?.id">
                                                                            <div class="video-container"
                                                                                id="video-container">
                                                                                <div class="play-button-wrapper">
                                                                                    <div (click)="playVideoFromPlayIcon()"
                                                                                        title="Play video"
                                                                                        class="play-gif circle-play-b-cls"
                                                                                        id="circle-play-b">
                                                                                        <!-- SVG Play Button -->
                                                                                        <svg *ngIf="!videoPlaying"
                                                                                            xmlns="http://www.w3.org/2000/svg"
                                                                                            viewBox="0 0 80 80">
                                                                                            <path
                                                                                                d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                                                                                        </svg>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="position-absolute delete-video-container"
                                                                                    (click)="removeFileCustom(i,j,'videoUrl')">
                                                                                    <i class="bi bi-x"></i>
                                                                                </div>
                                                                                <video playsinline class="mw-100"
                                                                                    [src]="courseTraining.url"
                                                                                    id="training_video"
                                                                                    controlslist="nodownload">
                                                                                    Your browser does not support HTML
                                                                                    video.
                                                                                </video>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-5">
                                                                        <div *ngIf="!courseTraining.trainingLibraryDetail.thumbnailImageUrl || courseTraining.trainingLibraryDetail.thumbnailImageUrl==''"
                                                                            class="col-12 mb-4 form-control text-center border-2 border-dark upload-img-container"
                                                                            [ngClass]="{'is-invalid': !courseTraining.trainingLibraryDetail.thumbnailImageUrl && onClickValidation}">
                                                                            <label id="file-input-thumbnail"
                                                                                class="upload-img-button cursor-pointer p-2"
                                                                                [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingThumbnail}"><img
                                                                                    src="/assets/images/icons/menu/image-thumbnail.svg"
                                                                                    class="me-2 mb-2 width-28-px upload-icon text-center"
                                                                                    alt="">
                                                                                <br>
                                                                                {{!uploadingThumbnail ? ('Add Thumbnail
                                                                                Image' |
                                                                                translate)
                                                                                :
                                                                                ('UPLOADIN THUMBNAIL..' |
                                                                                translate) }}
                                                                                <div *ngIf="uploadingThumbnail"
                                                                                    class="spinner-border ms-2"
                                                                                    role="status"
                                                                                    style="width: 1.5rem; height: 1.5rem; padding:10px;">
                                                                                    <span
                                                                                        class="visually-hidden">Loading...</span>
                                                                                </div>
                                                                                <input *ngIf="!uploadingThumbnail"
                                                                                    name="thumbnailUrl" class="d-none"
                                                                                    [disabled]="courseTraining.trainingLibraryDetail?.id"
                                                                                    (change)="uploadThumbnail($event, courseTraining, i, j)"
                                                                                    id="file-input-thumbnail"
                                                                                    type="file"
                                                                                    accept="image/png, image/jpg, image/jpeg" />
                                                                            </label>
                                                                        </div>
                                                                        <div class="moment-image-container mb-4 max-width-none"
                                                                            *ngIf="courseTraining.trainingLibraryDetail.thumbnailImageUrl && courseTraining.trainingLibraryDetail.thumbnailImageUrl!=''">
                                                                            <img
                                                                                [src]="courseTraining.trainingLibraryDetail.thumbnailImageUrl" />
                                                                            <div class="position-absolute delete-video-container"
                                                                                (click)="removeFileCustom(i,j,'thumbnailImageUrl')">
                                                                                <i class="bi bi-x"></i>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-6">
                                                                <div
                                                                    class="d-flex justify-content-start align-items-center">
                                                                    <span class="me-2 text-start">
                                                                        <label for="flexSwitchCheckChecked"
                                                                            class="form-label">Public</label>
                                                                    </span>
                                                                    <div class="form-check form-switch d-inline-block">
                                                                        <input
                                                                            (change)="selectAccessibility(i, j, $event)"
                                                                            [disabled]="courseTraining.trainingLibraryDetail?.id"
                                                                            class="form-check-input toggle-width custom-switch"
                                                                            type="checkbox"
                                                                            id="flexSwitchCheckChecked{{i}}{{j}}"
                                                                            [checked]="course.courseParts[i].trainings[j].trainingLibraryDetail.accessibility === 'Private'">
                                                                    </div>

                                                                    <span class="ms-2">
                                                                        <label for="flexSwitchCheckChecked"
                                                                            class="form-label">Private</label>
                                                                    </span>
                                                                </div>

                                                            </div>
                                                            <div class="col-12 col-md-6">
                                                                <div class="form-check">
                                                                    <input
                                                                        class="form-check-input custom-form-check ms-0"
                                                                        type="checkbox" name="isPublish" value=""
                                                                        [ngClass]="{'is-invalid':!isPublish.valid && onClickValidation}"
                                                                        id="flexCheckDefault"
                                                                        [(ngModel)]="courseTraining.trainingLibraryDetail.isTermsConditions"
                                                                        [disabled]="courseTraining.trainingLibraryDetail?.id"
                                                                        #isPublish="ngModel" required="required">
                                                                    <label class="form-check-label ms-3"
                                                                        for="flexCheckDefault">
                                                                        I have read and accept the
                                                                        <a href="https://integrax.co/terms-and-conditions"
                                                                            target="_blank">
                                                                            Terms & Conditions
                                                                        </a> <br>
                                                                        for uploading this video to <span
                                                                            class="fw-bold">Integrax</span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div
                                                                class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
                                                                <button
                                                                    class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                                                                    (click)="saveCourseTrainingForm(courseTrainingForm.form,courseTraining,j,i)"
                                                                    [disabled]="!courseTraining.url && !courseTraining?.trainingLibraryDetail?.url">
                                                                    {{'Save and continue'}}
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mt-3">
            <div class="d-flex justify-content-between">
                <div>
                    <button *ngIf="course.id"
                        class="btn btn-danger site-button btn-sm large-button save-button rounded-3"
                        [routerLink]="['/dashboard/program-admin/courses']">
                        {{ 'Cancel' }}
                    </button>
                </div>
                <div class="d-flex">
                    <button (click)="saveAsDraft()"
                        class="btn btn-secondary site-button btn-sm large-button save-button rounded-3 mx-3 unselected-button">SAVE
                        AS
                        DRAFT
                    </button>
                    <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                        (click)="toPublishAll(data)">
                        publish
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>