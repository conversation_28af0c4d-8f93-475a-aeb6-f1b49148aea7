import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { MostCompletedTrainingUsersListManager } from './most-completed-training-users-list.manager';
import { MostCompletedTrainingUsersListService } from './most-completed-training-users-list.service';
import { MostCompletedTrainingsList } from 'src/app/models/mostcompletedtaininglist';

@Component({
  selector: 'app-most-completed-training-users-list',
  templateUrl: './most-completed-training-users-list.component.html',
  styleUrls: ['./most-completed-training-users-list.component.scss']
})
export class MostCompletedTrainingUsersListComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() getcompletedTrainingData: any | undefined;
  filterParam: FilterParam | undefined;
  constructor(protected mostCompletedTrainingUsersListManager: MostCompletedTrainingUsersListManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService,
    protected router: Router, public commonUtil: CommonUtil, protected mostCompletedTrainingUsersListService: MostCompletedTrainingUsersListService) {
    super(mostCompletedTrainingUsersListManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.filterParam.trainingId = this.getcompletedTrainingData.id;
    this.init();
    this.records = new Array<MostCompletedTrainingsList>();
  }

  ngOnDestroy(): void {
  }

}
