import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
export class CourseInvited extends BaseModel {

    fullName: string;
    email: string;
    code: string;
    courseDetail: { email: string, code?: string; };
    userDetail: { email: string, fullName?: string; userName?: string };
    description: any;
    username: string;
    course:string;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        // this.courseDetail = new Array<any>();
        // this.userDetail = new Array<any>();
    }

    static fromResponse(data: any): CourseInvited {
        const obj = new CourseInvited();
        obj.id = data.id;
        obj.createdBy = data.createdBy;
        obj.updatedBy = data.updatedBy;
        obj.createdOn = data.createdOn;
        obj.updatedOn = data.updatedOn;
        obj.userDetail = data.userDetail ? data.userDetail : [];
        obj.isDeleted = data.isDeleted;
        obj.isActive = data.isActive;
        obj.fullName = data.fullName;
        obj.username = data.username;
        obj.email = data.email;
        obj.code = data.code;
        obj.description = data.description;
        obj.userDetail = data.userDetail;
        obj.course = data.course;
        return obj;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        if (this.isNullOrUndefinedAndEmpty(this.fullName)) {
            form.controls.title.setErrors({ invalid: true });
            return false;
        }
        if (this.isNullOrUndefinedAndEmpty(this.description)) {
            form.controls.description.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
        this.fullName = this.trimMe(this.fullName);
        this.description = this.trimMe(this.description);
        return this;
    }
}
