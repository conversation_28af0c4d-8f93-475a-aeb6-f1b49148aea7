import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';

@Injectable({
    providedIn: 'root'
})
export class CategoryService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/category', '/api/categories');
    }

    assignProgram(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/assign-category', data);
    }
}

