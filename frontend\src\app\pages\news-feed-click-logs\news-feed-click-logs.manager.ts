import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { NewsFeedClickLogsService } from './news-feed-click-logs.service';

@Injectable({
    providedIn: 'root'
})
export class NewsFeedClickLogsManager extends BaseManager {

    constructor(protected newsFeedClickLogsService: NewsFeedClickLogsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(newsFeedClickLogsService, loadingService, toastService);
    }
}
