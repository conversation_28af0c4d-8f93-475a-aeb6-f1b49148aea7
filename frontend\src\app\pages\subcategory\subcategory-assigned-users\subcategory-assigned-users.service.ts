import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseService } from "src/app/config/base.service";
import { FilterParam } from "src/app/models/filterparam";
import { RestResponse } from "src/app/shared/auth.model";

@Injectable({
    providedIn: 'root'
})

export class SubCategoryAssignedUsersService extends BaseService {

    subCatgeoryId: string;
    type : string;

    constructor(public http: HttpClient) {
        super(http, '', '');
    }

    getCategoryId(id: string) {
        this.subCatgeoryId = id;
    }

    setType(type: string) {
        this.type = type;
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/subcategory/' + this.subCatgeoryId + (this.type == 'USER' ? '/users' : '/farms'), filterParam);
    }

    unassignSubCategoryUsers(data: any, type: string): Promise<RestResponse> {
        return this.updateRecord('/api/unassign/subcategory/users/' + type, data);
    }

    unassignSubCategoryFarms(data: any, type: string): Promise<RestResponse> {
        return this.updateRecord('/api/unassign/subcategory/farms/' + type, data);
    }
}