<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-4 text-start">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="Search" appDelayedInput
            (delayedInput)="search($event)" [delayTime]="1000">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div class="col-12 col-sm-4">
      </div>
      <div class="col-12 col-sm-4 text-end">
        <button type="button" class="btn add-button btn-primary btn-lg" (click)="onNewRecord()">
          <img src="/assets/images/icons/menu/add_icon.svg" class="me-3 width-15px" alt="">Add {{'Farm.objName' |
          translate}}
        </button>
      </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th width="170">{{'Farm.name' | translate}}</th>
            <th width="170">{{'Farm.location' | translate}}</th>
            <th width="170">{{'Farm.identifier' | translate}}</th>
            <th width="170" class="text-center">{{'Farm.staffCount' | translate}}</th>
            <th width="170" class="text-center">{{'Farm.farmAdminCount' | translate}}</th>
            <th width="170" class="text-end">{{'COMMON.ACTION' | translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;">
            <td>{{record.name}}</td>
            <td>{{record.country}}</td>
            <td>{{record.farmCode}} - {{record.name}}</td>
            <td class="text-center">
              <ng-container *ngIf="record.farmStaffCount > 0; else noUsers">
                <a [routerLink]="['/dashboard/users']" [queryParams]="{ farmId: record.id }"
                  class="badge bg-primary text-white admin-count-badge farm-badge" title="View Farm Users">
                  {{ record.farmStaffCount }}
                </a>
              </ng-container>
              <ng-template #noUsers>
                <span class="badge bg-secondary farm-badge">
                  0
                </span>
              </ng-template>
            </td>
            <td class="text-center">
              <ng-container>
                <!-- Active Admins -->
                <a [routerLink]="['/dashboard/farm/admins']"
                   [queryParams]="{ farmId: record.id, tabView: 'ACTIVE' }"
                   class="badge bg-primary text-white admin-count-badge farm-badge"
                   title="View Active Company Admins">
                  {{ record.activeFarmAdminCount }}
                </a>
                <!-- Inactive Admins -->
                <a [routerLink]="['/dashboard/farm/admins']"
                   [queryParams]="{ farmId: record.id, tabView: 'INACTIVE' }"
                   class="badge bg-secondary text-white admin-count-badge farm-badge"
                   title="View Inactive Company Admins">
                  {{ record.inactiveFarmAdminCount }}
                </a>
                <!-- Invited Admins -->
                <a [routerLink]="['/dashboard/farm/admins']"
                   [queryParams]="{ farmId: record.id, tabView: 'INVITED' }"
                   class="badge bg-info text-white admin-count-badge farm-badge"
                   title="View Invited Company Admins">
                  {{ record.invitedFarmAdminCount }}
                </a>
              </ng-container>
            </td>
            <td class="custom-action-button text-end">
              <div class="custom-action-button text-end mb-2">
                <i *ngIf="authService.isAccessible('FARM','EditButton') && !isDetailPage && !isPlusButton"
                  [class.disabled]="authService.isDisabled('FARM','EditButton')" title="Edit"
                  (click)="editRecord(record.id)" class="bi bi-pencil font-21px me-2 cursor-pointer"></i>
                <i *ngIf="authService.isAccessible('FARM','DeleteButton') && !isPlusButton"
                  [class.disabled]="authService.isDisabled('FARM','DeleteButton')" title="Delete"
                  (click)="remove(record?.id)" class="bi bi-trash font-21px cursor-pointer"></i>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="modal fade" id="myModal" aria-hidden="true" aria-labelledby="myModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">{{recordData?.name}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div *ngIf="myModal&& myModal._isShown" class="modal-body">
            <img *ngIf="recordData?.farmingUrl" [src]="recordData?.farmingUrl" class="img-fluid " />
            <p [ngClass]="{'mt-3': recordData?.farmingUrl }">{{recordData?.description}}</p>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>