import { Injectable } from '@angular/core';
import { HttpServiceRequests } from '../shared/http.service';
import { IResourceWithId, RestResponse } from '../shared/auth.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FilterParam } from '../models/filterparam';

@Injectable({
  providedIn: 'root'
})
export class AccountService extends HttpServiceRequests<IResourceWithId> {

  constructor(public http: HttpClient) {
    super(http);
  }

  fetchMe(): Observable<RestResponse> {
    return this.getRecord('/api/account/my/profile');
  }

  fetchAuthodProfile(id:string): Observable<RestResponse> {
    return this.getRecord('/api/account/user/'+id);
  }

  changePassword(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/changePassword', data);
  }

  forgotPassword(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/forgot/password', data);
  }
  forgotPasswordVerifyOtp(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/forgot-password/verify/otp', data);
  }

  update(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/account/my/profile', data);
  }

  // resendOtp(data: any): Promise<RestResponse> {
  //   return this.saveRecord('/api/account/resend/otp', data);
  // }

  resendEmailOtp(data: any, uniqueCode: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/resend/email/' + uniqueCode, data);
  }

  verifyYourPhone(uniqueCode: any, data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/verify/otp/' + uniqueCode, data);
  }
  contactUs(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/contact/', data);
  }
  staffRegister(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/user/register', data);
  }
  programAdminRegister(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/program/administrator/register', data);
  }

  staffOrAdminChangePassword(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/changePassword', data);
  }

  staffRequestSiteChange(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/requestSiteChange', data);
  }

  getRegisterPhoneNumber(uniqueCode: any): Observable<RestResponse> {
    return this.getRecord('/api/account/get-register-phone-number/' + uniqueCode);
  }

  confirmRegisterEmail(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/account/confirm/email', data);
  }

  updatePhoneNumber(uniqueCode: any, data: any): Promise<RestResponse> {
    return this.updateRecord('/api/account/wrongPhoneNumber/' + uniqueCode, data);
  }

  //active user program admins
  fetchActiveUsers(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/account/active/programadmins', filterParam);
  }

  //for content transfer
  saveContentTransfer(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/contenttransfer', data);
  }

  //profile get by url data 
  getUserProfileDetail(id: any): Observable<RestResponse> {
    return this.getRecord('/api/contenttransfer/guid/' + id);
  }

}
