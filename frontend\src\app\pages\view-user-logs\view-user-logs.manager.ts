import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ViewUserLogsService } from './view-user-logs.service';

@Injectable({
    providedIn: 'root'
})
export class ViewUserLogsManager extends BaseManager {

    constructor(protected viewUserLogsService: ViewUserLogsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(viewUserLogsService, loadingService, toastService);
    }
}
