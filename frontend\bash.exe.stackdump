Stack trace:
Frame         Function      Args
0007FFFF78F0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF67F0) msys-2.0.dll+0x1FEBA
0007FFFF78F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7BC8) msys-2.0.dll+0x67F9
0007FFFF78F0  000210046832 (000210285FF9, 0007FFFF77A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF78F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF78F0  0002100690B4 (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF7BD0  00021006A49D (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9D0640000 ntdll.dll
7FF9CF960000 KERNEL32.DLL
7FF9CDEB0000 KERNELBASE.dll
7FF9CFC30000 USER32.dll
7FF9CDE80000 win32u.dll
7FF9CF680000 GDI32.dll
7FF9CE2A0000 gdi32full.dll
7FF9CDDD0000 msvcp_win.dll
7FF9CD9A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9D0210000 advapi32.dll
7FF9CF230000 msvcrt.dll
7FF9D0350000 sechost.dll
7FF9CFFF0000 RPCRT4.dll
7FF9CCD90000 CRYPTBASE.DLL
7FF9CDD30000 bcryptPrimitives.dll
7FF9CE460000 IMM32.DLL
