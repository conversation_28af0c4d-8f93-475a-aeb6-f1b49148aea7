import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { CategoryService } from './category.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class CategoryManager extends BaseManager {

    constructor(private categoryService: CategoryService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(categoryService, loadingService, toastService);
    }
}
