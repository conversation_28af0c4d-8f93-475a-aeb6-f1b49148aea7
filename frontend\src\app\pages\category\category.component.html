<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
	<div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
		<div class="row">
			<div class="col-12 col-sm-4 text-start">
				<div class="custom-input-group">
					<input class="form-control search-form-control" placeholder="" appDelayedInput
						(delayedInput)="search($event)" [delayTime]="1000">
					<i class="bi bi-search pe-3"></i>
				</div>
			</div>
			<div class="col-12 col-sm-4">
			</div>
			<div class="col-12 col-sm-4 text-end">
				<button type="button" class="btn add-button btn-primary btn-lg" (click)="onNewRecord()"
					*ngIf="authService.isAccessible('CATEGORY','AddButton')">
					<img src="/assets/images/icons/menu/add_icon.svg" class="me-3" alt="">Add Program
				</button>
			</div>
		</div>
		<div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
			<table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
				<thead>
					<tr>
						<th width="160">{{'Category.titleOfList' | translate}}</th>
						<th width="">{{'Category.description' | translate}}</th>
						<th width="400"></th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let record of records;">
						<td>{{record.title}}</td>
						<td>
							{{record.description}}
						</td>
						<td class="text-center custom-action-button text-right">
							<div class="d-flex justify-content-end mb-3">
								<button title="Assign Program" class="btn btn-secondary text-light action-button"
									*ngIf="authService.isAccessible('CATEGORY','AssignButton')" (click)="openAssignProgramModal(record)">
									ASSIGN PROGRAM
								</button>
								<button (click)="unAssignFarmOrUser(record)" type="button"
									class="btn btn-secondary icon-button" *ngIf="authService.isAccessible('CATEGORY','ShowAssignUsersButton')">
									<img src="/assets/icons/employees.svg" alt="">
								</button>
								<button
									*ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
									[class.disabled]="authService.isDisabled('CATEGORY','EditButton')" title="Edit"
									class="btn btn-outline-light action-button" (click)="editRecord(record.id)">
									<i class="bi bi-pencil"></i> EDIT
								</button>
								<button *ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
									[class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')" title="Delete"
									class="btn btn-primary action-button" (click)="remove(record.id)">
									<i class="bi bi-trash"></i> DELETE
								</button>
							</div>
						</td>
					</tr>
					<!-- <tr *ngIf="records.length===0">
						<td class="text-center" colspan="5">
							{{"COMMON.NORECORDS" | translate}}
						</td>
					</tr> -->
				</tbody>
			</table>
		</div>
	</div>
</div>
<!--
<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Category Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Category.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('CATEGORY','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('CATEGORY','AddButton')">
            <span class="hidden-xs">{{'Category.ADD_NEW_CATEGORY' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Category.title' | translate}}</th>
					    		<th>{{'Category.description' | translate}}</th>
					    		<th>{{'Language.objName' | translate}} {{'Language.name' | translate}}</th>
					    		<th>{{'Category.commonTitle' | translate}}</th>
					    		<th>{{'Category.groupCode' | translate}}</th>
					    		<th>{{'Category.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        			<td>{{record.title}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
				                    		<a *ngIf="record.languageIdDetail" class="primary-color" [routerLink]="['/dashboard/language/detail/'+record.languageIdDetail.id]">
				                       		{{record.languageIdDetail.name}}

						        		</a>
					        		</td>
					        			<td>{{record.commonTitle}}</td>
					        			<td>{{record.groupCode}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('CATEGORY','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('CATEGORY','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('CATEGORY','EditButton')"
                           [routerLink]="['/dashboard/category/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>
            </table>
    </div>
</div>

<app-category-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-category-edit>

<div class="modal fade site-detail-modal right" id="categoryDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="categoryDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-category-detail [recordId]="selectedId"></app-category-detail>
            </div>
        </div>
    </div>
</div>
 -->

<div class="modal fade" id="assignProgramModal" tabindex="-1">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="assignProgramModalLabel">Assign Program</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div *ngIf="assignProgramModal && assignProgramModal._isShown" class="modal-body">
				<form #assignProgramForm="ngForm" novalidate="novalidate">
					<div class="mb-3">
						<ng-multiselect-dropdown required="required" placeholder="Select Sites"
							[settings]="dropdownSettings" [data]="farms"
							class="multi-select-dropdown-cls form-control padding-bottom-8" name="sites"
							[(ngModel)]="selectedFarms" #farmIds="ngModel" (onSelect)="onItemSelectForFarm($event)"
							(onDeSelect)="onDeSelectForFarm($event)" [disabled]="selectedUsers.length > 0">
						</ng-multiselect-dropdown>
					</div>
					<div class="mb-3">
						<ng-multiselect-dropdown required="required" placeholder="Select Users"
							[settings]="dropdownSettingsUsers" [data]="users"
							class="multi-select-dropdown-cls form-control padding-bottom-8" name="users"
							[(ngModel)]="selectedUsers" #userIds="ngModel" (onSelect)="onItemSelectForUser($event)"
							(onDeSelect)="onDeSelectForUser($event)" [disabled]="selectedFarms.length > 0">
						</ng-multiselect-dropdown>
					</div>
					<div class="mb-3">
						<app-validation-message [optionalCustomErrorMessage]="optionalValidationMessage"
							[field]="farmIds" [field1]="userIds" [onClickValidation]="onClickValidation">
						</app-validation-message>
					</div>
					<div class="modal-footer">
						<button type="button" class="text-white btn btn-secondary" data-bs-dismiss="modal"
							aria-label="Close">Close</button>
						<button [disabled]="sendingRequestForAssignProgram"
							(click)="onClickAssignProgramorm(assignProgramForm.form.valid)" type="button"
							class="btn btn-primary">{{sendingRequestForAssignProgram ? 'Please Wait...' :
							'Assign'}}</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>