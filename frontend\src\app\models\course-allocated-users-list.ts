import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Language } from './language';
import { Course } from './course';
export class CourseAllocatedUsersList extends BaseModel {

	id: string;
	isDeleted: boolean;
	isActive: boolean;
    courseDetail : Course;
    createdOn: Date;

	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
        this.courseDetail = new Course();
	}

	static fromResponse(data: any): CourseAllocatedUsersList {
		const obj = new CourseAllocatedUsersList();
		obj.id = data.id;
        obj.createdOn = data.createdOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
        obj.courseDetail = data.courseDetail;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
	}

	forRequest() {
		
	}
}
