import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import * as moment from 'moment';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AssignedSiteMomentsTitle implements Resolve<any> {
  staffAssignedSiteMomentsPageTitle: string;
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    if (route.queryParams.n) {
      this.staffAssignedSiteMomentsPageTitle = route.queryParams.n + ' (Moments)';
    } else {
      this.staffAssignedSiteMomentsPageTitle = 'Assigned Site Moments'
    }
    return of(
      this.staffAssignedSiteMomentsPageTitle
    )
  }
}
