import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { Farm } from '../../../models/farm';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { FarmManager } from '../farm.manager';
import { RestResponse } from 'src/app/shared/auth.model';
import { FarmService } from '../farm.service';
import { Address } from '../../../models/address';

declare const $: any;

@Component({
  selector: 'app-farm-edit',
  templateUrl: './farm-edit.component.html',
  styleUrls: ['./farm-edit.component.scss']
})

export class FarmEditComponent extends BaseEditComponent implements OnInit {
  public farm: Farm;
  public uploader: any;
  @ViewChild('FarmCode') FarmCode: any;
  isLoader: boolean = false;
  fileUploadingMessage: string = 'UPLOADING..';
  fileData: any;

  googleOptions = {
    types: ['address'],
    // componentRestrictions: { country: 'IN' } // Optional: restrict to a country
  };

  constructor(
    protected route: ActivatedRoute,
    protected farmManager: FarmManager,
    protected farmService: FarmService,
    protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected router: Router,
    protected commonService: CommonService,
    public authService: AuthService,
    protected translateService: TranslateService,
    public commonUtil: CommonUtil
  ) {
    super(farmManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.farm = new Farm();
    this.farm.isActive = true;
    this.farm.addressDetail = new Address();
    this.setRecord(this.farm);
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.init();
  }

  onFetchCompleted() {
    this.farm = Farm.fromResponse(this.record);
    this.setRecord(this.farm);
  }

  uploadFarmImage(event: any) {
    if (event) {
      this.isLoader = true;
      const file = event.target.files[0];
      if (file) {
        this.fileData = {} as any;
        this.fileData.files = event.target.files;
        this.onFileProcessingCompleted(this.fileData.files);
      }
    }
  }

  onFileProcessingCompleted(files: any) {
    this.uploader.addToQueue(files);
    this.uploader.uploadAll();
  }

  async onUploadSuccess(file: any, files: any): Promise<void> {
    this.isLoader = false;
    this.farm.logoUrl = file.path;
  }

  fileValidationError(data: string, toastService: any) {
    this.isLoader = false;
    toastService.error(data);
  }

  removeFile() {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), null);
  }

  removeFileCallback() {
    this.farm.logoUrl = "";
  }
  fileUploadValidationBeforeSave() {
    return true;
  }

  afterFetchAssociatedCompleted() {
  }

  onSaveSuccess(data: any) {
    this.navigate('/dashboard/farms');
  }

  async save(form: any) {
    this.onClickValidation = !form.valid;

    // If logo is missing, trigger the same visual validation as other fields
    if (!this.farm.logoUrl) {
      this.onClickValidation = true;
      return;
    }

    if (!form.valid) {
      return;
    }

    // Combine addressDetail fields into a single address string
    if (this.farm.addressDetail && this.farm.addressDetail instanceof Address) {
      // Only overwrite farm.address if addressDetail was filled by autocomplete
      const ad = this.farm.addressDetail;
      const structuredAddress = [ad.houseNo, ad.street, ad.city, ad.state, ad.country, ad.pincode]
        .filter(Boolean)
        .join(', ');
    }

    if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
      return;
    }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.farmManager[method](this.farm.forRequest());
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.data);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async verifyFarmCode(event: Event) {
    const farmCode = this.farm?.farmCode;
    if (!farmCode || farmCode.trim() === "") {
      return;
    }
    try {
      const response: RestResponse = await this.farmService.verifyFarmCode(farmCode).toPromise();
      if (response.data) {
        console.log(response.data, "response.data");
        if (this.FarmCode && this.FarmCode.control) {
          this.FarmCode.control.setErrors({ exists: true });
        }
        return;
      } else {
        if (this.FarmCode && this.FarmCode.control) {
          this.FarmCode.control.setErrors(null);
        }
      }
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  handleAddressChange(address: any) {
    // this.farm.address = address.formatted_address;
    const components = address.address_components || [];
    const getComponent = (types: string[]) => {
      const comp = components.find((c: any) => types.every(t => c.types.includes(t)));
      return comp ? comp.long_name : '';
    };
    this.farm.addressDetail.houseNo = getComponent(['street_number']);
    this.farm.addressDetail.street = getComponent(['route']) || address.formatted_address || this.farm.addressDetail.street;
    this.farm.addressDetail.city = getComponent(['locality']) || getComponent(['administrative_area_level_2']);
    this.farm.addressDetail.state = getComponent(['administrative_area_level_1']);
    this.farm.addressDetail.country = getComponent(['country']);
    this.farm.addressDetail.pincode = getComponent(['postal_code']);
    // Optionally, set lat/lng if needed
    if (address.geometry && address.geometry.location) {
      this.farm.addressDetail.latitude = address.geometry.location.lat();
      this.farm.addressDetail.longitude = address.geometry.location.lng();
    }

    console.log('Updated farm address detail:', this.farm.addressDetail);
  }
}
