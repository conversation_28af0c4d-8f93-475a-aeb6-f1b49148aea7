<div data-aos="fade-up" data-aos-duration="1000">
  <div class="row">
    <div class="col-12 col-sm-4 text-start">
    </div>
    <div class="col-12 col-sm-3">
    </div>
    <div class="col-12 col-sm-5 d-flex align-items-center justify-content-end">
      <button (click)="openFilterTrainingModal()" type="button"
        class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg padding-10">
        <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-22px" alt="">Filter
      </button>
    </div>
  </div>
  <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
    <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
      <thead>
        <tr>
          <th width="100">Date</th>
          <th>{{'SubCategory.program' | translate}}</th>
          <th>{{'SubCategory.course' | translate}}</th>
          <th>{{'Training.videoTitle' | translate}}</th>
          <!-- <th>{{ trainingTabs == 'approved-training' || trainingTabs == 'rejected-training' ? 'Approved' :
							'Approve/Reject'}}</th> -->
          <th width="100">Language</th>
          <th width="120">Approved</th>
          <th width="150">User Video</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let record of records;">
          <td>{{moment(record.createdOn).format('DD-MM-YYYY')}}</td>
          <td>
            <div *ngFor="let category of record.trainingCategoryMappingList;">{{category.categoryCommmonTitle}}</div>
          </td>
          <td><div *ngFor="let subCategory of record.trainingSubCategoryMappingList;">{{subCategory.subCategoryCommmonTitle}}</div></td>
          <td>
            {{record?.traningIdDetail?.commonVideoTitle}}
          </td>
          <td width="120">{{record?.languageIdDetail?.name}}</td>
          <td>
            <i [ngClass]="{'bi-x-circle text-danger': record.status == 'REJECTED', 'bi-check-circle text-success': record.status == 'APPROVED'  }"
              class="bi fs-3"></i>
          </td>
          <td>
            <button (click)="watchVideo(record)" class="user-video-button bg-secondary">
              <img src="/assets/images/icons/menu/watch-video.svg" class="px-2">WATCH VIDEO</button>
          </td>
          <!-- <td class="text-center custom-action-button text-right">
							<div class="d-flex justify-content-end mb-3">
								<button *ngIf="authService.isAccessible('FARM','DeleteButton') && !isPlusButton"
									[class.disabled]="authService.isDisabled('FARM','DeleteButton')" title="Delete"
									class="btn btn-secondary text-light action-button" (click)="remove(record.id)">
									RE-ASSIGN STAFF
								</button>
								<button [routerLink]="['/dashboard/assigned-users/'+record.id]" type="button"
									class="btn btn-secondary icon-button">
									<img src="/assets/icons/employees.svg" alt="">
								</button>
								<button
									*ngIf="authService.isAccessible('FARM','EditButton') && !isDetailPage && !isPlusButton"
									[class.disabled]="authService.isDisabled('FARM','EditButton')" title="Edit"
									class="btn btn-outline-light action-button" (click)="editRecord(record.id)">
									<i class="bi bi-pencil"></i> EDIT
								</button>
								<button *ngIf="authService.isAccessible('FARM','DeleteButton') && !isPlusButton"
									[class.disabled]="authService.isDisabled('FARM','DeleteButton')" title="Delete"
									class="btn btn-primary action-button" (click)="remove(record.id)">
									<i class="bi bi-trash"></i> DELETE
								</button>
							</div>
						</td> -->
        </tr>
        <!-- <tr *ngIf="records.length===0">
        <td class="text-center" colspan="5">
          {{"COMMON.NORECORDS" | translate}}
        </td>
      </tr> -->
      </tbody>
    </table>
  </div>
</div>
<div class="modal fade" id="watchVideoModalApprovedRejected" aria-hidden="true"
  aria-labelledby="watchVideoModalApprovedRejected" tabindex="-1">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="watchVideoModalApprovedRejectedLabel">
          {{recordData?.trainingIdDetail?.commonVideoTitle}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="loadingVideo" class="loading-container-video-training">
          <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
          <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        <video autoplay playsinline [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
          id="staff-video"></video>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="filterApprovedOrRejectedTrainingModal" tabindex="-1"
  aria-labelledby="filterApprovedOrRejectedTrainingModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="filterApprovedOrRejectedTrainingModalLabel">Filter Assigned Training</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div *ngIf="filterApprovedOrRejectedTrainingModal && filterApprovedOrRejectedTrainingModal._isShown"
        class="modal-body">
        <form #trainingFeedFilterForm="ngForm" novalidate="novalidate">
          <mat-form-field class="example-form-field w-100">
            <mat-label>Search Video Title...</mat-label>
            <input [(ngModel)]="filterParam.SearchCommonTitle" name="searchCommonTitle" matInput type="text">
            <button mat-button *ngIf="filterParam.SearchCommonTitle" matSuffix mat-icon-button aria-label="Clear"
              (click)="filterParam.SearchCommonTitle=''">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <mat-form-field class="example-form-field w-100">
            <mat-label>Search Program...</mat-label>
            <input [(ngModel)]="filterParam.categorySearch" name="searchCategory" matInput type="text">
            <button mat-button *ngIf="filterParam.categorySearch" matSuffix mat-icon-button aria-label="Clear"
              (click)="filterParam.categorySearch=''">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <mat-form-field class="example-form-field w-100">
            <mat-label>Search Course...</mat-label>
            <input [(ngModel)]="filterParam.subCategorySearch" name="searchSubCategory" matInput type="text">
            <button mat-button *ngIf="filterParam.subCategorySearch" matSuffix mat-icon-button aria-label="Clear"
              (click)="filterParam.subCategorySearch=''">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
          <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="fromDate" [toDateInput]="toDate"></app-date-range-filter>
          <div class="modal-footer">
            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
            <button (click)="onClickTrainingFilter(trainingFeedFilterForm.form.valid)" type="button"
              class="btn btn-primary">Filter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
