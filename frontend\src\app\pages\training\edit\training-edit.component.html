<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
	<div class="dashboard-content-container d-block text-center">
		<form #trainingForm="ngForm" novalidate="novalidate" class="text-left d-inline-block custom-form">
			<div class="row">
				<div class="mt-2 mb-3">
					<h4 class="fw-bold">{{request.recordId == 0 ? "Add New Training" : "Edit Training"}}</h4>
					<p class="user-edit-msg">Please make sure you fill all the fields before you click on
						{{request.recordId ==
						0 ? 'save' : 'update'}} button
					</p>
				</div>
			</div>
			<div class="col-12 col-md-12">
				<div class="form-floating mb-4 w-100 category-language">
					<input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
						class="form-control" type="text" name="title" #title="ngModel" [(ngModel)]="training.title"
						required="required" placeholder="{{'Training.titles' | translate}}">
					<label for="floatingInput">{{"Training.titles" | translate}}</label>
				</div>
			</div>
			<div class="col-12 col-md-12">
				<div class=" mb-4 w-100 category-language">
					<label class="color-dark-grey p-2" for="trainingLibraryDetail">{{"Course.description" |
						translate}}</label>
					<ckeditor [editor]="Editor" id="trainingLibraryDetail" name="trainingLibraryDetail"
						#trainingLibraryDetail="ngModel" [(ngModel)]="training.description" [config]="editorConfig"
						required>
					</ckeditor>
					<app-validation-message [field]="trainingLibraryDetail" [onClickValidation]="onClickValidation">
					</app-validation-message>
				</div>
			</div>
			<div class="col-12 col-md-12 col-lg-12 col-xl-12">
				<div class="form-floating">
					<div class="mb-4 mt-2 form-control select-width ng-select-main-container"
						[ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
						<ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
							required="required" [items]="learningSeries" bindLabel="title" bindValue="id"
							(change)="selectLearningSeries($event)"
							class="custom-multiselect form-control padding-bottom-8"
							[(ngModel)]="training.learningSeries" #learningSeriesId="ngModel" [searchable]="false">
						</ng-select>
					</div>
					<label for="language">{{"Training.learningSeries" | translate}}</label>
				</div>
			</div>
			<div class="col-12 col-md-12 col-lg-12 col-xl-12">
				<div class="form-floating">
					<div class="mb-4 mt-2 form-control select-width ng-select-main-container"
						[ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
						<ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
							[items]="contentTypes" bindLabel="title" bindValue="id" (change)="selectContentType($event)"
							required="required" class="custom-multiselect form-control padding-bottom-8"
							[(ngModel)]="training.contentType" #contentTypeId="ngModel" [searchable]="false">
						</ng-select>
					</div>
					<label for="language">{{"Training.contentTypes" | translate}}</label>
				</div>
			</div>
			<div class="row">
				<!-- <div class="col-12 col-md-6 ps-0">
					<div class="form-floating mb-4 w-100 category-language">
						<input maxlength="80" [ngClass]="{'is-invalid':!videoUrlTitle.valid && onClickValidation}"
							class="form-control" type="text" name="videoUrlTitle" #videoUrlTitle="ngModel"
							[(ngModel)]="training.videoUrlTitle" required="required"
							placeholder="{{'Training.videoUrlTitle' | translate}}">
						<label for="floatingInput">{{"Training.videoUrlTitle" | translate}}</label>
					</div>
				</div>
				<div class="col-12 col-md-6 pe-0">
					<div class="form-floating mb-4 w-100 category-language">
						<input maxlength="80"
							[ngClass]="{'is-invalid':!thumbnailImageUrlTitle.valid && onClickValidation}"
							class="form-control" type="text" name="thumbnailImageUrlTitle"
							#thumbnailImageUrlTitle="ngModel" [(ngModel)]="training.thumbnailImageUrlTitle"
							required="required" placeholder="{{'Training.thumbnailImageUrlTitle' | translate}}">
						<label for="floatingInput">{{"Training.thumbnailImageUrlTitle" | translate}}</label>
					</div>
				</div> -->
				<div class="col-12 col-md-6 ps-0">
					<div class="video-title">
						<div *ngIf="!training.url"
							class="upload-video-container mb-4 form-control text-light bg-secondary p-3"
							[ngClass]="{'is-invalid': !training.url && onClickValidation}">
							<label *ngIf="!training.url"
								[ngClass]="{'d-flex align-items-center justify-content-center': !training.url, 'cursor-default': loading}"
								class="fw-bold" for="file-input">
								<img src="/assets/images/icons/menu/upload-video-icon.svg"
									class="img-fluid me-2 mb-2 width-28-px text-center" alt="" />
								{{loading ?
								fileUploadingMessage : 'UPLOAD VIDEO HERE' }}
								<div *ngIf="loading" class="spinner-border ms-2" role="status"
									style="width: 1.5rem; height: 1.5rem">
									<span class="visually-hidden">Loading...</span>
								</div>
							</label>
							<input *ngIf="!training.url && !loading" name="trainingVideo" (change)="uploadVideo($event)"
								id="file-input" type="file" accept="video/*" />
						</div>
						<div *ngIf="training.url" class="video-wrapper mb-4"
							[ngStyle]="{'display': training.url ? 'block' : 'none' }">
							<div class="video-container" id="video-container">
								<div class="play-button-wrapper">
									<div (click)="playVideoFromPlayIcon()" title="Play video"
										class="play-gif circle-play-b-cls" id="circle-play-b">
										<!-- SVG Play Button -->
										<svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg"
											viewBox="0 0 80 80">
											<path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
										</svg>
									</div>
								</div>
								<div class="position-absolute delete-video-container"
									(click)="removeFile(training.url)">
									<i class="bi bi-x"></i>
								</div>
								<video playsinline class="mw-100" [src]="training.url" id="training_video"
									controlslist="nodownload">
									Your browser does not support HTML video.
								</video>
							</div>
						</div>
					</div>
				</div>
				<div class="col-12 col-md-6 pe-0">
					<div *ngIf="!training.thumbnailImageUrl"
						class="mb-4  form-control text-center border-2 border-dark upload-img-container"
						[ngClass]="{'is-invalid': !training.thumbnailImageUrl && onClickValidation}">
						<label id="file-input-thumbnail" class="upload-img-button cursor-pointer mt-1"
							[ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingThumbnail}"><img
								src="/assets/images/icons/menu/image-thumbnail.svg"
								class="me-2 mb-2 width-28-px upload-icon text-center" alt="">
							<br>
							{{!uploadingThumbnail ? ('UPLOAD VIDEO THUMBNAIL' | translate) : ('UPLOADING THUMBNAIL..' |
							translate) }}
							<div *ngIf="uploadingThumbnail" class="spinner-border ms-2" role="status"
								style="width: 1.5rem; height: 1.5rem; padding:10px;">
								<span class="visually-hidden">Loading...</span>
							</div>
							<input *ngIf="!uploadingThumbnail" name="thumbnailUrl" class="d-none"
								(change)="uploadThumbnail($event)" id="file-input-thumbnail" type="file"
								accept="image/png, image/jpg, image/jpeg" />
						</label>
					</div>
					<div *ngIf="training.thumbnailImageUrl" class="moment-image-container mb-4 max-width-none">
						<img [src]="training.thumbnailImageUrl" />
						<div class="position-absolute delete-video-container"
							(click)="removeFile(training.thumbnailImageUrl)">
							<i class="bi bi-x"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-12 col-md-12 clearfix">
				<div class="form-check">
					<input class="form-check-input custom-form-check ms-0" type="checkbox" name="isTermsConditions"
						[checked]="training.isTermsConditions"
						[ngClass]="{'is-invalid':!isTermsConditions.valid && onClickValidation}" id="flexCheckDefault"
						[(ngModel)]="training.isTermsConditions" #isTermsConditions="ngModel" required="required">
					<label class="form-check-label ms-2 mt-1" for="flexCheckDefault">
						I have read and accept the
						<a href="https://integrax.co/terms-and-conditions" target="_blank">
							Terms & Conditions
						</a>
						for uploading this video to <span class="fw-bold">Integrax</span>
					</label>
				</div>
			</div>
			<div class="col-12 col-md-12">
				<div class="d-flex justify-content-start align-items-center">
					<span class="me-2 text-start">
						<label for="flexSwitchCheckChecked" class="form-label">Private</label>
					</span>
					<div class="form-check form-switch d-inline-block">
						<input (change)="selectAccessibility($event)"
							class="form-check-input toggle-width custom-switch" type="checkbox"
							id="flexSwitchCheckChecked" [checked]="training.accessibility === 'Public'">
					</div>
					<span class="ms-2">
						<label for="flexSwitchCheckChecked" class="form-label">Public</label>
					</span>
				</div>
			</div>
			<div class="col-12 mt-3">
				<div class="d-flex justify-content-between">
					<div>
						<button class="btn btn-danger site-button btn-sm large-button save-button rounded-3"
							[routerLink]="['/dashboard/training']">
							{{ 'Cancel' }}
						</button>
					</div>
					<div class="d-flex">
						<button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3 me-2"
							type="button" (click)="saveTrainingsForm(trainingForm.form,!training.isPublish)">
							{{!training.isPublish ? 'publish':'Unpublish'}}
						</button>
						<button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
							type="button" (click)="saveTrainingsForm(trainingForm.form)"
							*ngIf="authService.isAccessible('CATEGORY','AddButton')"
							[disabled]="authService.isDisabled('CATEGORY','AddButton')">
							{{request.recordId ==
							0 ? 'SAVE As Draft' : 'UPDATE'}}
						</button>
					</div>
				</div>
			</div>
		</form>
	</div>
</div>

<!--
<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Training Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/training']">{{'Training.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'Training.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{training.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div> -->
<!-- <div class="clearfix"></div>
<div class="site-page-container">
	<div class="site-card">
		<form #trainingForm="ngForm" novalidate="novalidate">
			<div class="row justify-content-start">
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.categoryId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="categories" bindLabel="title" bindValue="id" name="trainingCategoryId"
								#trainingCategoryId="ngModel" [(ngModel)]="training.categoryId" required="required"
								#CategoryId="ngModel"
								[ngClass]="{'invalid-field':trainingCategoryId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} category">
							</ng-select> -->
<!-- <span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('subCategoryCategoryIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span> -->
<!-- <div class="form-floating">
								<div class="mb-4 form-control select-width ng-select-main-container"
									[ngClass]="{'is-invalid':trainingCategoryId.invalid && onClickValidation}">
									<ng-select [items]="categories" bindLabel="title" bindValue="id"
										name="trainingCategoryId" #trainingCategoryId="ngModel"
										[(ngModel)]="training.categoryId" #CategoryId="ngModel" required="required"
										placeholder="{{'COMMON.SELECT_OPTION' | translate}}">
									</ng-select>
								</div>
							</div>

						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.subCategoryId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="subCategories" bindLabel="title" bindValue="id"
								name="trainingSubCategoryId" #trainingSubCategoryId="ngModel"
								[(ngModel)]="training.subCategoryId" required="required" #SubCategoryId="ngModel"
								[ngClass]="{'invalid-field':trainingSubCategoryId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} subCategory">
							</ng-select>
							<span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('trainingSubCategoryIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.languageId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="languages" bindLabel="name" bindValue="id" name="trainingLanguageId"
								#trainingLanguageId="ngModel" [(ngModel)]="training.languageId" required="required"
								#LanguageId="ngModel"
								[ngClass]="{'invalid-field':trainingLanguageId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} language">
							</ng-select>
							<span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button"
									(click)="loadAssociatedPopup('trainingLanguageIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.videoTitle" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255"
								name="trainingVideoTitle" [(ngModel)]="training.videoTitle" #VideoTitle="ngModel">
						</div>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.url" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="MAX"
								name="trainingurl" [(ngModel)]="training.url" #url="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.publishedForTrainingFeed" | translate}}
						</label>
						<div class="material-switch">
							<input id="trainingPublishedForTrainingFeedId" name="trainingPublishedForTrainingFeed"
								type="checkbox" [(ngModel)]="training.publishedForTrainingFeed" />
							<label for="trainingPublishedForTrainingFeedId" class="label-primary"></label>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.commonVideoTitle" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="MAX"
								name="trainingCommonVideoTitle" [(ngModel)]="training.commonVideoTitle"
								#CommonVideoTitle="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Training.groupCode" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255"
								name="trainingGroupCode" [(ngModel)]="training.groupCode" #GroupCode="ngModel">
						</div>
					</div>
				</div>
			</div>
		</form>
		<div class="clearfix"></div>
		<div class="col-md-12 no-padding text-right">
			<button title="Save" class="btn btn-primary site-button" type="button" (click)="save(trainingForm.form)"
				*ngIf="authService.isAccessible('TRAINING','AddButton')"
				[disabled]="authService.isDisabled('TRAINING','AddButton')">
				{{"COMMON.SAVE" | translate}}
			</button>
			<button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button"
				(click)="navigate()">
				{{"COMMON.CANCEL" | translate}}
			</button>
			<div class="clearfix"></div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="modal fade nav-scroll" id="trainingCategoryIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-category [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-category>
			</div>
		</div>
	</div>
</div>
<div class="modal fade nav-scroll" id="trainingSubCategoryIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-subcategory [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-subcategory>
			</div>
		</div>
	</div>
</div>
<div class="modal fade nav-scroll" id="trainingLanguageIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-language [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-language>
			</div>
		</div>
	</div>
</div> -->