import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageLearningSeriesService } from './manage-learning-series.service';

@Injectable({
    providedIn: 'root'
})
export class ManageLearningSeriesManager extends BaseManager {

    constructor(protected manageLearningSeriesService: ManageLearningSeriesService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageLearningSeriesService, loadingService, toastService);
    }
}
