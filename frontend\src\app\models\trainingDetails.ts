export class TrainingLibraryDetail {
  id: string;
  title: string;
  description: string='';
  contentType: string;
  contentTypeDetail: ContentTypeDetail;
  accessibility: string;
  url: string;
  thumbnailImageUrl: string;
  isViewed: number;
  sequence: string;
  isTermsConditions: boolean;
  prerequisitesTraining: string;
  totalCount: number;
  isLock:boolean=false;

  constructor() {
    this.id = null;
    this.title = '';
    this.description = '';
    this.contentType = '';
    this.contentTypeDetail = new ContentTypeDetail();
    this.accessibility = 'Private';
    this.url = '';
    this.thumbnailImageUrl = '';
    this.isViewed = 0;
    this.sequence = '';
    this.isTermsConditions = false;
    this.prerequisitesTraining = '';
    this.totalCount = 0;
    this.contentTypeDetail = new ContentTypeDetail();
  }
}

export class ContentTypeDetail {
  id: string;
  title: string;
  totalCount: number;

  constructor() {
    this.id = '';
    this.title = '';
    this.totalCount = 0;

  }
}
