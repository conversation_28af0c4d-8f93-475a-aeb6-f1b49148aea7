<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="m-t-20">
  <div class="dashboard-content-container">
    <form *ngIf="user.id || request.recordId == 0" #recordForm="ngForm" novalidate="novalidate">
      <div class="row site-form-container">
        <div class="col-12 col-md-12 offset-xxl-1 col-xxl-9 d-flex justify-content-between">
          <div>
            <h4 class="fw-bold">{{!userDetails ? request.recordId == 0 ? "Add New User" : "Edit User" : "Manage
              Profile"}}
            </h4>
            <p class="user-edit-msg">Please make sure you fill all the fields before you click on {{request.recordId ==
              0 ? 'save' : 'update'}} button</p>
          </div>
          <div class="profile-image-container position-relative"
            [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
            <img *ngIf="!profileImageLoader" alt=""
              [src]="user.profileImageUrl ? user.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
            <div *ngIf="profileImageLoader" class="spinner-border">
              <span class="visually-hidden">Loading...</span>
            </div>
            <div *ngIf="!profileImageLoader"
              class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
              <label for="file-input">
                <i class="bi bi-pencil-fill"></i>
                <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect [uploader]="uploader"
                  id="file-input" type="file" accept="image/png, image/jpg, image/jpeg" />
              </label>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-3 offset-xxl-1 col-xxl-2 mb-4">
          <div class="form-floating">
            <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text" name="firstName"
              #firstName="ngModel" [(ngModel)]="user.firstName" required="required" placeholder="First Name"
              [ngClass]="{'is-invalid':!firstName.valid && request.onClickValidation}" autocomplete="off">
            <label for="floatingInput">{{"USERS.FirstName" | translate}}</label>
          </div>
          <app-validation-message [field]="firstName" [onClickValidation]="request.onClickValidation">
          </app-validation-message>
        </div>

        <div class="col-12 col-md-3 col-xxl-2 mb-4">
          <div class="form-floating">
            <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text" name="lastname"
              #lastname="ngModel" [(ngModel)]="user.lastName" required="required" placeholder="Last Name"
              [ngClass]="{'is-invalid':!lastname.valid && request.onClickValidation}" autocomplete="off">
            <label for="floatingInput">{{"USERS.LastName" | translate}}</label>
          </div>
          <app-validation-message [field]="lastname" [onClickValidation]="request.onClickValidation">
          </app-validation-message>
        </div>

        <div class="col-12 col-md-6 col-xxl-5 mb-4">
          <div class="form-floating">
            <input autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
              (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
              (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="user.phoneNumber"
              [ngClass]="{'is-invalid':!phone.valid && request.onClickValidation}"
              required="required" autocomplete="off"
              placeholder="Phone Number" minlength="7" maxlength="12" pattern="^[0-9]+$"
              (countryChange)="onCountryChange($event)">
          </div>
          <app-validation-message [field]="phone" [onClickValidation]="request.onClickValidation">
          </app-validation-message>
        </div>

        <div class="col-12 col-md-6 offset-xxl-1 col-xxl-4 mb-4">
          <div class="form-floating">
            <input [ngClass]="{'is-invalid':!email.valid && request.onClickValidation}" class="form-control"
              type="email" name="email" #email="ngModel" [(ngModel)]="user.email" placeholder="Email"
              pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$" required="required" autocomplete="off">
            <label for="floatingInput">{{"USERS.Email" | translate}}</label>
          </div>
          <app-validation-message [field]="email" [onClickValidation]="request.onClickValidation">
          </app-validation-message>
        </div>

        <div class="col-12 col-md-6 col-xxl-5 mb-4">
          <div class="form-floating">
            <div class="mb-3 form-control select-width ng-select-main-container"
            [ngClass]="{'is-invalid':!farmId.valid && request.onClickValidation}">
              <ng-select [items]="farms" bindLabel="name" bindValue="id" name="farmId" [(ngModel)]="user.farmId" #farmId="ngModel"
                (ngModelChange)="onFarmSelect($event)" placeholder="Select {{'Farm.objName' | translate}}"
                [class.is-invalid]="!farmId.valid && request.onClickValidation">
              </ng-select>
            </div>
            <label for="farmId">{{'Farm.objName' | translate}}</label>
          </div>
          <app-validation-message [field]="farmId" [onClickValidation]="onClickValidation"></app-validation-message>
        </div>

        <div class="col-12 col-md-12 offset-xxl-1 col-xxl-9 d-flex justify-content-end">
          <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3" type="button"
            (click)="save(recordForm.form.valid)" [disabled]="request.isRequested">
            {{request.recordId ==
            0 ? 'SAVE' : 'UPDATE'}}
          </button>
        </div>
      </div>
    </form>
    <div class="clearfix"></div>
  </div>
</div>