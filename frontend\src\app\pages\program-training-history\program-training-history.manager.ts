import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramTrainingHistoryService } from './program-training-history.service';

@Injectable({
    providedIn: 'root'
})
export class ProgramTrainingHistoryManager extends BaseManager {

    constructor(protected programTrainingHistoryService: ProgramTrainingHistoryService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(programTrainingHistoryService, loadingService, toastService);
    }
}
