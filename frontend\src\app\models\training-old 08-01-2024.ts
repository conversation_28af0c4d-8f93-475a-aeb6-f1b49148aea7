import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Category } from './category';
import { SubCategory } from './subcategory';
import { Language } from './language';
import { TrainingCategoryMapping } from './trainingcategorymapping';
import { TrainingSubCategoryMapping } from './trainingsubcategorymapping';
export class Training extends BaseModel {

	tenantId: number;
	slug: string;
	categoryIdDetail: Category;
	categoryId: string;
	subCategoryIdDetail: SubCategory;
	subCategoryId: string;
	languageIdDetail: Language;
	languageId: string;
	videoTitle: string;
	description: string;
	videoUrl: string;
	publishedForTrainingFeed: boolean;
	commonVideoTitle: string;
	groupCode: string;
	videoCredit: string;
	assignFarmCount: number;
	trainingCategoryMappingList: TrainingCategoryMapping[];
	selectedCategories: Category[];
	trainingSubCategoryMappingList: TrainingSubCategoryMapping[];
	selectedSubCategories: SubCategory[];
	tempCategoryIds: string[];
	tempSubCategoryIds: string[];

	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
		this.publishedForTrainingFeed = false;
		this.trainingCategoryMappingList = new Array<TrainingCategoryMapping>();
		this.selectedCategories = new Array<Category>();
		this.trainingSubCategoryMappingList = new Array<TrainingSubCategoryMapping>();
		this.selectedSubCategories = new Array<SubCategory>();
		this.tempCategoryIds = new Array<string>();
		this.tempSubCategoryIds = new Array<string>();
	}

	static fromResponse(data: any): Training {
		const obj = new Training();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.categoryIdDetail = data.categoryIdDetail;
		obj.categoryId = data.categoryId;
		obj.subCategoryIdDetail = data.subCategoryIdDetail;
		obj.subCategoryId = data.subCategoryId;
		obj.languageIdDetail = data.languageIdDetail;
		obj.languageId = data.languageId;
		obj.videoTitle = data.videoTitle;
		obj.description = data.description;
		obj.videoUrl = data.videoUrl;
		obj.publishedForTrainingFeed = data.publishedForTrainingFeed;
		obj.commonVideoTitle = data.commonVideoTitle;
		obj.groupCode = data.groupCode;
		obj.assignFarmCount = data.assignFarmCount;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		return true;
	}

	forRequest() {
		this.videoTitle = this.trimMe(this.videoTitle);
		this.description = this.trimMe(this.description);
		this.videoUrl = this.trimMe(this.videoUrl);
		this.commonVideoTitle = this.trimMe(this.commonVideoTitle);
		this.groupCode = this.trimMe(this.groupCode);
		return this;
	}
}
