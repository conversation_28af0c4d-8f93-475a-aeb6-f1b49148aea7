import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BaseService } from "src/app/config/base.service";
import { FilterParam } from "src/app/models/filterparam";
import { RestResponse } from "src/app/shared/auth.model";

@Injectable({
    providedIn: 'root'
})

export class CategoryAssignedUsersService extends BaseService {

    catgeoryId: string;
    type : string;

    constructor(public http: HttpClient) {
        super(http, '', '');
    }

    getCategoryId(id: string) {
        this.catgeoryId = id;
    }

    setType(type: string) {
        this.type = type;
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/category/' + this.catgeoryId + (this.type == 'USER' ? '/users' : '/farms'), filterParam);
    }

    unassignCategoryUsers(data: any, type: string): Promise<RestResponse> {
        return this.updateRecord('/api/unassign/category/users/' + type, data);
    }

    unassignCategoryFarms(data: any, type: string): Promise<RestResponse> {
        return this.updateRecord('/api/unassign/category/farms/' + type, data);
    }
}