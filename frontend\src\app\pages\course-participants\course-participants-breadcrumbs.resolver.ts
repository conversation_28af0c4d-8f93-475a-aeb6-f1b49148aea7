import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class CourseParticipantsBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const userId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard", active: false
            },
            {
                title: "Manage Course", link: "/dashboard/program-admin/courses", active: false
            },
            // {
            //     title: "Manage Course", link: "/dashboard/program-admin/courses", active: false
            // },
            // {
            //     title: "Course Participants", active: false
            // }
        ])



    }
}
