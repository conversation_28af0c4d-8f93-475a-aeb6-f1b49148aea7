import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { Constant } from 'src/app/config/constants';
import { Content } from 'src/app/models/content';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { CourseTrainingsManager } from '../course-trainings-manager';
import { ToastService } from 'src/app/shared/toast.service';
import { LoadingService } from 'src/app/services/loading.service';
import { IImage, ImageCompressService, ResizeOptions } from 'ng2-image-compress';
import { CommonService } from 'src/app/shared/common.service';
import { AuthService } from 'src/app/shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CourseTrainingsService } from '../course-trainings-service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { ManageCourseLearningSeriesManager } from '../../manage-course-learning-series/manage-course-learning-series.manager';
import { ManageContentManager } from '../../manage-content-type/manage-content.manager';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { environment } from 'src/environments/environment';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';

declare const $: any;

@Component({
  selector: 'app-course-training-edit',
  templateUrl: './course-training-edit.component.html',
  styleUrls: ['./course-training-edit.component.scss']
})
export class CourseTrainingEditComponent extends BaseEditComponent implements OnInit, OnDestroy {
  trainings: CourseTrainings;
  contentTypes: Content[];
  public learningSeries: any[];
  public loading: boolean = false;
  public videoPlaying: boolean = false;
  readonly MY_CONSTANT = Constant;
  private fileData: any;
  uploadingThumbnail: boolean;
  uploader: any;
  fileUploadingMessage: string = "UPLOADING..";
  intervalId: NodeJS.Timeout;
  dropdownSettings = {};
  deletedUsersSelected: any[] = [];
  thumbnailUploader: FileUploader;
  selectedCurrentType: string;
  fileUploadType: string;
  public Editor = ClassicEditor;
  public editorConfig = {
    toolbar: [
      'heading', '|',
      'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'highlight', '|',
      'link', 'imageUpload', 'blockQuote', 'codeBlock', '|',
      'bulletedList', 'numberedList', 'outdent', 'indent', '|',
      'alignment', 'horizontalLine', 'insertTable', 'mediaEmbed', '|',
      'fontSize', 'fontColor', 'fontBackgroundColor', '|',
      'undo', 'redo', 'removeFormat', 'sourceEditing'
    ],
    height: 'auto',
    shouldNotGroupWhenFull: true
  };

  constructor(protected route: ActivatedRoute, protected courseTrainingsManager: CourseTrainingsManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private imgCompressService: ImageCompressService,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService
    , private manageContentManager: ManageContentManager, private courseTrainingsService: CourseTrainingsService
    , public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private manageCourseLearningSeriesManager: ManageCourseLearningSeriesManager) {
    super(courseTrainingsManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.fileUploadType = "";
    this.setDropdownSettings();
    this.trainings = new CourseTrainings();
    this.trainings.isActive = true;
    this.setRecord(this.trainings);
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.contentTypes = new Array<Content>();
    this.learningSeries = new Array<CourseLearningSeries>();
    this.request.recordId != 0 && this.getLearningSeriesListOnEdit()
    this.onFetchCompleted();
    this.init();
    this.uploader = this.initializeUploaderTraining(null, 'mp4,mkv', null, null, this.toastService, "Only Mkv, Mp4 files are allowed", null);
    this.thumbnailUploader = this.initializeUploaderTraining(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  selectAccessibility(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const isChecked = inputElement.checked;
    // Update the model based on the checkbox state
    this.trainings.accessibility = isChecked ? 'Public' : 'Private';
  }

  async getLearningSeriesListOnEdit() {
    this.learningSeries = await this.manageCourseLearningSeriesManager.fetchAllData(this.filterParam);
  }

  selectLearningSeries(event: any) {
    this.selectedCurrentType = event.title;
  }
  // selectContentType(event: any) {
  //   this.selectedCurrentType = event.title;
  // }

  selectContentType(event: any): void {
    this.trainings.uploadRequired = event.title === 'Apply';
    this.trainings.watchRequired = !this.trainings.uploadRequired;
  }

  onFetchCompleted() {
    this.trainings = CourseTrainings.fromResponse(this.record);
    this.setRecord(this.trainings);
  }

  // // Implement logic when "Apply" is selected
  // // For example, setting a flag to make the upload required
  // handleApplySelected(): void {
  //   this.trainings.isUploadRequired = true;
  //   this.trainings.isWatchRequired = false;
  // }

  // // Implement logic when any other option is selected
  // // For example, setting a flag to make the watch required
  // handleOtherSelected(): void {
  //   this.trainings.isUploadRequired = false;
  //   this.trainings.isWatchRequired = true;
  // }

  // async setlearningSeriesData() {
  //   this.loadingService.show();
  //   try {
  //     const response: RestResponse = await this.courseTrainingsService.getLearningSeries();
  //     if (!response.status) {
  //       this.loadingService.hide();
  //       this.toastService.error(response.message);
  //       return;
  //     }
  //     if (response.status) {
  //       this.learningSeries = response.data.map(data => {
  //         return {
  //           id: data.learningSeriesDetail.id,
  //           title: data.learningSeriesDetail.title,
  //           accessibility: data.learningSeriesDetail.accessibility
  //         }
  //       })
  //       this.loadingService.hide();
  //     }
  //   } catch (error) {
  //     this.loadingService.hide();
  //     this.toastService.error(error.message);
  //   }
  // }


  uploadThumbnail(event: any) {
    this.fileUploadType = 'thumbnailImage';
    // if (event && event.target.files && event.target.files.length > 0) {
    this.uploadingThumbnail = true;
    this.fileData = {} as any;
    this.fileData.files = event.target.files;
    //this.onThumbnailFileProcessingCompleted(this.fileData.files);
    this.compressThumbnail();
    // }
  }

  compressThumbnail() {
    let images: Array<IImage> = [];
    let option: ResizeOptions = { Resize_Quality: 90, Resize_Max_Width: 500, Resize_Max_Height: 400, Resize_Type: 'jpeg' };
    ImageCompressService.filesToCompressedImageSource(this.fileData.files).then(observableImages => {
      observableImages.subscribe((image) => {
        // console.log(image);
        images.push(image);
      }, (error) => {
        // console.log("Error while converting");
      }, () => {
        let compresImages = new Array<any>();
        let obj: any = {};
        obj.base64 = images[0].compressedImage.imageDataUrl;
        obj.type = images[0].type;
        compresImages.push(this.base64ToFile(obj));
        // console.log(compresImages, 'compress images', this.fileData.files, 'file data files', this.training.thumbnailImageUrl, 'url')
        this.onThumbnailFileProcessingCompleted(compresImages);
      });
    });
  }


  base64ToFile(obj: any) {
    const byteCharacters = atob(obj.base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, ''));
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    // console.log(obj.type)
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: obj.type });
    var extention = obj.type.split('/');
    let file = new File([blob], 'cropImage.' + extention[1], { type: obj.type });
    return file;
  }

  onThumbnailFileProcessingCompleted(files) {
    this.thumbnailUploader.addToQueue(files);
    this.thumbnailUploader.uploadAll();
  }

  async uploadVideo(event: any) {
    this.fileUploadType = 'thumbnailVideo';
    const video: any = await this.commonUtil.loadVideo(event.target.files[0])
    this.trainings.duration = video.duration;
    if (this.trainings.mediaType === 'VIDEO') {
      this.loadingService.show();
    }
    this.loading = true;

    const file = event.target.files[0];
    if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg"
      && event.target.files[0].type != "image/png") {
      this.commonService.convertVideoFormat(file).then(res => {
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    }
    else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  async fetchAssociatedData() {
    this.contentTypes = await this.manageContentManager.fetchAllData(this.filterParam);
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/program-admin/trainings');
  }

  async onUploadSuccessTraining(file: any, files: any) {
    this.loadingService.hide();
    const filePath = file.streamingPath ? file.streamingPath : file.path;
    // console.log(this.fileUploadType, file.path);
    switch (this.fileUploadType) {
      case 'thumbnailImage':
        this.trainings.thumbnailImageUrl = file.path;
        this.uploadingThumbnail = false;
        break;
      case 'thumbnailVideo':
        let gumletResponse = null;

        if (file.streamingId) {
          this.filterParam.gumletId = file.streamingId;

          gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

          this.intervalId = setInterval(async () => {
            gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

            if (gumletResponse && gumletResponse.status) {
              // console.log('Status:', gumletResponse.data.status);
              this.fileUploadingMessage = gumletResponse.data.status ? gumletResponse.data.status.toUpperCase() + ".." : "UPLOADING..";

              if (gumletResponse.data.status == "ready" || gumletResponse.data.status == "queued" || gumletResponse.data.status == "downloading" || gumletResponse.data.status == "downloaded" || gumletResponse.data.status == "validating" || gumletResponse.data.status === "errored") {
                // console.log("errored:", gumletResponse.responseMessage);

                if (gumletResponse.data.status == "errored") {
                  // Handle the "errored" status
                  // console.log("errored:", gumletResponse.data.responseMessage);
                  this.toastService.error('Video is errored while processing');
                  this.loading = false; // Set loading to false
                  clearInterval(this.intervalId); // Clear the interval
                  return;
                  // You can add additional handling for the error if needed
                  // You might want to set a flag or handle the error state appropriately
                } else {
                  if (this.intervalId) {
                    clearInterval(this.intervalId);
                  }
                  this.trainings.url = filePath;
                  this.fileUploadingMessage = "UPLOADING..";
                  this.loading = false;
                }
              }
            } else {
              if (this.intervalId) {
                clearInterval(this.intervalId);
              }
              this.trainings.url = file.path;
              this.fileUploadingMessage = "UPLOADING..";
              this.loading = false;
            }
          }, 5000);
        } else {
          this.trainings.url = filePath;
          this.loading = false;
        }
        break;
    }
  }

  fileValidationErrorTraining(data: string, toastService: any) {
    this.loading = false;
    toastService.error(data);
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessTraining(file, files);
        }, 200);
      }
    };

    uploader.onErrorItem = (fileItem, response) => {
      this.loading = false;
      toastService.error('Something error occurred please try again later');
    }

    return uploader;
  }

  playVideoFromPlayIcon() {
    var videoId = document.getElementById("training_video") as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true;
        this.videoPlaying = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      this.videoPlaying = true
      videoId.controls = true;
    });
    videoId.addEventListener("ended", (event) => {
      this.videoPlaying = false
      videoId.controls = false;

    });
  }

  removeFile(fileUrl: string) {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), fileUrl);
  }

  removeFileCallback(fileUrl) {
    if (fileUrl == this.trainings.thumbnailImageUrl) {
      this.trainings.thumbnailImageUrl = null;
    } else {
      this.commonService.deleteVideo(this.trainings.url)
      this.trainings.url = null;
      this.trainings.duration = 0;
      this.videoPlaying = false;
    }
  }

  async saveTrainingsForm(form: any, isPublish: boolean, type: string) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    if (!this.trainings.url) {
      this.onClickValidation = true;
      return
    }
    if (!this.trainings.thumbnailImageUrl) {
      this.onClickValidation = true;
      return
    }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.courseTrainingsManager[method](this.trainings);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if (type === 'Update_Publish') {
        this.trainings.isPublish = isPublish;
      }
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async onFileProcessingCompleted(files: any) {
    this.uploader.addToQueue(files);
    this.uploader.uploadAll();
  }

  // using publish data
  async toPublishAll() {
    // try {
    //   var course = new Course();
    //   course.id = this.selectedCourseId;
    //   course.isPublish = true;

    //   this.loadingService.show();
    //   const response: RestResponse = await this.manageCourseManager.updatePublish(course);
    //   this.loadingService.hide();
    //   if (!response.status) {
    //     this.toastService.error(response.message);
    //     return;
    //   }
    //   this.toastService.success(response.message);
    //   this.navigate('/dashboard/program-admin/courses');
    // } catch (error) {
    //   this.loadingService.hide();
    //   this.toastService.error(error.message);
    // }
  }

}
