import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class FarmAdminEditBreadcrumbs implements Resolve<any> {
  resolve(): Observable<any> {
    return of([
      { title: 'Company Admins', link: '/dashboard/farm/admins' },
      { title: 'Edit Company Admin', active: true }
    ]);
  }
}
