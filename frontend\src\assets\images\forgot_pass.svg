<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="957.636" height="1080.001" viewBox="0 0 957.636 1080.001">
  <defs>
    <linearGradient id="linear-gradient" x1="0.419" y1="1.009" x2="0.442" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1681ff"/>
      <stop offset="1" stop-color="#061428"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.69" y1="0.68" x2="0.613" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#061428"/>
      <stop offset="0.939" stop-color="#0e4a93"/>
      <stop offset="1" stop-color="#0e4d9a"/>
    </linearGradient>
    <filter id="Subtraction_2" x="297.067" y="393.126" width="363.138" height="185.058" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_4662" data-name="Group 4662" transform="translate(-962.364 0)">
    <g id="Group_4661" data-name="Group 4661">
      <path id="Path_53206" data-name="Path 53206" d="M0,0H320V1080H0Z" transform="translate(1600)" fill="url(#linear-gradient)"/>
      <path id="Path_53217" data-name="Path 53217" d="M0,0H320V1080H0Z" transform="translate(1281)" fill="url(#linear-gradient-2)"/>
      <path id="Subtraction_3" data-name="Subtraction 3" d="M473.871-313h-320v-783.174l26.954-23.888a10.138,10.138,0,0,1,6.726-2.527,10.139,10.139,0,0,1,6.727,2.527l42.575,37.732a9.984,9.984,0,0,0,6.7,2.551h65.251c1.045,0,2.161.107,3.344.221,1.209.116,2.458.236,3.648.236,2.412,0,5.338-.43,6.569-3.787,1.21-3.336-1.872-5.744-4.853-8.073a33.268,33.268,0,0,1-2.669-2.231c-53.517-52.053-103.271-100.331-152.105-147.593l-.069-.067c-3.121-3.022-6.068-5.876-8.8-8.495V-1393h320V-313ZM259.14-1278.371a26.717,26.717,0,0,0-10.439,2.108,26.068,26.068,0,0,0-9,6.27c-9.1,9.216-20,20.063-31.737,30.632a24.783,24.783,0,0,0-4.458,5.132,9.157,9.157,0,0,0-1.523,4.9,8.712,8.712,0,0,0,1.353,4.44,20.6,20.6,0,0,0,4.123,4.734c9.888,8.771,19.408,17.988,28.294,27.4,4.116,4.214,7.666,6.177,11.173,6.176s7.115-1.962,11.313-6.176c16.269-16.3,33.129-32.6,49.435-48.356,8.206-7.931,16.691-16.132,25.005-24.242a91.75,91.75,0,0,0,6.636-7.814,7.319,7.319,0,0,0-3.233-4.4,8.861,8.861,0,0,0-4.386-.926c-.673,0-1.345.034-2,.067h-.011c-.458.023-.891.045-1.319.055H259.556Q259.348-1278.371,259.14-1278.371Z" transform="translate(808.492 1393)" fill="url(#linear-gradient)"/>
      <g id="Layer_1-2" transform="translate(1042.46 110.303)">
        <g id="Group_4508" data-name="Group 4508" transform="translate(100.149 113.179)">
          <path id="Path_53277" data-name="Path 53277" d="M577.1,396.047V334.622c0-46.434,34.081-71.464,75.928-71.464s75.928,25.031,75.928,71.464v61.425h47.684V334.622c0-75.722-55.461-124.722-123.612-124.722s-123.6,49-123.6,124.722v61.425Z" transform="translate(-354.646 -209.9)" fill="#fff"/>
          <g transform="matrix(1, 0, 0, 1, -180.25, -223.48)" filter="url(#Subtraction_2)">
            <path id="Subtraction_2-2" data-name="Subtraction 2" d="M-1678.406-1295.842h-345.138v-107.312a59.317,59.317,0,0,1,4.7-23.247,59.59,59.59,0,0,1,12.809-18.992,59.594,59.594,0,0,1,18.992-12.809,59.319,59.319,0,0,1,23.247-4.7h225.647a59.32,59.32,0,0,1,23.247,4.7,59.591,59.591,0,0,1,18.992,12.809A59.59,59.59,0,0,1-1683.1-1426.4a59.317,59.317,0,0,1,4.7,23.247v107.312Zm-172.569-83.53a26.615,26.615,0,0,0-26.585,26.585,26.413,26.413,0,0,0,10.418,21.105l-3.791,23.175h39.919l-3.794-23.176a26.412,26.412,0,0,0,10.417-21.1A26.615,26.615,0,0,0-1850.975-1379.372Z" transform="translate(2329.61 1862.03)" fill="url(#linear-gradient-4)"/>
          </g>
        </g>
        <g id="Group_4510" data-name="Group 4510">
          <path id="Path_53281" data-name="Path 53281" d="M26,21.81H434L458,148H0Z" transform="translate(169.54 481.887)" fill="#07182f"/>
          <path id="Path_53280" data-name="Path 53280" d="M327.32,985.394h70.147v82.629l108.876-82.629h199.12c60.116,0,109.039-49.019,109.039-109.325l-.415-2.389A109.187,109.187,0,0,0,704.981,764.63l-378.222.415A109.17,109.17,0,0,0,217.72,874.084l.4,2.68c0,60.138,48.952,108.624,109.19,108.624ZM643.693,862.079l11.669-20.214,22.423,12.942V828.917h23.343v25.889l22.418-12.942,11.669,20.214L712.8,875.02l22.412,12.942-11.669,20.22L701.128,895.24v25.877H677.785V895.24l-22.423,12.942-11.669-20.22,22.412-12.942-22.412-12.942Zm-115.555,0,11.669-20.214,22.418,12.942V828.917h23.338v25.889l22.423-12.942,11.669,20.214L597.243,875.02l22.412,12.942-11.669,20.22L585.563,895.24v25.877H562.225V895.24l-22.418,12.942-11.669-20.22,22.412-12.942Zm-115.56,0,11.669-20.214,22.423,12.942V828.917h23.343v25.889l22.412-12.942,11.669,20.214L481.682,875.02l22.412,12.942-11.669,20.22L470.013,895.24v25.877H446.67V895.24l-22.423,12.942-11.669-20.22L434.99,875.02Zm-115.555,0,11.669-20.214,22.418,12.942V828.917h23.343v25.889l22.418-12.942,11.669,20.214L366.128,875.02l22.412,12.942-11.669,20.22L354.453,895.24v25.877H331.11V895.24l-22.418,12.942-11.669-20.22,22.412-12.942Z" transform="translate(-117.57 -321.807)" fill="#fff"/>
        </g>
      </g>
    </g>
  </g>
</svg>
