import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { StaffMomentsService } from 'src/app/services/staff-moments.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';
import * as moment from 'moment';
import AOS from 'aos';
import { environment } from 'src/environments/environment';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { FilterParam } from 'src/app/models/filterparam';
import { ActivatedRoute } from '@angular/router';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { AssignedSiteMomentsTitle } from '../staff-assigned-sites/assignedtitle-moments-title.resolver';
import { threadId } from 'worker_threads';
import { Constant } from 'src/app/config/constants';

declare var bootstrap: any;
@Component({
  selector: 'app-staffmoments',
  templateUrl: './staffmoments.component.html',
  styleUrls: ['./staffmoments.component.scss'],
  providers: [FilterParam]
})
export class StaffmomentsComponent implements OnInit, OnDestroy {
  moments: any = [];
  moment: any = moment;
  readonly MY_CONSTANT = Constant;
  momentDataOnlyWithAssignedVideos: any = [];
  recordData: any;
  loadMore: boolean = false;
  mediaType: string;
  noMomentsDataFound: boolean = false;
  filterMomentModal: any;
  momentIds: any = [];
  currentAssignedVideoData: any;
  staffInstructionsOrDescriptionModal: any
  staffMomentVideoOrImageModal: any;
  staffMomentAssignedTrainingVideo: any;
  loadingVideo: boolean = false;
  next: number = 10;
  totalPageNumber: number;
  offset: number = 1;
  totalRecordsCount: number;
  private fileData: any;
  momentId: string;

  constructor(private toastService: ToastService, private staffMomentService: StaffMomentsService, private authService: AuthService, private commonService: CommonService, private filterParam: FilterParam, private route: ActivatedRoute, private loadVideoFromUrl: LoadVideoFromUrl, private assginedTitleMomentsTitle: AssignedSiteMomentsTitle) {
  }
  ngOnDestroy(): void {
    delete this.filterParam.next;
    delete this.filterParam.offset;
  }

  ngOnInit(): void {
    this.filterParam.next = this.next;
    this.filterParam.offset = this.offset;
    if (this.isFarmIdExist()) {
      this.filterParam.StrFarmId = this.route.snapshot.paramMap.get('id');
    }
    this.fetchAllMoments('/api/moments', false);
  }

  async fetchAllMoments(url: string, loadMore: boolean) {
    try {
      const response: RestResponse = await this.staffMomentService.fetchAllMoments(this.filterParam, url);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if(!response.data?.length){
        this.noMomentsDataFound = true;
      }
      this.totalRecordsCount = response.data?.length ? response.data[0].totalCount : 0;
      this.totalPageNumber = Math.ceil(this.totalRecordsCount / this.filterParam.next);
      if (loadMore) {
        this.loadMore = false;
        const newMoments = this.setFileUploaderPropertyInMoments(response);
        this.moments = this.moments.concat(newMoments);
      } else {
        this.moments = this.setFileUploaderPropertyInMoments(response);
      }

      this.filterMomentsByAssignedVideos()
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setFileUploaderPropertyInMoments(response: any) {
    const moments = (response.data || []).map(moment => ({
      ...moment,
      uploader: this.initializeUploaderMoment(null, 'mp4,mkv,jpg,jpeg,png', null, null, this.toastService, "Only Mkv, Mp4, jpeg, png, jpg files are allowed", null, moment)
    }));
    return moments
  }

  getInstructionCheckByStaffLanguage(record: any) {
    const InstructionExistBasedOnStaffLanguage = (record.instructions || []).some(data => data.LanguageId == this.authService.getUser().languageId && data.Instruction !== '')
    return InstructionExistBasedOnStaffLanguage;
  }

  filterMomentsByAssignedVideos() {
    this.momentDataOnlyWithAssignedVideos = this.moments.map((element) => {
      return { id: element.id, status: element.status, isRecurring: element.isRecurring, trainingIdDetail: element.TrainingIdDetail.filter((subElement) => subElement.languageId == this.authService.getUser().languageId) }
    }).filter(element => element.trainingIdDetail?.length).map(element => ({ ...element, trainingIdDetail: element.trainingIdDetail[0] }))

  }

  isFarmIdExist() {
    if (this.route.snapshot.paramMap.get('id')) {
      return true
    }
  }

  uploadVideoOrImage(momentId: string, event: any) {
    const file = event.target.files[0];
    if (momentId) {
      this.momentId = momentId;
      this.momentIds.push(momentId);
    }
    if (event.target.files[0].type != "video/mp4" && event.target.files[0].type !="image/jpg" && event.target.files[0].type !="image/jpeg" 
      && event.target.files[0].type !="image/png") {
      this.commonService.convertVideoFormat(file).then(res => {
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    }
    else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  onClickMomentFilter(valid) {
    this.filterMomentModal.hide();
    this.filterParam.next = 10;
    this.filterParam.offset = 1;
    this.fetchAllMoments('/api/moments', false);
  }

  initializeUploaderMoment(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string, moment: any) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorMoment(fileSizeMessage, this.toastService, moment);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorMoment(fileTypeMessage, this.toastService, moment);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessMoment(file, files, moment);
        }, 200);
      }
    };
    return uploader;
  }
  async onUploadSuccessMoment(file: any, files: any, moment: any) {

    const data = {
      id: moment.id,
      userVideo: file.path,
      isDeleted: false,
      isActive: true,
    }
    try {
      const response: RestResponse = await this.staffMomentService.updateStatusOfMoment(data);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.moments.map(data => {
        if (moment.id == data.id && moment.isRecurring === false) {
          data.status = Constant.MOMENT_STATUS.WAITING_FOR_APPROVAL;
        }
        return data
      })
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }

    this.momentIds = this.momentIds.filter(momentId => momentId !== moment.id)
  }

  isNullOrUndefined(value: any) {
    return value === undefined || value === null;
  }

  backToMomentsList(data: any) {
    this.currentAssignedVideoData = null;
    if (data.status == Constant.MOMENT_STATUS.WAITING_FOR_APPROVAL) {
      this.moments.map(moment => {
        if (moment.id == data.id && moment.isRecurring === false) {
          moment.status = Constant.MOMENT_STATUS.WAITING_FOR_APPROVAL;
        }
        return moment
      })
    }
  }

  fileValidationErrorMoment(fileSizeMessage: string, toastService: ToastService, moment: any) {
    this.momentIds = this.momentIds.filter(momentId => momentId !== moment.id)
    toastService.error(fileSizeMessage);
  }

  getUploadIconStatus(data: any) {
    if (data.status == 'OPEN') {
      return 'upload-icon.svg'
    } else {
      return 'check-circle-regular.svg'
    }
  }

  viewInstructionsOrDesc(data: any, type: string) {
    AOS.init({ disable: true });
    if (type == 'instruction') {
      (data.instructions || []).map(instruction => {
        if (this.authService.getUser().languageId == instruction.LanguageId) {
          this.recordData = {
            data: instruction.Instruction,
            type: "instruction"
          }
        }
      })
    } else {
      this.recordData = { data: data.description, type: "description" }
    }
    this.staffInstructionsOrDescriptionModal.show();
  }

  getMediaType(url) {
    const extension = url.split(/[#?]/)[0].split('.').pop().trim();
    if (extension == "jpg" || extension == "jpeg" || extension == "png") {
      this.mediaType = "image"
    }
    if (extension == "mkv" || extension == "mp4") {
      this.mediaType = "video";
    }
  }

  getMomentMediaType(record: any, type: string) {
    this.getMediaType(record.mediaUrl);
    if (this.mediaType == 'image') {
      if (type == 'title') {
        return "Open Image"
      }
      if (type == 'icon') {
        return 'image-solid.svg'
      }
    }
    if (this.mediaType == 'video') {
      if (type == 'title') {
        return "Watch Video"
      }
      if (type == 'icon') {
        return 'video-logo.svg'
      }
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.staffMomentVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('staffMomentVideoOrImageModal')
      );
    }, 0)
    setTimeout(() => {
      this.staffMomentAssignedTrainingVideo = new bootstrap.Modal(
        document.getElementById('staffMomentAssignedTrainingVideo')
      );
    }, 0)
    setTimeout(() => {
      this.staffInstructionsOrDescriptionModal = new bootstrap.Modal(
        document.getElementById('staffInstructionsOrDescriptionModal')
      );
    }, 0)
    setTimeout(() => {
      this.filterMomentModal = new bootstrap.Modal(
        document.getElementById('filterMomentModal')
      );
    }, 0)

  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.mediaUrl);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.staffMomentVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      this.loadVideoFromUrl.UrlToBlobUrl(record.mediaUrl)
        .then(blobUrl => { // now it's loaded
          document.body.className = 'loaded';
          setTimeout(() => {
            let vid = document.getElementById('staff-video') as HTMLVideoElement;
            this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
            vid.addEventListener('canplaythrough', (event) => {
              this.loadingVideo = false;
            })
          }, 0);
        }).catch((err) => console.log(err));
    }
  }

  openFilterMomentModal() {
    AOS.init({ disable: true });
    this.filterMomentModal.show();
  }

  getImageWidthClass(record: any) {
    this.getMediaType(record.mediaUrl);
    if (this.mediaType == 'image') {
      return true;
    } else {
      return false;
    }
  }

  resetFilter() {
    this.removeFilterData();
    this.filterMomentModal.hide();
    this.filterParam.next = 10;
    this.filterParam.offset = 1;
    this.fetchAllMoments('/api/moments', false);
  }

  removeFilterData(){
    delete this.filterParam.status;
    delete this.filterParam.type;
    delete this.filterParam.startDate
    delete this.filterParam.endDate
    delete this.filterParam.searchText;
    delete this.filterParam.isRecurring;
  }

  watchVideo(record: any) {
    const data = { status: record.status, id: record.id }
    record = record.TrainingIdDetail.filter(data => data.languageId == this.authService.getUser().languageId)
    this.currentAssignedVideoData = { trainingIdDetail: record[0], ...data };
  }

  removeMoment(id: any) {
    this.commonService.confirmation('Would you like to remove this moment?', this.removeMomentCallback.bind(this), id, null, null, null);
  }

  async removeMomentCallback(id: string) {
    try {
      const response: RestResponse = await this.staffMomentService.deleteMoment(id);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.moments = this.moments.filter(moment => moment.id !== id)
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  onScrollingFinished() {
    if(this.totalRecordsCount){
      if (this.moments.length >= this.totalRecordsCount && this.filterParam.offset >= this.totalPageNumber) {
        return false
      }
      this.filterParam.next = 10;
      this.filterParam.offset = this.filterParam.offset + 1;
      this.loadMore = true;
      this.fetchAllMoments('/api/moments', true);

    }
  }

  async onFileProcessingCompleted(files: any) {
    let index = this.moments.findIndex(x => x.id == this.momentId);
    this.moments[index].uploader.addToQueue(files);
    this.moments[index].uploader.uploadAll();
  }

}
