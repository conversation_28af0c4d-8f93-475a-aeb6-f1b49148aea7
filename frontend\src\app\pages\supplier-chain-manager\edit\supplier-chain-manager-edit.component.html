<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
    <div class="dashboard-content-container">
        <form #recordForm="ngForm" novalidate="novalidate" *ngIf="supplier.id || request.recordId == 0">
            <div class="offset-xxl-1">
                <div class="row site-form-container">
                    <div class="col-12 col-md-12 col-xxl-10 d-flex justify-content-between">
                        <div>
                            <h4 class="fw-bold">{{request.recordId == 0 ? "New Team" : "Edit Team"}}</h4>
                            <p class="user-edit-msg">Please make sure you fill all the fields before you click on save
                                button
                            </p>
                        </div>
                        <div class="profile-image-container position-relative"
                            [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
                            <img *ngIf="!profileImageLoader"
                                [src]="supplier.profileImageUrl ? supplier.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
                            <div *ngIf="profileImageLoader" class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div *ngIf="!profileImageLoader"
                                class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
                                <label for="file-input">
                                    <i class="bi bi-pencil-fill"></i>
                                    <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect
                                        [uploader]="uploader" id="file-input" type="file"
                                        accept="image/png, image/jpg, image/jpeg" />
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12 col-md-6 col-xxl-5 mb-4">
                        <div class="form-floating">
                            <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text"
                                name="firstName" #firstName="ngModel" [(ngModel)]="supplier.firstName"
                                required="required" placeholder="First Name"
                                [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
                            <label for="floatingInput">{{"USERS.FirstName" | translate}}</label>
                        </div>
                        <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 col-md-6 col-xxl-5 mb-4">
                        <div class="form-floating">
                            <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text"
                                name="lastname" #lastname="ngModel" [(ngModel)]="supplier.lastName" required="required"
                                placeholder="Last Name" [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}">
                            <label for="floatingInput">{{"USERS.LastName" | translate}}</label>
                        </div>
                        <app-validation-message [field]="lastname" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 col-md-6 col-xxl-5 mb-4">
                        <div class="form-floating">
                            <input autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
                                (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                                (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="supplier.phoneNumber"
                                [ngClass]="{'is-invalid':!phone.valid && onClickValidation}" required="required"
                                placeholder="Phone Number" minlength="7" maxlength="12" pattern="^[0-9]+$"
                                (countryChange)="onCountryChange($event)">
                        </div>
                        <app-validation-message [field]="phone" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <!-- <div class="col-12 col-md-6 col-xxl-5 mb-4">
                        <div class="form-floating">
                            <select class="form-select form-control" name="languageId" aria-label="Please Select Language"
                                [(ngModel)]="supplier.languageId"
                                [ngClass]="{'is-invalid':!language.valid && onClickValidation}" required="required"
                                #language="ngModel">
                                <option [ngValue]="undefined" selected disabled>Select Language</option>
                                <option *ngFor="let language of languages" [value]="language.id">{{language.name}}
                                </option>
                            </select>
                            <label for="language">{{"USERS.language" | translate}}</label>
                        </div>
                        <app-validation-message [field]="language" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div> -->
                    <div class="col-12 col-md-6 col-xxl-5 mb-4">
                        <div class="form-floating">
                            <input class="form-control" type="text" name="companyName" #companyName="ngModel"
                                [(ngModel)]="supplier.companyName" required="required" placeholder="Company"
                                [ngClass]="{'is-invalid':!companyName.valid && onClickValidation}" (keypress)="omitSpecialChar($event)">
                            <label for="floatingInput">{{"Supplier.companyName" | translate}}</label>
                        </div>
                        <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 col-md-6 col-xxl-5 mb-4">
                        <div class="form-floating">
                            <input class="form-control" type="text" name="companyCode" #companyCode="ngModel"
                                [(ngModel)]="supplier.companyCode" required="required" placeholder="Company Code"
                                [ngClass]="{'is-invalid':!companyCode.valid && onClickValidation}" (keypress)="omitSpecialChar($event)">
                            <label for="floatingInput">{{"Supplier.companyCode" | translate}}</label>
                        </div>
                        <app-validation-message [field]="companyCode" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 col-md-12 offset-xxl-1 col-xxl-9 d-flex justify-content-end">
                        <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                            type="button" (click)="save(recordForm.form)" [disabled]="request.isRequested">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </form>
        <div class="clearfix"></div>
    </div>
</div>