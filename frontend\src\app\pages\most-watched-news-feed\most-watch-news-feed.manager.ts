import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MostWatchNewsFeedsService } from './most-watch-news-feed.service';

@Injectable({
    providedIn: 'root'
})
export class MostWatchNewsFeedsManager extends BaseManager {

    constructor(protected mostWatchNewsFeedsService: MostWatchNewsFeedsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(mostWatchNewsFeedsService, loadingService, toastService);
    }
}
