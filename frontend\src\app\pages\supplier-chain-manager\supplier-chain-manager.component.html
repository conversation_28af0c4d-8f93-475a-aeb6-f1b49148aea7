<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 col-sm-4 text-start">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="" appDelayedInput
                        (delayedInput)="search($event)" [delayTime]="1000">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div class="col-12 col-sm-4">
            </div>
            <div class="col-12 col-sm-4 text-end">
                <button type="button" class="btn add-button btn-primary btn-lg" (click)="onNewRecord()">
                    <img src="/assets/images/icons/menu/add_icon.svg" class="me-3" alt="">Add Team
                </button>
            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th width="180">{{"USERS.Name" | translate}}</th>
                        <th width="180">{{"Supplier.MobileNo" | translate}}</th>
                        <th width="180">{{"Supplier.companyName" | translate}}</th>
                        <th width="180">{{"Supplier.companyCode" | translate}}</th>
                        <th width="180"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>{{record.fullName}}</td>
                        <td>{{record.phoneNumber}}</td>
                        <td>{{record.companyName}}</td>
                        <td>{{record.companyCode}}</td>
                        <td class="text-center custom-action-button text-right">
                            <div class="d-flex justify-content-end mb-3">
                                <button title="Edit" class="btn btn-outline-light  mb-3 action-button"
                                    [routerLink]="['/dashboard/team/edit/'+record.id]">
                                    <i class="bi bi-pencil"></i> EDIT
                                </button>
                                <button title="Delete" class="btn btn-primary  mb-3 action-button"
                                    (click)="remove(record.id)">
                                    <i class="bi bi-trash"></i> DELETE
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>