import { Component, On<PERSON><PERSON>roy, OnInit, Input, Output, ViewChild, EventEmitter } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { TrainingManager } from './training.manager';
import { Training } from '../../models/training';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Constant } from 'src/app/config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
import { UsersService } from 'src/app/services/users.service';
import AOS from 'aos';
import { TrainingService } from './training.service';
import * as moment from 'moment';
import { FilterParam } from 'src/app/models/filterparam';
import { event } from 'jquery';
import { ManageContentManager } from '../manage-content-type/manage-content.manager';
import { Content } from 'src/app/models/content';
import { LearningSeries } from 'src/app/models/learningseries';
import { ManageLearningSeriesManager } from '../manage-learning-series/manage-learning-series.manager';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-training',
  templateUrl: './training.component.html',
  styleUrls: ['./training.component.scss']
})
export class TrainingComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() assignVideoFromMoment: boolean | false;
  @Input() assignTrainingIdFromMoment: string | undefined;
  dropdownSettings = {};
  moment: any = moment;
  dropdownSettingsUsers = {};
  farms: any = [];
  onClickValidation = false;
  selectedFarms: any = [];
  selectedUsers: any = [];
  farmIds: any = [];
  filterTrainingModal: any;
  searchTraining: any;
  userIds: any = [];
  users: any = [];
  fromDate: any;
  toDate: any;
  contentTypes: Content[];
  learningSeries: LearningSeries[];
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  reassignTrainingModal: any;
  @Output() assignTrainingIdOutput = new EventEmitter<string>();
  selectedFarmsClone: any = [];
  restrictedUsersData: any;
  restrictedUsersModal: any;
  trainingFilterData: RestResponse;
  records: Training[];
  learningSeriesData: any[] = [];

  constructor(protected trainingManager: TrainingManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, protected activatedRoute: ActivatedRoute, public commonUtil: CommonUtil, private usersService: UsersService, private trainingService: TrainingService, private manageContentManager: ManageContentManager, private managelearningSeriesManager: ManageLearningSeriesManager) {
    super(trainingManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.setDropdownSettings();
    this.setDropdownSettingsUsers();
    this.request.loadEditPage = false;
    //this.fetchFarms();
    //this.fetchUsers();
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<Training>();
    this.contentTypes = new Array<Content>();
    this.learningSeries = new Array<LearningSeries>();
    this.fetchAssociatedData();
    
    // Check for query parameters to apply published filter
    this.checkQueryParameters();
    
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.reassignTrainingModal = new bootstrap.Modal(
        document.getElementById('reassignTrainingModal')
      );
    }, 0)
    setTimeout(() => {
      this.filterTrainingModal = new bootstrap.Modal(
        document.getElementById('filterTrainingModal')
      );
    }, 0)
    setTimeout(() => {
      this.restrictedUsersModal = new bootstrap.Modal(
        document.getElementById('restrictedUsersModal')
      );
    }, 0)
  }

  async fetchAssociatedData() {
    this.trainingFilterData = await this.trainingService.getTrainingFilterData(null);
  }

  checkQueryParameters() {
    // Get query parameters from the current route
    const queryParams = this.router.parseUrl(this.router.url).queryParams;
    
    // Check if the filter parameter is set to 'published'
    if (queryParams['filter'] === 'published') {
      // Apply published filter
      this.filterParam.isPublish = true;
      
      // Wait for the component to be fully initialized before refreshing
      setTimeout(() => {
        this.refreshRecord();
      }, 200);
    }
  }

  //Accssibility update status
  getTooltipText(accessibility: string): string {
    return accessibility === 'Public' ? 'Public' : 'Private';
  }

  updateStatusAccssibility(record: any, index) {
    this.commonService.confirmation('Would you like to change the Accessibility Mode?', this.updateAccessibilityCallback.bind(this), { id: record.id, accessibility: record.accessibility }, null, null, this.cancelUpdateStatusCallback.bind(this));
  }

  updateStatusAccssibilityMethod(record: any, index) {
    if (record.accessibility === "Public") {
      this.records[index].accessibility = "Private";
    }
    else {
      this.records[index].accessibility = "Public";
    }
    const data = {
      id: record.id,
      accessibility: record.accessibility
    };
    this.updateAccessibilityCallback(data);
  }

  async updateAccessibilityCallback(data) {
    data.accessibility = data.accessibility === "Public" ? "Private" : "Public";
    try {
      this.loadingService.show();
      console.log(data)
      const response: RestResponse = await this.trainingManager.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  //Accssibility update status end

  updateStatus(data: any, recordData: any) {
    let status = JSON.parse(JSON.stringify(data.currentTarget.checked));
    if (!data.currentTarget.checked) {
      data.currentTarget.checked = true;
    }
    this.commonService.confirmation('Would you like to change the status of Training?', this.updateStatusCallback.bind(this), { id: recordData.id, isPublish: status }, null, null, this.cancelUpdateStatusCallback.bind(this));
  }

  cancelUpdateStatusCallback() {
    this.onCancel();
  }

  async updateStatusCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.trainingManager.update(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchFarms(trainingId) {
    try {
      let param = new FilterParam();
      if (trainingId != null) {
        param.strTrainingId = trainingId;
      }
      const response: RestResponse = await this.usersService.GetAllAdminFarms(param);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.farms = response.data;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      delete this.filterParam.searchCommonTitle;
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  onClickTrainingFilter(valid) {
    // Update URL query parameter if published filter is applied
    if (this.filterParam.isPublish) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { filter: 'published' },
        queryParamsHandling: 'merge'
      });
    } else {
      // Remove the filter query parameter if published filter is not applied
      const currentUrl = this.router.parseUrl(this.router.url);
      if (currentUrl.queryParams['filter'] === 'published') {
        this.router.navigate([], {
          relativeTo: this.activatedRoute,
          queryParams: { filter: null },
          queryParamsHandling: 'merge'
        });
      }
    }
    
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  resetFilter() {
    // this.fromDate = null;
    // this.toDate = null;
    // delete this.filterParam.startDate
    // delete this.filterParam.endDate;
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.searchText;
    delete this.filterParam.isPublish;
    delete this.filterParam.accessibility;
    
    // Clear the URL query parameter if it exists
    const currentUrl = this.router.parseUrl(this.router.url);
    if (currentUrl.queryParams['filter']) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { filter: null },
        queryParamsHandling: 'merge'
      });
    }
    
    this.filterTrainingModal.hide();
    this.onCancel();
  }

  openTrainingDetailPage(record: any) {
    if (this.assignVideoFromMoment) {
      this.assignTrainingIdOutput.emit(null);
    }
    this.router.navigate(['/dashboard/training/detail/' + record.id]);
  }


  async fetchUsers() {
    let response: RestResponse;
    try {

      if (this.farmIds.length > 0) {
        const data = {
          FarmIds: this.farmIds
        }
        response = await this.usersService.getAllUsersByFarmList(data);
        this.setUsersList(response, true)
      } else {
        let param = new FilterParam();
        if (this.recordData != null) {
          param.strTrainingId = this.recordData.id;
        }
        response = await this.usersService.fetchAll(param);
        this.setUsersList(response, false)
      }
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setUsersList(response, farmBasedUsers) {
    if (!response.status) {
      this.toastService.error(response.message);
      return;
    }
    if (farmBasedUsers) {
      this.users = response.data.map(response => ({ ...response.userIdDetail }))
    } else {
      this.users = response.data
    }
  }

  async openRessignStaffModal(record: any) {
    this.setEmptyModalData();
    this.recordData = record;
    await this.fetchFarms(record.id);
    this.selectedFarms = this.farms.filter(x => x.assignFarm == true);
    this.selectedFarmsClone = JSON.parse(JSON.stringify(this.selectedFarms));
    await this.fetchUsers();
    if (this.selectedFarms.length == 0) {
      this.selectedUsers = this.users.filter(x => x.assignUser == true);
    }
    AOS.init({ disable: true });
    this.reassignTrainingModal.show();
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  setDropdownSettingsUsers() {
    this.dropdownSettingsUsers = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  onItemSelectForFarm(item: any) {
    if (this.farmIds && this.farmIds.length > 0) {
      if (!this.farmIds.some(id => id === item.id)) {
        this.setItemsDataForFarm(item);
      }
    } else {
      this.setItemsDataForFarm(item);
    }
    if (this.farmIds.length > 0) {
      this.fetchUsers();
    }
  }

  openRestrictedUsersList(record: any) {
    AOS.init({ disable: true });
    this.filterParam.trainingId = record.id
    this.restrictedUsersData = record;
    this.restrictedUsersModal.show();
  }

  setItemsDataForFarm(item) {
    this.farmIds = [
      ...this.farmIds,
      item.id,
    ];
  }

  onDeSelectForFarm(item: any) {
    if (this.selectedFarmsClone.find(x => x.id == item.id)) {
      this.selectedFarms.push(item);
      this.selectedFarms = [...this.selectedFarms];
      this.commonService.preventRemoveExistingSite("You can't remove this site because this training already assign to users?", null, null);
    }
    // if (this.farmIds && this.farmIds.length > 0) {
    //   this.farmIds = this.farmIds.filter(id => id !== item.id);
    // }
    // this.fetchUsers();
  }

  onDeSelectForUser(item: any) {
    if (this.userIds && this.userIds.length > 0) {
      this.userIds = this.userIds.filter(id => id !== item.id);

    }
  }

  onItemSelectForUser(item: any) {
    if (this.userIds && this.userIds.length > 0) {
      if (!this.userIds.some(id => id === item.id)) {
        this.setItemsDataForUser(item);
      }
    } else {
      this.setItemsDataForUser(item);
    }
  }

  setItemsDataForUser(item) {
    this.userIds = [
      ...this.userIds,
      item.id,
    ];
  }

  async onClickReassignStaffForm(valid) {
    if (this.userIds.length == 0 && this.farmIds.length == 0) {
      this.onClickValidation = true;
      return
    }
    const data = {
      TrainingId: this.recordData.id,
      IsActive: true,
      IsDeleted: false,
      FarmIds: this.farmIds?.length ? this.farmIds : null,
      userIds: this.userIds?.length ? this.userIds : null,
    }
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.trainingService.assignTraining(this.removeNullDataFromObject(data));
      if (!response.status) {
        this.setEmptyModalData()
        this.toastService.error(response.message);
        return;
      }
      this.reassignTrainingModal.hide();
      this.setEmptyModalData();
      this.onCancel();
      this.toastService.success(response.message);
    } catch (e) {
      this.setEmptyModalData();
      this.toastService.error(e.message);
    }
  }

  removeNullDataFromObject(obj: any) {
    for (var propName in obj) {
      if (obj[propName] === null || obj[propName] === undefined) {
        delete obj[propName];
      }
    }
    return obj
  }

  setEmptyModalData() {
    // console.log('setEmptyModalData')
    this.farmIds = [];
    this.userIds = [];
    this.recordData = undefined;
    this.selectedFarms = [];
    this.selectedUsers = [];
    this.onClickValidation = false;
  }


  onItemSelection(record: any) {
    this.onAssociatedValueSelected(record);
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/training/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/training/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  removeSuccess() {
    this.onCancel();
  }

  assignTrainingVideo(id: string) {
    this.commonService.confirmation('Would you like to assign this training video?', this.assignTrainingVideoCallback.bind(this), id, null, null, null);
  }

  assignTrainingVideoCallback(id: string) {
    this.assignTrainingIdOutput.emit(id);
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/training/edit/' + id])
  }

  ngOnDestroy() {
    this.clean();
  }

  loadDetailPage(recordId) {
    this.selectedId = recordId;
    setTimeout(() => {
      $('#trainingDetailPage').appendTo('body').modal('show');
      $('#trainingDetailPage').on('hidden.bs.modal', () => {
        setTimeout(() => {
          this.selectedId = undefined;
        });
      });
    }, 500);
  }
  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  unAssignFarmOrUser(record) {
    if (record.assignFarmCount > 0) {
      this.router.navigate(['/dashboard/training-assigned-sites/' + record.id]);
      return;
    }
    this.router.navigate(['/dashboard/training-assigned-users/' + record.id])
  }

  onAuthorChange(author: string) {
    let param = new FilterParam();
    param.author = author;
    this.fetchAuthorLearningSeriesData(param);
  }

  async fetchAuthorLearningSeriesData(param: FilterParam) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.trainingService.fetchLearningSeriesAuthorBased(param);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.learningSeriesData = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }
  
}
