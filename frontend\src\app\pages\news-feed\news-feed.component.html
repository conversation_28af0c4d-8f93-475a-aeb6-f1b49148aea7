<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 col-sm-4 text-start">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="Search" appDelayedInput
                        (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchNewsFeed">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div class="col-12 col-sm-4">
            </div>
            <div class="col-12 col-sm-4 d-flex align-items-center justify-content-end mb-2 pe-0">
                <div class="manage-moment-button">
                    <button (click)="openFilterNewsFeedModal()" type="button"
                        class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg font-15px height-51px filter-button-cls">
                        <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                            alt="">Filter
                    </button>
                </div>
                <button type="button"
                    class="btn add-button btn-primary btn-lg font-15px manage-learning-series-add-button"
                    (click)="onNewRecord()" *ngIf="authService.isAccessible('CATEGORY','AddButton')">
                    <img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-15px" alt="">Add News Feed
                </button>
            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th>{{'NewsFeed.title' | translate}}</th>
                        <!-- <th>{{'NewsFeed.description' | translate}}</th> -->
                        <th style="width: 150px;">{{'NewsFeed.type' | translate}}</th>
                        <th style="width: 80px;">{{'NewsFeed.Accessibility' | translate}}</th>
                        <th style="width: 80px;">{{'NewsFeed.status' | translate}}</th>
                        <th style="width: 110px; text-wrap: nowrap;">{{'Training.lastUpdatedDate' | translate}}</th>
                        <th style="width: 50px;" class="text-center">{{'NewsFeed.action' | translate}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>
                            <a class="text-decoration-underline"
                                (click)="openNewsFeedDetailPage(record)">{{record.title}}
                            </a>
                        </td>
                        <!-- <td>
                            {{record.description}}
                        </td> -->
                        <td>
                            {{record.type}}
                        </td>
                        <td>
                            <img [title]="record.isNewsFeedPublic == false ? 'Private' : record.isNewsFeedPublic == true && 'Public'"
                            [src]="record.isNewsFeedPublic == false ? '/assets/images/icons/menu/private-red.svg' : record.isNewsFeedPublic == true && '/assets/images/icons/menu/users-green.svg'"
                            class="width-27px mb-3" />
                        </td>
                        <td>
                            <div class="form-check form-switch">
                                <input [disabled]="!record.isNewsFeedPublic" (change)="updateStatus($event,record)" class="form-check-input toggle-width"
                                    type="checkbox" id="flexSwitchCheckChecked" [(ngModel)]="record.isPublish"
                                    [ngModelOptions]="{standalone: true}">
                                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                            </div>
                        </td>
                        <td>
                            <div>{{moment(record.updatedOn).format('DD/MM/YYYY')}}</div>
                        </td>
                        <td class="text-center custom-action-button text-right">
                            <div class="d-flex justify-content-end mb-3">
                                <img src="/assets/images/icons/menu/restriction.svg"
                                    *ngIf="authService.isAccessible('TRAINING','RestrictedUsers') && !isDetailPage && !isPlusButton && record.accessibility == 'RESTRICTED' && record.restrictedUsersCount && record.restrictedUsersCount > 0"
                                    [class.disabled]="authService.isDisabled('TRAINING','RestrictedUsers')"
                                    title="Restricted Users" (click)="openRestrictedUsersList(record)"
                                    class=" me-2 width-27px cursor-pointer" />
                                <i *ngIf="authService.isAccessible('CATEGORY','EditButton') && !isDetailPage && !isPlusButton"
                                    [class.disabled]="authService.isDisabled('CATEGORY','EditButton')" title="Edit"
                                    (click)="editRecord(record.id)"
                                    class="bi bi-pencil font-21px me-2 cursor-pointer"></i>
                                <i *ngIf="authService.isAccessible('CATEGORY','DeleteButton') && !isPlusButton"
                                    [class.disabled]="authService.isDisabled('CATEGORY','DeleteButton')" title="Delete"
                                    (click)="remove(record.id)" class="bi bi-trash font-21px cursor-pointer"></i>
                            </div>
                        </td>
                    </tr>
                    <!-- <tr *ngIf="records.length===0">
						<td class="text-center" colspan="5">
							{{"COMMON.NORECORDS" | translate}}
						</td>
					</tr> -->
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="filterNewsFeedModal" tabindex="-1" aria-labelledby="filterNewsFeedModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterNewsFeedModalLabel">Filter News Feed</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div *ngIf="filterNewsFeedModal && filterNewsFeedModal._isShown" class="modal-body">
                <form #newsFeedFilterForm="ngForm" novalidate="novalidate">
                    <!-- <mat-form-field class="example-form-field w-100">
                        <mat-label>Search...</mat-label>
                        <input [(ngModel)]="filterParam.searchText" name="searchNewsFeed" matInput type="text">
                        <button mat-button *ngIf="filterParam.searchText" matSuffix mat-icon-button aria-label="Clear"
                            (click)="filterParam.searchText=''">
                            <mat-icon>close</mat-icon>
                        </button>
                    </mat-form-field> -->
                    <div class="form-floating mb-3">
                        <select class="form-select form-control" name="type" aria-label="Please Select Type"
                            [(ngModel)]="filterParam.type" [ngClass]="{'is-invalid':!type.valid && onClickValidation}"
                            required="required" #type="ngModel">
                            <option [ngValue]="undefined" selected disabled>Select Option</option>
                            <option value="TRAINING">Training</option>
                            <option value="SPONSORED">Sponsored</option>
                            <option value="BLOG">Blog</option>
                        </select>
                        <label for="type">{{"NewsFeed.type" | translate}}</label>
                    </div>
                    <div class="form-floating mb-3">
                        <select class="form-select form-control" name="status" aria-label="Please Select status"
                            [(ngModel)]="filterParam.isPublish"
                            [ngClass]="{'is-invalid':!status.valid && onClickValidation}" required="required"
                            #status="ngModel">
                            <option [ngValue]="undefined" selected disabled>Select Option</option>
                            <option value="true">Publish</option>
                            <option value="false">Draft</option>

                        </select>
                        <label for="type">{{"SubCategory.status" | translate}}</label>
                    </div>
                    <div class="form-floating mb-3">
                        <select class="form-select form-control" name="accessibility"
                            aria-label="Please Select accessibility setup" [(ngModel)]="filterParam.isNewsFeedPublic"
                            [ngClass]="{'is-invalid':!accessibility.valid && onClickValidation}" required="required"
                            #accessibility="ngModel">
                            <option [ngValue]="undefined" selected disabled>Select Option</option>
                            <option value="true">Public</option>
                            <option value="false">Private</option>
                        </select>
                        <label for="type">{{"SubCategory.ACCESSIBILITY_SETUP" | translate}}</label>
                    </div>
                    <app-date-range-filter (fromDateOutput)="fromDateOutput($event)"
                        (toDateOutput)="toDateOutput($event)" [fromDateInput]="filterParam.startDate"
                        [toDateInput]="filterParam.endDate"></app-date-range-filter>
                    <div class="modal-footer">
                        <button (click)="resetFilter()" type="button"
                            class="text-white btn btn-secondary">Reset</button>
                        <button (click)="onClickNewsFeedFilter(newsFeedFilterForm.form.valid)" type="button"
                            class="btn btn-primary">Filter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade modal-xl" id="restrictedUsersModal" aria-hidden="true" aria-labelledby="restrictedUsersModal"
    tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="restrictedUsersModalLabel">User Restrcited List</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <app-restricted-users-list [getRestrictedUsersData]="restrictedUsersData && restrictedUsersData"
                    *ngIf="restrictedUsersModal && restrictedUsersModal._isShown"></app-restricted-users-list>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>