import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Language } from './language';
import { Content } from './content';
export class NewsFeed extends BaseModel {

	contentType: string;
	learningSeries: string;
	title: string;
	description: string;
	url: string;
	type: string;
	mediaType: string;
	accessibility: string;
	uploadRequired: boolean;
	watchRequired: boolean;
	duration: number;
	isPublish: boolean;
	isNewsFeedPublic: boolean;
	thumbnailImageUrl: string;
	usersAssignTraining: any[];


	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
		this.usersAssignTraining = new Array<any>();
	}

	static fromResponse(data: any): NewsFeed {
		const obj = new NewsFeed();
		obj.id = data.id;
		obj.createdBy = data.createdBy;
		obj.isNewsFeedPublic = data.isNewsFeedPublic;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.mediaType = data.mediaType;
		obj.title = data.title;
		obj.type = data.type;
		obj.usersAssignTraining = data.usersAssignTraining ? data.usersAssignTraining : [];
		obj.description = data.description;
		obj.url = data.url;
		obj.thumbnailImageUrl = data.thumbnailImageUrl
		obj.contentType = data.contentType;
		obj.learningSeries = data.learningSeries;
		obj.uploadRequired = data.uploadRequired;
		obj.watchRequired = data.watchRequired;
		obj.accessibility = data.accessibility;
		obj.isPublish = data.isPublish;
		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.title)) {
			form.controls.title.setErrors({ invalid: true });
			return false;
		}
		if (this.isNullOrUndefinedAndEmpty(this.description)) {
			form.controls.description.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	forRequest() {
		this.title = this.trimMe(this.title);
		this.description = this.trimMe(this.description);
		return this;
	}
}
