import { Directive, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DataTableDirective } from 'angular-datatables';
import { Subject } from 'rxjs';
import { FilterParam } from '../models/filterparam';
import { LoadingService } from '../services/loading.service';
import { RestResponse } from '../shared/auth.model';
import { CommonService } from '../shared/common.service';
import { ToastService } from '../shared/toast.service';
import { BaseComponent } from './base.component';
import { BaseManager } from './base.manager';
import { BaseModel } from './base.model';

@Directive()
export class BaseListServerSideComponent extends BaseComponent {

  @Input()
  filterParam: FilterParam;
  @Input()
  onAssociatedValueSelected: (data: any) => void;
  @Input()
  isDetailPage: boolean;

  @ViewChild(DataTableDirective, { static: false })
  dtElement: DataTableDirective;
  dtTrigger: Subject<any>;
  dtOptions: DataTables.Settings;

  records: BaseModel[];
  selectedId: string;
  hasDataLoad: boolean;
  isPlusButton: boolean;
  columnOrders: Array<string>;
  constructor(protected manager: BaseManager, protected commonService: CommonService, protected toastService: ToastService,
    protected loadingService: LoadingService, protected router: Router) {
    super(manager, commonService, toastService, loadingService, router);
    this.hasDataLoad = true;
    this.columnOrders = new Array<string>();
    this.dtTrigger = new Subject();
    if (!this.filterParam) {
      this.filterParam = new FilterParam();
    }
    this.dtOptions = {
      order: [],
      responsive: true,
      pagingType: 'full_numbers',
      pageLength: 10,
      serverSide: true,
      searching: false,
      lengthChange: false,
      ordering: false,
      language: {
        search: '',
        searchPlaceholder: "Search...",
        "emptyTable": "No Records Available"
      },
      // oLanguage: {
      //   "sEmptyTable": "No matching records found"
      // },
      ajax: (dataTablesParameters: any, dataTableCallback) => {
        this.fetchRecords(dataTablesParameters, dataTableCallback);
      }
    } as DataTables.Settings;
  }

  init(): void {
    setTimeout(() => {
      this.dtTrigger.next();
    }, 100);
  }

  refreshRecord() {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      dtInstance.destroy();
    });
    setTimeout(() => {
      this.dtTrigger.next();
    }, 50);
  }

  async fetchRecords(param, callBack) {
    try {
      this.hasDataLoad = false;
      this.setParam(param);
      this.loadingService.show();
      const response: RestResponse = await this.manager.fetchAll(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
      this.onFetchCompleted();
      callBack({ recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length, recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length, data: [] });
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  setParam(dataTablesParameters: any) {
    dataTablesParameters.offset = (dataTablesParameters.start / dataTablesParameters.length) + 1;
    dataTablesParameters.next = dataTablesParameters.length;
    if (this.dtOptions.searching) {
      dataTablesParameters.searchText = dataTablesParameters.search.value.length > 0 ? dataTablesParameters.search.value.trim() : null;
    }
    // Sorting with Keypress text
    //this.filterParam.searchText = dataTablesParameters.search.value;

    // Sorting with column Name
    const selectedOrder = dataTablesParameters.order.length > 0 ? dataTablesParameters.order[0] : null;
    if (selectedOrder) {
      this.filterParam.orderByColumn = this.columnOrders[selectedOrder.column];
      this.filterParam.searchOrderBy = selectedOrder.dir;
    }

    //this.filterParam.searchPageLenth = dataTablesParameters.length;
    this.filterParam.offset = dataTablesParameters.offset;
    this.filterParam.next = dataTablesParameters.next;
  }


  clean() {
    super.clean();
    this.dtTrigger.unsubscribe();
  }

  onFetchCompleted() {
    this.hasDataLoad = true;
  }

    onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }
}
