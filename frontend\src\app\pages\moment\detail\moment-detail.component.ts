import { Component, Input, OnInit } from '@angular/core';
import { BaseDetailComponent } from '../../../config/base.detail.component';
import { Moment } from '../../../models/moment';
import { ActivatedRoute, Router } from '@angular/router';
import { MomentManager } from '../moment.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';
import AOS from 'aos';
import { Constant } from 'src/app/config/constants';
import * as moment from 'moment';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { RestResponse } from 'src/app/shared/auth.model';
import { Users } from 'src/app/models/users';
import { MomentService } from '../moment.service';
import { MomentRecurringLogs } from 'src/app/models/momentrecurringlogs';
import { FilterParam } from 'src/app/models/filterparam';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-moment-detail',
  templateUrl: './moment-detail.component.html',
  styleUrls: ['./moment-detail.component.scss']
})
export class MomentDetailComponent extends BaseDetailComponent implements OnInit {
  momentAssignedTrainingVideo: any;
  loadingVideo: boolean = false;
  moment: any = moment;
  readonly MY_CONSTANT = Constant;
  recordData: any;
  momentVideoOrImageModal: any;
  staffVideoOrImageModal: any;
  mediaType: string;
  momentRecurringLogsModal: any;
  momentData: string;
  selectedRecord: any;
  previewUrl: string | null = null;
  previewType: 'image' | 'video' | null = null;
  override record: Moment;

  constructor(
    protected route: ActivatedRoute,
    protected momentManager: MomentManager,
    protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected router: Router,
    protected commonService: CommonService,
    public authService: AuthService,
    protected translateService: TranslateService,
    public commonUtil: CommonUtil,
    private loadVideoFromUrl: LoadVideoFromUrl,
    private momentService: MomentService // <-- inject service
  ) {
    super(momentManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.record = new Moment();
    this.isDetailPage = true;
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.momentAssignedTrainingVideo = new bootstrap.Modal(
        document.getElementById('momentAssignedTrainingVideo')
      );
    }, 0)
    setTimeout(() => {
      this.momentVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('momentVideoOrImageModal')
      );
    }, 0)
    setTimeout(() => {
      this.staffVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('staffVideoOrImageModal')
      );
    }, 0)
    setTimeout(() => {
      this.momentRecurringLogsModal = new bootstrap.Modal(
        document.getElementById('momentRecurringLogsModal')
      );
    }, 0)
  }

  getMediaType(url) {
    if (url) {
      const extension = url.split(/[#?]/)[0].split('.').pop().trim();
      if (extension == "jpg" || extension == "jpeg" || extension == "png") {
        this.mediaType = "image"
      }
      if (extension == "mkv" || extension == "mp4" || extension == "avi" || extension == 'avi' || extension == "mov") {
        this.mediaType = "video";
      }
    }
  }

  openMomentRecurringModal(id: string) {
    AOS.init({ disable: true });
    this.momentData = id;
    if (!this.momentRecurringLogsModal) {
      const modalEl = document.getElementById('momentRecurringLogsModal');
      if (modalEl) {
        this.momentRecurringLogsModal = new bootstrap.Modal(modalEl);
        this.momentRecurringLogsModal.show();
      }
    } else {
      this.momentRecurringLogsModal.show();
    }
  }

  getInstructionByUserLanguage(record: any) {
    const instructionData = record.filter(data => data.LanguageId == this.authService.getUser().languageId);
    return instructionData[0].Instruction
  }

  getMomentMediaType(mediaUrl: any, type: string) {
    if (mediaUrl) {
      this.getMediaType(mediaUrl);
      if (this.mediaType == 'image') {
        if (type == 'title') {
          return "Open Image"
        }
        if (type == 'icon') {
          return 'image-solid.svg'
        }
      }
      if (this.mediaType == 'video') {
        if (type == 'title') {
          return "Watch Video"
        }
        if (type == 'icon') {
          return 'video-logo.svg'
        }
      }
    }
  }

  getImageWidthClass(mediaUrl: any) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      return true;
    } else {
      return false;
    }
  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.mediaUrl);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.momentVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.mediaUrl);
        this.loadingVideo = false;
      })
    }
  }

  openStaffImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.userVideo);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.staffVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.userVideo);
        this.loadingVideo = false;
      })
    }
  }

  watchVideo(record: any) {
    record = record.filter(data => data.languageId == this.authService.getUser().languageId)
    this.recordData = record[0];
    this.loadingVideo = true
    this.momentAssignedTrainingVideo.show();
    AOS.init({ disable: true });
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.videoUrl);
      this.loadingVideo = false;
    })
  }


  onFetchCompleted() {
    super.onFetchCompleted();
    this.filterParam.relationTable = "Moment";
    this.filterParam.relationId = this.record.id;
  }

  async openLogsModal(record: any) {
    this.selectedRecord = record;
    const filterParam = new FilterParam();
    filterParam.id = record.id;
    if (record.isRecurring && !record.recurringLogs) {
      const res = await this.momentService.fetchRecurringLogs(filterParam);
      record.recurringLogs = res.data;
    } else if (!record.isRecurring && !record.rejectedLogs) {
      const res = await this.momentService.fetchRejectedLogs(filterParam);
      record.rejectedLogs = res.data;
    }
    setTimeout(() => {
      const modal = (window as any).bootstrap ? (window as any).bootstrap.Modal.getOrCreateInstance(document.getElementById('logsModal')) : new bootstrap.Modal(document.getElementById('logsModal'));
      modal.show();
    }, 0);
  }

  showPreview(url: string) {
    const ext = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
      this.previewType = 'image';
    } else if (['mp4', 'webm', 'ogg', 'mov'].includes(ext)) {
      this.previewType = 'video';
    } else {
      this.previewType = null;
    }
    this.previewUrl = url;
    setTimeout(() => {
      const modal = new (window as any).bootstrap.Modal(document.getElementById('previewModal'));
      modal.show();
    }, 0);
  }
}
