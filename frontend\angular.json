{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"integrax": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/integrax", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/assets/favicon.ico", "src/assets"], "styles": ["src/assets/scss/plugins/jquery.mCustomScrollbar.min.css", "./node_modules/datatables.net-dt/css/jquery.dataTables.css", "./node_modules/ngx-spinner/animations/ball-clip-rotate.css", "src/assets/scss/dataTables.bootstrap5.min.css", "src/assets/scss/animate.min.css", "src/assets/scss/sweetalert.scss", "src/styles.scss", "node_modules/intl-tel-input/build/css/intlTelInput.css"], "scripts": ["./node_modules/jquery/dist/jquery.min.js", "./node_modules/@popperjs/core/dist/umd/popper.min.js", "./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "src/assets/js/plugins/jquery.mCustomScrollbar.concat.min.js", "node_modules/apexcharts/dist/apexcharts.min.js", "./node_modules/datatables.net/js/jquery.dataTables.js", "src/assets/js/plugins/dataTables.bootstrap5.min.js", "src/assets/js/plugins/moment.min.js", "src/assets/js/bootstrap-notify.min.js", "src/assets/js/sweetalert.min.js", "node_modules/intl-tel-input/build/js/intlTelInput.min.js", "node_modules/intl-tel-input/build/js/utils.js", "src/assets/js/ffmpeg.js"], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "integrax:build"}, "configurations": {"production": {"browserTarget": "integrax:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "integrax:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss", "node_modules/intl-tel-input/build/css/intlTelInput.css"], "scripts": [], "assets": ["src/assets/favicon.ico", "src/assets", "node_modules/intl-tel-input/build/js/intlTelInput.min.js"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "integrax-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "integrax:serve"}, "configurations": {"production": {"devServerTarget": "integrax:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "integrax"}