import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CourseTrainingsService } from './course-trainings-service';

@Injectable({
    providedIn: 'root'
})
export class CourseTrainingsManager extends BaseManager {

    constructor(protected courseTrainingsService: CourseTrainingsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(courseTrainingsService, loadingService, toastService);
    }
    
}
