<div class="site-customer-main-container px-0" data-aos="fade-up" data-aos-duration="1000">
    <div [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table allocated-users-list"
            [ngClass]="{'has-records': records.length > 0 }">
            <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th width="50"></th>
                        <th width="60" class="text-nowrap">{{'Course.fullName' | translate}}</th>
                        <th width="80" class="text-nowrap">{{'Course.email' | translate}}</th>
                        <th width="60" class="text-nowrap">{{'Course.phoneNumber' | translate}}</th>
                        <th width="60" *ngIf="!authService.isProgramAdmin()" class="text-nowrap">
                            {{'Course.programAdminName' |translate}}
                        </th>
                        <th width="40" class="text-nowrap">Account Status</th>
                        <th width="100" class="text-nowrap">
                            {{'Course.action' |translate}}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                        <td style="text-align: center;">
                            <img src="/assets/images/icons/menu/user-icon.svg" height="38" width="38" class="ms-3"
                                alt="">
                        </td>
                        <td class="pe-0">
                            <h5 class="code ms-2" title="View User Profile"
                                style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                [routerLink]="['/dashboard/program-detail/' +record?.id]">
                                {{record?.fullName}}
                            </h5>
                        </td>
                        <td>
                            {{record?.email}}
                        </td>
                        <td>
                            {{record?.phoneNumber}}
                        </td>
                        <td>
                            {{record?.programName}}
                        </td>
                        <td>
                            <div class="form-check form-switch">
                                <input (change)="activateDeactiveUserConfirmation($event,record)"
                                    class="form-check-input toggle-width" title="Activate/Deactivate User"
                                    type="checkbox" id="flexSwitchCheckChecked" [(ngModel)]="record.isActive"
                                    [ngModelOptions]="{standalone: true}">
                                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                            </div>
                        </td>
                        <td>
                            <div class="custom-action-button text-right mb-2 d-sm-flex">
                                <img src="/assets/images/icons/menu/manage-traning-icon.svg" title="Manage Training"
                                    class="width-25-px me-2 cursor-pointer"
                                    [routerLink]="['/dashboard/program-detail/'+ record.id]"
                                    [queryParams]="{ tab: 'training', 'username': record.fullName }"
                                    queryParamsHandling="merge" />
                                <img src="/assets/images/icons/menu/course-ims.svg" title="Manage Course"
                                    [routerLink]="['/dashboard/program-detail/' +record?.id]"
                                    class="width-25-px me-2 cursor-pointer"
                                    [queryParams]="{ tab: 'course', 'username': record?.fullName }"
                                    queryParamsHandling="merge" />
                                <i title="Edit" [routerLink]="['/dashboard/program-detail/'+record?.id]"
                                    class="bi bi-pencil font-21px me-2 cursor-pointer">
                                </i>
                                <i *ngIf="authService.isAccessible('ADMIN_MANAGE_PROGRAM','DeleteButton') && !isPlusButton"
                                    [class.disabled]="authService.isDisabled('ADMIN_MANAGE_PROGRAM','DeleteButton')"
                                    title="Delete" (click)="removeUserRecord(record?.id)"
                                    class="bi bi-trash font-21px cursor-pointer"></i>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</div>