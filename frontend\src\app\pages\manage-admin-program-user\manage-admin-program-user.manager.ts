import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageAdminProgramUserService } from './manage-admin-program-user.service';

@Injectable({
    providedIn: 'root'
})
export class ManageAdminProgramUserManager extends BaseManager {

    constructor(protected manageAdminProgramUserService: ManageAdminProgramUserService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageAdminProgramUserService, loadingService, toastService);
    }
}
