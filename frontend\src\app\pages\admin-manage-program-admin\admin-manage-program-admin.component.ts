import { Compo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Content } from 'src/app/models/content';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { FilterParam } from 'src/app/models/filterparam';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { AdminManageProgramAdminService } from './admin-manage-program-admin.service';
import { AdminManageProgramAdminManager } from './admin-manage-program-admin.manager';
import { ManageAdminTrainingComponent } from '../manage-admin-training/manage-admin-training.component';
import { ManageAdminCourseComponent } from '../manage-admin-course/manage-admin-course.component';
import AOS from 'aos';
import { Course } from 'src/app/models/course';
import { UsersService } from 'src/app/services/users.service';
import { RouteDataService } from 'src/app/shared/title.service';
import { Users } from 'src/app/models/users';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-admin-manage-program-admin',
  templateUrl: './admin-manage-program-admin.component.html',
  styleUrls: ['./admin-manage-program-admin.component.scss']
})
export class AdminManageProgramAdminComponent extends BaseListServerSideComponent implements OnInit {
  @ViewChild(ManageAdminTrainingComponent) manageAdminTrainingComponent: ManageAdminTrainingComponent;
  @ViewChild(ManageAdminCourseComponent) manageAdminCourseComponent: ManageAdminCourseComponent;

  userDetails: boolean = true;
  userDetailsTabs: string = "profile";
  currentActiveTab: string;
  isTrainingCsvExport: boolean = false
  adminFilterProgramDetail: any;
  filterParam: FilterParam;
  courseFilterParam = new FilterParam;
  selectedCourseId: any;
  moment: any = moment;
  selectedCourseUserId: string;
  selectedCourseUser: any;
  publishCourseList: any[];
  dropdownSettings = {};
  dropdownSettingsUsers = {};
  searchTraining: any;
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  recordData: any;
  contentTypes: Content[];
  learningSeries: CourseLearningSeries[];
  trainingFilterData: RestResponse;
  videos: any[] = [];
  isViewAllTableVisible: boolean = false;
  records: CourseTrainings[];
  username: any;
  searchText: string;
  strProgramAdmin: any;
  courses: Course;
  user: Users;

  constructor(private route: ActivatedRoute, public authService: AuthService, protected router: Router, private zone: NgZone, protected toastService: ToastService,
    protected loadingService: LoadingService, private usersService: UsersService,public routeDataService: RouteDataService,
    protected adminManageProgramAdminService: AdminManageProgramAdminService, protected adminManageProgramAdminManager: AdminManageProgramAdminManager,
    protected commonService: CommonService, public commonUtil: CommonUtil) {
    super(adminManageProgramAdminManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.currentActiveTab = params.tab ?? 'profile';
    });
    this.filterParam = new FilterParam();
    this.courseFilterParam = new FilterParam();
    this.setDropdownSettings();
    this.setDropdownSettingsUsers();
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseTrainings>();
    this.contentTypes = new Array<Content>();
    this.userDetailsTabs = this.currentActiveTab ? this.currentActiveTab : "profile";
    this.strProgramAdmin = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.fetchAssociatedData();
    this.fetchCoursesForFilters();
    this.fetchUserDetail()
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    if (this.userDetailsTabs === "training") {
      this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    }
    this.filterChildRecords();
  }


  // onClickTrainingFilter(valid) {
  //   this.filterTrainingModal.hide();
  //   this.filterChildRecords();
  //   // this.onCancel();
  // }


  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  setDropdownSettingsUsers() {
    this.dropdownSettingsUsers = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  onClickUserDetailsTab(name: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          // queryParams: { tab: name },
          queryParamsHandling: 'merge'
        });
    });
    this.userDetailsTabs = name;
  }

  // filter common
  onClickProgramDetailFilter(valid) {
    this.adminFilterProgramDetail.hide();
    // this.filterChildRecords();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('adminFilterProgramDetail');
      if (modalElement) {
        this.adminFilterProgramDetail = new bootstrap.Modal(modalElement);
      }
    }, 0);
  }

  resetFilter() {
    delete this.courseFilterParam.courseTitle;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.status;
    this.adminFilterProgramDetail.hide();
    this.filterChildRecords();
  }

  openProrgamDetailFilter() {
    this.fetchCoursesForFilters();
    AOS.init({ disable: true });
    this.adminFilterProgramDetail.show();
  }


  async fetchCoursesForFilters() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.adminManageProgramAdminService.getCourseFilter(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchAssociatedData() {
    this.trainingFilterData = await this.adminManageProgramAdminService.getTrainingFilterData(null);
  }

  async fetchAllTrainings() {
    try {
      const filterParam: FilterParam = new FilterParam();
      filterParam.sortBy = true;
      this.loadingService.show();
      const response: RestResponse = await this.adminManageProgramAdminService.getTrainingFilterData(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.videos = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  filterChildRecords() {
    switch (this.currentActiveTab) {
      case 'training':
        this.manageAdminTrainingComponent.refreshRecord();
        break;

      case 'course':
        this.manageAdminCourseComponent.refreshRecord();
        break;
    }
  }

  exportTrainingHistroyCsv() {
    switch (this.currentActiveTab) {
      case 'training':
        this.manageAdminTrainingComponent.exportTrainingHistroyCsv();
        break;

      case 'course':
        this.manageAdminCourseComponent.exportTrainingHistroyCsv();
        break;
    }
  }

  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  // filter common
  onClickCourseDetailFilter(valid) {
    this.adminFilterProgramDetail.hide();
    this.filterChildRecords();
  }

  openInviteCourseUserModal(){
    this.manageAdminCourseComponent.openInviteCourseUserModal();
  }

  async fetchUserDetail() {
    try {
      const id = this.route.snapshot.paramMap.get('id')
      const response: RestResponse = await this.usersService.fetch(id).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.request.isNewRecord = false;
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      this.setbrreadCrumbTitle();
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setbrreadCrumbTitle() {
    this.routeDataService.setData(this.router.url, this.user.fullName);
  }

}
