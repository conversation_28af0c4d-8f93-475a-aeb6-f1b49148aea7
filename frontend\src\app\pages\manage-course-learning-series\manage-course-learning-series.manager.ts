import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageCourseLearningSeriesService } from './manage-course-learning-series.service';

@Injectable({
    providedIn: 'root'
})
export class ManageCourseLearningSeriesManager extends BaseManager {

    constructor(protected manageCourseLearningSeriesService: ManageCourseLearningSeriesService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageCourseLearningSeriesService, loadingService, toastService);
    }
}
