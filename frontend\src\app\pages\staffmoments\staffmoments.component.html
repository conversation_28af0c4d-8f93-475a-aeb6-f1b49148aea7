<div *ngIf="!currentAssignedVideoData" class="staffmoments-bg mb-4">
  <div class="staffmoments container loadMore">
    <div class="text-center">
      <button [routerLink]="['/staff-add-moment']" type="button"
        class="btn me-2 mb-3 filter-buttton btn-secondary text-light btn-lg">
        <img class="moment-icon" src="/assets/images/icons/menu/add_icon.svg" class="me-3" alt="">ADD NEW MOMENT
      </button>
      <button (click)="openFilterMomentModal()" type="button" class="btn filter-buttton me-2 bg-dark text-light btn-lg">
        <img src="/assets/images/icons/menu/filter.svg" class="moment-icon me-2 img-fluid" alt="">FILTER
      </button>
    </div>
    <div *ngIf="noMomentsDataFound" class="staffmoments-content text-center p-5">
      No Moments Found
    </div>
    <div class="loadMore" scrollTracker (scrollingFinished)="onScrollingFinished()">
      <div *ngFor="let data of moments" class="staffmoments-content"
        [ngClass]="{'recurring-moment-cls': data.isRecurring}">
        <div class="row">
          <div class="col-10">
            <button
              [ngClass]="{'confirmation-icon': data.status == MY_CONSTANT.MOMENT_STATUS.OPEN,'waiting-icon': data.status == MY_CONSTANT.MOMENT_STATUS.WAITING_FOR_APPROVAL,'close-icon': data.status == MY_CONSTANT.MOMENT_STATUS.CLOSED}">
              <img
                [src]="data.status == MY_CONSTANT.MOMENT_STATUS.OPEN ? '/assets/images/icons/menu/confirmation.svg' : data.status == MY_CONSTANT.MOMENT_STATUS.WAITING_FOR_APPROVAL ? '/assets/images/icons/menu/refresh.svg' : data.status == MY_CONSTANT.MOMENT_STATUS.CLOSED && '/assets/images/icons/menu/cancel.svg'"
                class="me-1" alt="">{{data.status == MY_CONSTANT.MOMENT_STATUS.OPEN ? MY_CONSTANT.MOMENT_STATUS.OPEN :
              data.status == MY_CONSTANT.MOMENT_STATUS.WAITING_FOR_APPROVAL ? 'WAITING':
              data.status == MY_CONSTANT.MOMENT_STATUS.CLOSED && MY_CONSTANT.MOMENT_STATUS.CLOSED}}
            </button>
          </div>
          <div class="col-2">
            <i (click)="removeMoment(data.id)"
              *ngIf=" data.status ==  MY_CONSTANT.MOMENT_STATUS.OPEN && authService.getUser().id == data.createdBy"
              class="bi bi-trash cursor-pointer" style="font-size: 25px;"></i>
          </div>
          <div class="col-12 mt-5">
            <h4 class="fw-bold">{{data.farmIdDetail?.name}}</h4>
            <p class="text-secondary">{{moment(data.createdOn).format('DD-MM-YYYY')}}</p>
          </div>
          <div class="col-6">
            <h6 class="content-heading">Issue Type</h6>
            <p class="fw-bold content-msg">{{data.type}}</p>
          </div>
          <!-- <div class="col-4">
                        <h6 class="content-heading">Site Name</h6>
                        <p class="fw-bold content-msg">Roman`s Farm</p>
                    </div> -->
          <div class="col-6 text-end">
            <h6 class="content-heading">Video Assigned</h6>
            <a (click)='watchVideo(data)'
              *ngIf="data.assignTrainingId && data?.TrainingIdDetail?.length else noTrainingAssigned"
              class="text-decoration-underline text-secondary white-space"><img
                src="/assets/images/icons/menu/video-logo.svg" class="me-1" alt=""> Watch
              Video</a>
            <ng-template #noTrainingAssigned>-</ng-template>
          </div>
          <div class="col-6">
            <h6 class="content-heading">Moment Video/Image</h6>
            <a class="text-decoration-underline text-secondary" (click)="openImageOrVideo(data)"><img
                [src]="'/assets/images/icons/menu/'+ this.getMomentMediaType(data, 'icon')" class="me-1"
                [ngClass]="{'width-27px': this.getImageWidthClass(data)}" alt="">{{this.getMomentMediaType(data,
              "title")}}</a>
          </div>
          <div *ngIf="!data.assignTrainingId && data?.TrainingIdDetail.length == 0" class="col-6 text-end">
            <h6 class="content-heading">Staff capture Video/Image</h6>
            <div [ngClass]="{'d-flex align-items-center justify-content-end': momentIds.includes(data.id)}">
              <div>
                <label [for]="'file-input'+data.id" href="" class="fw-bold cursor-pointer white-space"><img
                    [ngClass]="{'width-25-px': data.status != MY_CONSTANT.MOMENT_STATUS.OPEN}"
                    [src]="'/assets/images/icons/menu/'+ this.getUploadIconStatus(data)" class="me-2" alt="">{{
                  momentIds.includes(data.id) ? 'Uploading' : data.status == MY_CONSTANT.MOMENT_STATUS.OPEN ? 'Upload
                  File' : 'File
                  Uploaded'}}</label>
              </div>
              <div>
                <div *ngIf="momentIds.includes(data.id)" class="spinner-border ms-2" role="status"
                  style="width: 1.5rem; height: 1.5rem">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
            <input class="d-none" *ngIf="data.status == MY_CONSTANT.MOMENT_STATUS.OPEN && !momentIds.includes(data.id)"
              (change)="uploadVideoOrImage(data.id,$event)" [name]="data.id+'Video'" [id]="'file-input'+data.id"  type="file" accept="video/*, image/jpg, image/jpeg, image/png" />
          </div>
          <div class="col-12 mt-5">
            <button (click)="viewInstructionsOrDesc(data, 'description')" type="button"
              class="manage-view-button me-2 btn text-dark btn-lg">
              <i class="moment-icon bi bi-eye"></i>VIEW DESCRIPTION
            </button>
          </div>
          <div class="col-12 mt-2">
            <button (click)="viewInstructionsOrDesc(data,'instruction')"
              *ngIf="this.getInstructionCheckByStaffLanguage(data)" type="button"
              class="manage-view-button me-2 btn text-dark btn-lg">
              <i class="moment-icon bi bi-eye"></i>VIEW INSTRUCTION
            </button>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="loadMore" class="mt-4">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</div>
<app-reel-view-videos acceptMediaType="audio/*, image/png, image/jpg, image/jpeg, video/mp4, video/mkv"
  (previousPage)="backToMomentsList($event)" fileTypeMessage="Only Mkv, Mp4, Jpeg , Jpg, Png, files are allowed"
  allowedExtension="mkv,mp4,jpg,jpeg,png" VideosType="moment" *ngIf="currentAssignedVideoData"
  [currentVideoData]="currentAssignedVideoData" [trainingData]="momentDataOnlyWithAssignedVideos">
</app-reel-view-videos>
<div class="modal fade" id="staffInstructionsOrDescriptionModal" aria-hidden="true"
  aria-labelledby="staffInstructionsOrDescriptionModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="instructionsModalLabel">{{recordData?.type == 'instruction' ? 'View Instruction' :
          recordData?.type == 'description' && 'View Description'}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body"
        *ngIf="staffInstructionsOrDescriptionModal && staffInstructionsOrDescriptionModal._isShown">
        {{recordData?.data}}
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="staffMomentAssignedTrainingVideo" aria-hidden="true"
  aria-labelledby="staffMomentAssignedTrainingVideo" tabindex="-1">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staffMomentAssignedTrainingVideoLabel">{{recordData?.videoTitle}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" *ngIf="staffMomentAssignedTrainingVideo && staffMomentAssignedTrainingVideo._isShown">
        <div *ngIf="loadingVideo" class="loading-container-video-training">
          <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
          <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
          id="staff-video"></video>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="staffMomentVideoOrImageModal" aria-hidden="true"
  aria-labelledby="staffMomentVideoOrImageModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staffMomentAssignedTrainingVideoLabel">{{recordData?.farmIdDetail?.name}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" *ngIf="staffMomentVideoOrImageModal && staffMomentVideoOrImageModal._isShown">
        <div *ngIf="recordData?.mediaType == 'video'">
          <div *ngIf="loadingVideo" class="loading-container-video-training">
            <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
            <div class="spinner-border text-light" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
          <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
            id="staff-video"></video>
        </div>
        <div *ngIf="recordData?.mediaType == 'image'">
          <img class="img-fluid" [src]="recordData?.mediaUrl" />
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="filterMomentModal" tabindex="-1" aria-labelledby="filterMomentModalLabel"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="filterMomentModalLabel">Filter Moment</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div *ngIf="filterMomentModal && filterMomentModal._isShown" class="modal-body">
        <form #momentFilterForm="ngForm" novalidate="novalidate">
          <div class="mb-3">
            <mat-form-field class="example-form-field w-100">
              <mat-label>Search...</mat-label>
              <input [(ngModel)]="filterParam.searchText" name="searchMoment" matInput type="text">
              <button mat-button *ngIf="filterParam.searchText" matSuffix mat-icon-button aria-label="Clear"
                (click)="filterParam.searchText=''">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <div class="mb-3">
            <mat-form-field class="accent w-100">
              <mat-label>Select Type</mat-label>
              <mat-select name="issue_type" [(ngModel)]="filterParam.type">
                <mat-option value="Physical">Physical</mat-option>
                <mat-option value="Procedural">Procedural</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-3">
            <mat-form-field class="accent w-100">
              <mat-label>Select Status</mat-label>
              <mat-select name="moment_status" [(ngModel)]="filterParam.status">
                <mat-option value="WAITING_FOR_APPROVAL">WAITING FOR APPROVAL</mat-option>
                <mat-option value="OPEN">OPEN</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="mb-3">
            <mat-form-field class="accent w-100">
              <mat-label>Select Moment Type </mat-label>
              <mat-select name="moment_type" [(ngModel)]="filterParam.isRecurring">
                <mat-option value="true">Recurring Moment</mat-option>
                <mat-option value="false">Non Recurring Moment</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
            [fromDateInput]="filterParam.startDate" [toDateInput]="filterParam.endDate"></app-date-range-filter>
          <div class="modal-footer">
            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
            <button (click)="onClickMomentFilter(momentFilterForm.form.valid)" type="button"
              class="btn btn-primary">Filter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
