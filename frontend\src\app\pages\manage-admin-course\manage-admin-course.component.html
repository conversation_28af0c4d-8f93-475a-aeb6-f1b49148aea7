<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000" style="padding: 10px 0px;">
    <div class=" allocated-users-list" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table mt-0" [ngClass]="{'has-records':records.length>0}">
            <table class="table mt-0" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <tbody>
                    <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                        <td (click)="openCourseDetailPage(record)" class="pe-0">
                            <h5 class="code ms-2" title="Course Detail">
                                {{record.code}}</h5>
                        </td>
                        <td>
                            <div class="vertical-line" style="border:1px solid #1681FF">
                            </div>
                        </td>
                        <td width="60" style="white-space: nowrap;">
                            <div class="title" [attr.title]="record.title">{{record.title}}</div>
                        </td>
                        <td width="80" style="white-space: nowrap;">
                            <div class="title">{{moment(record.updatedOn).format('DD-MM-YYYY')}}</div>
                        </td>
                        <td></td>
                        <td>
                            <div class="form-check form-switch">
                                <input (change)="updateIspublish($event,record)" class="form-check-input toggle-width"
                                    type="checkbox" id="flexSwitchCheckChecked" [(ngModel)]="record.isPublish"
                                    [ngModelOptions]="{standalone: true}">
                                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                            </div>
                        </td>
                        <td width="700"></td>
                        <td width="40" class="text-right">
                            <div class="d-flex">
                                <button [routerLink]="['/dashboard/admin/program/course/trainings/'+ record.id]"
                                    class="btn manage-filter-buttton bg-dark text-light btn-lg font-15px filter-button-cls">
                                    view trainings
                                </button>
                                <div class="vertical-line">
                                </div>
                            </div>
                        </td>
                        <td width="60" class="text-right">
                            <div style="white-space: nowrap;" class="cursor-pointer title"
                                [routerLink]="['/dashboard/admin/program/course/users/' + record.id]">
                                <i class="bi bi-person fw-bold"></i>
                                <span class="fw-bold mx-1">{{record.allocatedUsersCount}}</span>Users Allocated
                            </div>
                        </td>

                        <td width="60" class="text-center custom-action-button text-right">
                            <div *ngIf="!assignVideoFromMoment">
                                <div class="d-flex justify-content-end">
                                    <i *ngIf="authService.isAccessible('MANAGE_COURSE_ADMIN','EditButton') && !isDetailPage && !isPlusButton"
                                        [class.disabled]="authService.isDisabled('MANAGE_COURSE_ADMIN','EditButton')"
                                        title="Edit" (click)="editRecord(record.id)"
                                        class="bi bi-pencil font-21px me-3 cursor-pointer">
                                    </i>
                                    <i *ngIf="authService.isAccessible('MANAGE_COURSE_ADMIN','DeleteButton') && !isPlusButton"
                                        [class.disabled]="authService.isDisabled('MANAGE_COURSE_ADMIN','DeleteButton')"
                                        title="Delete" (click)="remove(record.id)"
                                        class="bi bi-trash font-21px cursor-pointer">
                                    </i>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!-- modal invite course user -->
    <div class="modal fade" id="adminInviteUserManageCourseModal" tabindex="-1"
        aria-labelledby="adminInviteUserManageCourseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0" style="padding-top: 30px;
                        margin: auto 15px;">
                    <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                        (click)="closeInviteModal()"></button>
                </div>
                <div *ngIf="adminInviteUserManageCourseModal && adminInviteUserManageCourseModal._isShown"
                    class="modal-body" style="padding: 10px 50px;">
                    <div class="modal-content-inside">
                        <h5 class="modal-title fw-bold" id="adminInviteUserManageCourseModalLabel">Invite User for
                            Course</h5>
                        <p class="modal-heading pt-1" id="adminInviteUserManageCourseModalLabel">
                            Please make sure you fill in all the fields before you click on the Send Invite button
                        </p>
                    </div>
                    <form #userInviteForm="ngForm" novalidate="novalidate">
                        <div class="form-floating">
                            <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                                [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                    name="selectedCourseUser" clearable="false" [items]="publishCourseList"
                                    bindLabel="title" bindValue="id" (change)="selectInviteUserCourse($event)"
                                    class="custom-multiselect form-control padding-bottom-8"
                                    [(ngModel)]="selectedCourseUserId" #selectedCourseUser="ngModel"
                                    [searchable]="false">
                                    <!-- [disabled]="selectedCourseUserId" -->
                                </ng-select>
                            </div>
                            <label for="selectedCourseUser">
                                {{"Course.chooseCourse" | translate}}
                            </label>
                        </div>
                        <div class="form-floating mb-4">
                            <input maxlength="20" [ngClass]="{'is-invalid':!username.valid && onClickValidation}"
                                class="form-control b-r-8" type="text" name="username" #username="ngModel" required
                                [(ngModel)]="courses.username" placeholder="{{'Course.name' | translate}}">
                            <label for="floatingInput">{{"Course.name" | translate}}</label>
                        </div>
                        <div class="form-floating mb-2">
                            <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}"
                                class="form-control b-r-8" type="email" name="email" #email="ngModel"
                                [(ngModel)]="courses.email" required placeholder="{{'Course.email' | translate}}"
                                pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                            <label for="floatingInput">{{"Course.email" | translate}}</label>
                        </div>
                        <div *ngIf="errorMessage" style="color: red;">
                            {{ errorMessage }}
                        </div>
                        <div class="text-end mb-2 mt-2">
                            <button class="btn" (click)="insertData(userInviteForm)"
                                style="border: 1px solid #000; border-style: dashed; font-size: 20px;"
                                title="Add username or email">
                                <i class="bi bi-plus-lg" style="color: #000;"></i>
                            </button>
                        </div>
                        <div class="mb-2" *ngFor="let insert of insertedData; let i = index">
                            <div class="d-flex justify-content-between align-middle"
                                style="border: 1px solid #1681FF; border-radius: 10px; padding: 10px;">
                                <h5 class="text-secondary mt-1">
                                    {{insert.username}} ( {{insert.email}} )
                                </h5>
                                <div class="mt-1" (click)="removeData()">
                                    <img src="/assets/images/icons/menu/remove-user.svg" class="me-2 img-fluid text-end"
                                        alt="">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
                            <button (click)="onClickInviteUserCourse()" type="button"
                                [disabled]="insertedData.length===0 || selectedCourseUserId == null || selectedCourseUserId == undefined"
                                class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                                INVITE
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>