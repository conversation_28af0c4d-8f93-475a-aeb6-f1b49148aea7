import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { FarmAdminService } from './farm-admin.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class FarmAdminManager extends BaseManager {

    constructor(private readonly farmAdminService: FarmAdminService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(farmAdminService, loadingService, toastService);
    }

}
