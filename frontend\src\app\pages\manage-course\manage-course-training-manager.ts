import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ManageCourseTrainingService } from './manage-course-training-service';

@Injectable({
    providedIn: 'root'
})
export class ManageCourseTrainingManager extends BaseManager {

  constructor(protected manageCourseTrainingService: ManageCourseTrainingService, protected loadingService: LoadingService, protected toastService: ToastService) {
    super(manageCourseTrainingService, loadingService, toastService);
  }


}
