import { Compo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Course } from 'src/app/models/course';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageProgramUserManager } from '../manage-program-user/manage-program-user.manager';
import { ManageProgramUserService } from '../manage-program-user/manage-program-user.service';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import AOS from 'aos';
import { UserPendingListingComponent } from '../user-pending-listing/user-pending-listing.component';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-course-access',
  templateUrl: './manage-course-access.component.html',
  styleUrls: ['./manage-course-access.component.scss']
})
export class ManageCourseAccessComponent extends BaseListServerSideComponent implements OnInit {
  userDetailsTabs: string = "enrolled";
  // @ViewChild(UserEnrolledListComponent) userEnrolledListComponent: UserEnrolledListComponent;
  // @ViewChild(UserInvitedListingComponent) userInvitedListingComponent: UserInvitedListingComponent;
  @ViewChild(UserPendingListingComponent) userPendingListingComponent: UserPendingListingComponent;

  currentActiveTab: string;
  userDetails: boolean = true;
  filterParam: FilterParam;
  searchText: string;
  filterTrainingModal: any;
  selectedCourseUserId: string;
  selectedCourseUser: any;
  courses: Course;
  selectedCourseId: any;
  moment: any = moment;
  publishCourseList: any[];

  constructor(private route: ActivatedRoute, public authService: AuthService, protected router: Router, private zone: NgZone, protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected manageProgramUserService: ManageProgramUserService, protected manageProgramUserManager: ManageProgramUserManager,
    protected commonService: CommonService, public commonUtil: CommonUtil) {
    super(manageProgramUserManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.filterParam = new FilterParam();
    this.route.queryParams.subscribe(params => {
      this.currentActiveTab = params.tab ?? 'enrolled';
    });
    this.publishCourseList = new Array<any>();
    this.userDetailsTabs = this.currentActiveTab ? this.currentActiveTab : "enrolled";
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    // this.fetchPublishRecords();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    if (this.userDetailsTabs === "enrolled") {
      this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    }
    // switch(this.currentActiveTab)
    this.filterChildRecords();

  }

  onClickTrainingFilter(valid) {
    this.filterTrainingModal.hide();
    this.filterChildRecords();
    // this.onCancel();
  }

  // filterChildRecords() {
  //   this.userEnrolledListComponent.refreshRecord();
  // }

  selectInviteUserCourse(event: any): void {
    if (event) {
      // this.selectedInviteUserCourse = event.title;
      this.selectedCourseUser = event.title;
      this.selectedCourseUserId = event ? event.id : null;
    }
  }

  filterChildRecords() {
    switch (this.currentActiveTab) {
      // case 'enrolled':
      //   // this.userEnrolledListComponent.fetchEnrolledCourseRecords(this.filterParam);
      //   this.userEnrolledListComponent.refreshRecord();
      //   break;
      // case 'invited':
      //   this.userInvitedListingComponent.refreshRecord();
      //   // this.userInvitedListingComponent.fetchInvitedCourseRecords(this.filterParam);
      //   break;
      case 'pending':
        this.userPendingListingComponent.refreshRecord();
        // this.userPendingListingComponent.fetchPendingCourseRecords(this.filterParam);
        break;
    }
  }

  //filter
  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('filterTrainingModal');
      if (modalElement) {
        this.filterTrainingModal = new bootstrap.Modal(modalElement);
      }
    }, 0);
  }

  resetFilter() {
    delete this.filterParam.course;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    this.selectedCourseUserId = null;
    this.filterTrainingModal.hide();
    this.filterChildRecords();
    // this.onCancel();
  }



  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  // onCancel() {
  //   this.request.loadEditPage = false;
  //   if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
  //     this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
  //       dtInstance.destroy();
  //     });
  //   }
  //   this.init();
  // }


  openFilterTrainingModal() {
    // if (this.searchTraining) {
    //   this.searchTraining = "";
    //   delete this.filterParam.searchCommonTitle;
    //   this.onCancel();
    // }
    this.fetchCoursesForFilters();
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  async fetchCoursesForFilters() {
    try {
      var course = new Course();
      course.id = this.selectedCourseId;
      this.loadingService.show();
      const response: RestResponse = await this.manageProgramUserService.getCourseFilter(null);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // open profile page
  openUserProfilePage() {
    this.router.navigate(['/dashboard/program-admin/profile-setting']);
  }

  openInvitedTab() {
    this.router.navigate(['/dashboard/program-admin/user/invited']);
  }

  onClickUserDetailsTab(name: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          queryParams: { tab: name },
          queryParamsHandling: 'merge'
        });
    });
    this.userDetailsTabs = name;
    this.filterParam = new FilterParam();
  }


}
