import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProgramUserTrainingDetailBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const trainingId: string | null = route.paramMap.get("id");

    return of([
      {
        title: "Dashboard", link: "/dashboard", active: false
      },
      {
        title: "Manage Training", link: "/dashboard/program-admin/trainings", active: false
      },
      // {
      //   title: "Training Detail", link: "/dashboard/program-admin/training/detail/" + trainingId, active: true
      // }
    ])
  }



}
