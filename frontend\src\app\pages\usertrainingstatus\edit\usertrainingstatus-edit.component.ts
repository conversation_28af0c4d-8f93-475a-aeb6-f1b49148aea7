import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMyDpOptions } from 'mydatepicker';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { UserTrainingStatus } from '../../../models/usertrainingstatus';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { UserTrainingStatusManager } from '../usertrainingstatus.manager';

import { UsersManager } from '../../users/users.manager';
import { Users } from '../../../models/users';
import { FarmManager } from '../../farm/farm.manager';
import { Farm } from '../../../models/farm';
import { TrainingManager } from '../../training/training.manager';
import { Training } from '../../../models/training';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
declare const $: any;

@Component({
  selector: 'app-usertrainingstatus-edit',
  templateUrl: './usertrainingstatus-edit.component.html',
  styleUrls: ['./usertrainingstatus-edit.component.scss']
})

export class UserTrainingStatusEditComponent extends BaseEditComponent implements OnInit {
   public myDatePickerOptions: IMyDpOptions;
  public userTrainingStatus: UserTrainingStatus;
	public users:Users[];
	public farms:Farm[];
	public trainings:Training[];
  
  constructor(protected route: ActivatedRoute, protected usertrainingstatusManager: UserTrainingStatusManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private usersManager:UsersManager, private farmManager:FarmManager, private trainingManager:TrainingManager
  			  ,public commonUtil:CommonUtil ) {
    	super(usertrainingstatusManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.userTrainingStatus = new UserTrainingStatus();
  	this.userTrainingStatus.isActive=true;   
    this.setRecord(this.userTrainingStatus);
	
     this.myDatePickerOptions = {
      dateFormat:Constant.MY_DATE_PICKER.DATE_TYPE
     } 
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.users = new Array<Users>();
  	this.farms = new Array<Farm>();
  	this.trainings = new Array<Training>();
    this.init();
  }

  onFetchCompleted() {
  	this.userTrainingStatus = UserTrainingStatus.fromResponse(this.record);
    this.setRecord(this.userTrainingStatus);
  }

  
  
  async fetchAssociatedData() {
	this.users = await this.usersManager.fetchAllData(null);       		
	this.farms = await this.farmManager.fetchAllData(null);       		
	this.trainings = await this.trainingManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const userIdId: string = this.route.snapshot.queryParamMap.get('Users');
		if (userIdId){
			this.onAssociatedValueSelected({"id":userIdId},'userTrainingStatusUserIdSelect');
		}
    	const farmIdId: string = this.route.snapshot.queryParamMap.get('Farm');
		if (farmIdId){
			this.onAssociatedValueSelected({"id":farmIdId},'userTrainingStatusFarmIdSelect');
		}
    	const traningIdId: string = this.route.snapshot.queryParamMap.get('Training');
		if (traningIdId){
			this.onAssociatedValueSelected({"id":traningIdId},'userTrainingStatusTraningIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/user-training-status');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userTrainingStatusUserIdSelect') || this.request.popupId==='userTrainingStatusUserIdPopup'){
			this.userTrainingStatus.userId = selectedRecord.id;
			this.checkConditionToReload(this.users, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userTrainingStatusFarmIdSelect') || this.request.popupId==='userTrainingStatusFarmIdPopup'){
			this.userTrainingStatus.farmId = selectedRecord.id;
			this.checkConditionToReload(this.farms, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userTrainingStatusTraningIdSelect') || this.request.popupId==='userTrainingStatusTraningIdPopup'){
			this.userTrainingStatus.traningId = selectedRecord.id;
			this.checkConditionToReload(this.trainings, selectedRecord);
			return;
	    }
  	
	 }
}
