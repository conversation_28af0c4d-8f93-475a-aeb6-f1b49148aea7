import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Farm } from 'src/app/models/farm';
import { FilterParam } from 'src/app/models/filterparam';
import { Users } from 'src/app/models/users';
import { AccountService } from 'src/app/services/account.service';
import { LoadingService } from 'src/app/services/loading.service';
import { UsersService } from 'src/app/services/users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { FarmService } from '../farm/farm.service';
import { LanguageManager } from '../language/language.manager';
import { FormControl, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
@Component({
  selector: 'app-register-user',
  templateUrl: './register-user.component.html',
  styleUrls: ['./register-user.component.scss']
})
export class RegisterUserComponent implements OnInit {
  onClickValidation: boolean = false;
  user: Users;
  languages: any = [];
  farms: any = [];
  dropdownSettings = {};
  selectedFarms: any = [];
  comparableField: any;
  buttonName: string = "CREATE ACCOUNT"
  constructor(private usersService: UsersService, private loadingService: LoadingService, private toastService: ToastService, private accountService: AccountService,
    private languageManager: LanguageManager, private router: Router, private farmService: FarmService, private titleService: Title) { }

  async ngOnInit() {
    this.user = new Users();
    this.titleService.setTitle("Create new account");
    // this.languages = await this.languageManager.fetchAllData(null);
    this.setDropdownSettings();
  }

  hasError(event: any) {
  }

  async fetchFarms(companyCode) {
    try {
      let param = new FilterParam();
      param.companyCode = companyCode;
      const response: RestResponse = await this.usersService.GetAllFarms(param);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.farms = response.data;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  onItemSelect(item: any) {
    if (this.user.userFarmMapping && this.user.userFarmMapping.length > 0) {
      if (!this.user.userFarmMapping.some(el => el.farmId === item.id)) {
        this.setItemsData(item);
      }
    } else {
      this.setItemsData(item);
    }
  }

  setItemsData(item) {
    this.user.userFarmMapping = [
      ...this.user.userFarmMapping,
      {
        farmId: item.id,
      },
    ];
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  onDeSelect(item: any) {
    let deletedUserFarms = []
    if (this.user.userFarmMapping && this.user.userFarmMapping.length > 0) {
      deletedUserFarms = this.user.userFarmMapping.filter(farmData => farmData.farmId !== item.id);

    }
    this.user.userFarmMapping = deletedUserFarms;
  }

  telInputObject(event: any) {
    event.setCountry('sg');
  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  async save(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return;
    }
    if (this.comparableField && this.comparableField !== this.user.password) {
      this.onClickValidation = true;
      return false
    }
    this.buttonName = "PLEASE WAIT..."
    const { isActive, isDeleted,  userFarmMapping, location, ...rest } = this.user
    
    
    
    this.loadingService.show();
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.accountService.staffRegister({address:location,...rest});
      
      this.loadingService.hide();
      if (!response.status) {
        this.buttonName = "CREATE ACCOUNT"
        this.toastService.error(response.message);
        return;
      }
      this.router.navigate(['/verify-otp'], { queryParams: { uc: response.data.uniqueCode } })
      this.toastService.success(response.message);
    } catch (e) {
      this.buttonName = "CREATE ACCOUNT"
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  async fetchFarmOnCompanyCode(companyCode) {
    if(CommonUtil.isNullOrUndefined(companyCode) || companyCode == null || companyCode == "") {
      return;
    }
    this.fetchFarms(companyCode);
  }

}
