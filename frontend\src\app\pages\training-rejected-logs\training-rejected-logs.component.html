<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th>{{'Training.title' | translate}}</th>
                        <th style="width: 150px;">{{'Training.date' | translate}}</th>
                        <th style="width: 110px; text-wrap: nowrap;">{{'Training.UserVideo' | translate}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>{{getRejectedLogsCountsData?.trainingDetail?.title}}</td>
                        <td>{{moment(record.createdOn).format('DD/MM/YYYY')}}</td>
                        <td>
                            <a class="text-decoration-underline text-secondary d-block height-30px"
                                (click)="openImageOrVideo(record)"><img
                                    [src]="'/assets/images/icons/menu/video-logo.svg'" class="me-1" alt="">Watch
                                Video
                            </a>
                        </td>
                    </tr>
                    <tr *ngIf="records.length===0">
                        <td class="text-center" colspan="5">
                            {{"COMMON.NORECORDS" | translate}}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="modal fade" id="rejectedTrainingLogsVideoModal" aria-hidden="true"
                aria-labelledby="momentVideoOrImageModalLabel" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body"
                            *ngIf="rejectedTrainingLogsVideoModal && rejectedTrainingLogsVideoModal._isShown">
                            <div>
                                <div *ngIf="loadingVideo" class="loading-container-video-training">
                                    <span class="text-white" style="font-size:25px; margin-right: 11px">Loading
                                        Video</span>
                                    <div class="spinner-border text-light" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
                                    id="staff-video"></video>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>