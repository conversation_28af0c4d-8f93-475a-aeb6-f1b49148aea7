import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ManageContentEditTitle implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const categoryId: any | null = route.paramMap.get("id");

        return of(
            categoryId == 0 ? "Add New Content Type" : "Edit Content Type"
        )
    }
}
