.disable-checkbox {
    background: #eee;
    opacity: 0.4;
    pointer-events: none;
}

.notification-container {
    text-align: center;
    max-width: 350px !important;
    overflow: hidden;
    height: auto;

    .notification-buttons {
        width: 100%;
        border-radius: 10px !important;
        margin-top: 10px;
        background-color: #07182f;
        color: #fff;
    }

    .checkbox-container {
        margin: 20px 0;
        display: flex;
        justify-content: end;

        .mark-all-notifications {
            padding-left: 0 !important;
            display: flex;
            justify-content: right;
            .form-check-label {
                margin-left: 10px;
            }
        }

        .remove-all-notifications {
            padding-right: 0;
            display: flex;
            justify-content: right;
            .form-check-label {
                margin-left: 10px;
            }
        }
    }

    .records {
        max-height: 620px;
        height: auto;
        overflow-y: scroll;
    }

    .notification-content {
        margin-top: 25px;
        background-color: #ffffff;
        padding: 20px 4px;
        max-width: 350px;
        text-align: start;

        .content-msg {
            font-size: 14px;
        }

        .f-s-10 {
            font-size: 10px;
        }

        &:first-child {
            margin-top: 0px;
        }
    }
}