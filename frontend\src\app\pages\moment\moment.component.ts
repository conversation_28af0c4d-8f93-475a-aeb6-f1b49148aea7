import { Component, On<PERSON><PERSON>roy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { MomentManager } from './moment.manager';
import { Moment } from '../../models/moment';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import * as moment from 'moment';
import AOS from 'aos';
import { RestResponse } from 'src/app/shared/auth.model';
import { LanguageManager } from '../language/language.manager';
import { Constant } from 'src/app/config/constants';
import { MomentService } from './moment.service';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { constants } from 'buffer';
import { Observable } from 'rxjs';
import { saveAs } from 'file-saver';
import { environment } from 'src/environments/environment.prod';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { FilterParam } from 'src/app/models/filterparam';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-moment',
  templateUrl: './moment.component.html',
  styleUrls: ['./moment.component.scss']
})
export class MomentComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() momentsByUserId: boolean | false;
  @Input() userDetails: any;

  readonly MY_CONSTANT = Constant;
  moment: any = moment;
  trainingListModal: any;
  instructionsModal: any;
  isMomentCsvExport: boolean = false
  momentAssignedTrainingVideo: any;
  assignVideoData: any;
  momentData: any;
  searchMoment: any = "";
  mediaType: string;
  momentVideoOrImageModal: any;
  staffVideoOrImageModal: any;
  momentRejectLogsModal: any;
  filterMomentModal: any;
  loadingVideo: boolean = false;
  public instructionsFormInputs: any = [];
  onClickValidation: boolean;
  momentRecurringLogsModal: any;
  myModal: any;
  recordData: any;
  recurringLogs: any;
  rejectedLogs: any;
  selectedRecord: any;
  companiesToShow: any[] = [];
  selectedUserId: string;
  previewUrl: string | null = null;
  previewType: 'image' | 'video' | null = null;
  selectedRecordForAction: any = null;
  actionTakenModalInstance: any = null;


  constructor(protected momentManager: MomentManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private languageManager: LanguageManager, protected momentService: MomentService, private route: ActivatedRoute, private loadVideoFromUrl: LoadVideoFromUrl, private http: HttpClient) {
    super(momentManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    const userId = this.route.snapshot.paramMap.get('id');
    this.momentService.getMomentUserIdStatus(this.momentsByUserId, userId)
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<Moment>();
    this.selectedUserId = this.route.snapshot.paramMap.get('id');
    this.init();
  }

  async fetchRecords(param, callBack) {
    try {
      this.hasDataLoad = false;
      this.setParam(param);
      this.loadingService.show();
      const response: RestResponse = this.selectedUserId
        ? await this.momentService.fetchMomentByUser(this.selectedUserId, this.filterParam)
        : await this.manager.fetchAll(this.filterParam);
      console.log(response, "response");
      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
      this.onFetchCompleted();
      callBack({ recordsTotal: this.records.length > 0 ? this.records[0].totalCount : this.records.length, recordsFiltered: this.records.length > 0 ? this.records[0].totalCount : this.records.length, data: [] });
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchMomentByUser() {
    const filterParam = new FilterParam();
    const res: RestResponse = await this.momentService.fetchMomentByUser(this.selectedUserId, filterParam);
    this.records = res.data;
    console.log(this.records, "records");
  }

  onItemSelection(record: any) {
    this.onAssociatedValueSelected(record);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.trainingListModal = new bootstrap.Modal(
        document.getElementById('trainingListModal')
      );
    }, 0)
    setTimeout(() => {
      this.instructionsModal = new bootstrap.Modal(
        document.getElementById('instructionsModal')
      );
    }, 0)

    setTimeout(() => {
      this.momentAssignedTrainingVideo = new bootstrap.Modal(
        document.getElementById('momentAssignedTrainingVideo')
      );
    }, 0)
    setTimeout(() => {
      this.momentVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('momentVideoOrImageModal')
      );
    }, 0)
    setTimeout(() => {
      this.staffVideoOrImageModal = new bootstrap.Modal(
        document.getElementById('staffVideoOrImageModal')
      );
    }, 0)
    setTimeout(() => {
      this.momentRejectLogsModal = new bootstrap.Modal(
        document.getElementById('momentRejectLogsModal')
      );
    }, 0)
    setTimeout(() => {
      this.momentRecurringLogsModal = new bootstrap.Modal(
        document.getElementById('momentRecurringLogsModal')
      );
    }, 0)
    setTimeout(() => {
      this.filterMomentModal = new bootstrap.Modal(
        document.getElementById('filterMomentModal')
      );
    }, 0)
    setTimeout(() => {
      this.myModal = new bootstrap.Modal(
        document.getElementById('myModal')
      );
    }, 0)
    setTimeout(() => {
      this.actionTakenModalInstance = new bootstrap.Modal(
        document.getElementById('actionTakenModal')
      );
    }, 0);
  }

  openFilterMomentModal() {
    if (this.searchMoment) {
      this.searchMoment = "";
      delete this.filterParam.searchText;
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterMomentModal.show();
  }

  exportMomentsCsv() {
    this.isMomentCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], 'Moments Report' + '.xlsx', { type: 'application/vnd.ms.excel' });

        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isMomentCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isMomentCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isMomentCsvExport = false;
      return;
    }
  }

  download(): Observable<Blob> {
    let url: string;
    if (this.authService.isSuperAdmin()) {
      url = environment.BaseApiUrl + '/api/moments/export';
    } else if (this.momentsByUserId || this.route.snapshot.paramMap.get('id')) {
      const userId = this.momentsByUserId ? this.route.snapshot.paramMap.get('id') : null;
      url = environment.BaseApiUrl + '/api/moments/' + userId + '/export';
    }
    return this.http.post(url, { ...this.filterParam }, { responseType: 'blob' }).pipe(
      catchError(this.parseErrorBlob)
    );
  }

  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isMomentCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }

  async assignTrainingIdOutputCallback(assignTrainingId: string) {

    if (assignTrainingId) {
      const data = {
        id: this.assignVideoData.id,
        type: this.assignVideoData.type,
        assignTrainingId: assignTrainingId,
        farmId: this.assignVideoData.farmId,
        isActive: true,
        isDeleted: false,
      }
      try {
        this.loadingService.show();
        const response: RestResponse = await this.momentManager.update(data);
        this.loadingService.hide();
        if (!response.status) {
          this.toastService.error(response.message);
          return;
        }
        this.onCancel();
        this.trainingListModal.hide();
        this.toastService.success("Video assigned and " + response.message);
      } catch (error) {
        this.loadingService.hide();
        this.toastService.error(error.message);
      }
    } else {
      this.trainingListModal.hide();
    }
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  openMomentRejectedModal(id: string) {
    AOS.init({ disable: true });
    this.momentData = id;
    this.momentRejectLogsModal.show();
  }

  openMomentRecurringModal(id: string) {
    AOS.init({ disable: true });
    this.momentData = id;
    this.momentRecurringLogsModal.show();
  }


  approveOrRejectMoment(record: any, status: boolean, isRecurringMoment: boolean) {
    let data = {}
    if (!isRecurringMoment) {
      data = {
        id: record.id,
        isApproved: status,
      }
    } else {
      data = {
        id: record.id,
      }
    }

    const statusText = status == true ? 'approve' : 'reject';
    const confirmatiomMessage = isRecurringMoment ? 'Would you like to close this recurring moment?' : 'Would you like to ' + statusText + ' this moment?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectMomentCallback.bind(this), data, status, isRecurringMoment, null);
  }

  async approveOrRejectMomentCallback(data: any, status: boolean, isRecurringMoment: boolean) {
    try {
      const response: RestResponse = await this.momentService.updateMomentStatus(data, status, isRecurringMoment)
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  getMediaType(url) {
    if (!url) {
      return;
    }
    const extension = url.split(/[#?]/)[0].split('.').pop().trim();
    if (extension == "jpg" || extension == "jpeg" || extension == "png") {
      this.mediaType = "image"
    }
    if (extension == "mkv" || extension == "mp4" || extension == "avi" || extension == 'avi' || extension == "mov") {
      this.mediaType = "video";
    }
  }

  watchVideo(record: any) {
    record = record.filter(data => data.languageId == this.authService.getUser().languageId)
    this.recordData = record[0];
    this.loadingVideo = true
    this.momentAssignedTrainingVideo.show();
    AOS.init({ disable: true });
    this.loadVideoFromUrl.UrlToBlobUrl(record[0].videoUrl)
      .then(blobUrl => { // now it's loaded
        document.body.className = 'loaded';
        setTimeout(() => {
          let vid = document.getElementById('staff-video') as HTMLVideoElement;
          this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
          vid.addEventListener('canplaythrough', (event) => {
            this.loadingVideo = false;
          })
        }, 0);
      }).catch((err) => console.log(err));
  }

  getMomentMediaType(mediaUrl: any, type: string) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      if (type == 'title') {
        return "Open Image"
      }
      if (type == 'icon') {
        return 'image-solid.svg'
      }
    }
    if (this.mediaType == 'video') {
      if (type == 'title') {
        return "Watch Video"
      }
      if (type == 'icon') {
        return 'video-logo.svg'
      }
    }
  }

  getImageWidthClass(mediaUrl: any) {
    this.getMediaType(mediaUrl);
    if (this.mediaType == 'image') {
      return true;
    } else {
      return false;
    }
  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.mediaUrl);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.momentVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.mediaUrl);
        this.loadingVideo = false;
      }, 0)
    }
  }

  openStaffImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.getMediaType(record.userVideo);
    this.recordData = record;
    this.recordData = { mediaType: this.mediaType, ...this.recordData }
    this.staffVideoOrImageModal.show();
    if (this.mediaType == 'video') {
      this.loadingVideo = true
      setTimeout(() => {
        let vid = document.getElementById('staff-video') as HTMLVideoElement;
        this.loadVideoFromUrl.setVideoUrl(vid, record.mediaUrl);
        this.loadingVideo = false;
      }, 0)
    }
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/moment/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/moment/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }
  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }
  removeSuccess() {
    this.onCancel();
  }

  assignVideo(record: any) {
    this.assignVideoData = record;
    this.trainingListModal.show();
    AOS.init({ disable: true });
  }

  ngOnDestroy() {
    this.clean();
  }

  addOrUpdateInstructions(record: any) {
    this.momentData = record;
    this.instructionsModal.show();
    AOS.init({ disable: true });
    if (!record.instructions) {
      this.getAllLanguagesInstruction();
    } else {
      this.getAllLanguagesInstructionOnEdit(record.instructions)
    }
  }

  async getAllLanguagesInstructionOnEdit(instructions: any) {
    const instructionsData = instructions.reduce((responseData, { LanguageId, ...rest }) => ({ ...responseData, ...{ [LanguageId]: { LanguageId, ...rest } } }), {});
    const languages = await this.languageManager.fetchAllData(null);
    this.instructionsFormInputs = languages.map(language => (
      {
        languageId: language.id,
        languageName: language.name,
        instruction: instructionsData[language.id] ? instructionsData[language.id].Instruction : '',
        isEditedByAnotherLanguage: false
      })).sort((a, b) => {
        return a.languageName.localeCompare(b.languageName);
      });
  }

  getEnglishInstructionCheck(record: any) {
    if (record) {
      const IsEnglishInstructionExist = (record.instructions || []).some(data => data.LanguageId == this.authService.getUser().languageId && data.Instruction !== '')
      return IsEnglishInstructionExist;
    } else
      return false
  }

  async getAllLanguagesInstruction() {
    const languages = await this.languageManager.fetchAllData(null);
    this.instructionsFormInputs = languages.map(language => (
      {
        languageId: language.id,
        languageName: language.name,
        instruction: "",
        isEditedByAnotherLanguage: false
      })).sort((a, b) => {
        return a.languageName.localeCompare(b.languageName);
      });
  }

  addInstructionToRemainingLanguages(data: any, languageName: string) {
    if (languageName == Constant.languages.English) {
      this.instructionsFormInputs.map(input => {
        if (input.languageName !== Constant.languages.English) {
          input.instruction = !input.isEditedByAnotherLanguage ? data : input.instruction;
        }
        return input;
      });
    }
    else {
      this.instructionsFormInputs.map(input => {
        if (input.languageName == languageName) {
          if (input.instruction != null && input.instruction != "") {
            input.isEditedByAnotherLanguage = true;
          }
        }
        return input;
      });
    }
  }


  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  onClickMomentFilter(valid) {
    this.filterMomentModal.hide()
    this.onCancel();
  }

  resetFilter() {
    delete this.filterParam.status;
    delete this.filterParam.type;
    delete this.filterParam.startDate
    delete this.filterParam.endDate;
    delete this.filterParam.searchText;
    delete this.filterParam.isRecurring;
    this.filterMomentModal.hide();
    this.onCancel();
  }

  async save(form: any) {
    const updatedInstructionFormInputsData = this.instructionsFormInputs.map(({ languageName, ...rest }) => rest).filter(data => data.instruction !== '')
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    // if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
    // 	return;
    // }
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manager['update'](
        {
          id: this.momentData.id,
          farmId: this.momentData.farmId,
          isDeleted: false,
          isActive: true,
          instructions: [...updatedInstructionFormInputsData]
        });
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.instructionsModal.hide();
      this.onCancel();
      this.toastService.success("Instructions Updated And " + response.message)
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  getImageFetchByLanguageName(name: any) {
    switch (name) {
      case Constant.languages.English:
        return "/assets/images/icons/menu/united.svg";
      case Constant.languages.Vietnamese:
        return "/assets/images/icons/menu/Vietnam.svg";
      case Constant.languages.Bahasa:
        return "/assets/images/icons/menu/indonesia.svg"
      default:
        return "/assets/images/icons/menu/united.svg";
    }
  }

  loadDetailPage(recordId) {
    this.selectedId = recordId;
    setTimeout(() => {
      $('#momentDetailPage').appendTo('body').modal('show');
      $('#momentDetailPage').on('hidden.bs.modal', () => {
        setTimeout(() => {
          this.selectedId = undefined;
        });
      });
    }, 500);
  }

  fullViewDescription(record: any) {
    this.recordData = record;
    console.log("Record Data", this.recordData, "rec", record);
    this.myModal.show();
    AOS.init({ disable: true });
  }

  async openLogsModal(record: any) {
    console.log("record", record);
    this.selectedRecord = record;
    const filterParam = new FilterParam();
    filterParam.id = record.id;
    if (record.isRecurring && !record.recurringLogs) {
      const res = await this.momentService.fetchRecurringLogs(filterParam);
      record.recurringLogs = res.data;
    } else if (!record.isRecurring && !record.rejectedLogs) {
      const res = await this.momentService.fetchRejectedLogs(filterParam);
      record.rejectedLogs = res.data;
    }
    setTimeout(() => {
      const modal = (window as any).bootstrap ? (window as any).bootstrap.Modal.getOrCreateInstance(document.getElementById('logsModal')) : new bootstrap.Modal(document.getElementById('logsModal'));
      modal.show();
    }, 0);
  }

  openCompanyModal(farms: any[]) {
    this.companiesToShow = farms;
    const modalEl = document.getElementById('companyListModal');
    if (modalEl) {
      const modal = (window as any).bootstrap
        ? (window as any).bootstrap.Modal.getOrCreateInstance(modalEl)
        : new bootstrap.Modal(modalEl);
      modal.show();
    }
  }

  showPreview(url: string) {
    const ext = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
      this.previewType = 'image';
    } else if (['mp4', 'webm', 'ogg', 'mov'].includes(ext)) {
      this.previewType = 'video';
    } else {
      this.previewType = null;
    }
    this.previewUrl = url;
    setTimeout(() => {
      const modal = new (window as any).bootstrap.Modal(document.getElementById('previewModal'));
      modal.show();
    }, 0);
  }

  openActionTakenModal(record: any) {
    this.selectedRecordForAction = record;
    setTimeout(() => {
      if (this.actionTakenModalInstance) {
        this.actionTakenModalInstance.show();
      }
    }, 0);
    AOS.init({ disable: true });
  }

  closeActionTakenModal() {
    if (this.actionTakenModalInstance) {
      this.actionTakenModalInstance.hide();
    }
  }

  async changeMomentStatus(newStatus: 'OPEN' | 'CLOSED', id?: string) {
    if (!id) return;
    const input = { id, status: newStatus };
    try {
      const response: RestResponse = await this.momentService.updateStatus(input);
      if (!response.status) {
        this.toastService.error(response.message || 'Failed to update status.');
        return;
      }
      this.refreshRecord();
      if (this.actionTakenModalInstance) {
        this.actionTakenModalInstance.hide();
        // Remove lingering modal backdrop if present
        const modalBackdrop = document.querySelector('.modal-backdrop');
        if (modalBackdrop) {
          modalBackdrop.parentNode.removeChild(modalBackdrop);
        }
      }
      this.toastService.success(response.message || `Moment status changed to ${newStatus}`);
    } catch (error: any) {
      this.toastService.error(error?.message || 'An error occurred while updating status.');
    }
  }
}
