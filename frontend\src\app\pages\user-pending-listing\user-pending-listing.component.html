<div class="site-customer-main-container pt-0 px-0" data-aos="fade-up" data-aos-duration="1000">
  <div [ngClass]="{'no-padding':isDetailPage}">
    <div class="row position-relative">
      <!-- <div class="col-6 pe-0 d-flex align-items-center justify-content-end position-absolute"
                style="top: -76px; right: 0;">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="Search" appDelayedInput
                        (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchText">
                    <i class="bi bi-search pe-3"></i>
                </div>
                <button type="button"
                    class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px pe-0">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button>
            </div> -->
    </div>
    <div class="table-responsive server-side-table allocated-users-list"
      [ngClass]="{'has-records': records.length >0 }">
      <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th width="20"></th>
            <th width="35" class="text-nowrap">{{'Course.fullName' | translate}}</th>
            <th width="35" class="text-nowrap">{{'Course.email' | translate}}</th>
            <th width="35" class="text-nowrap">{{'Course.lastUpdatedDate' | translate}}</th>
            <th width="35" class="text-nowrap">{{'Course.code' | translate}}</th>
            <!-- <th width="120" class="text-nowrap">{{'Course.phoneNumber' | translate}}</th> -->
            <th width="35" *ngIf="!authService.isProgramAdmin()" class="text-nowrap">
              {{'Course.programAdminName' |translate}}
            </th>
            <th width="20" class="text-nowrap">
              {{'Course.action' |translate}}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
            <td style="text-align: center;">
              <img src="/assets/images/icons/menu/user-icon.svg" height="38" width="38" class="ms-3" alt="">
            </td>
            <td class="pe-0">
              <h5 class="code ms-2" title="View User Profile"
                style="font-size:24px; font-weight: 600; white-space: nowrap;"
                [routerLink]="['/dashboard/program-admin/user-details/' +record?.user]">
                {{record?.userDetail?.fullName}}
              </h5>
            </td>
            <td>
              {{record?.userDetail.email}}
            </td>
            <!-- <td>
                            {{record?.userDetail.phoneNumber}}
                        </td> -->
            <td>
              <div>{{moment(record?.createdOn).format('DD/MM/YYYY')}}</div>
            </td>
            <td>
              {{record?.courseDetail.code}}
            </td>
            <td *ngIf="!authService.isProgramAdmin()">
              {{record?.userDetail?.programAdminName}}
            </td>
            <td>
              <div class="mb-2 custom-action-button text-right d-sm-flex">
                <i (click)="approveOrRejectTraining(record?.id, 'APPROVED')"
                  class="bi bi-check-circle font-21px cursor-pointer me-2" title="Approve">
                </i>
                <i (click)="approveOrRejectTraining(record?.id, 'REJECTED')"
                  class="bi bi-x-circle font-21px cursor-pointer" title="Reject">
                </i>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
