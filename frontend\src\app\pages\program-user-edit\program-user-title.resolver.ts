import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ProgramUserEditTitle implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const strProgramAdmin: any | null = route.paramMap.get("id");

        return of(
            strProgramAdmin == 0 ? "Add New User" : "Edit User"
        )
    }
}
