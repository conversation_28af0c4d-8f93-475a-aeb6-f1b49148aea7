.wrapper {
  display: flex;
  width: 100%;

  .mCustomScrollbar {
    touch-action: initial !important;
  }

  #sidebar {
    width: 250px;
    position: fixed;
    overflow-y: auto;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    background-color: #fff;
    color: #fff;
    transition: all 0.3s;
    box-shadow: 0px 3px 8px #15223214;

    &.final-active {
      margin-left: -250px;
    }

    .sidebar-logo-header {
      padding: 30px;
      text-align: center;
      margin-bottom: 0px;

      img {
        max-width: 167px;
      }
    }

    ul.components {
      padding: 20px 0;
      padding-top: 50px !important;

      li {
        padding: 5px 25px;
        margin-bottom: 5px;
        white-space: nowrap;

        a {
          font-size: 16px;
          line-height: 40px;
          text-decoration: none;
          color: #000;
          font-weight: 500;

          img {
            width: 25px;
            margin-right: 15px;
            vertical-align: sub;
          }
        }

        // &:hover,
        // &:active,
        // &:focus {
        //   color: $primary;

        //   a:not(.sub-a) {
        //     color: $primary;
        //   }
        // }

        &.active {
          color: $secondary;

          a {
            font-weight: 500;
            color: $secondary;
          }
        }

        &.parent-active {
          color: $secondary !important;

          .parent {
            font-weight: 500;
            color: $secondary !important;
          }
        }

        &.sub-menu-container {
          ul {
            padding: 0px 14px;
            list-style: none;

            li {
              border-left: 0px !important;
              margin-bottom: 0px;

              a {
                line-height: 35px;
              }

              &:hover,
              &:active,
              &:focus {
                border-left: 0px !important;

                a {
                  color: $primary;
                }
              }
            }
          }

          a {
            .bi {
              display: inline-block;
              vertical-align: middle;
              float: right;
            }

            .bi.bi-chevron-down {
              display: none;
            }

            .bi.bi-chevron-up {
              display: inline-block;
            }

            &.collapsed {
              .bi.bi-chevron-up {
                display: none;
              }

              .bi.bi-chevron-down {
                display: inline-block;
              }
            }
          }
        }
      }
    }

    .user-info-section-container {
      padding: 10px 20px;

      .user-info-section {
        background-color: $primary;
        border-radius: 10px;
        padding: 15px;
        color: #fff;
        fill: #fff;

        .user-logo-image {
          height: 37px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 7px;
          width: 37px;
          background-color: #fff;
          color: $primary;
          border-radius: 50%;
          text-align: center;
          line-height: 37px;
          font-size: 21px;
          font-weight: 600;
          font-family: "Blinker", sans-serif !important;
          text-transform: capitalize;
        }

        .user-info-body {
          display: inline-block;
          vertical-align: middle;
          font-size: 14px;
          width: calc(100% - 60px);

          .user-info-body-container {
            display: flex;
            justify-content: space-between;
          }

          .user-name-text {
            font-size: 14px;
            line-height: 22px;
            margin: 0px;
            width: 122px;
            text-overflow: ellipsis;
            overflow: hidden;
            text-transform: capitalize;
            white-space: nowrap;
          }

          .user-type-text {
            font-size: 10px;
            margin: 0px;
          }
        }
      }
    }
  }

  #content {
    width: calc(100% - 250px);
    min-height: 100vh;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    right: 0;
    background-color: #fbfbfb;

    &.active {
      width: 100%;
    }

    .page-info-top-bar {
      justify-content: space-between;
      padding: 25px 48px;
      text-align: right;
      max-height: 100px;
      background-color: #fbfbfb;

      .menu-toggle-icon {
        font-size: 2em;
        float: left;
        cursor: pointer;
      }

      .page-name-container {
        text-align: left;
        display: flex;
        flex-direction: column;
        margin-left: 10px;

        h3,
        .breadcrumb {
          margin-bottom: 0px;
        }

        h3 {
          font-size: 28px;
          line-height: 40px;
          font-weight: 600;
        }

        .breadcrumb {
          li {
            a {
              font-size: 14px;
              line-height: 25px;
              color: #7f8fa4 !important;
            }

            &.active {
              a {
                color: $primary !important;
              }
            }
          }
        }
      }

      .page-user-info-container {
        .user-image-logo {
          width: 45px;
          height: 45px;
          border-radius: 50%;
          text-align: center;
          line-height: 45px;
          background: transparent url("/assets/images/icons/user-logo-background.png") 0% 0%;
          background-size: 100% 100%;
          font-size: 18px;
          font-weight: 600;
          color: $primary;
        }

        .user-info-logout-container {
          display: flex;
          flex-direction: column;
          text-align: left;
          margin-left: 12px;

          .user-info-name {
            font-size: 18px;
            line-height: 24px;
            color: $primary;
            font-weight: 600;
          }

          .logout-text {
            font-size: 12px;
            line-height: 14px;
            cursor: pointer;
            font-weight: 500;
            color: #2c2c2c;
          }
        }
      }
    }

    .site-customer-main-container {
      padding: 20px 50px;
      background-color: #fbfbfb;

      // manage user detail
      &.manage-p-l-r {
        padding-left: 4px;
        padding-right: 4px;
      }

      .dashboard-content-container {
        min-height: 600px;
        background-color: #ffffff;
        padding: 30px 18px;
        box-shadow: 0px 3px 8px #15223214;
        position: relative;

        .checked-blue:checked {
          background-color: #1681ff !important;
          border: 0 !important;
        }

        &.section-edit-form {
          min-height: auto;
        }

        .site-form-container {
          width: 100%;
          max-width: 80%;
          margin-left: 10% !important;

          &.small-container {
            max-width: 60%;
            margin-left: 20% !important;
          }
        }

        &.section-divider {
          background: url("/assets/images/icons/section-divider.svg");
          background-color: #fff;
          background-repeat: no-repeat;
          background-position: -6px 70px;
        }
      }

      .detail-section-heading {
        background: url("/assets/images/icons/detail-section-background.png");
        background-repeat: no-repeat;
        height: 60px;
        max-width: 373px;
        width: 90%;
        background-size: 100% 100%;
        margin-left: -40px;
        font-size: 14px;
        line-height: 60px;
        font-weight: 600;
        padding-left: 70px;
        text-transform: uppercase;
        margin-bottom: 40px;
      }
    }
  }

  .video-credit-height {
    height: 68px !important;
  }

  .show-entries {
    width: auto !important;
    display: inline-block !important;
    height: 52px;
    border-radius: 10px !important;
    padding-right: 34px;
  }

  .publish-training-check-cls {
    .form-check-input {
      width: 25px !important;
      height: 25px !important;

      &:checked {
        background-color: black;
      }
    }

    label {
      margin-top: 4px;
      margin-left: 7px;
    }
  }

  // ng-select common css for all pages
  .ng-select-main-container {
    height: auto !important;
    padding-bottom: 12px !important;

    .ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container {
      box-shadow: unset !important;
      border: unset !important;
    }
  }

  .ng-select-container {
    background-image: unset !important;
    align-items: center !important;
    overflow: unset !important;

    &:hover {
      box-shadow: none !important;
    }

    .ng-arrow-wrapper {
      height: 17px !important;
    }

    border: none !important;
    height: 28px !important;
    min-height: unset !important;
    padding-left: 0px !important;

    .ng-value-container {
      padding-left: 0px !important;

      input {
        height: 17px !important;
      }

      .ng-input {
        padding-left: 0px !important;
      }
    }
  }
}

.site-customer-main-container {
  .custom-input-group {
    position: relative;
    //max-width: 300px;
    width: 100%;
    margin-right: 10px;

    .search-form-control {
      padding-left: 40px;
      font-size: 13.5px !important;
      //height: 55px;
      box-shadow: none !important;
      border-radius: 10px !important;
      margin-bottom: 20px;
      padding: 15px;
      // margin-left: 0px;
    }

    .bi-search {
      position: absolute;
      right: 0px;
      top: 6px;
      font-size: 25px;
    }
  }
}

.section-heading {
  font-size: 21px;
  font-weight: 600;
  padding-left: 0px;
  margin-bottom: 25px;
  font-family: "Blinker", sans-serif !important;
}

.customer-active-icon {
  font-size: 1.5rem;

  &.success-color {
    color: #29bdb1;
  }

  &.failure-color {
    color: #dc3545;
  }
}

.limit-tag {
  text-align: left;

  span {
    display: inline-block;
    min-width: 150px;
    font-weight: 500;
    text-align: left;
  }

  &.ower-detail {
    span {
      text-align: left;
      min-width: 100px;
    }
  }

  &.voucher-detail {
    span {
      text-align: left;
      min-width: 100px;
    }
  }

  &.voucher-discount-detail {
    span {
      text-align: left;
      min-width: 125px;
    }
  }

  b {
    font-weight: 500 !important;
  }
}

.plan-selection {
  .label-text {
    display: inline-block;
    vertical-align: middle;
    width: 100px;
    background-color: #f8f8f8;
    border: 1px solid $secondary;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;

    &.selected {
      background-color: $primary;
      color: #fff;
    }

    &:first-child {
      border-right: 0px;
    }
  }
}

.card.pricing-card {
  margin: 0 0.5em;
  // box-shadow: 2px 6px 8px 0 rgba(22, 22, 26, 0.18);
  border: none;
  min-height: 359px;
  border: 1px solid $secondary;
  cursor: pointer;

  .card-header-container {
    padding: 1.5em;
    max-width: 100%;
    min-height: 11em;
    padding-bottom: 0px;

    .box-info {
      background: $primary;
      width: 160px;
      height: 55px;
      color: #fff;
      line-height: 55px;
      text-align: center;
      box-shadow: 2px 6px 8px 0 rgba(22, 22, 26, 0.18);
      font-size: 13px;
      font-weight: 500;
    }

    .amount-text {
      display: inline-block;
      vertical-align: middle;
      margin-top: 30px;
      font-size: 13px;

      span {
        display: inline-block;
        vertical-align: middle;
        font-size: 55px;
        line-height: 60px;
        font-weight: bold;
        color: $primary;
      }
    }
  }

  .card-body {
    padding: 1.5em;

    // padding-top: 10px;
    .card-description {
      border: 1px solid rgba(30, 0, 109, 0.25);
      padding: 10px;
      font-size: 12px;
      font-weight: 300;
      line-height: 30px;
      min-height: 202px;
    }

    .card-checklist-point {
      list-style: none;
      padding: 0px;
      margin-top: 33px;
      margin-bottom: 0px;

      li {
        font-size: 12px;
        margin-top: 20px;

        svg,
        span {
          display: inline-block;
          vertical-align: middle;
        }

        svg {
          color: $primary;
          fill: $primary;
        }

        span {
          margin-left: 10px;
          line-height: 19px;
        }
      }
    }

    .box-action {
      margin: 20px auto;
      background: $primary;
      width: 160px;
      height: 45px;
      color: #fff;
      line-height: 45px;
      text-align: center;
      box-shadow: 2px 6px 8px 0 rgba(22, 22, 26, 0.18);
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 0px;
    }
  }

  &.current-selected-plan {
    background-color: $primary;
    padding: 0px;
    display: flex;
    flex-direction: column;
    color: #fff;

    .card-header-container {
      .box-info {
        background: #fff;
        color: $primary;
      }

      .amount-text {
        span {
          color: #fff;
        }
      }

      .card-description {
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 10px;
        font-size: 12px;
        font-weight: 300;
        line-height: 30px;
        min-height: 112px;
      }
    }

    .card-body {
      .card-checklist-point {
        margin-top: 40px;

        li {
          svg {
            color: #fff;
            fill: #fff;
          }
        }
      }

      .box-action {
        background: #fff;
        color: $primary;
      }
    }
  }

  &.selected {
    background-color: $primary;
    color: #fff;

    .card-header-container {
      .box-info {
        background: #fff;
        color: $primary;
      }

      .amount-text {
        span {
          color: #fff;
        }
      }
    }

    .card-body {
      .card-description {
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .card-checklist-point {
        li {
          svg {
            color: #fff;
            fill: #fff;
          }
        }
      }

      .box-action {
        background: #fff;
        color: $primary;
      }
    }
  }
}

.carousel-action-section {
  text-align: center;

  button {
    height: 43px;
    width: 43px;
    border-radius: 50%;
    margin: 7px;

    svg {
      width: 1.5em;
      height: 1.5em;
    }
  }

  .back-button {
    box-shadow: 0px 3px 5px #49424a4d;
    border: 1px solid #8e8e8e;
    background: transparent;
  }

  .next-button {
    background: $primary;
    box-shadow: 0px 3px 5px #49424a4d;
    border: 1px solid $primary;
    color: #fff;
  }
}

.voucher-form-section-actions {
  text-align: center;

  .voucher-action {
    display: inline-block;
    vertical-align: top;
    width: 300px;
    height: 65px;
    background: $secondary;
    margin-right: 10px;
    margin-bottom: 1.5rem !important;
    padding: 12px 15px;

    &:last-child {
      margin-right: 0px;
    }

    .number-box {
      background: $primary;
      box-shadow: 0px 5px 10px #00000029;
      width: 40px;
      height: 40px;
      color: #fff;
      font-size: 24px;
      line-height: 39px;
      font-weight: 600;
      text-align: center;
      display: inline-block;
      vertical-align: middle;
    }

    .text-info-section {
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 40px);
      text-align: left;
      padding-left: 15px;
      color: #000000;
      letter-spacing: 0.22px;

      .step-number {
        font-size: 13px;
        line-height: 18px;
        text-transform: uppercase;
      }

      .action-title {
        font-size: 15px;
        line-height: 22px;
        font-weight: 500;
      }
    }

    &.completed {
      border: 1px solid $primary;
      background: $primary;
      color: #fff;

      .number-box {
        background: #fff;
        color: $primary;
      }

      .text-info-section {
        color: #fff;
      }
    }

    &.active {
      border: 1px solid $primary;
    }
  }
}

.marker-section-container {
  margin-bottom: 4rem;

  .marker-heading {
    font-size: 27px;
    line-height: 25px;
    font-size: 600;
  }

  .custom-marker-table {
    thead>tr>th {
      font-size: 13px;
      line-height: 19px;
      font-weight: 500;
      color: #333539;
    }

    tbody>tr>td {
      border-bottom: 1px solid rgb(112, 112, 112, 0.1);
      padding: 1rem 0.5rem !important;
    }
  }
}

agm-map {
  height: 300px;

  .gm-style-iw button>img {
    display: none !important;
  }
}

.custom-badge {
  padding: 10px 0.65em !important;
  border-radius: 0px !important;

  &.bg-danger {
    background-color: rgba(220, 53, 69, 0.5) !important;
    color: rgb(220, 53, 69) !important;
  }

  &.bg-success {
    background-color: rgba(25, 135, 84, 0.5) !important;
    color: rgb(25, 135, 84) !important;
  }

  &.bg-light {
    background-color: rgba(13, 35, 64, 0.1) !important;
    color: #0d2340 !important;
  }

  &.bg-accepted {
    background-color: rgba(19, 181, 244, 0.14) !important;
    color: #13b5f4 !important;
  }

  &.bg-scheduled {
    background-color: rgba(200, 151, 15, 0.14) !important;
    color: #c8970f !important;
  }

  &.bg-completed {
    background-color: rgba(25, 193, 115, 0.14) !important;
    color: #19c173 !important;
  }
}

.custom-action-button {
  .action-button {
    margin-left: 5px;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
    border-radius: 6px !important;
    padding: 10px 12px;
    font-size: 13px;
    font-weight: 500;
    min-width: 95px;

    &.btn-outline-light {
      color: $primary !important;
      border-color: $primary !important;
    }

    &.btn-primary {
      color: #fff;
    }

    i {
      display: inline-block;
      vertical-align: middle;
    }
  }

  .icon-button {
    border-radius: 10px !important;
    width: 55px;
    padding: 10px;
  }
}

.site-customer-main-container {
  .add-button {
    background: #1681ff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 10px !important;
    border: 0;
    font: normal normal normal 13px/21px Blinker;
    width: 100%;
    padding: 15px 10px;
  }
}

@media (max-width: 767px) {
  .site-customer-main-container {
    .add-button {
      padding: 15px;
    }
  }
}

.dashboard-main-filter-section {
  margin-bottom: 15px;
  padding: 0px !important;

  .custom-input-group {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    max-width: 300px;
    width: 100%;
    margin-right: 10px;

    .search-form-control {
      padding-left: 40px;
      font-size: 13px !important;
      height: 52px;
      box-shadow: none !important;
    }

    .bi-search {
      position: absolute;
      right: 15px;
      top: 16px;
      padding-right: 20px !important;
    }
  }

  .filter-button {
    display: inline-block;
    vertical-align: middle;
    min-height: 52px;
    text-transform: uppercase;
    font-size: 13px !important;
    letter-spacing: 0.26px !important;
    font-weight: 500 !important;

    i {
      font-size: 1.4rem !important;
      margin-right: 5px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .card-body {
    padding: 2rem 1.5rem !important;
  }
}

.logout-item {
  position: relative;
  bottom: 0px;
  left: 0px;
  width: 100%;
  padding: 8px 25px;

  .logout-icon-container {
    width: 50px;
    height: 90px;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 47px;
  }

  .logout-button {
    width: 50px;
    border-radius: 10px;
    background: #1681ff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border: 0;
  }

  .logout-button-content {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    font-weight: 500;
    color: #000000;
    height: 82px;
    font: normal normal normal 18px/40px Blinker;
  }
}

.custom-nav-tabs {
  list-style: none;
  padding: 0px !important;

  li {
    display: inline-block;
    vertical-align: top;
    border-bottom: 5px solid transparent !important;
    text-align: center;
    padding-bottom: 10px;

    a {
      padding: 15px 30px;
      min-width: 140px;
      display: inline-block;
    }

    &.active {
      border-bottom: 5px solid $primary !important;

      a {
        border: 1px solid $primary !important;
      }
    }
  }

  .new-record-button {
    float: right;
  }
}

.dashboard-items-container {
  padding: 40px 55px;

  .dashboard-item-container {
    display: inline-block;
    vertical-align: top;
    background-color: #fff;
    color: #000;
    margin: 10px;
    padding: 40px;
    border-radius: 36px;
    max-width: 439px;
    width: 100%;

    .dashboard-item-title {
      font-size: 18px;
      line-height: 23px;
      font-weight: 400;
    }

    .logo-stat-container {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      flex-direction: column;
      text-align: left;

      h2 {
        font-size: 71px;
        line-height: 87px;
        font-weight: bold;
        color: $primary;
      }

      img {
        height: 91px;
      }
    }

    .more-info-container {
      margin-top: 20px;
      font-size: 16px;
      line-height: 20px;

      span,
      i {
        display: inline-block;
        vertical-align: middle;
      }

      span {
        margin-right: 15px;
      }
    }

    &.primary-color {
      max-width: 555px;
      width: 100%;
      background-color: $primary;
      color: #fff !important;
      border-radius: 50px;

      .logo-stat-container {
        flex-direction: row;
        align-items: center;

        h2 {
          color: #fff !important;
          font-size: 103px;
          line-height: 126px;
          font-weight: bold;
          color: $primary;
        }

        img {
          height: 186px;
        }
      }
    }
  }
}

// Usere Edit Component
.wrapper {
  #content {
    #userEditComponent {
      .dashboard-content-container {
        min-height: 78vh;

        .iti {
          display: block !important;

          input {
            height: 61.19px !important;
          }
        }
      }
    }
  }
}

.wrapper {
  #content {
    #userEditComponent {
      .dashboard-content-container {
        .form-description {
          height: 180px !important;
        }
      }
    }
  }
}

.wrapper {
  #content {
    #userEditComponent {
      .dashboard-content-container {
        .form-floating-textarea {
          position: relative;
        }

        .form-floating-textarea:before {
          content: "";
          position: absolute;
          top: 1px;
          left: 1px;
          width: calc(100% - 14px);
          height: 32px;
          border-radius: 4px;
          background-color: #ffffff;
        }

        .form-floating-textarea textarea.form-control {
          padding-top: 32px;
          min-height: 80px;
        }

        .multi-select-dropdown-cls {
          height: auto !important;
          padding: 29px 10px 14px 10px !important;

          .dropdown-btn {
            padding: 6px 11px !important;

            .selected-item {
              margin-bottom: 4px;
              margin-top: 4px;
            }
          }
        }

        .delete-video-container {
          top: 9px;
          right: 11px;
          width: 27px;
          height: 27px;
          background: white;
          border-radius: 23px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          i {
            font-size: 25px;
            z-index: 2;
          }
        }

        .moment-image-container {
          border-radius: 23px;
          max-width: 410px;
          overflow: hidden;
          position: relative;

          img {
            width: 100%;
            height: 250px;
            object-fit: cover;
          }
        }

        .video-container {
          max-width: 100%;
          border-radius: 4px;
          margin: 0 auto;
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          border-radius: 23px;
          background: black;
          aspect-ratio: 16/9;

          .video-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          video {
            position: absolute;
            right: 0;
            bottom: 0;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            z-index: -100;
            background-size: cover;
            overflow: hidden;
            border-radius: 23px;
          }
        }

        .play-button-wrapper {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: auto;
          pointer-events: none;

          .circle-play-b-cls {
            cursor: pointer;
            pointer-events: auto;
            z-index: 1;

            svg {
              width: 55px;
              fill: #fff;
              stroke: #fff;
              cursor: pointer;
              background-color: rgba(black, 0.2);
              border-radius: 50%;
              opacity: 0.9;
            }
          }
        }

        .profile-image-container {
          width: 165px;
          height: 165px;
          border-radius: 23px;
          margin-bottom: 10px;
          box-shadow: 0px 3px 6px #00000029;

          img {
            border-radius: 23px;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .profile-image-edit-container {
            border-radius: 36px;
            background: #1681ff 0% 0% no-repeat padding-box;
            width: 40px;
            height: 39px;
            bottom: 11px;
            right: 11px;

            input {
              display: none;
            }

            .bi-pencil-fill {
              cursor: pointer;
              color: white;
            }
          }
        }

        .form-control {
          border: 1px solid #f0f4f9;
          border-radius: 10px !important;
        }
      }
    }
  }
}

.wrapper {
  #content {
    #userEditComponent {
      .dashboard-content-container {
        .container {
          .user-edit-msg {
            font: normal normal normal 13px/19px Blinker;
            color: #5d5d5d;
          }
        }
      }
    }
  }
}

// .wrapper {
//   #content {
//     #userEditComponent {
//       .dashboard-content-container {
//         .form-floating {
//           .dropdown-btn {
//             border: 1px solid #f0f4f9;
//             border-radius: 10px !important;
//           }
//         }
//       }
//     }
//   }
// }

// .wrapper {
//   #content {
//     #userEditComponent {
//       .dashboard-content-container {
//         .site-button {
//           min-width: 150px !important;
//         }
//       }
//     }
//   }
// }

// .wrapper {
//   #content {
//     #userEditComponent {
//       .dashboard-content-container {
//         .category-language {
//           border-radius: 12px;
//         }
//       }
//     }
//   }
// }

// .wrapper {
//   #content {
//     #userEditComponent {
//       .dashboard-content-container {
//         .category-language {
//           .program-heading {
//             font-size: 20px;
//             margin-left: 8px;
//             font-weight: 600;
//           }
//         }
//       }
//     }
//   }
// }

// .wrapper {
//   #content {
//     #userEditComponent {
//       .dashboard-content-container {
//         .video-title {
//           .upload-video-section {
//             margin-top: 100px;

//             label {
//               cursor: pointer;
//             }

//             input {
//               display: none;
//             }
//           }
//         }
//       }
//     }
//   }
// }

.wrapper {
  #content {
    .manage-detail {
      padding: 0 35px;
    }

    #userEditComponent {
      .add-training {
        background: none !important;
        padding: 0 !important;
        box-shadow: none !important;
        min-height: auto;
      }

      .dashboard-content-container {
        .form-floating {
          .dropdown-btn {
            border: 1px solid #f0f4f9;
            border-radius: 10px !important;
          }
        }

        .site-button {
          min-width: 150px !important;
        }

        .video-title {
          .upload-video-section {
            margin-top: 24px;

            label {
              cursor: pointer;
            }

            input {
              display: none;
            }
          }
        }

        .category-language {
          border-radius: 12px;

          .program-heading {
            font-size: 20px;
            margin-left: 8px;
            font-weight: 600;
          }
        }

        .training-section {
          background: #ffffff 0% 0% no-repeat padding-box;
          border-radius: 20px;
          opacity: 1;
          padding: 10px 32px;
        }

        .training-feed {
          margin-top: 50px !important;
        }
      }
    }
  }
}

.filter-button-cls {
  max-width: 125px;
}

.revoke-access-btn {
  background-color: #0CA925 !important;
}

.invite-user-btn {
  background-color: $primary !important;
  color: #fff !important;
}

.user-icon {
  height: 36px;
  width: 36px;
  background-color: #eaf4ff;
  border-radius: 50%;
  display: flex;
  color: #1681FF;
  justify-content: center;
  align-items: center;
}

// .wrapper {
//   #content {
//     #userEditComponent {
//       .add-training {
//         background: none !important;
//         padding: 0 !important;
//         box-shadow: none !important;
//         min-height: auto;
//       }
//     }
//   }
// }

// .wrapper {
//   #content {
//     .manage-detail {
//       padding: 0 35px;
//     }
//   }
// }

// manage staff edit//
.user-details-btn {
  border-radius: 10px;
  border: 1px solid #d6d6d6;
  margin: 5px;
  width: 200px;
  padding: 5px 0px;
  font-size: 18px;
}

// manage-moment-section//
.box {
  .margin-right-118px {
    margin-right: 118px !important;
  }
}

.site-customer-main-container {
  #manage-moments {
    // padding: 50px;
    background-color: #fbfbfb;
    min-height: 78vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #staff-video {
    width: 100%;
    height: 100%;
    aspect-ratio: 16/9;
    background-color: black;
  }

  .loading-container-video-training {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: black;
  }

  .box {
    background-color: #ffffff;
  }

  .manage-moment-section {
    display: flex;
    justify-content: end;
    flex-direction: column;

    .manage-moment-button {
      // padding: 0 20px;
      border-radius: 10px !important;

      .manage-moment-add-btn {
        width: 100%;
        padding: 12px 22px;
      }
    }
  }
}

.allocated-users-list {
  table {
    border-collapse: separate !important;
    border-spacing: 0 12px !important;
  }
}

table {
  .description-limit {
    white-space: nowrap;
    overflow: hidden;
    max-width: 200px;
    text-overflow: ellipsis;
  }

  .confirmation-icon {
    background-color: #d9f4e4 !important;
    color: #07c526;
    font-size: 15px;
    width: 100px;
    height: 40px;
    border: none !important;
    border-radius: 7px;
  }

  .close-icon {
    background-color: #f9eae5;
    color: #d85143;
    font-size: 15px;
    width: 100px;
    height: 40px;
    border: none !important;
    border-radius: 7px;
  }

  .waiting-icon {
    background-color: #ffebd1 !important;
    color: #ee8c0c;
    font-size: 15px;
    width: 210px;
    height: 40px;
    border: none !important;
    border-radius: 7px;
  }
}

.moment-icon {
  font-size: 27px;
  width: 37px;
  vertical-align: inherit;
  margin-right: 10px;
}

.manage-view-button {
  white-space: nowrap;
  border-radius: 10px !important;
  width: 200px;
  border: 1px solid #07182f;
  font-size: 16px !important;
}

.m-t-20 {
  margin-top: 20px;
}

.common-icons {
  width: 27px !important;
  height: 25px !important;
  // border: 0 !important;
}

.status-button {
  border: 0;
  border-radius: 7px;
  width: 200px;
  padding: 9px 0px 9px 0px;
}

::ng-deep .cdk-overlay-container {
  z-index: 2000;
}

.status-button-progress-color {
  background-color: #fcecd7;
  color: #f1a855;
}

.status-button-pending-color {
  background-color: #ffffb4;
  color: #898882;
}

.status-button-completed-color {
  background-color: #e6faef;
  color: #07c526;
}

.completed-color {
  color: #09C527;
}

.waiting-for-approval-color {
  color: #F59B13;

  img {
    width: 22px;
    height: 22px;
  }
}


.rejected-color {
  color: #E80909
}

.in-progress-color {
  color: #061428;
}

.icon-export {
  font-size: 21px;
}

.export-btn {
  width: 145px !important;
  padding: 10px 11px !important;
}

.user-video-button {
  border: 0;
  padding: 8px;
  border-radius: 6px;
  color: #ffff;
}

.white-space {
  white-space: nowrap;
}

// moment-detail//
.moment-detail-box {
  background-color: #fff;
  padding: 2rem;
  min-height: 78vh;
}

// admin-setting
.admin-setting {
  padding: 20px 4px;
  max-width: 483px !important;
}

// program admin-setting
.program-admin-setting {
  padding: 20px 4px;
  max-width: 850px !important;
}

// course edit
.course-edit-section {
  padding: 35px;
}

// course training section
.course-training-section {
  padding: 50px;
}

.setting-button {
  width: 140px;
  height: 55px;
  white-space: nowrap;
}

.farm-view-button {
  border-radius: 6px !important;
  padding: 14px 12px !important;
  font-size: 13px !important;
  white-space: nowrap;
  font-weight: 500;
  min-width: 95px;
}

@media (min-width: 768px) {
  .wrapper {
    #content {
      #userEditComponent {
        .dashboard-content-container {
          min-height: 78vh;
          padding: 100px;
        }

        .course-main {
          min-height: 50vh;
        }
      }
    }
  }
}

@media (min-width: 992px) {
  .site-customer-main-container {
    .add-button {
      max-width: 200px;
    }

    .manage-filter-buttton {
      width: 140px;
      border-radius: 10px !important;
      font-size: 17px;
      padding: 12px 12px;
    }

    .manage-moment-section {
      flex-direction: row;

      .manage-moment-add-btn {
        width: 130px;
      }
    }

    .plan-carousel {
      .carousel-inner {
        padding: 1.5em;
        display: flex;

        .carousel-item {
          margin-right: 0;
          flex: 0 0 33.333333%;
          display: block;
        }
      }
    }

    #no-background-border {
      padding: 0px 0px 0px 50px !important;
      min-height: auto !important;
      box-shadow: none !important;
    }

    .logout-item {
      position: absolute;
    }
  }
}

@media screen and (max-width: 991px) {
  .wrapper {
    #sidebar {
      margin-left: -250px;

      &.final-active {
        margin-left: 0;
      }
    }

    #content {
      width: 100%;
      left: 0px;

      &.active {
        left: 250px;
      }

      .page-info-top-bar {
        .page-user-info-container {
          .new-record-button {
            min-width: 50px !important;
          }
        }

        .page-name-container {
          text-align: left;

          h3 {
            font-size: 26px !important;
          }
        }
      }

      .site-customer-main-container {
        .dashboard-content-container {
          .form-floating-textarea {
            position: relative;
          }

          .form-floating-textarea:before {
            content: "";
            position: absolute;
            top: 1px;
            left: 1px;
            width: calc(100% - 14px);
            height: 32px;
            border-radius: 4px;
            background-color: #ffffff;
          }

          .form-floating-textarea textarea.form-control {
            padding-top: 32px;
            min-height: 80px;
          }

          &.section-edit-form {
            .no-padding-left {
              padding-right: 0px !important;
            }

            .no-padding-right {
              padding-left: 0px !important;
            }
          }

          .site-form-container {
            width: 100%;
            max-width: 90%;
            margin-left: 5% !important;

            &.small-container {
              max-width: 90%;
              margin-left: 5% !important;
            }
          }
        }
      }
    }

    .sidebarCollapse span {
      display: none;
    }

    .plan-carousel {
      padding: 0px !important;

      .card .card-header-container {
        min-height: 11em;
        margin-right: 0px;
        margin-left: 0px;
      }
    }

    .card.pricing-card {
      margin: 0px !important;
    }

    .voucher-form-section-actions {
      .voucher-action {
        display: inline-block;
        vertical-align: top;
        width: 100%;
      }
    }
  }

  .custom-nav-tabs {
    list-style: none;
    padding: 0px !important;

    li {
      display: inline-block;
      vertical-align: top;
      border-bottom: 5px solid transparent !important;
      text-align: center;
      padding-bottom: 10px;

      a {
        padding: 15px;
        min-width: 100px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
      }

      &.active {
        border-bottom: 5px solid $primary !important;

        a {
          border: 1px solid $primary !important;
        }
      }
    }

    .new-record-button {
      min-width: 50px !important;
    }
  }

  .dashboard-items-container {
    padding: 40px 10px !important;
    text-align: center;

    .dashboard-item-container {
      margin-left: 0px !important;
      margin-right: 0px !important;
      text-align: left;

      &.primary-color {
        border-radius: 36px;

        .logo-stat-container {
          h2 {
            font-size: 81px;
            line-height: 100px;
          }

          img {
            height: 140px;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 481px) and (max-width: 991px) {
  .wrapper {
    #content {
      .site-customer-main-container {
        padding: 25px 15px;

        .dashboard-content-container {
          padding: 40px 20px;
        }

        .detail-section-heading {
          margin-left: -20px;
          margin-bottom: 20px;
        }
      }

      .page-info-top-bar {
        padding: 25px 10px;
      }
    }
  }
}

@media screen and (max-width: 600px) {
  .wrapper {
    #content {
      &.reel-height {
        min-height: unset !important;
        height: 100%;
      }

      .site-customer-main-container {
        padding: 15px 10px;

        .dashboard-content-container {
          padding: 40px 10px !important;
        }

        .detail-section-heading {
          margin-left: -10px;
          margin-bottom: 20px;
        }
      }

      .reel-logo-header {
        display: block !important;

        img {
          width: 160px;
        }
      }

      .reelMobileClass {
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .hide-title-cls {
        display: none !important;
      }

      .page-info-top-bar {
        padding: 15px 10px;
        padding-bottom: 10px;

        &.reel-header {
          background-color: white !important;
        }

        .page-user-info-container {
          .new-record-button {
            min-width: 40px !important;
            min-height: 40px !important;

            i {
              font-size: 1rem !important;
            }
          }
        }

        .page-name-container {
          text-align: left;

          h3 {
            font-size: 18px !important;
            line-height: 30px;
            font-weight: 600;
          }

          .breadcrumb {
            margin-top: -8px !important;

            .breadcrumb-item {
              &::before {
                margin-top: 5px;
                height: 15px;
                overflow: hidden;
              }

              a {
                font-size: 8px;
              }
            }
          }
        }
      }

      .card.pricing-card {
        margin: 0px !important;

        .card-header-container {
          .amount-text span {
            font-size: 40px;
          }
        }

        .card-body {
          .card-checklist-point {
            li span {
              font-size: 10px !important;
            }
          }
        }
      }
    }
  }

  .custom-nav-tabs {
    list-style: none;
    padding: 0px !important;
    text-align: right !important;

    li {
      display: inline-block;
      vertical-align: top;
      border-bottom: 5px solid transparent !important;
      text-align: center;
      padding-bottom: 10px;
      width: 33%;

      a {
        padding: 15px;
        min-width: 100% !important;
        font-size: 12px !important;
        font-weight: 500 !important;
      }

      &.active {
        border-bottom: 5px solid $primary !important;

        a {
          border: 1px solid $primary !important;
        }
      }
    }

    .new-record-button {
      min-width: 50px !important;
      float: none !important;
      margin-top: 10px;
    }
  }

  .dashboard-items-container {
    padding: 40px 10px !important;
    text-align: center;

    .dashboard-item-container {
      margin-left: 0px !important;
      margin-right: 0px !important;
      text-align: left;

      &.primary-color {
        border-radius: 36px;

        .logo-stat-container {
          flex-direction: column !important;
          align-items: initial !important;

          h2 {
            font-size: 81px;
            line-height: 100px;
          }

          img {
            height: 91px;
          }
        }
      }
    }
  }

  .user-details-btn {
    width: 100%;
  }
}

@media only screen and (min-width: 576px) {
  .site-customer-main-container {
    .custom-input-group {
      width: 300px;
    }
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .wrapper {
    #content {
      #userEditComponent {
        .dashboard-content-container {
          min-height: 78vh;
          padding: 100px;
        }

        .site-form-container {
          margin-left: 0% !important;
          max-width: 100% !important;
        }
      }
    }
  }
}

.dataTables_empty {
  background-color: white !important;
}

.LogsIconMoment {
  margin-right: 15px;
  cursor: pointer;

  img {
    width: 22px;
    margin-top: 3px;
  }
}

@media (min-width: 1400px) {
  .site-customer-main-container {
    .manage-moment-section {
      .manage-moment-button {
        margin-left: 8px;
      }
    }
  }

  .moment-detail-box {
    padding: 3rem;
    min-height: 78vh;
  }
}

.moment-video-width {
  max-width: 410px;
}

.custom-multiselect {
  border: none !important;
  // height: 28px !important;
  padding: 0 !important;

  .dropdown-btn {
    padding: 6px 11px !important;
    border: none !important;

    .selected-item {
      margin-bottom: 4px;
      margin-top: 4px;
    }
  }
}

.disable-multi-select {
  .multiselect-dropdown {
    .dropdown-list {
      .item2 {
        .multiselect-item-checkbox {
          cursor: default !important;
          pointer-events: none !important;
        }
      }
    }
  }

}

.badge {
  display: inline-block;
  padding: 0.5em 0.75em !important;
  font-size: 0.87rem !important;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge-success {
  color: #fff;
  background-color: #28a745;
}

.badge-danger {
  color: #fff;
  background-color: #dc3545;
}

.farm-badge {
    font-size: 0.7rem !important;
    margin-right: 0.25rem;
    padding: 0.25rem 0.5rem !important;
}

.preview-modal-body {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.preview-modal-body img,
.preview-modal-body video {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
} 

.confirmation-icon {
  background-color: #d9f4e4 !important;
  color: #07c526;
  font-size: 15px;
  width: 100px;
  height: 40px;
  border: none !important;
  border-radius: 7px;
}