import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import { Language } from './language';
import { CoursePart } from './coursepart';
export class Course extends BaseModel {

    id: string;
    tenantId: number;
    slug: string;
    title: string;
    description: string;
    // description: string='';
    isPublish: boolean;
    isDraft: boolean;
    isPrivate: boolean;
    url: string;
    code: string;
    email: string;
    status: string;
    isLock: false;
    courseParts: CoursePart[];
    percentage: string;
    courseEnrolled: { email?: string, username?: string; user?: string; farmId?: string}[];
    courseUsersMapping: { email?: string, username?: string; user?: string}[];
    programAdmin: string;
    allocatedUsersCount: number;
    username: any;
    selfInviteUser: boolean;
    otherInviteUser: boolean;
    userDetails: any;

    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.isPublish = false;
        this.isDraft = false;
        this.isPrivate = false;
        this.courseParts = new Array<CoursePart>();
        this.courseEnrolled = [];
        this.courseUsersMapping = [];
    }

    static fromResponse(data: any): Course {
        const obj = new Course();
        obj.id = data.id;
        obj.programAdmin = data.programAdmin;
        obj.tenantId = data.tenantId;
        obj.slug = data.slug;
        obj.createdBy = data.createdBy;
        obj.updatedBy = data.updatedBy;
        obj.createdOn = data.createdOn;
        obj.updatedOn = data.updatedOn;
        obj.isDeleted = data.isDeleted;
        obj.isActive = data.isActive;
        obj.isPrivate = data.isPrivate;
        obj.isDraft = data.isDraft;
        obj.isPublish = data.isPublish;
        obj.title = data.title;
        obj.description = data.description;
        obj.url = data.url;
        obj.code = data.code;
        obj.email = data.email;
        obj.status = data.status;
        obj.courseParts = data.courseParts;
        obj.allocatedUsersCount = data.allocatedUsersCount;
        obj.isLock = data.isLock;
        obj.courseEnrolled = data.courseEnrolled || [];
        obj.percentage = data.percentage;
        obj.courseUsersMapping = data.courseUsersMapping || [];
        return obj;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.title = this.trimMe(this.title);
        this.description = this.trimMe(this.description);
        this.url = this.trimMe(this.url);
        return this;
    }
}
