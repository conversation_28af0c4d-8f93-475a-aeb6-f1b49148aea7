import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MomentRejectedLogsService } from './momentrejectedlogs.service';

@Injectable({
    providedIn: 'root'
})
export class MomentRejectedLogsManager extends BaseManager {

    constructor(protected momentRejectedLogsService: MomentRejectedLogsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(momentRejectedLogsService, loadingService, toastService);
    }
}
