import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { TranslateService } from '@ngx-translate/core';
import { UserFarmDetail } from './userfarmdetail';

export class Users extends BaseModel {
	emailConfirmed: boolean;
	phoneNumber: string;
	userName: string;
	user: string;
	email: string;
	fullName: string;
	fullNameAndEmail: string;
	programName: string;
	firstName: string;
	lastName: string;
	isActive: boolean;
	uniqueCode: string;
	isDeleted: boolean;
	roles: Array<string>;
	isFacebookConnected: boolean;
	isGoogleConnected: boolean;
	countryCode: string;
	password: string;
	roleName: string;
	languageId: string;
	profileImageUrl: string;
	userFarmMapping: any = [];
	location: string;
	company: any;
	trainingStatus: string;
	companyName: string;
	companyCode: string;
	address: string;
	programProfileImage: string;
	about: string;
	imageUrl: string;
	programImageUrl: any;
	existingProgramAdmin: boolean;
	externalProgramAdmin: boolean;
	userId: any;
	id: string;
	guid: string;
	fromUser: string;
	farm: string;
	farmId: string;
	farmName: string;
	userFarmDetail: Partial<UserFarmDetail>[] = [];

	constructor() {
		super();
		// this.roles = new Array<string>();
	}

	isValidAccountSettingRequest(form: any) {
		if (this.isNullOrUndefinedAndEmpty(this.firstName) || this.firstName.trim() === '') {
			form.controls.firstName.setErrors({ invalid: true });
			return false;
		}
		if (this.isNullOrUndefinedAndEmpty(this.lastName) || this.lastName.trim() === '') {
			form.controls.lastName.setErrors({ invalid: true });
			return false;
		}
		if (this.isNullOrUndefinedAndEmpty(this.phoneNumber) || this.phoneNumber.trim() === '') {
			form.controls.phoneNumber.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	forRequest() {
		this.phoneNumber = this.phoneNumber.trim();
		this.userName = this.userName.trim();
		this.email = this.email.trim();
		this.fullName = this.fullName.trim();
		this.firstName = this.firstName.trim();
		this.lastName = this.lastName.trim();
		if (!this.isNullOrUndefinedAndEmpty(this.uniqueCode)) {
			this.uniqueCode = this.uniqueCode.trim();
		}
		return this;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		return true;
	}

	static fromResponse(data: any): Users {
		const users = new Users();
		users.id = data.id;
		users.emailConfirmed = data.emailConfirmed;
		users.phoneNumber = data.phoneNumber;
		users.userName = data.userName;
		users.user = data.user;
		users.email = data.email;
		users.fullName = data.fullName;
		users.programName = data.programName;
		users.firstName = data.firstName;
		users.lastName = data.lastName;
		users.isActive = data.isActive;
		users.uniqueCode = data.uniqueCode;
		users.isDeleted = data.isDeleted;
		users.isGoogleConnected = data.isGoogleConnected;
		users.isFacebookConnected = data.isFacebookConnected;
		users.company = data.company;
		users.trainingStatus = data.trainingStatus;
		users.companyName = data.companyName;
		users.profileImageUrl = data.profileImageUrl;
		users.countryCode = data.countryCode;
		users.companyCode = data.companyCode;
		users.programProfileImage = data.programProfileImage;
		users.about = data.about;
		users.imageUrl = data.imageUrl;
		users.userId = data.userId;
		users.guid = data.guid;
		users.farm = data.farm;
		users.farmId = data.farmId;
		users.farmName = data.farmName;
		users.userFarmDetail = data.userFarmDetail;

		return users;
	}
}
