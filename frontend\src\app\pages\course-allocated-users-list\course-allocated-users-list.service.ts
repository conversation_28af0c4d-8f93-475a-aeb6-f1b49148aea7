import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { BaseService } from "../../config/base.service";
import { RestResponse } from "../../shared/auth.model";

@Injectable({
    providedIn: 'root'
})
export class CourseAllocatedUsersListService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/course/user', '/api/course/users');
    }

    CourseAllocatedUsers(data: any): Promise<RestResponse> {
        return this.getRecords('/api/course/users', data);
    }
}