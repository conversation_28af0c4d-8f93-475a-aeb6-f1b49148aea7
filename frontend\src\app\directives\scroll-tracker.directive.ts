import { Directive, Output, EventEmitter, HostListener } from '@angular/core';
declare const $: any;
@Directive({
  selector: '[scrollTracker]'
})
export class ScrollTrackerDirective {
  @Output() scrollingFinished = new EventEmitter<void>();

  emitted = false;

  @HostListener("window:scroll", [])
  onScroll(): void {
    if ((window.innerHeight + window.scrollY) >= $(".loadMore").height() && !this.emitted) {
      this.emitted = true;
      this.scrollingFinished.emit();
    } else if ((window.innerHeight + window.scrollY) < $(".loadMore").height()) {
      this.emitted = false;
    }
  }
}
