import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CourseTrainingsService extends BaseService {

  constructor(public http: HttpClient) {
    super(http, '/api/training', '/api/trainings');
  }
  getLearningSeries(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/learningseries/' + filterParam, null);
  }

  fetchCourseTrainings(id: string): Observable<RestResponse> {
    return this.getRecord('/api/trainings/' + id);
  }

  getcourseTitle(id: string): Observable<RestResponse> {
    return this.getRecord('/api/course/title/' + id);
  }

  getAllTrainings(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/trainings', filterParam);
  }

  getTrainingFilterData(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/filter/data', null);
  }

  fetchAvailableTrainings(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/available/trainings', filterParam);
  }

}

