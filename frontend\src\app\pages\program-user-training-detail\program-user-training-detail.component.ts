import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { LoadingService } from 'src/app/services/loading.service';
import { CourseTrainingsManager } from '../course-trainings/course-trainings-manager';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from 'src/app/shared/auth.services';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { RouteDataService } from 'src/app/shared/title.service';
import { Training } from 'src/app/models/training';

@Component({
  selector: 'app-program-user-training-detail',
  templateUrl: './program-user-training-detail.component.html',
  styleUrls: ['./program-user-training-detail.component.scss']
})
export class ProgramUserTrainingDetailComponent extends BaseEditComponent implements OnInit {
  public trainings: any;
  public videoPlaying: boolean = false;
  public loadingVideo: boolean = false;
  record: Training;
  username: any;

  constructor(protected route: ActivatedRoute, protected courseTrainingsManager: CourseTrainingsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
    protected translateService: TranslateService, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, public routeDataService: RouteDataService) {
    super(courseTrainingsManager, commonService, toastService, loadingService, route, router, translateService);

  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.title;
    });
    this.init();
    // this.setBreadcrumbs();
  }

  //Accssibility update status
  getTooltipText(accessibility: string): string {
    return accessibility === 'Public' ? 'Public' : 'Private';
  }

  playVideoFromPlayIcon() {
    var videoId = document.querySelector<HTMLVideoElement>("#training_video");
    if (videoId) {
      const thisUrl = this.router.url;
      videoId.addEventListener('leavepictureinpicture', () => {
        const currentUrl = this.router.url;
        if (currentUrl !== thisUrl) {
          this.router.navigate([thisUrl]);
        }
      });
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
        this.videoPlaying = true;
      }
    }
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;
    });
  }

  async fetchExistingRecord() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainings = response.data;
      this.setBreadcrumbs()
      //set page title on the basis fo the training details
      this.setPageTitle()
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  //set breadcrumbs for this training
  setBreadcrumbs() {
    //set the breadcrumb
    const fullUrl = this.router.url; // Current full URL
    const queryIndex = fullUrl.indexOf('?'); // Find query parameter start
    const cleanUrl = queryIndex > -1 ? fullUrl.slice(0, queryIndex) : fullUrl;
    this.routeDataService.addBreadcrumb(this.trainings.title ?? this.trainings.trainingLibraryDetail.title, cleanUrl,this.trainings.id ?? this.trainings.trainingLibraryDetail.id)
  }

  setPageTitle() {
    this.routeDataService.setData(this.router.url, this.trainings.title ?? this.trainings.trainingLibraryDetail.title);
  }

}
