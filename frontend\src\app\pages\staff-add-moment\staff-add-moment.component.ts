import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { Farm } from 'src/app/models/farm';
import { Moment } from 'src/app/models/moment';
import { AssignedUsersService } from 'src/app/services/assigned-users.service';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { ToastService } from 'src/app/shared/toast.service';
import { MomentManager } from '../moment/moment.manager';

@Component({
  selector: 'app-staff-add-moment',
  templateUrl: './staff-add-moment.component.html',
  styleUrls: ['./staff-add-moment.component.scss']
})
export class StaffAddMomentComponent extends BaseEditComponent implements OnInit {
  public moment: Moment;
  public farms: Farm[];
  public uploader: any;
  public mediaType: any;
  public loadingVideo: boolean = false;
  public videoPlaying: boolean = false;
  private fileData: any;

  constructor(protected route: ActivatedRoute, protected momentManager: MomentManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService, private assignedUsersService: AssignedUsersService, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl) {

    super(momentManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.moment = new Moment();
    this.filterParam.userId = this.authService.getUser().id;
    this.moment.isActive = true;
    this.setRecord(this.moment);
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg,mkv,mp4', null, null, this.toastService, "Only Jpeg, Jpg, Png, Mp4, Mkv are allowed", null)

    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.farms = new Array<Farm>();
    this.init();
  }

  async fetchAssociatedData() {
    try {
      const response: RestResponse = await this.assignedUsersService.fetchAll(this.filterParam);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.farms = response.data.map(data => ({
        id: data.farmIdDetail.id,
        name: data.farmIdDetail.name,
      }));
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  getMediaType(url) {
    const extension = url.split(/[#?]/)[0].split('.').pop().trim();
    if (extension == "jpg" || extension == "jpeg" || extension == "png") {
      this.mediaType = "image"
    }
    if (extension == "mkv" || extension == "mp4") {
      this.mediaType = "video";
    }
  }

  onFetchCompleted() {
    this.moment = Moment.fromResponse(this.record);
    this.setRecord(this.moment);
  }

  uploadMomentVideoOrImage(event: any) {
    if (event) {
      this.isLoader = true;
      const file = event.target.files[0];
      if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg"
        && event.target.files[0].type != "image/png") {
        this.commonService.convertVideoFormat(file).then(res => {
          this.fileData = {} as any;
          this.fileData.files = [] as any;
          this.fileData.files.push(res);
          this.onFileProcessingCompleted(this.fileData.files);
        });
      }
      else {
        this.fileData = {} as any;
        this.fileData.files = event.target.files;
        this.onFileProcessingCompleted(this.fileData.files);
      }
    }
  }

  removeFile() {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), null);
  }

  removeFileCallback() {
    this.moment.mediaUrl = "";
    this.videoPlaying = false;
  }

  playVideoFromPlayIcon() {
    var videoId = document.getElementById("videoId") as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      videoId.controls = true;
      this.videoPlaying = true;
    });
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;

    });
  }

  onUploadSuccess(file: any, files: any): void {
    this.moment.mediaUrl = file.path;
    this.getMediaType(file.path);
    this.isLoader = false
    if (this.mediaType == "video") {
      this.loadingVideo = true
      this.loadVideoFromUrl.UrlToBlobUrl(file.path)
        .then(blobUrl => { // now it's loaded
          document.body.className = 'loaded';
          setTimeout(() => {
            let vid = document.getElementById('videoId') as HTMLVideoElement;
            this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
            vid.addEventListener('canplaythrough', (event) => {
              this.loadingVideo = false;
            })
          }, 0)
        }).catch((err) => console.log(err));
    }

  }

  onSaveSuccess(data: any) {
    this.navigate('/staff-moments');
  }
  fileUploadValidationBeforeSave(): boolean {
    if (!this.moment.mediaUrl) {
      this.onClickValidation = true;
      return false;
    }
    return true;
  }

  onFileProcessingCompleted(files) {
    this.uploader.addToQueue(files);
    this.uploader.uploadAll();
  }

}
