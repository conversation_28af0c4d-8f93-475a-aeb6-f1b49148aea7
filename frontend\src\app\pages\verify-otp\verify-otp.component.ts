import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LocalStorageService } from 'angular-2-local-storage';
import { AccountService } from 'src/app/services/account.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import AOS from 'aos';

declare var bootstrap: any;

@Component({
  selector: 'app-verify-otp',
  templateUrl: './verify-otp.component.html',
  styleUrls: ['./verify-otp.component.scss'],
})

export class VerifyOtpComponent implements OnInit, OnDestroy {
  userName: any;
  countryCode: any;
  phoneNumber: any;
  phoneNumberUpdating: boolean = false;
  validLinkMessage: string = "Please wait... we are validating the link";
  otp: any;
  uniqueCode: any | null;
  onClickValidation: boolean = false
  onClickValidationUpdateNumber: boolean = false
  updatePhoneNumberModal: any;
  constructor(private accountService: AccountService, private toastService: ToastService, private router: Router, private commonService: CommonService, private route: ActivatedRoute) {

  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updatePhoneNumberModal = new bootstrap.Modal(
        document.getElementById('updatePhoneNumberModal')
      );
    }, 0)
  }

  ngOnDestroy() {
  }

  ngOnInit() {
    setTimeout(() => {
      this.getRegisterPhoneNumber()
    }, 1000)
  }

  async getRegisterPhoneNumber() {
    let isProgramAdmin = 'false'
    this.route.queryParams
      .subscribe(params => {
        if (params.uc) {
          this.uniqueCode = params.uc;
          isProgramAdmin = params.isProgramAdmin
        }
      }
      );
    console.log(this.router.url, isProgramAdmin)
    if (this.uniqueCode) {
      try {
        const response: RestResponse = await this.accountService.getRegisterPhoneNumber(this.uniqueCode).toPromise();
        if (!response.status) {
          this.validLinkMessage = "Link is invalid redirect to login page"
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2000)
          return;
        }
        this.userName = response.data;

      } catch (error) {
        this.toastService.error(error.message);
      }
    } else if (this.uniqueCode && isProgramAdmin === 'true') {
      this.userName = this.uniqueCode;
      return
    }
    else {
      this.validLinkMessage = "Unauthorized access redirect to login page"
      setTimeout(() => {
        this.router.navigate(['/login']);
      }, 2000)
    }
  }

  openUpdateNumberModal() {
    this.onClickValidationUpdateNumber = false
    AOS.init({ disable: true });
    this.updatePhoneNumberModal.show();
  }

  hasError(event: any) {
  }

  telInputObject(event: any) {
    event.setCountry('sg');
  }

  onCountryChange(event) {
    this.countryCode = event.dialCode;
    this.countryCode = "+" + this.countryCode;
  }

  getNumber(event: any) {
  }

  async onClickUpdatePhoneNumber(valid) {
    if (!valid) {
      this.onClickValidationUpdateNumber = true
      return
    }
    this.phoneNumberUpdating = true;
    const data = {
      countryCode: this.countryCode,
      phoneNumber: this.phoneNumber
    }
    try {
      const response: RestResponse = await this.accountService.updatePhoneNumber(this.uniqueCode, data);
      if (!response.status) {
        this.phoneNumberUpdating = false;
        this.toastService.error(response.message);
        return;
      }
      this.phoneNumberUpdating = false;
      this.userName = this.phoneNumber;
      this.updatePhoneNumberModal.hide();
      this.emptyDataUpdatePhoneNumber();
      this.toastService.success(response.message);

    } catch (error) {
      this.phoneNumberUpdating = false;
      this.toastService.error(error.message);
    }
  }

  emptyDataUpdatePhoneNumber() {
    this.phoneNumber = null;
    this.countryCode = null;
  }

  async verifyPhone(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return
    }
    const data = {
      OTP: this.otp,
      verifyUsingEmail: true,
    }
    try {
      const response: RestResponse = await this.accountService.verifyYourPhone(this.uniqueCode, data);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
      this.router.navigate(['/login']);

    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  resendEmailOtp() {
    this.commonService.confirmation('Would you like to resend Verification otp?', this.resendOtpCallback.bind(this), this.userName, null, null, null);
  }

  async resendOtpCallback(userName: string, uniqueCode: any) {
    const data = {
      countryCode: this.countryCode,
      userName: this.userName
    }
    try {
      const response: RestResponse = await this.accountService.resendEmailOtp(data, this.uniqueCode);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

}
