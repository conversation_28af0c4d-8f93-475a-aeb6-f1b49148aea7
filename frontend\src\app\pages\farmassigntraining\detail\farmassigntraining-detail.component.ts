import { Component, OnInit } from '@angular/core';
import {BaseDetailComponent} from '../../../config/base.detail.component';
import { FarmAssignTraining } from '../../../models/farmassigntraining';
import { ActivatedRoute, Router } from '@angular/router';
import { FarmAssignTrainingManager } from '../farmassigntraining.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';

@Component({
  selector: 'app-farmassigntraining-detail',
  templateUrl: './farmassigntraining-detail.component.html',
  styleUrls: ['./farmassigntraining-detail.component.scss']
})
export class FarmAssignTrainingDetailComponent extends BaseDetailComponent implements OnInit {

	  constructor(protected route: ActivatedRoute, protected farmAssignTrainingManager: FarmAssignTrainingManager, protected toastService: ToastService,
	    		  protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService, 
	    		  protected translateService: TranslateService,public commonUtil: CommonUtil ) {
	    	super(farmAssignTrainingManager, commonService, toastService, loadingService, route, router, translateService);
	    	
	  }

	  ngOnInit() {
	  	this.record = new FarmAssignTraining();    
	    this.isDetailPage =  true;
	    this.init();
	  }
  
	  onFetchCompleted() { 
	    super.onFetchCompleted();
	    this.filterParam.relationTable = "FarmAssignTraining";
	    this.filterParam.relationId = this.record.id;
	  }
	  
}
