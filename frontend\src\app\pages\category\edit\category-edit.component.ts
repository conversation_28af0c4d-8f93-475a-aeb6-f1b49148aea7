import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Category } from '../../../models/category';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { CategoryManager } from '../category.manager';

import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
declare const $: any;

@Component({
	selector: 'app-category-edit',
	templateUrl: './category-edit.component.html',
	styleUrls: ['./category-edit.component.scss']
})

export class CategoryEditComponent extends BaseEditComponent implements OnInit {
	public category: Category;
	public languages: Language[];
	public groupCodeOptions: Array<any>;
	public categoryFormInputs: any[];
  readonly MY_CONSTANT = Constant;
	constructor(protected route: ActivatedRoute, protected categoryManager: CategoryManager,
		protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
		protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService
		, private languageManager: LanguageManager
		, public commonUtil: CommonUtil) {
		super(categoryManager, commonService, toastService, loadingService, route, router, translateService);
	}

	ngOnInit() {
		this.category = new Category();
		this.category.isActive = true;
		this.setRecord(this.category);
		this.groupCodeOptions = Constant.CATEGORY_GROUPCODE_OPTIONS;


		this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
		this.languages = new Array<Language>();
		this.init();
	}

	onFetchCompleted() {
		this.category = Category.fromResponse(this.record);
		this.setRecord(this.category);
	}

	async fetchExistingRecord() {
		try {
			const response: RestResponse = await this.manager.fetch(this.request.recordId);
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.setCategoryFormInputs(response.data);

			this.request.isNewRecord = false;
		} catch (error) {
			this.toastService.error(error.message);
		}
	}

	setCategoryFormInputs(allData: any) {
		this.categoryFormInputs = allData.map(data => (
			{
				id: data.id,
				title: data.title,
				description: data.description,
				isActive: data.isActive,
				isDeleted: data.isDeleted,
				languageId: data.languageId,
				languageName: data.languageIdDetail.name,
			})).sort((a, b) => {
				return a.languageName.localeCompare(b.languageName);
			});
	}

  addDescriptionToRemainingLanguages(data: any) {
    this.categoryFormInputs.map(input => {
      if (input.languageName !== Constant.languages.English) {
        input.description = data;
      }
      return input
    })
  }

  addCategoryTitleToRemainingLanguages(data: any) {
    this.categoryFormInputs.map(input => {
      if (input.languageName !== Constant.languages.English) {
        input.title = data;
      }
      return input
    })
  }


	async fetchAssociatedData() {
		this.languages = await this.languageManager.fetchAllData(null);
		if (this.request.recordId == 0) {
			this.categoryFormInputs = this.languages.map(language => (
				{
					languageId: language.id,
					languageName: language.name,
					title: "",
					description: "",
					isActive: true,
					isDeleted: false,

				})).sort((a, b) => {
					return a.languageName.localeCompare(b.languageName);
				});
		}
		this.afterFetchAssociatedCompleted();
	}

	getImageFetchByLanguageName(name: any) {
		switch (name) {
			case Constant.languages.English:
				return "/assets/images/icons/menu/united.svg";
			case Constant.languages.Vietnamese:
				return "/assets/images/icons/menu/Vietnam.svg";
			case Constant.languages.Bahasa:
				return "/assets/images/icons/menu/indonesia.svg"
			default:
				return "/assets/images/icons/menu/united.svg";
		}
	}
	afterFetchAssociatedCompleted() {
		const languageIdId: string = this.route.snapshot.queryParamMap.get('Language');
		if (languageIdId) {
			this.onAssociatedValueSelected({ "id": languageIdId }, 'categoryLanguageIdSelect');
		}
	}

	onSaveSuccess(data: any) {
		this.navigate('/dashboard/category');
	}


	checkConditionToReload(records: BaseModel[], selectedRecord: any) {
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}

	async save(form: any) {
		let updatedCategoryFormInputsData = this.categoryFormInputs.map(({ languageName, ...rest }) => rest)
		this.onClickValidation = !form.valid;
		if (!form.valid) {
			return;
		}

		// if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
		// 	return;
		// }
		try {
			this.loadingService.show();
			const method = this.request.isNewRecord ? 'save' : 'update';
			const response: RestResponse = await this.manager[method](updatedCategoryFormInputsData);
			this.loadingService.hide();
			if (!response.status) {
				this.toastService.error(response.message);
				return;
			}
			this.onSaveSuccess(response.data);
		} catch (error) {
			this.loadingService.hide();
			this.toastService.error(error.message);
		}
	}

	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {
		if (this.request.popupId) {
			$('#' + this.request.popupId).appendTo('body').modal('hide');
		}
		if ((!this.isNullOrUndefined(selectedField) && selectedField === 'categoryLanguageIdSelect') || this.request.popupId === 'categoryLanguageIdPopup') {
			this.category.languageId = selectedRecord.id;
			this.checkConditionToReload(this.languages, selectedRecord);
			return;
		}

	}
}
