import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class AdminCourseEditTitle implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const strProgramAdmin: any | null = route.paramMap.get("id");

        return of(
            strProgramAdmin == 0 ? "Add New Course" : "Edit Course"
        )
    }
}
