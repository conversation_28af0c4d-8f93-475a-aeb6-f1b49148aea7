import { Component, Input, OnD<PERSON>roy, OnInit, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { ManageUserCourseDetail } from 'src/app/models/manageusercoursedetail';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminActiveService } from './program-admin-active-user.service';
import { ProgramAdminActiveManager } from './program-admin-active-user.manager';

@Component({
  selector: 'app-program-admin-active-user',
  templateUrl: './program-admin-active-user.component.html',
  styleUrls: ['./program-admin-active-user.component.scss']
})
export class ProgramAdminActiveUserComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  searchEnrolled: any;

  @Input() filterParam: FilterParam;

  constructor(protected programAdminActiveManager: ProgramAdminActiveManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil,
    private programAdminActiveService: ProgramAdminActiveService, protected route: ActivatedRoute) {
    super(programAdminActiveManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<ManageUserCourseDetail>();
    this.fetchActiveUserRecords();
    this.init();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.hasOwnProperty('filterParam')) {
      this.fetchActiveUserRecords();
    }
  }

  onItemSelection(record: ManageUserCourseDetail) {
    this.onAssociatedValueSelected(record);
  }

  activateDeactiveUserConfirmation(data: any, recordData: any) {
    const confirmationMessage = data.currentTarget.checked
      ? 'Are you sure you want to activate this user account?'
      : 'Are you sure you want to deactivate this user account?';

    this.commonService.confirmation(
      confirmationMessage,
      this.activateDeactiveCallback.bind(this),
      { id: recordData.id, isActive: data.currentTarget.checked },
      null,
      null,
      this.cancelActivateDeactiveCallback.bind(this)
    );
  }

  cancelActivateDeactiveCallback() {
    this.onCancel();
  }

  async activateDeactiveCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.programAdminActiveService.activateDeactivateUser(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  ngOnDestroy() {
    this.clean();
  }

  removeSuccess() {
    console.log('remove success');
    this.onCancel();
  }

  async fetchActiveUserRecords(filterParam?: FilterParam) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.programAdminActiveService.getActiveUser(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/user/edit/' + id])
  }

  removeUserRecord(id: string) {
    this.commonService.confirmation('Would you like to delete?', this.DeleteUserCallback.bind(this), id, null, null, null);
  }

  async DeleteUserCallback(id: string) {
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.programAdminActiveService.removeUserAccess(id);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

}
