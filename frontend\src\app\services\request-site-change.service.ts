import {Injectable} from '@angular/core';
import {HttpServiceRequests} from '../shared/http.service';
import {IResourceWithId, RestResponse} from '../shared/auth.model';
import {HttpClient} from '@angular/common/http';
import {Observable, Subject} from 'rxjs';
import { BaseService } from '../config/base.service';

@Injectable({
  providedIn: 'root'
})
export class RequestSiteChangeService extends BaseService {
  $setCountRead = new Subject<any>();
  constructor(public http: HttpClient) {
    super(http,'/api/requestSiteChange', '/api/requestSiteChanges');
  }

  getRequestSiteChangesCount(data:any): Promise<RestResponse> {
    return this.getRecords('/api/RequestSiteChanges',data);
  }

  requestSiteChangeMarkSeenUpdate(data:any): Promise<RestResponse> {
    return this.updateRecord('/api/seen/requestSiteChange', data);
  }

  updateStatusOfRequestSiteChange(data:any): Promise<RestResponse> {
    return this.updateRecord('/api/requestSiteChange', data);
  }

}
