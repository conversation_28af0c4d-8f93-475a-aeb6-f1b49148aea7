<!--  -->
<div class="breadcrumb-container" *ngIf="!isPlusButton">
	<div class="col-md-12 breadcrumb-detail-container">
		<a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
			<img src="/assets/images/menu.png" class="img-responsive">
		</a>
		<div class="project-name-container">
			<h3 class="project-name">{{"COMMON.ADD" | translate}} {{'Farm.objName' | translate}}</h3>
			<ol class="breadcrumb">
				<li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
				<li><a [routerLink]="['/dashboard/farms']">{{'Farm.objNames' | translate}}</a></li>
				<li class="active" *ngIf="request.isNewRecord">{{"COMMON.ADD" | translate}} {{'Farm.objName' |
					translate}}</li>
				<li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{farm.name}}</li>
			</ol>
		</div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
	<div class="site-card">
		<form #farmForm="ngForm" novalidate="novalidate">
			<div class="row justify-content-start">
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Farm.name" | translate}}
						</label>
						<div class="color-picker-input">
							<input [ngClass]="{'is-invalid':!Name.valid && onClickValidation}" class="form-control"
								type="text" minlength="0" maxlength="255" name="farmName" required="required"
								[(ngModel)]="farm.name" #Name="ngModel">
						</div>
						<app-validation-message [field]="Name" [onClickValidation]="onClickValidation">
						</app-validation-message>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Farm.location" | translate}}
						</label>
						<div class="color-picker-input">
							<input [ngClass]="{'is-invalid':!Country.valid && onClickValidation}" class="form-control"
								type="text" minlength="0" maxlength="255" name="farmCountry" [(ngModel)]="farm.country"
								#Country="ngModel" required="required">
						</div>
						<app-validation-message [field]="Country" [onClickValidation]="onClickValidation">
						</app-validation-message>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Farm.latitude" | translate}}
						</label>
						<div class="color-picker-input">
							<input [ngClass]="{'is-invalid':!Latitude.valid && onClickValidation}" class="form-control"
								type="text" minlength="0" maxlength="100" name="farmLatitude"
								[(ngModel)]="farm.latitude" #Latitude="ngModel" required="required">
						</div>
						<app-validation-message [field]="Latitude" [onClickValidation]="onClickValidation">
						</app-validation-message>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Farm.longitude" | translate}}
						</label>
						<div class="color-picker-input">
							<input [ngClass]="{'is-invalid':!Longitude.valid && onClickValidation}" class="form-control"
								type="text" minlength="0" maxlength="100" name="farmLongitude"
								[(ngModel)]="farm.longitude" #Longitude="ngModel" required="required">
						</div>
						<app-validation-message [field]="Longitude" [onClickValidation]="onClickValidation">
						</app-validation-message>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Farm.description" | translate}}
						</label>
						<textarea required="required" [ngClass]="{'is-invalid':!Description.valid && onClickValidation}"
							class="form-control" rows="5" name="farmDescription" [(ngModel)]="farm.description"
							#Description="ngModel"></textarea>
					</div>
					<app-validation-message [field]="Description" [onClickValidation]="onClickValidation">
					</app-validation-message>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"Farm.propertyNo" | translate}}
						</label>
						<div class="color-picker-input">
							<input [ngClass]="{'is-invalid':!PropertyNo.valid && onClickValidation}" required="required"
								class="form-control" type="text" minlength="0" maxlength="255" name="farmPropertyNo"
								[(ngModel)]="farm.propertyNo" #PropertyNo="ngModel">
						</div>
					</div>
					<app-validation-message [field]="PropertyNo" [onClickValidation]="onClickValidation">
					</app-validation-message>
				</div>
				<!-- <div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Farm.farmingUrl" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="farmFarmingUrl"  [(ngModel)]="farm.farmingUrl" #FarmingUrl="ngModel"> 
									</div>										 
							</div>
						</div> -->
			</div>
		</form>
		<div class="clearfix"></div>
		<div class="col-md-12 no-padding text-right">
			<button title="Save" class="btn btn-primary site-button" type="button" (click)="save(farmForm.form)"
				*ngIf="authService.isAccessible('FARM','AddButton')"
				[disabled]="authService.isDisabled('FARM','AddButton')">
				{{"COMMON.SAVE" | translate}}
			</button>
			<button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button"
				(click)="navigate()">
				{{"COMMON.CANCEL" | translate}}
			</button>
			<div class="clearfix"></div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="clearfix"></div>
</div>