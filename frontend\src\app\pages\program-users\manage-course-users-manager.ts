import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageCourseUserService } from './manage-course-users-service';

@Injectable({
    providedIn: 'root'
})
export class ManageCourseUserManager extends BaseManager {

    constructor(protected manageCourseUserService: ManageCourseUserService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageCourseUserService, loadingService, toastService);
    }
}
