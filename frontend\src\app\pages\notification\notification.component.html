<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container"
    *ngIf="authService.isAdmin()||authService.isProgramAdmin()">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 col-sm-6  mt-2">
                <label>Show <select (change)="onChangeShowEntries($event.target.value)" name="assined-users-length"
                        aria-controls="assined-users-show-entries" class="form-select show-entries form-select-sm">
                        <option value="10" selected="selected">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select> Rows</label>
            </div>
            <div class="col-12 col-sm-6 d-flex justify-content-end mt-2">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="" appDelayedInput
                        (delayedInput)="search($event)">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div *ngIf="records.length > 0" class="custom-action-button text-right">
                <button [disabled]="markNotificationSelected ? false : true"
                    (click)="markNotificationSelected ? seenNotification() : null" type="button"
                    class="btn btn-primary mb-3 action-button" title="Unassigned Staff">Mark As Read
                </button>
                <button [disabled]="markNotificationSelected ? false : true"
                    (click)="markNotificationSelected ? removeNotification() : null" type="button"
                    class="btn btn-primary mb-3 action-button" title="Unassigned Staff">Delete Notification
                </button>
            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th style="width: 60%;">{{'Notification.message' | translate}}</th>
                        <th style="width: 10%;">{{'Notification.createdOn' | translate}}</th>
                        <th style="width: 15%;">
                            <div *ngIf="records.length > 0" class="form-check publish-training-check-cls">
                                <input name="markAll" class="form-check-input mark-all" type="checkbox" value=""
                                    id="markAll" (change)="onChangeMarkNotification($event)"
                                    [ngClass]="{'disable-checkbox':notificationSelected}">
                                <label class="form-check-label" for="markAll">
                                    Select All
                                </label>
                            </div>
                        </th>
                        <!-- <th style="width: 15%;" style="padding-bottom: 11px">
                            <div *ngIf="records.length > 0" class="form-check publish-training-check-cls">
                                <input (change)="selectUnselectAll($event)" name="selectAll"
                                    class="form-check-input selectAll" type="checkbox" value="" id="flexCheckDefault"
                                    [ngClass]="{'disable-checkbox':markNotificationSelected}">
                                <label class="form-check-label" for="flexCheckDefault">
                                    Delete
                                </label>
                            </div>
                        </th> -->
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;" class="records-cls"
                        [ngStyle]="{'background-color': !record.isSeen ? '#eee' : 'transparent' }">
                        <td [innerHTML]="record.message"></td>
                        <td>{{record.createdOn | date:'dd/MM/yyyy'}}</td>
                        <td>
                            <div class="form-check publish-training-check-cls">
                                <!-- <input name="markNotification" class="form-check-input mark-checkbox"
                                    [ngClass]="{'disable-checkbox':notificationSelected || record.isSeen}"
                                    type="checkbox" id="{{'mark'+record.id}}" [value]="record.id"
                                    [checked]="record.isSeen" (change)="onselectUnselectMarkCheck()"> -->
                                <input name="markNotification" class="form-check-input mark-checkbox" type="checkbox"
                                    id="{{'mark'+record.id}}" [value]="record.id"
                                    (change)="onselectUnselectMarkCheck()">
                            </div>
                        </td>
                        <!-- <td>
                            <div class="form-check form-check-inline publish-training-check-cls">
                                <input name="removeNotification" (change)="selectUnselectCheck()"
                                    [ngClass]="{'disable-checkbox':markNotificationSelected}"
                                    class="form-check-input remove-checkbox" type="checkbox" id="{{'remove'+record.id}}"
                                    [value]="record.id">
                            </div>
                        </td> -->
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div data-aos="fade-up" data-aos-duration="1000" class="notification-container container"
    *ngIf="!authService.isAdmin() && !authService.isProgramAdmin()">
    <div class="text-center">
        <button type="button" class="btn notification-buttons training-buttons btn-lg"
            [disabled]="markNotificationSelected ? false : true"
            (click)="markNotificationSelected ? seenNotification() : null">MARK AS READ</button>
        <button type="button" class="btn notification-buttons training-buttons btn-lg"
            [disabled]="markNotificationSelected ? false : true"
            (click)="markNotificationSelected ? removeNotification() : null">DELETE NOTIFICATION</button>
    </div>
    <div *ngIf="records.length == 0" class="p-5 text-center">No notifications found </div>
    <div class="loadMore" scrollTracker (scrollingFinished)="onScrollingFinished()">
        <div class="row checkbox-container">
            <div class="col-6 mark-all-notifications">
                <div *ngIf="records.length > 0" class="form-check publish-training-check-cls">
                    <input name="markAll" class="form-check-input mark-all" type="checkbox" value="" id="markAll"
                        (change)="onChangeMarkNotification($event)"
                        [ngClass]="{'disable-checkbox':notificationSelected}">
                    <label class="form-check-label" for="markAll">
                        Select All
                    </label>
                </div>
            </div>
            <!-- <div class="col-6 remove-all-notifications">
                <div *ngIf="records.length > 0" class="form-check publish-training-check-cls">
                    <input (change)="selectUnselectAll($event)" name="markAll" class="form-check-input selectAll"
                        type="checkbox" value="" id="markAll"
                        [ngClass]="{'disable-checkbox':notificationSelected}">
                    <label class="form-check-label" for="flexCheckDefault">
                        Select All
                    </label>
                </div>
            </div> -->
        </div>
        <div *ngFor="let record of records" class="notification-content"
            [ngStyle]="{'background-color': !record.isSeen ? '#eee' : '#ffffff' }">
            <div class="row">
                <div class="col-8">
                    <p class="text-secondary">{{moment(record.createdOn).format('LL')}}</p>
                </div>
                <div class="col-4 d-flex justify-content-end">
                    <div class="form-check publish-training-check-cls">
                        <input name="markNotification" class="form-check-input mark-checkbox" type="checkbox"
                            id="{{'mark'+record.id}}" [value]="record.id" (change)="onselectUnselectMarkCheck()">
                    </div>
                </div>
                <div class="col-12 margin-top-5">
                    <p class="content-msg" [innerHTML]="record.message"></p>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="loadMore" class="mt-4">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>