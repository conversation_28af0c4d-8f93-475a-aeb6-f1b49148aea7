import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { UsersContactList } from 'src/app/models/userscontactlist';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersContactListManager } from './userscontactlist.manager';

@Component({
  selector: 'app-users-contact-list',
  templateUrl: './users-contact-list.component.html',
  styleUrls: ['./users-contact-list.component.scss']
})
export class UsersContactListComponent extends BaseListServerSideComponent implements OnInit, OnD<PERSON>roy {
  constructor(protected userContactListsManager: UsersContactListManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil) {
    super(userContactListsManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {
  }

  ngOnInit(): void {
    this.records = new Array<UsersContactList>();
    this.init();
  }

  removeSuccess() {
		this.onCancel();
	}
  onCancel() {
    this.request.loadEditPage = false;
		if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
			this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
				dtInstance.destroy();
			});
		}
		this.init();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

}
