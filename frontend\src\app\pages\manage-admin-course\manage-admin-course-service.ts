import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ManageAdminCourseService extends BaseService {
  strProgramAdmin: string;

  constructor(public http: HttpClient) {
    super(http, '/api/course', '/api/courses');
  }

  getUserId(id: string) {
    this.strProgramAdmin = id;
  }

  saveCoursePart(data: any): Promise<RestResponse> {
    return this.saveRecord('/api/coursepart', data);
  }
  updateCoursePart(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/coursepart', data);
  }

  updatePublish(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/course/ispublish', data);
  }

  getisPublish(data: any): Promise<RestResponse> {
    return this.getRecords('/api/ispublish/courses', data);
  }

  // getisPublish(filterParam: FilterParam): Promise<RestResponse> {
  //   return this.getRecords('/api/ispublish/courses', filterParam);
  // }

  changeOrder(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/reorder/coursepart/trainings', data);
  }

  getsCourseDetails(id: string): Observable<RestResponse> {
    return this.getRecord('/api/course/' + id);
  }

  getCourseUserRecords(data: any): Promise<RestResponse> {
    return this.getRecords('/api/courses', data);
  }

  // GET COURSES BY USER ID
  getCourseUserById(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/courses/getByUserId' + this.strProgramAdmin, filterParam);
  }

  fetchAll(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/courses/program/admin', filterParam);
  }

}

