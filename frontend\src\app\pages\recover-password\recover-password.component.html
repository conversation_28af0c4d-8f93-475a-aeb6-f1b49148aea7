<div class="site-login-page">
  <div class="row g-0">
    <div class="col-12 col-lg-5 col-xl-3 col-xxl-3 offset-lg-1 offset-xl-1 offset-xxl-2 login-left-side-section">
      <div class="login-form-section">
        <div class="margin-bottom-40">
          <img src="/assets/images/logo.svg" class="img-fluid" width="200">
        </div>
        <h2>Reset Password</h2>
        <p>There are many variations of passages of the majority</p>
        <div class="clearfix"></div>
        <form class="login-form" #recoverForm="ngForm" novalidate="novalidate">
          <div class="col-12 margin-bottom-30">
            <div class="form-floating position-relative">
              <!-- <input class="form-control" type="password" placeholder="New Password" name="password" #password="ngModel"
                [ngClass]="{'is-invalid':!password.valid && onClickValidation}" [(ngModel)]="data.password"
                required="required" autofocus minlength="8">
              <label for="useUserName"> {{"New Password" | translate}}</label> -->
              <input class="form-control" type="password" placeholder="New Password" name="password" #password="ngModel"
                [ngClass]="{'is-invalid': (!validatePassword(data.password) && onClickValidation)}"
                [(ngModel)]="data.password" required autofocus minlength="8" (ngModelChange)="onClickValidation = true"
                (keypress)="validateFirstLetterCapitalInput($event)">
              <label for="password">{{ "New Password" | translate }}</label>
            </div>
            <!-- <app-validation-message [field]="password" [onClickValidation]="onClickValidation">
            </app-validation-message> -->
            <!-- Error messages -->
            <div *ngIf="!validatePassword(data.password) && onClickValidation" class="text-danger">
              <small *ngIf="!validateFirstLetterCapital(data.password)">Password must start with a capital
                letter.</small><br>
              <small *ngIf="!validateIncludesSymbol(data.password)">Password must include a symbol.</small><br>
              <small *ngIf="!validateIncludesNumber(data.password)">Password must include at least one
                number.</small><br>
              <small *ngIf="!validateMinLength(data.password)">Password must be at least 8 characters long.</small><br>
              <small *ngIf="!validateIncludesLowercase(data.password)">Password must include a lowercase letter.</small>
            </div>
          </div>
          <div class="col-12 margin-bottom-30">
            <div class="form-floating">
              <input class="form-control" [type]="passwordFieldType" placeholder="Confirm Password"
                name="confirmPassword"
                [ngClass]="{'is-invalid':(!confirmPassword.valid || password.value != confirmPassword.value) && onClickValidation}"
                #confirmPassword="ngModel" [(ngModel)]="data.confirmPassword" required="required">
              <label for="useUserName"> {{"CONFIRM PASSWORD" | translate}}</label>
              <i (click)="eyePassword()" *ngIf="passwordFieldType == 'text'" class="bi bi-eye"></i>
              <i (click)="eyePassword()" class="bi bi-eye-slash" *ngIf="passwordFieldType == 'password'"></i>
            </div>

            <app-validation-message [field]="confirmPassword" [comparableField]="password"
              [onClickValidation]="onClickValidation"></app-validation-message>
          </div>
          <div class="clearfix"></div>
          <div class="col-12 margin-bottom-40">
            <button [disabled]="changePasswordButtonDisabled"
              class="btn btn-secondary site-button large-button full-width uppercase-text"
              (click)="resetPassword(recoverForm.form.valid)">
              {{"RECOVER PASSWORD" | translate}}
            </button>
          </div>
        </form>
      </div>
    </div>
    <div class="col-12 col-lg-7 col-xl-8 col-xxl-7 forgot-right-side-section">
    </div>
  </div>
</div>