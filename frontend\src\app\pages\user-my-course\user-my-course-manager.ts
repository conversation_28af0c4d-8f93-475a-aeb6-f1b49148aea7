import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Course } from 'src/app/models/course';
import { UserMyCourseService } from './user-my-course-service';

@Injectable({
    providedIn: 'root'
})
export class UserMyCourseManager extends BaseManager {

    constructor(protected userMyCourseService: UserMyCourseService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userMyCourseService, loadingService, toastService);
    }
}
