<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
  <div class="row">
    <div class="col-12 col-sm-4 text-start">
      <div class="custom-input-group">
        <input class="form-control search-form-control" placeholder="Search" appDelayedInput
          (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchTraining">
        <i class="bi bi-search pe-3"></i>
      </div>
    </div>
    <div class="col-12 col-sm-3">
    </div>
    <div class="col-12 col-sm-5 d-flex align-items-center justify-content-end mb-2 pe-0">
      <button (click)="openFilterTrainingModal()" type="button"
        class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px mb-2 pe-0">
        <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
      </button>
      <!-- <div class="col-12 col-sm-4 text-end pe-0 mb-2">
        <button type="button" class="btn add-button btn-primary btn-lg font-15px add-button-content"
          (click)="onNewRecord()" *ngIf="authService.isAccessible('MANAGE_COURSE_TRAINING','AddButton')">
          <img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-15px" alt="">Add</button>
      </div> -->
    </div>
  </div>
  <div class="training-container">
    <!-- Tabs Container -->
    <div class="tabs-container">
      <div class="tabs">
        <!-- <button class="tab" (click)="selectTab('All');resetFilter()" [class.active]="selectedTab === 'All'">All</button>
        <button *ngFor="let series of learningSeriesData; let i = index" class="tab" (click)="selectTab(series.id)"
          [class.active]="selectedTab === series.id">
          {{ series.title.trim() }}
        </button> -->
        <div *ngIf="showLearningSeriesTabs">
          <button *ngFor="let series of learningSeriesData; let i = index" class="tab" (click)="selectTab(series.id)"
            [class.active]="selectedTab === series.id">
            {{ series.title.trim() }}
          </button>
        </div>
      </div>
    </div>

    <!-- Cards Grid -->
    <div class="cards-grid">
      <app-training-card *ngFor="let training of libraryTraining" [training]="training">
      </app-training-card>
    </div>
  </div>

  <!-- No Record Found -->
  <div *ngIf="isNoRecord" class="no-record-message">
    No record found
  </div>
</div>

<!-- filter training listing -->
<div class="modal fade" id="filterTrainingModal" tabindex="-1" aria-labelledby="filterTrainingModalLabel"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="filterTrainingModalLabel">Filter Training Library</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div *ngIf="filterTrainingModal && filterTrainingModal._isShown" class="modal-body">
        <form #trainingFilterForm="ngForm" novalidate="novalidate">
          <!-- <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':!author.valid && onClickValidation}">
              <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="author"
                [items]="trainingFilterData.data[0].authorDetail" bindLabel="fullName" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.author"
                #author="ngModel">
              </ng-select>
            </div>
            <label for="language">{{"Training.author" | translate}}</label>
          </div> -->
          <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid': !author.valid && onClickValidation}">
              <ng-select placeholder="{{ 'COMMON.SELECT_OPTION' | translate }}" name="author"
                [items]="trainingFilterData.data[0].authorDetail" bindLabel="fullName" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.author"
                (ngModelChange)="onAuthorChange($event)" #author="ngModel">
              </ng-select>
            </div>
            <label for="author">{{ 'Training.author' | translate }}</label>
          </div>
          <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
              <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
                [items]="learningSeriesData" bindLabel="title" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.learningSeries"
                #learningSeriesId="ngModel">
              </ng-select>
            </div>
            <label for="language">{{"Training.learningSeries" | translate}}</label>
          </div>
          <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
              <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                [items]="trainingFilterData.data[0].contentTypeDetail" bindLabel="title" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.contentType"
                #contentTypeId="ngModel">
              </ng-select>
            </div>
            <label for="language">{{"Training.contentTypes" | translate}}</label>
          </div>
          <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
            [fromDateInput]="filterParam.startDate" [toDateInput]="filterParam.endDate"></app-date-range-filter>
          <div class="modal-footer">
            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
            <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
              class="btn btn-primary">Filter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>