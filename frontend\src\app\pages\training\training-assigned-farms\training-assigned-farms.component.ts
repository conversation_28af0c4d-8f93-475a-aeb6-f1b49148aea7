import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { BaseListServerSideComponent } from "src/app/config/base.list.server.side.component";
import { Constant } from "src/app/config/constants";
import { Farm } from "src/app/models/farm";
import { FilterParam } from "src/app/models/filterparam";
import { UserFarm } from "src/app/models/userfarm";
import { Users } from "src/app/models/users";
import { LoadingService } from "src/app/services/loading.service";
import { TrainingAssignedUsersService } from "src/app/services/training-assigned-users.service";
import { RestResponse } from "src/app/shared/auth.model";
import { CommonService } from "src/app/shared/common.service";
import { ToastService } from "src/app/shared/toast.service";
import { TrainingAssignedFarmsManager } from "./training-assigned-farms.component.manager";

declare const $: any;
@Component({
    selector: 'app-training-assigned-farms',
    templateUrl: './training-assigned-farms.component.html',
    styleUrls: ['./training-assigned-farms.component.scss']
})

export class TrainingAssignedFarmsComponent extends BaseListServerSideComponent implements OnInit {
    records: UserFarm[];
    filterParam: FilterParam;
    farmSelected: boolean;
    farmUsers: Users[];
    readonly MY_CONSTANT = Constant;

    constructor(private route: ActivatedRoute, protected trainingAssignedFarmsManager: TrainingAssignedFarmsManager, protected commonService: CommonService,
        protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private trainingAssignedUsersService: TrainingAssignedUsersService) {
        super(trainingAssignedFarmsManager, commonService, toastService, loadingService, router);
    }

    ngOnInit(): void {
        this.records = new Array<UserFarm>();
        this.filterParam = new FilterParam();
        this.farmUsers = new Array<Users>();
        const trainingId = this.route.snapshot.paramMap.get('id');
        this.filterParam.strTrainingId = trainingId;
        this.init();
    }

    // onFetchCompleted() {
    //     console.log(this.records);
    //     var result = this.records.reduce((user, { userId }) => {
    //         if (!user.some(o => o.userId == userId)) {
    //             user.push({ userId, users: this.records.filter(v => v.userId == userId) });
    //         }
    //         return user;
    //       }, []);
    //     result.forEach(res=>{
    //         let farmUser = new Users();
    //         let index = this.farmUsers.findIndex(x=> x.id == res.userId);
    //         if(index !== -1) {
    //             let userIds=null;
    //             res.users.forEach(y=> {
    //                 userIds += userIds !=null ? y.id + "," + userIds : y.id;
    //                 farmUser.id = y.id;
    //                 farmUser.firstName = y.firstName;
    //                 farmUser.fullName = y.fullName;
    //                 farmUser.isActive = y.isActive;
    //                 farmUser.isDeleted = y.isDeleted;
    //                 farmUser.lastName = y.lastName;
    //                 farmUser.phoneNumber = y.phoneNumber;
    //                 farmUser.profileImageUrl = y.profileImageUrl;
    //             });
                
    //         }
    //     });
    // }

    onCancel() {
        if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
                dtInstance.destroy();
            });
        }
        this.init();
    }

    removeSuccess() {
        this.onCancel();
    }

    ngOnDestroy() {
        this.clean();
    }

    search($event) {
        const value = ($event.target as HTMLInputElement).value;
        this.filterParam.searchText = (value && value != '') ? value.trim() : null;
        this.farmSelected = false;
        $(".selectAll").prop('checked', false)
        this.refreshRecord();
    }

    onChangeShowEntries(value: any) {
        this.dtOptions.pageLength = parseInt(value);
        this.farmSelected = false;
        $(".selectAll").prop('checked', false)
        this.refreshRecord();
    }

    selectUnselectAll(event: any) {
        if (event.currentTarget.checked) {
            this.farmSelected = true;
            $('tbody').find('input[type="checkbox"]').prop('checked', true);
        } else {
            this.farmSelected = false;
            $('tbody').find('input[type="checkbox"]').prop('checked', false);
        }

    }

    selectUnselectCheck() {
        let rowsLength = $('tbody').find('.records-cls').length;
        let checkedCheckboxLength = $('tbody').find('input[type="checkbox"]:checked').length;
        if (rowsLength == checkedCheckboxLength)
            $(".selectAll").prop('checked', true)
        else
            $(".selectAll").prop('checked', false)

        if (checkedCheckboxLength > 0)
            this.farmSelected = true;
        else
            this.farmSelected = false;
    }

    unassignedFarm() {
        let trainingIds = []
        $('input[name="trainingId"]:checked').each(function () {
            trainingIds.push(this.value);
        });
        this.commonService.confirmation(trainingIds.length == 1 ? "Would you like to unassigned site?" : "Would you like to unassigned multiple site?",
            this.unAssignedFarmCallback.bind(this), trainingIds);
    }

    async unAssignedFarmCallback(data: any) {
        // console.log(data,'data');
        let modifiedData = [] as any;
        data.forEach(x=> {
            let item =  this.records.find(rec=> rec.id == x);
            let obj = {} as any;
            obj.farmId = item.farmId;
            obj.userId = item.userId;
            obj.languageId = item.userIdDetail.languageId;
            modifiedData.push(obj);
        });
        try {
            this.loadingService.show();
            const response: RestResponse = await this.trainingAssignedUsersService.unassignFarmUsers(modifiedData, this.route.snapshot.paramMap.get('id'));
            this.loadingService.hide();
            if (!response.status) {
                this.toastService.error(response.message);
                return;
            }
            this.onCancel();
            this.toastService.success(response.message);
        } catch (error) {
            this.loadingService.hide();
            this.toastService.error(error.message);
        }
    }
}