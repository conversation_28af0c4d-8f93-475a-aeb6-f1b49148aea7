// SCSS variables for reuse
$primary: #0d6efd;
$modal-bg: #f8fafc;
$modal-radius: 14px;
$modal-shadow: 0 4px 18px rgba(0, 0, 0, 0.13);
$media-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);

.moment-detail-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.07);
  padding: 1.2rem 1.2rem 1rem 1.2rem;
  margin: 1.2rem auto;
  max-width: 820px;
  transition: box-shadow 0.2s;
}

.moment-detail-title {
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -1px;
  margin-bottom: 0;
}

.moment-detail-status {
  vertical-align: middle;
  margin-left: 1rem;
}

.moment-detail-grid,
.elegant-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.7rem 1.2rem;
  margin: 1.2rem 0 0.7rem 0;
}

@media (max-width: 767px) {
  .moment-detail-card {
    padding: 1rem;
  }

  .moment-detail-grid {
    grid-template-columns: 1fr;
  }
}

.info-label {
  font-weight: 600;
  color: #888;
  font-size: 0.97rem;
  margin-bottom: 1px;
  letter-spacing: 0.01em;
}

.info-value {
  font-size: 1.01rem;
  color: #222;
  word-break: break-word;
  margin-bottom: 0.2rem;
}

.section-divider {
  border-top: 1px solid #eee;
  margin: 1.1rem 0 0.7rem 0;
}

.media-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #444;
  margin-bottom: 0.3rem;
}

.recurring-moment-list-cls {
  width: 23px;
  margin-top: -1px;
  margin-right: 5px !important;
}

.elegant-link {
  transition: color 0.18s, text-decoration 0.18s;
  cursor: pointer;
  font-size: 0.97rem;
  min-height: 28px;

  &:hover,
  &:focus {
    color: #0d6efd;
    text-decoration: underline wavy #0d6efd 2px;
  }
}

.elegant-btn {
  transition: box-shadow 0.18s, background 0.18s;
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.08);
  padding: 0.3rem 0.9rem 0.3rem 0.9rem !important;
  font-size: 0.97rem;

  &:hover,
  &:focus {
    background: #0b5ed7;
    box-shadow: 0 4px 16px rgba(13, 110, 253, 0.13);
  }
}

.elegant-media-row {
  margin-bottom: 0.2rem;
}

.elegant-media-row .media-section-title {
  letter-spacing: 0.01em;
  font-size: 1.13rem;
  color: #2a2a2a;
  display: flex;
  align-items: center;
  gap: 0.4em;
}

.elegant-detail-grid>div {
  background: #fafbfc;
  border-radius: 10px;
  padding: 0.5em 0.7em 0.3em 0.7em;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  margin-bottom: 0.1em;
}

@media (max-width: 767px) {
  .elegant-detail-grid>div {
    padding: 0.8em 0.7em 0.5em 0.7em;
  }
}

.modern-moment-card {
  padding: 1.2rem 1.2rem 1rem 1.2rem;
  margin: 1.2rem auto;
  max-width: 800px;
  border-radius: 18px;
  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.10);
  background: #fff;
}

.modern-header {
  min-height: 2.5rem;
}

.modern-accent-bar {
  width: 6px;
  height: 2.5rem;
  border-radius: 6px;
  background: linear-gradient(135deg, #0d6efd 60%, #00c48c 100%);
  flex-shrink: 0;
}

.moment-detail-title {
  font-size: 1.7rem;
  font-weight: 800;
  color: #1a1a1a;
  letter-spacing: -0.5px;
  margin-bottom: 0;
}

.modern-section-divider {
  border-top: 1.5px solid #e3e8ee;
  margin: 0.7rem 0 1.1rem 0;
}

.modern-info-grid {
  display: flex;
  flex-wrap: wrap;
  background: none;
}

.modern-info-grid>.modern-info-divider {
  border-left: 1.5px solid #e3e8ee;
}

.modern-info-grid>div {
  background: none;
  min-width: 0;
}

.info-label {
  font-size: 1.01rem;
  color: #888;
  font-weight: 600;
  margin-bottom: 0.1rem;
  letter-spacing: 0.01em;
}

.info-value {
  font-size: 1.13rem;
  color: #232323;
  margin-bottom: 0.2rem;
  word-break: break-word;
}

.modern-action-bar {
  border-top: none;
  padding-top: 0.2rem;
  margin-top: 0.2rem;
  gap: 1.2rem;
}

.modern-action-btn {
  background: #f6fafd;
  color: #0d6efd;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 1.01rem;
  padding: 0.45rem 1.2rem;
  border: none;
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.07);
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.modern-action-btn:hover,
.modern-action-btn:focus {
  background: #e3e8ee;
  color: #0070f3;
  box-shadow: 0 4px 16px rgba(13, 110, 253, 0.13);
  text-decoration: none;
}

@media (max-width: 767px) {
  .modern-moment-card {
    padding: 0.7rem 0.3rem 0.5rem 0.3rem;
    max-width: 99vw;
  }

  .modern-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem;
  }

  .modern-info-grid {
    flex-direction: column;
  }

  .modern-info-grid>.modern-info-divider {
    border-left: none;
    border-top: 1.5px solid #e3e8ee;
    margin-top: 0.7rem;
    padding-top: 0.7rem;
  }
}

.modern-training-title {
  display: flex;
  align-items: center;
  min-width: 160px;
  justify-content: flex-end;
}

.logs-modal-content-modern {
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.13);
  background: #fff;
  overflow: hidden;
}

.logs-modal-body-modern {
  padding: 1.5rem 1.2rem 1.2rem 1.2rem;
  background: #f8fafc;
  border-radius: 0 0 18px 18px;
}

.modern-logs-table {
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
}

.modern-logs-table th {
  font-weight: 700;
  font-size: 1.01rem;
  background: #f3f6fa !important;
  border-bottom: 2px solid #e3e8ee !important;
}

.modern-logs-table td {
  font-size: 0.99rem;
  vertical-align: middle;
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
}

.modern-logs-table tr:hover {
  background: #eaf4ff !important;
  transition: background 0.18s;
}

.logs-modal-content-modern .modal-header {
  border-bottom: none;
  padding-bottom: 0.5rem;
}

.logs-modal-content-modern .modal-footer {
  border-top: none;
  padding-top: 0.5rem;
}

@media (max-width: 767px) {
  .logs-modal-body-modern {
    padding: 0.7rem 0.2rem 0.7rem 0.2rem;
  }

  .logs-modal-content-modern {
    border-radius: 8px;
  }
}

#momentVideoOrImageModal .modal-content {
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.13);
  background: #fff;
  overflow: hidden;
}

#momentVideoOrImageModal .modal-header {
  border-bottom: none;
  padding-bottom: 0.5rem;
  background: #f8fafc;
}

#momentVideoOrImageModal .modal-title {
  font-weight: 700;
  font-size: 1.18rem;
  color: #0d6efd;
}

#momentVideoOrImageModal .modal-body {
  background: #f8fafc;
  padding: 1.5rem 1.2rem 1.2rem 1.2rem;
  border-radius: 0 0 18px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 220px;
}

#momentVideoOrImageModal .img-fluid {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.07);
  max-width: 100%;
  max-height: 420px;
  margin: 0 auto;
  display: block;
}

#momentVideoOrImageModal video {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.07);
  width: 100%;
  max-height: 420px;
  background: #000;
  margin: 0 auto;
  display: block;
}

.loading-container-video-training {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba($primary, 0.08);
  border-radius: 10px;
  padding: 0.7rem 0.5rem;
  margin-bottom: 0.7rem;
  width: 100%;
}

#momentVideoOrImageModal .modal-footer {
  border-top: none;
  padding-top: 0.5rem;
  background: #f8fafc;
}

@media (max-width: 767px) {
  #momentVideoOrImageModal .modal-content {
    border-radius: 8px;
  }

  #momentVideoOrImageModal .modal-body {
    padding: 0.7rem 0.2rem 0.7rem 0.2rem;
  }
}

.modern-media-modal-content {
  border-radius: $modal-radius;
  box-shadow: $modal-shadow;
  background: #fff;
  overflow: hidden;
  max-width: 420px;
  margin: 0 auto;

  .modern-media-modal-header {
    border-bottom: none;
    background: $modal-bg;
    padding-bottom: 0.3rem;
    padding-top: 0.7rem;
    padding-left: 1rem;
    padding-right: 1rem;

    .modern-media-header-bar {
      font-size: 1.05rem;
      font-weight: 700;
      color: $primary;
      letter-spacing: -0.5px;
      flex-grow: 1;
    }
  }

  .modern-media-modal-body {
    background: $modal-bg;
    padding: 1.1rem 0.7rem 0.7rem 0.7rem;
    border-radius: 0 0 $modal-radius $modal-radius;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 120px;

    .modern-media-video {
      border-radius: 10px;
      box-shadow: $media-shadow;
      width: 100%;
      max-width: 320px;
      max-height: 220px;
      background: #000;
      margin: 0 auto;
      display: block;
    }

    .modern-media-image {
      border-radius: 10px;
      box-shadow: $media-shadow;
      max-width: 100%;
      max-height: 220px;
      margin: 0 auto;
      display: block;
    }

    .loading-container-video-training {
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba($primary, 0.08);
      border-radius: 10px;
      padding: 0.7rem 0.5rem;
      margin-bottom: 0.7rem;
      width: 100%;
    }
  }

  .modern-media-modal-footer {
    border-top: none;
    padding-top: 0.3rem;
    background: $modal-bg;
    padding-bottom: 0.7rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.close-icon {
  background-color: #f9eae5;
  color: #d85143;
  font-size: 15px;
  width: 100px;
  height: 40px;
  border: none !important;
  border-radius: 7px;
}

@media (max-width: 767px) {
  .modern-media-modal-content {
    border-radius: 7px;
    max-width: 98vw;

    .modern-media-modal-header,
    .modern-media-modal-footer {
      padding-left: 0.4rem;
      padding-right: 0.4rem;
    }

    .modern-media-modal-body {
      padding: 0.5rem 0.1rem 0.5rem 0.1rem;

      .modern-media-video,
      .modern-media-image {
        max-width: 95vw;
        max-height: 120px;
      }
    }
  }
}