import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MomentEditBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const momentId: string | null = route.paramMap.get("id");
    const isNew = momentId == '0';
    return of([
      { title: "Dashboard", link: "/dashboard", active: false },
      { title: "Manage Moments", link: "/dashboard/moments", active: false },
      { title: isNew ? "Moment" : "Moment", link: `/dashboard/moment/edit/${momentId}`, active: true }
    ]);
  }
} 