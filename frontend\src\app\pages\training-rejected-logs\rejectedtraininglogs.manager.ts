import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RejectedTrainingLogsService } from './rejectedtraininglogs.service';

@Injectable({
    providedIn: 'root'
})
export class RejectedTrainingLogsManager extends BaseManager {

    constructor(protected rejectedTrainingLogsService: RejectedTrainingLogsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(rejectedTrainingLogsService, loadingService, toastService);
    }
}
