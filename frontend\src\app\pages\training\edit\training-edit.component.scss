.form-check {
    padding-left: 0px;

    .form-switch {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .custom-form-check {
        padding: 12px;
        width: 28px;
    }
}

.custom-switch {
    margin: 20px 0px;
}

.upload-video-container label img {
    width: auto !important;
    margin-top: 13px;
}

.upload-video-container label {
    border-radius: 50;
    width: auto !important;
    height: auto !important;
    margin-top: 6px;
}

#userEditComponent .dashboard-content-container .moment-image-container img {
    width: 100%;
    height: 140px !important;
    -o-object-fit: cover;
    object-fit: cover;
}

.upload-img-container {
    border: 1px dashed #07182F !important;
    border-radius: 20px !important;
    opacity: 1;
    padding: 24px 40px !important;
    height: 140px;
}

.upload-video-container {
    border-radius: 20px !important;
    border: 0px;
    opacity: 1;
    height: 140px !important;
}

.video-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 140px !important;
}

.custom-form {
    max-width: 650px;
    margin: auto;
}

.publish-training-check-cls .form-check-input {
    width: 28px !important;
    margin-left: 0px;
}

.form-check-label{
  a {
    text-decoration: none;
    color: #007bff;

    &:hover {
      text-decoration: underline;
    }
  }

}

::ng-deep .ck-toolbar {
  display: flex;
  flex-wrap: nowrap;  /* Disable wrapping of the toolbar items */
  overflow-x:hidden;
  overflow-y:visible;  /* Allow horizontal scrolling if the toolbar is too long */
}

.color-dark-grey{
  color: #71828a;
}

::ng-deep .ck-editor__editable_inline {
  min-height: 180px;   /* Set minimum height */
  max-height: auto;    /* Set maximum height to auto (grows as needed) */
  width: 100%;         /* Ensure the width is responsive */
}

.color-dark-grey{
  color: #71828a;
}
