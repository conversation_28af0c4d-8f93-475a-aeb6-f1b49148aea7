import { Component, Input, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, On<PERSON>nit, SimpleChanges } from '@angular/core';
import { Course } from 'src/app/models/course';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadingService } from 'src/app/services/loading.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ManageCourseUserService } from '../program-users/manage-course-users-service';
import { ManageCourseService } from '../manage-course/manage-course-service';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { ManageUserCourseDetail } from 'src/app/models/manageusercoursedetail';
import { UserEnrolledManager } from './user-enrolled.manager';
import { UserEnrolledService } from './user-enrolled.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RouteDataService } from 'src/app/shared/title.service';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-user-enrolled-list',
  templateUrl: './user-enrolled-list.component.html',
  styleUrls: ['./user-enrolled-list.component.scss']
})
export class UserEnrolledListComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  // courseUser: ManageUserCourseDetail;
  // userDetailsTabs: string = "enrolled";
  // records: ManageUserCourseDetail[];
  searchEnrolled: any;
  public user: any;

  @Input() filterParam: FilterParam;

  constructor(protected userEnrolledManager: UserEnrolledManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil,
    private userEnrolledService: UserEnrolledService, protected route: ActivatedRoute) {
    super(userEnrolledManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<ManageUserCourseDetail>();
    // this.courseUser = new ManageUserCourseDetail();
    //this.fetchEnrolledCourseRecords();
    this.init();
  }

  // ngOnChanges(changes: SimpleChanges) {
  //   if (changes.hasOwnProperty('filterParam')) {
  //     this.fetchEnrolledCourseRecords();
  //   }
  // }

  onItemSelection(record: ManageUserCourseDetail) {
    this.onAssociatedValueSelected(record);
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  ngOnDestroy() {
    this.clean();
  }

  removeSuccess() {
    this.onCancel();
  }

  async fetchEnrolledCourseRecords() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userEnrolledService.getEnrolledgCourse(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async RevokeCourseCallback(id: string) {
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.userEnrolledService.revokeAccessCourse(id);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  revokeCourse(id: string) {
    this.commonService.confirmation('Would you like to revoke all course access for this user?', this.RevokeCourseCallback.bind(this), id, null, null, null);
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/user/edit/' + id])
  }

}
