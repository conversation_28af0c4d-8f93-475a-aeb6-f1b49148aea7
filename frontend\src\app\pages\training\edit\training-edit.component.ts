import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Training } from '../../../models/training';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { TrainingManager } from '../training.manager';

import { Category } from '../../../models/category';
import { SubCategoryManager } from '../../subcategory/subcategory.manager';
import { SubCategory } from '../../../models/subcategory';
import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from 'src/app/config/constants';
import { RestResponse } from 'src/app/shared/auth.model';
import { environment } from 'src/environments/environment';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { lang } from 'moment';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { TrainingCategoryMapping } from 'src/app/models/trainingcategorymapping';
import { TrainingSubCategoryMapping } from 'src/app/models/trainingsubcategorymapping';
import { Content } from '@angular/compiler/src/render3/r3_ast';
import { LearningSeries } from 'src/app/models/learningseries';
import { ManageContentManager } from '../../manage-content-type/manage-content.manager';
import { ManageLearningSeriesService } from '../../manage-learning-series/manage-learning-series.service';
import { TrainingService } from '../training.service';
import { FilterParam } from 'src/app/models/filterparam';
import { UsersManager } from '../../users/users.manager';
import { ManageLearningSeriesManager } from '../../manage-learning-series/manage-learning-series.manager';
import { DOC_ORIENTATION, NgxImageCompressService } from 'ngx-image-compress';
import { IImage, ImageCompressService, ResizeOptions } from 'ng2-image-compress';
import { error } from 'console';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';

declare const $: any;

@Component({
  selector: 'app-training-edit',
  templateUrl: './training-edit.component.html',
  styleUrls: ['./training-edit.component.scss']
})

export class TrainingEditComponent extends BaseEditComponent implements OnInit, OnDestroy {
  public training: Training;
  public contentTypes: Content[];
  public learningSeries: any[];
  public loading: boolean = false;
  public videoPlaying: boolean = false;
  readonly MY_CONSTANT = Constant;
  private fileData: any;
  uploadingThumbnail: boolean;
  uploader: any;
  users: any[] = [];
  usersSelected: any[] = [];
  fileUploadingMessage: string = "UPLOADING..";
  intervalId: NodeJS.Timeout;
  dropdownSettings = {};
  deletedUsersSelected: any[] = [];
  thumbnailUploader: FileUploader;
  selectedCurrentType: string;
  fileUploadType: string;
  public Editor = ClassicEditor;
  public editorConfig = {
    toolbar: [
      'heading', '|',
      'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'highlight', '|',
      'link', 'imageUpload', 'blockQuote', 'codeBlock', '|',
      'bulletedList', 'numberedList', 'outdent', 'indent', '|',
      'alignment', 'horizontalLine', 'insertTable', 'mediaEmbed', '|',
      'fontSize', 'fontColor', 'fontBackgroundColor', '|',
      'undo', 'redo', 'removeFormat', 'sourceEditing'
    ],
    height: 'auto',
    shouldNotGroupWhenFull: true
  };


  constructor(protected route: ActivatedRoute, protected trainingManager: TrainingManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private imgCompressService: ImageCompressService,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService
    , private manageContentManager: ManageContentManager, private trainingService: TrainingService
    , public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private usersManager: UsersManager, private manageLearningSeriesManager: ManageLearningSeriesManager) {
    super(trainingManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.fileUploadType = "";
    this.setDropdownSettings();
    this.training = new Training();
    this.training.isActive = true;
    this.setRecord(this.training);
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.contentTypes = new Array<Content>();
    this.learningSeries = new Array<LearningSeries>();
    this.request.recordId != 0 && this.getLearningSeriesListOnEdit()
    this.init();
    this.uploader = this.initializeUploaderTraining(null, 'mp4,mkv', null, null, this.toastService, "Only Mkv, Mp4 files are allowed", null);
    this.thumbnailUploader = this.initializeUploaderTraining(null, 'jpg,png,jpeg', null, null, this.toastService, "Only Jpeg, Jpg, Png are allowed", null)
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  async fetchUsersList(methodType: string, filterParam: FilterParam, accessibility: string, editRecord: boolean) {

    try {
      const response: RestResponse = await this.usersManager[methodType](filterParam);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      this.users = response.data.map(user => {
        return {
          id: accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED ? user.userId : user.id,
          fullName: accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED ? user.userFirstName + " " + user.userLastName + " (" + user.userEmail + ")" : user.fullName + " (" + user.email + ")",
        }
      });
      if (accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED && !editRecord) {
        this.usersSelected = this.users;
        const usersId = this.users.map(user => ({ userId: user.id }));
        this.training.userAssignTrainings = usersId;
      }
      this.loadingService.hide();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }

  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  onFetchCompleted() {
    this.training = Training.fromResponse(this.record);
    this.setRecord(this.training);
  }

  async setlearningSeriesData(filterParam: FilterParam) {
    this.loadingService.show();
    try {
      const response: RestResponse = await this.trainingService.getLearningSeriesByContentTypeId(filterParam);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      if (response.status) {
        this.learningSeries = response.data.map(data => {
          return {
            id: data.learningSeriesDetail.id,
            title: data.learningSeriesDetail.title,
            // accessibility: data.learningSeriesDetail.accessibility
          }
        })
        this.loadingService.hide();
      }
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  uploadThumbnail(event: any) {
    this.fileUploadType = 'thumbnailImage';
    // if (event && event.target.files && event.target.files.length > 0) {
    this.uploadingThumbnail = true;
    this.fileData = {} as any;
    this.fileData.files = event.target.files;
    //this.onThumbnailFileProcessingCompleted(this.fileData.files);
    this.compressThumbnail();
    // }
  }

  compressThumbnail() {
    let images: Array<IImage> = [];
    let option: ResizeOptions = { Resize_Quality: 90, Resize_Max_Width: 500, Resize_Max_Height: 400, Resize_Type: 'jpeg' };
    ImageCompressService.filesToCompressedImageSource(this.fileData.files).then(observableImages => {
      observableImages.subscribe((image) => {
        // console.log(image);
        images.push(image);
      }, (error) => {
        // console.log("Error while converting");
      }, () => {
        let compresImages = new Array<any>();
        let obj: any = {};
        obj.base64 = images[0].compressedImage.imageDataUrl;
        obj.type = images[0].type;
        compresImages.push(this.base64ToFile(obj));
        // console.log(compresImages, 'compress images', this.fileData.files, 'file data files', this.training.thumbnailImageUrl, 'url')
        this.onThumbnailFileProcessingCompleted(compresImages);
      });
    });
  }


  base64ToFile(obj: any) {
    const byteCharacters = atob(obj.base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, ''));
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    // console.log(obj.type)
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: obj.type });
    var extention = obj.type.split('/');
    let file = new File([blob], 'cropImage.' + extention[1], { type: obj.type });
    return file;
  }

  onThumbnailFileProcessingCompleted(files) {
    this.thumbnailUploader.addToQueue(files);
    this.thumbnailUploader.uploadAll();
  }


  addUser(event: any) {
    this.training.userAssignTrainings.push({ userId: event.id });
  }

  removeUser(event: any) {
    let index = this.training.userAssignTrainings.findIndex(user => user.userId == event.id);
    if (this.training.userAssignTrainings[index].id) {
      this.training.userAssignTrainings[index].isDeleted = true;
      this.deletedUsersSelected.push(this.training.userAssignTrainings[index]);
      this.training.userAssignTrainings.splice(index, 1);
    } else {
      this.training.userAssignTrainings.splice(index, 1);
    }
  }

  selectContentType(event: any) {
    // console.log(event, "event");
    this.selectedCurrentType = event.title;
    // console.log(this.selectedCurrentType, "this.selectedCurrentType")
    //this.training.learningSeries = undefined
    // let param = new FilterParam();
    // param.strCategoryId = event.id
    //this.setlearningSeriesData(param);
  }

  async uploadVideo(event: any) {
    this.fileUploadType = 'thumbnailVideo';
    const video: any = await this.commonUtil.loadVideo(event.target.files[0])
    this.training.duration = video.duration;
    if (this.training.mediaType === 'VIDEO') {
      this.loadingService.show();
    }
    this.loading = true;

    const file = event.target.files[0];
    if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg"
      && event.target.files[0].type != "image/png") {
      this.commonService.convertVideoFormat(file).then(res => {
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    }
    else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  selectLearningSeries(event: any) {
    this.training.accessibility = event.accessibility;
    if (event.accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED) {
      this.loadingService.show()
      this.filterParam.learningSeriesId = event.id;
      this.deleteAllSelectedUsers();
      this.fetchUsersList("fetchLearningSeriesUsers", this.filterParam, event.accessibility, false);
    } else {
      this.loadingService.show()
      this.deleteAllSelectedUsers();
      this.fetchUsersList("fetchActiveUsers", null, null, false);
    }
  }

  async fetchAssociatedData() {
    this.contentTypes = await this.manageContentManager.fetchAllData(this.filterParam);
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/training');
  }

  async onUploadSuccessTraining(file: any, files: any) {
    this.loadingService.hide();
    const filePath = file.streamingPath ? file.streamingPath : file.path;
    // console.log(this.fileUploadType, file.path);
    switch (this.fileUploadType) {
      case 'thumbnailImage':
        this.training.thumbnailImageUrl = file.path;
        this.uploadingThumbnail = false;
        break;
      case 'thumbnailVideo':
        let gumletResponse = null;

        if (file.streamingId) {
          this.filterParam.gumletId = file.streamingId;

          gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

          this.intervalId = setInterval(async () => {
            gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

            if (gumletResponse && gumletResponse.status) {
              // console.log('Status:', gumletResponse.data.status);
              this.fileUploadingMessage = gumletResponse.data.status ? gumletResponse.data.status.toUpperCase() + ".." : "UPLOADING..";

              if (gumletResponse.data.status == "ready" || gumletResponse.data.status == "queued" || gumletResponse.data.status == "downloading" || gumletResponse.data.status == "downloaded" || gumletResponse.data.status == "validating" || gumletResponse.data.status === "errored") {
                // console.log("errored:", gumletResponse.responseMessage);

                if (gumletResponse.data.status == "errored") {
                  // Handle the "errored" status
                  // console.log("errored:", gumletResponse.data.responseMessage);
                  this.toastService.error('Video is errored while processing');
                  this.loading = false; // Set loading to false
                  clearInterval(this.intervalId); // Clear the interval
                  return;
                  // You can add additional handling for the error if needed
                  // You might want to set a flag or handle the error state appropriately
                } else {
                  if (this.intervalId) {
                    clearInterval(this.intervalId);
                  }
                  this.training.url = filePath;
                  this.fileUploadingMessage = "UPLOADING..";
                  this.loading = false;
                }
              }
            } else {
              if (this.intervalId) {
                clearInterval(this.intervalId);
              }
              this.training.url = file.path;
              this.fileUploadingMessage = "UPLOADING..";
              this.loading = false;
            }
          }, 5000);
        } else {
          this.training.url = filePath;
          this.loading = false;
        }
        break;
    }
  }


  fileValidationErrorTraining(data: string, toastService: any) {
    this.loading = false;
    toastService.error(data);
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessTraining(file, files);
        }, 200);
      }
    };

    uploader.onErrorItem = (fileItem, response) => {
      this.loading = false;
      toastService.error('Something error occurred please try again later');
    }

    return uploader;
  }

  playVideoFromPlayIcon() {
    var videoId = document.getElementById("training_video") as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true;
        this.videoPlaying = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      this.videoPlaying = true
      videoId.controls = true;
    });
    videoId.addEventListener("ended", (event) => {
      this.videoPlaying = false
      videoId.controls = false;

    });
  }

  selectAccessibility(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const isChecked = inputElement.checked;
    // Update the model based on the checkbox state
    this.training.accessibility = isChecked ? 'Public' : 'Private';
  }

  selectTrainingRequired(event: boolean, type) {
    if (type == this.MY_CONSTANT.TRAINING_REQUIRED.WATCH_REQUIRED) {
      this.training.watchRequired = event;
      this.training.uploadRequired = false;
    }
    if (type == this.MY_CONSTANT.TRAINING_REQUIRED.UPLOAD_REQUIRED) {
      this.training.watchRequired = false;
      this.training.uploadRequired = event;
    }
  }

  async getLearningSeriesListOnEdit() {
    this.learningSeries = await this.manageLearningSeriesManager.fetchAllData(this.filterParam);
  }

  deleteAllSelectedUsers() {
    if (this.training.userAssignTrainings && this.training.userAssignTrainings.length > 0) {
      this.training.userAssignTrainings?.map(user => {
        if (user.id) {
          this.deletedUsersSelected.push({
            ...user,
            isDeleted: true
          })
        }
        return user;
      })
    }
    this.training.userAssignTrainings = [];
    this.usersSelected = [];
  }
  async fetchExistingRecord() {
    this.loadingService.show();
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.loadingService.hide();
        this.toastService.error(response.message);
        return;
      }
      this.training = { ...response.data, mediaType: "VIDEO" };
      // if (this.training.learningSeriesDetail.accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED) {
      //   this.filterParam.learningSeriesId = this.training.learningSeries;
      //   this.fetchUsersList("fetchLearningSeriesUsers", this.filterParam, this.training.learningSeriesDetail.accessibility, true);
      // } else {
      //   this.fetchUsersList("fetchActiveUsers", null, null, true);
      // }
      let usersSelected: any = [];
      this.training.userAssignTrainings?.map(user => {
        usersSelected.push({
          id: user.userId,
          fullName: user.userFirstName + " " + user.userLastName,
        })
      })
      this.usersSelected = usersSelected;
      this.request.isNewRecord = false;
      this.loadingService.hide();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }


  removeFile(fileUrl: string) {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), fileUrl);
  }

  removeFileCallback(fileUrl) {
    if (fileUrl == this.training.thumbnailImageUrl) {
      this.training.thumbnailImageUrl = null;
    } else {
      this.commonService.deleteVideo(this.training.url)
      this.training.url = null;
      this.training.duration = 0;
      this.videoPlaying = false;
    }
  }

  async saveTrainingsForm(form: any, isPublish: boolean) {

    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    if (!this.training.url) {
      this.onClickValidation = true;
      return
    }

    if (!this.training.thumbnailImageUrl) {
      this.onClickValidation = true;
      return
    }

    // if (this.training.userAssignTrainings?.length == 0 && this.training.accessibility == this.MY_CONSTANT.TRAINING_ACCESSIBILITY.RESTRICTED) {
    //   this.onClickValidation = true;
    //   return
    // }
    this.training.isCourseTraining = false;
    let training = JSON.parse(JSON.stringify(this.training));
    // training.userAssignTrainings = training.userAssignTrainings.concat(this.deletedUsersSelected);
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.manager[method](training);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.training.isPublish = isPublish;
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async onFileProcessingCompleted(files: any) {
    this.uploader.addToQueue(files);
    this.uploader.uploadAll();
  }
}