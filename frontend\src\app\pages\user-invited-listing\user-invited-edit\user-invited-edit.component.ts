import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { BaseModel } from 'src/app/config/base.model';
import { CourseInvited } from 'src/app/models/course-invited';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { UserInvitedManager } from '../user-invited.manager';
import { UserInvitedService } from '../user-invited.service';
import { Course } from 'src/app/models/course';

@Component({
  selector: 'app-user-invited-edit',
  templateUrl: './user-invited-edit.component.html',
  styleUrls: ['./user-invited-edit.component.scss']
})
export class UserInvitedEditComponent extends BaseEditComponent implements OnInit {
  public courseInvited: CourseInvited;
  selectedCourseUserId: string;
  selectedCourseCode: any;
  publishCourseList: any[];
  selectedCourseId: string;
  dropdownSettings: {};

  constructor(protected route: ActivatedRoute, private userInvitedService: UserInvitedService, protected userInvitedManager: UserInvitedManager,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router,
    protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService,
    public commonUtil: CommonUtil) {
    super(userInvitedManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
    this.courseInvited = new CourseInvited();
    this.publishCourseList = new Array();
    this.courseInvited.isActive = true;
    this.setRecord(this.courseInvited);
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.init();
    this.fetchPublishRecords();
    // this.fetchExistingRecord();
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);

  }

  async fetchPublishRecords() {
    try {
      var course = new Course();
      course.id = this.selectedCourseId;
      this.loadingService.show();
      const response: RestResponse = await this.userInvitedService.getisPublish(null);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'code',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }


  // onFetchCompleted() {
  //   this.courseInvited = CourseInvited.fromResponse(this.record);
  //   this.setRecord(this.courseInvited);
  // }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/program-admin/users');
  }

  checkConditionToReload(records: BaseModel[], selectedRecord: any) {
    if (!records.some(x => x.id === selectedRecord.id)) {
      this.fetchAssociatedData();
    }
  }

  hasError(event: any) {
  }

  // selectInviteUserCourse(event: any): void {
  //   if (event) {
  //     // this.selectedInviteUserCourse = event.title;
  //     this.selectedCourseCode = event.code;
  //     this.selectedCourseUserId = event ? event.id : null;
  //   }
  // }

  selectInviteUserCourse(event: any) {
    if (event) {
      this.courseInvited.course = event.id;
    }
  }


  async fetchExistingRecord() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.courseInvited = response.data;
      if(this.courseInvited && this.courseInvited.courseDetail){
        this.selectedCourseUserId =   this.courseInvited.course;
      }
      this.request.isNewRecord = false;
      this.onFetchCompleted();
    } catch (error) {
      this.toastService.error(error.message);
    }
  }


  async save(form: any) {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.userInvitedManager[method](this.courseInvited);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
