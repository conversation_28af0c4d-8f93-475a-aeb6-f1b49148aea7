<input
  [attr.required]="required ? true : null"
  [attr.placeholder]="placeholder"
  mask="000000000000000"
  class="form-control"
  type="text"
  ng2TelInput
  [disabled]="disabled || isDisabled"
  [(ngModel)]="value"
  (ngModelChange)="handleInput($event)"
  (hasError)="handleError($event)"
  maxlength="15"
  [ngClass]="{'is-invalid': isFieldInvalid() && onClickValidation}"
/>
<div *ngIf="isFieldInvalid() && onClickValidation" class="invalid-feedback d-block">
  Please enter a valid phone number.
</div>
<!-- Optionally integrate with <app-validation-message> here if needed -->