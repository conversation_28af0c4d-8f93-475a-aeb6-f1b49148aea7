import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageCourseManager } from '../manage-course-manager';
import * as moment from 'moment';
import { Course } from 'src/app/models/course';
import { RestResponse } from 'src/app/shared/auth.model';
import AOS from 'aos';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { Training } from 'src/app/models/training';
import { RouteDataService } from 'src/app/shared/title.service';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-course-detail',
  templateUrl: './course-detail.component.html',
  styleUrls: ['./course-detail.component.scss']
})
export class CourseDetailComponent extends BaseListServerSideComponent implements OnInit {
  selectedCourseId: any;
  searchCourse: any;
  moment: any = moment;
  fromDate: any;
  toDate: any;
  recordData: any;
  course: Course;
  coursePartModal: any;
  trainings: CourseTrainings;
  description: string;
  isCourseDetailVisible: boolean = false;
  username: any;

  constructor(protected route: ActivatedRoute, protected manageCourseManager: ManageCourseManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
    protected translateService: TranslateService, public commonUtil: CommonUtil, public routeDataService: RouteDataService) {
    super(manageCourseManager, commonService, toastService, loadingService, router);
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1).split('?');
    // Read the 'type' query parameter and set the boolean accordingly
    this.route.queryParams.subscribe((params) => {
      const type = params['type'];
      this.isCourseDetailVisible = type === 'preview'; // Set to true if 'preview', otherwise false
    });
    this.init();
    this.fetchCourseDetailsRecord(this.selectedCourseId);
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1).split('?');
    const currentRoute = this.router.url
    if (currentRoute.includes('/preview/course/detail/')) {
      this.isCourseDetailVisible = true
    }

  }

  toRoman(num: number): string {
    const romanNumerals: { [key: number]: string } = {
      1: 'I', 2: 'II', 3: 'III', 4: 'IV', 5: 'V', 6: 'VI', 7: 'VII', 8: 'VIII', 9: 'IX',
      10: 'X', 11: 'XI', 12: 'XII', 13: 'XIII', 14: 'XIV', 15: 'XV', 16: 'XVI', 17: 'XVII',
      18: 'XVIII', 19: 'XIX', 20: 'XX', 40: 'XL', 50: 'L', 90: 'XC', 100: 'C', 400: 'CD',
      500: 'D', 900: 'CM', 1000: 'M'
    };
    const keys = Object.keys(romanNumerals).map(Number).reverse();
    let result = '';

    keys.forEach(key => {
      while (num >= key) {
        result += romanNumerals[key];
        num -= key;
      }
    });

    return result;
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  async fetchCourseDetailsRecord(id) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseManager.getsCourseDetails(id);
      this.loadingService.hide();
      this.onFetchCompleted();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.course = response.data;
      this.setBreadcrumb()
      //set the page title on the basis of course details
      this.setPageTitle()

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // modal open course part content
  openCoursePartModal(event, record) {
    this.description = record.description;
    // event.delegateTarget.stopPropagation()
    event.preventDefault();
    event.stopPropagation();
    AOS.init({ disable: true });
    // this.coursePartModal.show();
    $("#coursePartModal").modal('show');
  }

  closeCoursePartModal() {
    // this.coursePartModal.hide();
    $("#coursePartModal").modal('hide');
  }


  openTrainingDetails(training: Training, record: any) {
    if (this.authService.isProgramAdmin()) {
      const route = `/dashboard/program-admin/course/training/detail/${training.id}`;
      this.router.navigate([route],);
    } else if (this.authService.isAdmin()) {
      const route = `/dashboard/program-admin/course/training/detail/${training.id}`;
      this.router.navigate([route],);
    }
  }


  openCourseParticipants(record: any) {
    this.router.navigate(['/dashboard/program-admin/course/training/participants/' + record.id]
    );
  }

  setPageTitle() {
    this.routeDataService.setData(this.router.url, this.course.title);
  }

  setBreadcrumb(){
    const fullUrl = this.router.url; // Current full URL
    const queryIndex = fullUrl.indexOf('?'); // Find query parameter start
    const cleanUrl = queryIndex > -1 ? fullUrl.slice(0, queryIndex) : fullUrl;
    this.routeDataService.addBreadcrumb(this.course.title, cleanUrl, this.course.id)
  }

}

