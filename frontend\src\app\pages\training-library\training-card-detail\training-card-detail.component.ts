import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ManageTrainingLibraryService } from '../training-library.service';
import { ToastService } from 'src/app/shared/toast.service';
import { VideoTraining } from 'src/app/models/trainingLibrary';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';


@Component({
  selector: 'app-training-card-detail',
  templateUrl: './training-card-detail.component.html',
  styleUrls: ['./training-card-detail.component.scss']
})
export class TrainingCardDetailComponent implements OnInit {

  trainingId: string;
  trainingData: VideoTraining;
  safeVideoUrl: SafeResourceUrl; // Declare a safe URL variable

  constructor(private route: ActivatedRoute, protected loadingService: LoadingService, private ManageTrainingLibraryService: ManageTrainingLibraryService,
    protected toastService: ToastService,private sanitizer: DomSanitizer) { }

  ngOnInit(): void {
    // Get the training ID from the route parameters
    this.route.paramMap.subscribe(params => {
      this.trainingId = params.get('id');

      // Fetch the training data based on the ID
      this.fetchTrainingLibrary(this.trainingId);
    });
  }


  async fetchTrainingLibrary(id:string) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.ManageTrainingLibraryService.fetch(id).toPromise();
      this.loadingService.hide();
      if (!response.status) {
        return;
      }
      this.trainingData = new VideoTraining(response.data);
      if (this.trainingData?.url) {
        this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.trainingData.url);
      }
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}
