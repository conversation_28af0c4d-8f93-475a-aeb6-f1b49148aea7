<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
  <div class="dashboard-content-container">
    <form #farmForm="ngForm" novalidate="novalidate">
      <div class="offset-xxl-1">
        <div class="row site-form-container">
          <div class="mt-2 mb-3">
            <h4 class="fw-bold">{{request.recordId == 0 ? "New Company" : "Edit Company"}}</h4>
            <p class="user-edit-msg">Please make sure you fill all the fields before you click on save button</p>
          </div>
          <div class="col-12 col-md-12 col-xxl-10 mb-4">
            <div class="form-floating">
              <input [ngClass]="{'is-invalid':!name.valid && onClickValidation}" class="form-control" type="text"
                name="farmName" #name="ngModel" [(ngModel)]="farm.name" required="required"
                placeholder="Name of the Site" autocomplete="off">
              <label for="floatingInput"> {{"Farm.name" | translate}}</label>
            </div>
            <app-validation-message [field]="name" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>

          <div class="col-12 col-md-12 col-xxl-10 mb-4">
            <div class="form-floating form-floating-textarea">
              <textarea [ngClass]="{'is-invalid':!Description.valid && onClickValidation}"
                class="form-control form-description" name="farmDescription" #Description="ngModel"
                [(ngModel)]="farm.description" placeholder="Description" id="floatingTextarea2"></textarea>
              <label for="floatingInput">{{"Farm.description" | translate}}</label>
            </div>
          </div>

          <div class="col-12 col-md-6 col-xxl-5 mb-4">
            <div class="form-floating">
              <input class="form-control" type="text" autocomplete="off" name="farmCode" #FarmCode="ngModel"
                [(ngModel)]="farm.farmCode" maxLength="10" required="required"
                placeholder="{{'Farm.identifier' | translate}}"
                [ngClass]="{'is-invalid':!FarmCode.valid && onClickValidation}" appDelayedInput
                (delayedInput)="verifyFarmCode($event)" [delayTime]="1000"
                [disabled]="!request.isNewRecord && farm.farmCode && !authService.isSuperAdmin()">
              <label for="farmCode"> {{"Farm.identifier" | translate}}</label>
            </div>
            <app-validation-message [field]="FarmCode" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>

          <div class="col-12 col-md-6 col-xxl-5 mb-4">
            <div class="form-floating">
              <input
                ngx-google-places-autocomplete
                [options]="googleOptions"
                (onAddressChange)="handleAddressChange($event)"
                class="form-control"
                name="googleAddress"
                placeholder="Address"
              >
              <label for="googleAddress">Location</label>
              <app-validation-message [onClickValidation]="onClickValidation"></app-validation-message>
            </div>
          </div>

          <!-- Logo uploader start (moved below description) -->
          <div class="col-12 col-md-12 col-xxl-10 mb-4">
            <div *ngIf="!farm.logoUrl" class="form-control text-center border-2 p-3 border-dark mb-3"
              [ngClass]="{'is-invalid': !farm.logoUrl && onClickValidation}">
              <label id="file-input" class="upload-img-button cursor-pointer"
                [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': isLoader}">
                <img src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">
                {{!isLoader ? 'UPLOAD LOGO' : 'UPLOADING..'}}
                <div *ngIf="isLoader" class="spinner-border ms-2" role="status" style="width: 1.7rem; height: 1.7rem">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <input *ngIf="!isLoader" name="logoUrl" class="d-none" (change)="uploadFarmImage($event)" required="required"
                  id="file-input" type="file" accept="image/png, image/jpg, image/jpeg"/>
              </label>
              <app-validation-message [field]="logoUrl" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>
            <div *ngIf="farm.logoUrl"
              class="moment-image-container mb-3 d-flex align-items-center justify-content-center" style="gap: 24px;">
              <img [src]="farm.logoUrl"
                style="height: 320px; width: 480px; object-fit: cover; border-radius: 32px;" />
              <div class="position-absolute delete-video-container"
                style="top: 24px; right: 24px; cursor:pointer; font-size: 2rem; background: white; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;"
                (click)="removeFile()">
                <i class="bi bi-x"></i>
              </div>
            </div>
          </div>
          <!-- Logo uploader end -->

          <div class="col-12 col-md-12 col-xxl-10 mb-4">
            <div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
              <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3" type="button"
                (click)="save(farmForm.form)" [disabled]="request.isRequested">
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="clearfix"></div>
  </div>
</div>