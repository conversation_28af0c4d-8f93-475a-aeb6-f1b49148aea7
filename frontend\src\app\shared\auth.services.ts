import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LocalStorageService } from 'angular-2-local-storage';
import { Constant } from '../config/constants';
import { IResourceWithId } from './auth.model';
import { CommonUtil } from './common.util';
import { HttpServiceRequests } from './http.service';

declare const $: any;
@Injectable({
  providedIn: 'root'
})
export class AuthService extends HttpServiceRequests<IResourceWithId> {

  constructor(public http: HttpClient, private localStorageService: LocalStorageService, private router: Router,
    private activatedRoute: ActivatedRoute) {
    super(http);
  }

  logout() {
    const currentRole = this.getRoles();
    this.localStorageService.remove('token');
    this.localStorageService.remove('user');
    this.localStorageService.remove('project');
    if (window.innerWidth < 992) {
      $("body").css("overflow", "auto");
    }
    this.router.navigate(['/login'])
  }

  getToken(): any {
    return this.localStorageService.get('token') as any;
  }

  getUser(): any {
    return this.localStorageService.get('user') as any;
  }

  getRoles() {
    const user = this.localStorageService.get('user') as any;
    if (CommonUtil.isNullOrUndefined(user)) {
      return '';
    }
    return user.roles;
  }

  hasRole(roles: any[]): boolean {
    // this is used in case user has single role
    //return roles.indexOf(this.getRoles()) !== -1;
    return roles.some(r => this.getUser().roles?.includes(r));
  }

  isAdmin(): boolean {
    return this.hasRole(["ROLE_ADMIN"]);
  }
  hasValidToken(): boolean {
    const token: any = this.getToken();
    return !CommonUtil.isNullOrUndefined(token) && token.accessToken && token.expires_at && token.expires_at > new Date().getTime();
  }

  isAuthorizedUser(roles: Array<string>) {
    const promise = new Promise((resolve) => {
      if (!this.hasValidToken()) {
        this.localStorageService.remove('token');
        this.localStorageService.remove('user');
      }
      resolve({ hasAccess: this.hasValidToken(), hasRoleAccess: roles.some(x => this.getRoles().indexOf(x) !== -1) });
    });
    return promise;
  }

  isAccessible(ENTITY, VIEW) {
    const userRoles = this.getUser().roles;
    if (CommonUtil.isNullOrUndefined(ENTITY) || CommonUtil.isNullOrUndefined(VIEW)) {
      return false;
    }
    const allowedRoles = Constant.VIEW_USER_MAPPING[ENTITY + '_ACCESS'][VIEW];
    if (CommonUtil.isNullOrUndefined(allowedRoles)) {
      return false;
    }
    return allowedRoles.SHOW_TO_ROLE.some((ele) => userRoles.includes(ele));
  }

  isDisabled(ENTITY, VIEW) {
    const userRoles = this.getUser().roles;
    if (CommonUtil.isNullOrUndefined(ENTITY) || CommonUtil.isNullOrUndefined(VIEW)) {
      return false;
    }
    const allowedRoles = Constant.VIEW_USER_MAPPING[ENTITY + '_ACCESS'][VIEW];
    if (CommonUtil.isNullOrUndefined(allowedRoles)) {
      return true;
    }
    return !allowedRoles.ENABLED_FOR_ROLE.some((ele) => userRoles.includes(ele));
  }

  isSuperAdmin(): boolean {
    return this.hasRole(["ROLE_SUPER_ADMIN"]);
  }

  isProgramAdmin(): boolean {
    return this.hasRole(["ROLE_PROGRAM_ADMINISTRATOR"]);
  }

  isFarmAdmin(): boolean {
    return this.hasRole(["ROLE_FARM_ADMIN"]);
  }

  setUer(user) {
    this.localStorageService.set('user', user);
  }

}
