.training-detail-main-cls {
    padding: 90px 45px !important;
  }
  
  .video-wrapper {
    height: unset !important;
  }
  
  :picture-in-picture {
    box-shadow: 0 0 0 5px red;
  }
  
  .dashboard-content-container {
    .moment-image-container {
      border-radius: 23px;
      max-width: 90% !important;
      overflow: hidden;
      position: relative;
      margin-left: 30px;
    }
  }
  
  .dashboard-content-container {
    .video-container {
      max-width: 90% !important;
    }
  }

  .form-switch .accessibility-switch:not(:checked) {
    margin-left: -2.5em;
    background-color: #f44336;
    border: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3ccircle cx='4' cy='4' r='4' fill='%23fff'/%3e%3c/svg%3e");
    background-position: left center;
    background-repeat: no-repeat;
    background-size: 16px;
    border-radius: 2em;
    transition: background-position 0.15s ease-in-out;
  }

  