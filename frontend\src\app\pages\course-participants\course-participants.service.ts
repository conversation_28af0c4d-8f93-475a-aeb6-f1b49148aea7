import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class CourseParticipantsService extends BaseService {
    strUserId: string;

    constructor(public http: HttpClient) {
        super(http, '/api/course/trainings/participant', '/api/course/trainings/participants');
    }

    getUserId(id: string) {
        this.strUserId = id;
    }

    fetchCourseTrainingParticipants(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/course/trainings/participants', filterParam);
    }

    approveOrRejectTraining(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/training/approve/reject', data);
    }
}

