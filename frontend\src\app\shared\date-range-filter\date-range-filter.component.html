<div>
  <mat-form-field class="accent w-100">
    <mat-label>Choose a from date</mat-label>
    <input class="mat-datepicker-input" readonly name="fromDate" matInput [min]="minFromDate"
      (focus)="frompicker.open()" [max]="maxFromDate" [matDatepicker]="frompicker" [(ngModel)]="fromDate"
      (dateInput)="fromDateChange('input', $event)">
    <mat-icon matDatepickerToggleIcon (click)="clearFromDate($event)">clear</mat-icon>
    <mat-datepicker-toggle matSuffix [for]="frompicker"></mat-datepicker-toggle>
    <mat-datepicker #frompicker></mat-datepicker>
  </mat-form-field>
</div>
<div>
  <mat-form-field class="accent w-100">
    <mat-label>Choose a to date</mat-label>
    <input class="mat-datepicker-input" readonly [(ngModel)]="toDate" name="toDate" matInput [min]="minToDate" [max]="maxToDate"
      [matDatepicker]="topicker" (dateInput)="toDateChange('input', $event)" (focus)="topicker.open()">
    <mat-icon matDatepickerToggleIcon (click)="clearToDate($event)">clear</mat-icon>
    <mat-datepicker-toggle matSuffix [for]="topicker"></mat-datepicker-toggle>
    <mat-datepicker #topicker></mat-datepicker>
  </mat-form-field>
</div>
