<div class="site-customer-main-container ps-1" data-aos="fade-up" data-aos-duration="1000">
  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-5  mt-2">
        <div class="custom-input-group">
          <input class="form-control search-form-control height-51px" placeholder="Search" appDelayedInput
            (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchTraining">
          <i class="bi bi-search pe-3"></i>
        </div>

      </div>
      <div class="col-12 col-sm-7 d-flex justify-content-end mt-2">
        <div class="manage-moment-button me-2">
          <button [disabled]="isTrainingCsvExport ? true : false" (click)="exportTrainingHistroyCsv()" type="button"
            class="btn manage-filter-buttton export-btn btn-secondary text-light btn-lg d-flex height-51px font-15px">
            <i class="icon-export bi bi-box-arrow-down me-2" alt=""></i><span class="margin-top-2">{{isTrainingCsvExport
              ?
              'Please Wait...' : 'Export Report'}} </span>
          </button>
        </div>
        <button (click)="openFilterTrainingModal()" type="button"
          class="btn manage-filter-buttton bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
        </button>
      </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th style="width: 110px; text-wrap: nowrap;">{{'Training.lastUpdatedDate' | translate}}</th>
            <th style="width:120px; text-wrap: nowrap;">{{"Training.learningSeries" | translate}}</th>
            <th style="width:120px; text-wrap: nowrap;">{{"Training.contentType" | translate}}</th>
            <th style="width:117px; text-wrap: nowrap;">{{"Training.trainingHistory" | translate}}</th>
            <th style="width:117px; text-wrap: nowrap;">{{'Training.TrainingRequired' | translate}}</th>
            <th style="width:200px; text-wrap: nowrap;">{{"Training.TrainingStatus" | translate}}</th>
            <th style="width:150px; text-wrap: nowrap;">{{"Training.UserVideo" | translate}}</th>
            <th style="width:150px; text-wrap: nowrap;">{{"Training.rejectedLogs" | translate}}</th>
            <th style="width: 50px; text-wrap: nowrap;">{{"Training.action" | translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr class="records-cls" *ngFor="let record of records;">
            <td>
              <div>{{moment(record?.updatedOn).format('DD/MM/YYYY')}}</div>
            </td>
            <td>
              {{record?.trainingDetail?.learningSeriesDetail?.title}}
            </td>
            <td class="text-lowercase">{{record?.trainingDetail?.contentTypeDetail?.title}}</td>
            <!-- <td class="text-capitalize"><a>{{record?.trainingDetail?.title}}</a></td> -->
            <td>
              <a (click)="openTrainingDetailPage(record)" class="text-decoration-underline">{{record?.trainingDetail?.title}}</a>
            </td>
            <!-- <td class="text-capitalize"><a class="text-decoration-underline"
                  [routerLink]="['/dashboard/farm/edit/'+record?.farmIdDetail?.id]">
                  {{record?.farmIdDetail?.name}}</a>
              </td> -->

            <td>
              <h6><span *ngIf="record?.trainingDetail?.uploadRequired == true"
                  class="badge integrax-tags integrax-font">Uploading Required</span></h6>
              <h6><span *ngIf="record?.trainingDetail?.watchRequired == true"
                  class="badge integrax-tags integrax-font integrax-black">Watching Required</span></h6>
            </td>
            <td>
              <div *ngIf="record?.status else noStatus">
                <div [ngClass]="commonUtil.getTitleStatusColor(record.status)"
                  class="status-button semi-bold">
                  <img class="me-1" [src]="commonUtil.getTitleStatusImg(record.status)">
                  {{commonUtil.getTitleStatus(record.status)}}
                </div>
              </div>
              <ng-template #noStatus>-</ng-template>
            </td>
            <td>
              <button *ngIf="record.videoUrl && record?.status !== MY_CONSTANT.TRAINING_STATUS.INPROGRESS; else NoVideo"
                (click)="watchVideo(record)" class="user-video-button bg-secondary">
                <img src="/assets/images/icons/menu/watch-video.svg"></button>
              <ng-template #NoVideo>-</ng-template>
            </td>
            <td>
              <img src="/assets/images/icons/menu/rejected-logs.png"
                *ngIf="authService.isAccessible('TRAINING','RejectedLogs') && record.rejectedLogsCount && record.rejectedLogsCount > 0 else NoRejectedLogs"
                [class.disabled]="authService.isDisabled('TRAINING','RejectedLogs')" title="Rejected Logs Count"
                (click)="openRejectedLogsCount(record)" class=" me-2 width-32px cursor-pointer" />
              <ng-template #NoRejectedLogs><span class="me-2 width-32px">-</span></ng-template>
            </td>
            <!-- <td>
              <div class="d-flex align-items-center">
                <i *ngIf="record?.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                  (click)="approveOrRejectTraining(record.trainingId, 'APPROVED')"
                  class="bi bi-check-circle fs-4 cursor-pointer"></i>
                <i *ngIf="record?.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                  (click)="approveOrRejectTraining(record.trainingId, 'REJECTED')"
                  class="bi bi-x-circle ps-2 fs-4 cursor-pointer"></i>
              </div>
            </td> -->
            <td>
              <div class="d-flex align-items-center">
                <i *ngIf="record && record.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                  (click)="approveOrRejectTraining(record.trainingId, 'APPROVED')"
                  class="bi bi-check-circle fs-4 cursor-pointer"></i>
                <i *ngIf="record && record.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                  (click)="approveOrRejectTraining(record.trainingId, 'REJECTED')"
                  class="bi bi-x-circle ps-2 fs-4 cursor-pointer"></i>
                <ng-container *ngIf="!record || record.status !== MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;">
                  <span>-</span>
                </ng-container>
              </div>
            </td>
          </tr>
          <!-- <tr *ngIf="records.length===0">
              <td class="text-center" colspan="5">
                {{"COMMON.NORECORDS" | translate}}
              </td>
            </tr> -->
        </tbody>
      </table>
      <label>Show <select (change)="onChangeShowEntries($event.target.value)" name="assined-users-length"
          aria-controls="assined-users-show-entries" class="form-select show-entries form-select-sm height-51px">
          <option value="10" selected="selected">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select> Rows</label>
    </div>
  </div>
</div>

<div class="modal fade" id="userTrainingVideoModal" aria-hidden="true" aria-labelledby="userTrainingVideoModalLabel"
  tabindex="-1">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="userTrainingVideoModalLabel"></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" *ngIf="userTrainingVideoModal && userTrainingVideoModal._isShown">
        <div>
          <div *ngIf="loadingVideo" class="loading-container-video-training">
            <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
            <div class="spinner-border text-light" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
          <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
            id="staff-video"></video>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="filterTrainingModal" tabindex="-1" aria-labelledby="filterTrainingModalLabel"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="filterTrainingModalLabel">Filter Training</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div *ngIf="filterTrainingModal && filterTrainingModal._isShown" class="modal-body">
        <form #trainingFilterForm="ngForm" novalidate="novalidate">
          <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
              <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
                [items]="trainingFilterData.data[0].learningSeriesDetail" bindLabel="title" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.learningSeries"
                #learningSeriesId="ngModel">
              </ng-select>
            </div>
            <label for="language">{{"Training.learningSeries" | translate}}</label>
          </div>
          <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
              <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                [items]="trainingFilterData.data[0].contentTypeDetail" bindLabel="title" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.contentType"
                #contentTypeId="ngModel">
              </ng-select>
            </div>
            <label for="language">{{"Training.contentTypes" | translate}}</label>
          </div>
          <div class="form-floating">
            <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
              [ngClass]="{'is-invalid':!trainingStatus.valid && onClickValidation}">
              <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="trainingStatus"
                [items]="trainingFilterData.data[0].trainingStatusDetail" bindLabel="status" bindValue="id"
                class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.status"
                #trainingStatus="ngModel">
              </ng-select>
            </div>
            <label for="language">{{"Training.TrainingStatus" | translate}}</label>
          </div>
          <!-- <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="fromDate" [toDateInput]="toDate"></app-date-range-filter> -->
          <div class="modal-footer">
            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
            <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
              class="btn btn-primary">Filter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div class="modal fade modal-xl" id="rejectedLogsCountsModal" aria-hidden="true"
  aria-labelledby="rejectedLogsCountsModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="rejectedLogsCountsModalLabel">Training Rejected Logs List</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <app-training-rejected-logs [getRejectedLogsCountsData]="rejectedLogsCountsData && rejectedLogsCountsData"
          *ngIf="rejectedLogsCountsModal && rejectedLogsCountsModal._isShown"></app-training-rejected-logs>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>