<div class="training-card">
  <div class="video-container" (click)="navigateToDetail(training.id)">
    <video #videoPlayer class="training-card-video" preload="metadata" [poster]="training.thumbnailImageUrl"
      (loadedmetadata)="onVideoLoad(videoPlayer)">
      <source [src]="training.url" type="video/mp4">
      Your browser does not support the video tag.
    </video>
    <div class="video-overlay"></div>
  </div>
  <div class="training-card-content">
    <div class="training-card-header">
      <div class="training-card-author">
        <img [src]="'/assets/images/icons/menu/author.svg'" alt="Author Icon" class="training-card-author-icon">
        <span class="training-card-author-name code" title="View Author Profile"
          (click)="openAuthorDetails(training.userDetails?.id)">
          {{ training.userDetails?.authorName || 'Unknown Author' }}
        </span>
      </div>
      <span class="training-card-date">{{ training.publishDate | date: 'dd-MM-yyyy' }}</span>
    </div>
    <h2 class="training-card-title code" [attr.title]="training.title"
      (click)="openTrainingDetails(training)">
      {{ training.title }}
    </h2>
    <hr class="training-card-divider">
    <div class="training-card-footer">
      <p class="training-card-description"
        [title]="training.learningSeriesDetail?.title || 'No title available.'">
        {{ training.learningSeriesDetail?.title || 'No title available.' }}
      </p>
    </div>
  </div>
</div>