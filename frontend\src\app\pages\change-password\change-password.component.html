<div class="breadcrumb-container">
  <div class="col-md-12 breadcrumb-detail-container">
    <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
      <img src="/assets/images/menu.png" class="img-responsive">
    </a>
    <div class="project-name-container">
      <h3 class="project-name">Change Password</h3>
      <ol class="breadcrumb">
        <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
        <li class="active">Change Password</li>
      </ol>
    </div>
  </div>
  <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
  <div class="site-card">
    <div class="col-md-4 col-md-offset-4">
      <form class="form-item" #changePasswordForm="ngForm" novalidate="novalidate">
        <div class="form-group" [ngClass]="{'has-error':!oldPassword.valid && onClickValidation}">
          <h5 class="ng-binding">{{"OLD PASSWORD" | translate}}</h5>
          <input class="form-control" type="password" placeholder="Enter Current Password" name="oldPassword"
            #oldPassword="ngModel" [(ngModel)]="data.oldPassword" required="required" autofocus>
        </div>
        <div class="form-group"
          [ngClass]="{'has-error': (!newPassword.valid && onClickValidation) || !newPassword.valid}">
          <h5 class="ng-binding">{{ "NEW PASSWORD" | translate }}</h5>
          <input class="form-control" type="password" placeholder="Enter New Password" name="newPassword"
            #newPassword="ngModel" [(ngModel)]="data.password" required autofocus
            pattern="^(?=.*[!@#$%^&*])(?=.*[0-9])(?=.*[A-Z]).{8,}$" />
          <small *ngIf="!newPassword.valid && onClickValidation" class="text-danger">
            Password must include at least one special character, one number, one uppercase letter, and be at least 8
            characters long.
          </small>
        </div>
        <div class="form-group"
          [ngClass]="{'has-error': (!confirmPassword.valid && onClickValidation) || data.password !== data.confirmPassword}">
          <h5 class="ng-binding">{{"CONFIRM NEW PASSWORD" | translate}}</h5>
          <input class="form-control" type="password" placeholder="Confirm Password" name="confirmPassword"
            #confirmPassword="ngModel" [(ngModel)]="data.confirmPassword" required="required" autofocus>
        </div>
        <div class="pull-right">
          <button class="btn btn-primary site-button large-btn" (click)="update(changePasswordForm.form)">{{"CHANGE
            PASSWORD" | translate}}</button>
        </div>
      </form>
      <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
  </div>
</div>