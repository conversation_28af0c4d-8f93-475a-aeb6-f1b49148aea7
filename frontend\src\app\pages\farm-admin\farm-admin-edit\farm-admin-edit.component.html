<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="m-t-20">
    <div class="dashboard-content-container">
        <form *ngIf="farmAdmin.id || request.recordId == 0" #addFarmAdminForm="ngForm" novalidate="novalidate">
            <div class="row site-form-container">
                <div class="col-12 col-md-12 offset-xxl-1 col-xxl-9 d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold">{{request.recordId == 0 ? "Add New User" : "Edit User" }}
                        </h4>
                        <p class="user-edit-msg">Please make sure you fill all the fields before you click on
                            {{request.recordId == 0 ? 'save' : 'update'}} button</p>
                    </div>
                </div>

                <div class="col-12 col-md-6 offset-xxl-1 col-xxl-2 mb-4">
                    <div class="form-floating">
                        <input class="form-control" type="text" name="firstName" #firstName="ngModel"
                            [(ngModel)]="farmAdmin.firstName" required="required" placeholder="First Name" (keypress)="commonService.restrictSpecialCharacters($event)"
                            [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
                        <label for="firstName">{{"USERS.FirstName" | translate}}</label>
                    </div>
                    <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div>

                <div class="col-12 col-md-6 col-xxl-2 mb-4">
                    <div class="form-floating">
                        <input class="form-control" type="text" name="lastname" #lastname="ngModel"
                            [(ngModel)]="farmAdmin.lastName" required="required" placeholder="Last Name" (keypress)="commonService.restrictSpecialCharacters($event)"
                            [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}">
                        <label for="lastname">{{"USERS.LastName" | translate}}</label>
                    </div>
                    <app-validation-message [field]="lastname" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div>

                <div class="col-12 col-md-6 col-xxl-5 mb-4">
                    <div class="form-floating">
                        <input autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
                            (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                            (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="farmAdmin.phoneNumber"
                            [ngClass]="{'is-invalid':!phone.valid && onClickValidation}"
                            [required]="!farmAdmin.email"
                            placeholder="Phone Number" maxlength="15" pattern="^[0-9]+$"
                            (countryChange)="onCountryChange($event)" #phoneField>
                    </div>
                    <app-validation-message [field]="phone" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div>

                <div class="col-12 col-md-6 offset-xxl-1 col-xxl-4 mb-4">
                    <div class="form-floating">
                        <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control"
                            type="email" name="email" #email="ngModel" [(ngModel)]="farmAdmin.email" placeholder="Email"
                            pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$" required="required">
                        <label for="floatingInput">{{"USERS.Email" | translate}}</label>
                    </div>
                    <app-validation-message [field]="email" [onClickValidation]="onClickValidation">
                    </app-validation-message>
                </div>

                <div class="col-12 col-xxl-5 mb-4">
                    <div class="form-floating">
                        <div class="form-control select-width ng-select-main-container"
                            [ngClass]="{'is-invalid':!farmIds.valid && onClickValidation}">
                            <ng-select [items]="farms" bindLabel="displayLabel" bindValue="id" name="farmIds" #farmIds="ngModel"
                                required="required" [(ngModel)]="farmAdmin.farmIds" placeholder="Select Companies"
                                [multiple]="true" (ngModelChange)="onFarmsChange($event)">
                            </ng-select>
                        </div>
                        <label for="farmIds">{{'Farm.objNames' | translate}}</label>
                        <app-validation-message [field]="farmIds"
                            [onClickValidation]="onClickValidation"></app-validation-message>
                    </div>
                </div>

                <div class="col-12 col-md-12 offset-xxl-1 col-xxl-9 d-flex justify-content-end">
                    <button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
                        type="button" (click)="save(addFarmAdminForm.form)" [disabled]="request.isRequested">
                        {{request.recordId == 0 ? 'SAVE' : 'UPDATE'}}
                    </button>
                </div>
            </div>
        </form>
        <div class="clearfix"></div>
    </div>
</div>