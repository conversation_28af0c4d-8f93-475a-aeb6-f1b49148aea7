import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminInactiveService } from './program-admin-inactive-user.service';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminInactiveManager extends BaseManager {

    constructor(protected programAdminInactiveService: ProgramAdminInactiveService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(programAdminInactiveService, loadingService, toastService);
    }
}
