import { Injectable } from '@angular/core';
import { HttpServiceRequests } from '../shared/http.service';
import { IResourceWithId, RestResponse } from '../shared/auth.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseService } from '../config/base.service';
import { FilterParam } from '../models/filterparam';

@Injectable({
  providedIn: 'root'
})
export class TrainingAssignedUsersService extends BaseService {
  trainingId: string;
  constructor(public http: HttpClient) {
    super(http, '', '');
  }

  getTrainingId(id: string) {
    this.trainingId = id;
  }

  fetchAll(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/' + this.trainingId + '/user', filterParam);
  }

  update(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/delete/userfarm', data);
  }

  fetchAllAssignFarms(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/' + filterParam.strTrainingId + '/farms', filterParam);
  }

  unassignFarm(data: any,trainingId: string): Promise<RestResponse> {
    return this.updateRecord('/api/unassign/farms/' + trainingId, data);
  }
  fetchAllAssignFarmUsers(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/' + filterParam.strTrainingId + '/farm/users', filterParam);
  }

  unassignFarmUsers(data: any,trainingId: string): Promise<RestResponse> {
    return this.updateRecord('/api/unassign/farm/users/' + trainingId, data);
  }
}
