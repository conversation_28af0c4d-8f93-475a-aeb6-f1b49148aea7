import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MostWatchTrainingsService } from './most-watch-trainings.service';

@Injectable({
    providedIn: 'root'
})
export class MostWatchTrainingsManager extends BaseManager {

    constructor(protected mostWatchTrainingsService: MostWatchTrainingsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(mostWatchTrainingsService, loadingService, toastService);
    }
}
