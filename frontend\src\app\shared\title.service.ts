import { Injectable, ViewChild } from '@angular/core';
import { LayoutComponent } from '../pages/layout/layout.component';

@Injectable({
  providedIn: 'root'
})
export class RouteDataService {
  private data: { [key: string]: any } = {};
  private breadcrumbs: { title: string, link: string, id: string }[] = [];
  // @ViewChild(LayoutComponent) layoutComponent!: LayoutComponent;
  private layoutComponent!: LayoutComponent;

  constructor() {

  }

  setLayoutComponent(layout: LayoutComponent) {
    this.layoutComponent = layout;
  }

  setData(key: string, value: any): void {
    this.data[key] = value;
  }

  getData(key: string): any {
    return this.data[key];
  }

  getDataall(key: string): any {
    return this.data;
  }

  //Breadcrumb functionality

  addBreadcrumb(title: string, link: string, id: string): void {
    const existing = this.breadcrumbs.find(b => b.id === id && b.link === link);
    if (!existing) {
      this.breadcrumbs.push({ title, link, id });
      this.breadcrumbs = [...this.breadcrumbs]
    }
    this.layoutComponent.getBreadcrumb();

  }

  // Remove breadcrumb (e.g., when navigating back)
  removeBreadcrumb(id: string): void {
    this.breadcrumbs = this.breadcrumbs.filter(b => b.id !== id);
  }

  // Get all breadcrumbs
  getBreadcrumbs(): { title: string, link: string, id: string }[] {
    return this.breadcrumbs;
  }

  // Function to update breadcrumbs based on current route
  updateBreadcrumbsBasedOnRoute(currentRoute: string): void {
    const includesCourseDetail = currentRoute.includes('/course/detail/') || currentRoute.includes('/preview/course/detail/');
    const includesTrainingDetail = currentRoute.includes('/course/training/detail/') || currentRoute.includes('/training/program/admin/detail/');
    const includesGenericTrainingDetail = currentRoute.includes('/training/detail/');
    const includesTrainingParticipants = currentRoute.includes('/course/training/participants/');

    // 1. Remove breadcrumb for `/course/training/detail/` if the current route does not include it
    if (!includesTrainingDetail) {
      this.breadcrumbs = this.breadcrumbs.filter(
        breadcrumb => !breadcrumb.link.includes('/course/training/detail/') && !breadcrumb.link.includes('/training/program/admin/detail/')
      );
    }

    // 2. Remove breadcrumb for `/course/training/participants/` if the current route does not include it
    if (!includesTrainingParticipants) {
      this.breadcrumbs = this.breadcrumbs.filter(
        breadcrumb => !breadcrumb.link.includes('/course/training/participants/')
      );
    }

    // 3. Remove breadcrumb for `/training/detail/` if the current route does not include it
    if (!includesGenericTrainingDetail) {
      this.breadcrumbs = this.breadcrumbs.filter(
        breadcrumb => !breadcrumb.link.includes('/training/detail/')
      );

    }

    // 4. Filter breadcrumbs to keep only relevant ones
    this.breadcrumbs = this.breadcrumbs.filter((breadcrumb) => {
      const isCourseRelated =
        breadcrumb.link.includes('/course/detail/') ||
        breadcrumb.link.includes('/preview/course/detail/')||
        breadcrumb.link.includes('/course/training/detail/') ||
        breadcrumb.link.includes('/training/program/admin/detail/') ||
        breadcrumb.link.includes('/training/detail/')||
        breadcrumb.link.includes('/course/training/participants/');
      const isInActiveRoute = breadcrumb.link === currentRoute;

      // Keep breadcrumb if it matches the active route or is course-related
      return isInActiveRoute || isCourseRelated;
    });

    // 4. If neither `/course/detail/`, `/course/training/detail/`, nor `/training/detail/` is active, clear breadcrumbs
    if (!includesCourseDetail && !includesTrainingDetail && !includesGenericTrainingDetail && !includesTrainingParticipants) {
      this.clearBreadcrumbs();
    }
  }

  // Clear all breadcrumbs
  clearBreadcrumbs(): void {
    this.breadcrumbs = [];
  }

}
