import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { MostCompletedTrainingUsersListService } from './most-completed-training-users-list.service';

@Injectable({
    providedIn: 'root'
})
export class MostCompletedTrainingUsersListManager extends BaseManager {

    constructor(protected mostCompletedTrainingUsersListService: MostCompletedTrainingUsersListService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(mostCompletedTrainingUsersListService, loadingService, toastService);
    }
}
