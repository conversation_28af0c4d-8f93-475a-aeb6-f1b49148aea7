import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminDashboardService } from './program-admin-dashboard.service';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminDashboardManager extends BaseManager {

    constructor(protected programAdminDashboardService: ProgramAdminDashboardService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(programAdminDashboardService, loadingService, toastService);
    }
}