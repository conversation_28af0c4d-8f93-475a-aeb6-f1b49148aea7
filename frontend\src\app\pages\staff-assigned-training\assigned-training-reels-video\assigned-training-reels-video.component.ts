import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { timeStamp } from 'console';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { FilterParam } from 'src/app/models/filterparam';
import { StaffAssignedTrainingService } from 'src/app/services/staff-assigned-training.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';
import { threadId } from 'worker_threads';
import { browserRefresh } from 'src/app/app.component';
import { browser } from 'protractor';
import { OsAndBrowserDetect } from 'src/app/shared/os-and-browser-detect';
import { Constant } from 'src/app/config/constants';
import { CommonService } from 'src/app/shared/common.service';
declare const $: any;
declare var require: any;
const { createFFmpeg, fetchFile } = require('@ffmpeg/ffmpeg');


@Component({
  selector: 'app-assigned-training-reels-video',
  templateUrl: './assigned-training-reels-video.component.html',
  styleUrls: ['./assigned-training-reels-video.component.scss'],
  providers: [FilterParam]
})
export class AssignedTrainingReelsVideoComponent implements OnInit, OnDestroy {
  trainingData: any = [];
  currentIntialSlide: number;
  public currentActiveVideo: string = null;
  public currentPlayVideo: string = null;
  allowNextSlide: boolean = true;
  allowSlidePrev: boolean = true;
  trainingIds: any = [];
  isTrainingData: boolean = false;
  public currentVideoWaiting: string = null
  videoMuted: any = false;
  private fileData: any;
  trainingId: string;
  ratioProgress: number;

  public browserHeight: any = null;
  constructor(private toastService: ToastService, private staffAssignedTrainingService: StaffAssignedTrainingService, private authService: AuthService,
    private filterParam: FilterParam, private route: ActivatedRoute, private router: Router, private zone: NgZone, private osAndBrowserDetect: OsAndBrowserDetect,
    private commonService: CommonService) { }

  ngOnInit() {
    let clientHeight = $(".videos-reel-cls").height();
    this.browserHeight = window.innerWidth <= 600 ? clientHeight : 590;
    if(!this.authService.getUser()?.companyCode || this.authService.getUser()?.companyCode == null) {
      $("#assignTeam").modal('show');
    }
    this.fetchAllTrainings('/api/user/training');
  }

  unmuteVideo(id: string) {
    setTimeout(() => {
      var videoId = document.getElementById("videoId" + id) as HTMLMediaElement | null;
      videoId.muted = false;
      this.videoMuted = false;
      if (videoId.paused) {
        videoId.play();
      }
    }, 0)
  }

  async fetchAllTrainings(url: string) {
    try {
      const response: RestResponse = await this.staffAssignedTrainingService.fetchAllTrainings(null, url);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainingData = response.data;
      this.setCurrentSlide();
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setCurrentSlide() {
    this.trainingData.map((training, index, row) => {
      training.uploader = this.initializeUploaderTraining(null, 'mp4', null, null, this.toastService, "Only Mp4 files are allowed", null, training);

      this.route.queryParams
        .subscribe(params => {
          if (training.trainingId == params.id) {
            this.currentIntialSlide = index;
            if (index == 0) {
              this.allowNextSlide = true;
              this.allowSlidePrev = false;
            }
            if (index + 1 === row.length) {
              this.allowNextSlide = false;
              this.allowSlidePrev = true;
            }
          }
        }
        );
    })
    if (this.trainingData.length > 0) {
      this.isTrainingData = true;
    }
  }

  resizeDiv() {
    if (window.innerWidth <= 600) {
      let clientHeight = $(".videos-reel-cls").height()
      let h = clientHeight;

      this.browserHeight = h;
    } else {
      this.browserHeight = 590;
    }
  }

  ngOnDestroy() {
  }


  init(event: any) {
    window.addEventListener('resize', () => this.resizeDiv());
    if (event.activeIndex == 0) {
      this.onChangeSlide(event);
    }
  }

  removeEndedVideoEventListener(videoId: any) {
    videoId.removeEventListener("ended", (event) => {
    });
  }

  addEndedVideoEventListener(videoId: any, id: string) {
    videoId.addEventListener("ended", (event) => {
      this.currentPlayVideo = id
    });
  }

  loadVideo(url) {
    return fetch(url)
      .then(resp => resp.blob())
      .then(blob => URL.createObjectURL(blob));
  }

  onChangeSlide(event: any) {
    this.videoMuted = false;
    this.trainingData.map((training, index, row) => {
      if (index === event.activeIndex) {
        this.currentActiveVideo = training.trainingId;
        this.currentVideoWaiting = training.trainingId;
        this.changeRouteTrainingId(training.trainingId);

        setTimeout(() => {
          var videoId = document.getElementById("videoId" + training.trainingId) as HTMLMediaElement | null;
          videoId.addEventListener('canplaythrough', (event) => {
            this.currentVideoWaiting = null;
            this.currentPlayVideo = null;
            if (browserRefresh) {
              videoId.muted = true;
              this.videoMuted = true;
              videoId.play();
            }
            if (videoId.paused && !videoId.muted && this.osAndBrowserDetect.OsDetails() == Constant.OS_VERSION.IOS) {
              videoId.muted = true;
              this.videoMuted = true;
              videoId.play();
            }
            this.addEndedVideoEventListener(videoId, training.trainingId)
          });
        }, 0)
      }
    })
    if (event.activeIndex > 0 && event.activeIndex + 1 < this.trainingData.length) {
      this.allowNextSlide = true;
      this.allowSlidePrev = true;
    }
  }

  changeRouteTrainingId(id: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          queryParams: { id: id },
          queryParamsHandling: 'merge'
        });
    });
  }

  reachEnd() {
    this.allowNextSlide = false;
    this.allowSlidePrev = true;
    this.toastService.info('End of training videos', null, null, 500, 500);
  }

  reachBeginning() {
    this.allowNextSlide = true;
    this.allowSlidePrev = false;
  }

  initializeUploaderTraining(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService, fileTypeMessage: string, fileSizeMessage: string, training: any) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileSizeMessage, this.toastService, training);
          }, 200);
          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationErrorTraining(fileTypeMessage, this.toastService, training);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const file = uploadResponse[0];
        file.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(file);
        setTimeout(() => {
          this.onUploadSuccessTraining(file, files, training);
        }, 200);
      }
    };
    return uploader;
  }

  fileValidationErrorTraining(data: string, toastService: any, training: any) {
    this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== training.trainingId)
    toastService.error(data);
  }

  async onUploadSuccessTraining(file: any, files: any, trainingData: any) {
    let data = {
      id: trainingData.id ? trainingData.id : null,
      isActive: true,
      isDeleted: false,
      userVideoUrl: file.path,
      trainingId: trainingData.trainingId
    }
    try {
      const response: RestResponse = await this.staffAssignedTrainingService.updateStatusOfTraining(this.removeNullDataFromObject(data));
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainingData.map(training => {
        if (trainingData.trainingId == training.trainingId) {
          training.status = "COMPLETED";
        }
        return training
      })
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }

    this.trainingIds = this.trainingIds.filter(trainingId => trainingId !== trainingData.trainingId)
  }

  removeNullDataFromObject(obj: any) {
    for (var propName in obj) {
      if (obj[propName] === null || obj[propName] === undefined) {
        delete obj[propName];
      }
    }
    return obj
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  async fileChangeEvent(trainingId: string, event: any) {
    const file = event.target.files[0];
    if (trainingId) {
      this.trainingId = trainingId;
      this.trainingIds.push(trainingId);
    }
    if (event.target.files[0].type != "video/mp4") {
      this.commonService.convertVideoFormat(file).then(res => {
        console.log(res,'res');
        this.fileData = {} as any;
        this.fileData.files = [] as any;
        this.fileData.files.push(res);
        this.onFileProcessingCompleted(this.fileData.files);
      });
    }
    else {
      this.fileData = {} as any;
      this.fileData.files = event.target.files;
      this.onFileProcessingCompleted(this.fileData.files);
    }
  }

  playVideoFromPlayIcon(id: string) {
    var videoId = document.getElementById("videoId" + id) as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        this.currentPlayVideo = null;
        this.addEndedVideoEventListener(videoId, id)
      } else {
        this.currentPlayVideo = id;
        videoId.pause();
        this.removeEndedVideoEventListener(videoId)
      }
    }
  }

  async onFileProcessingCompleted(files: any) {
    let index = this.trainingData.findIndex(x => x.trainingId == this.trainingId);
    this.trainingData[index].uploader.addToQueue(files);
    this.trainingData[index].uploader.uploadAll();
  }

  closeModal() {
    $("#assignTeam").modal('hide');
    this.authService.logout();
  }

}
