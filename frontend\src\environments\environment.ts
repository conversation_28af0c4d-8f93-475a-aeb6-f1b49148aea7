// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { HttpHeaders } from '@angular/common/http';

export const environment = {
  production: false,
  BaseApiUrl: 'https://integrax-dev.iotasolstaging.com',
  // BaseApiUrl: 'https://integrax-staging.iotasolstaging.com',
  // BaseApiUrl: 'https://integrax-stg.azurewebsites.net',
  // BaseApiUrl:'https://localhost:44366',
  AppHeaders: new HttpHeaders({
    'Content-Type': 'application/json',

    Accept: 'application/json'
  })
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
