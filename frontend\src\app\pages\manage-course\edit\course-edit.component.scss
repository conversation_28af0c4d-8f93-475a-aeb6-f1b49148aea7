// .unselected-button :hover{
//   background-color: #1681ff;
//   color: #fff;
// }

// .unselected-button {
//   background-color: transparent;
//   color: #000 !important;
// }

// .accordion-button {
//   font-weight: bold;
// }

// .invalid-feedback {
//   display: block;
// }

// .custom-switch:checked {
//   background: transparent linear-gradient(234deg, #1681FF 0%, #FFF 100%) 0% 0% no-repeat padding-box;
// }

// /* Additional styling to ensure proper alignment */
// .form-check.form-switch {
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// .form-label {
//   margin-bottom: 0;
//   /* Remove default margin for alignment */
// }

// //
// /* Remove background and shadow from the collapse */
// .accordion-collapse {
//   background-color: transparent !important;
//   box-shadow: none !important;
// }

// /* Customize the accordion button */
// .accordion-button {
//   background-color: transparent;
//   border: none;
//   box-shadow: none;
//   outline: none;
// }

// /* Arrow customization */
// .collapse-arrow {
//   display: inline-block;
//   margin-left: auto;
//   transition: transform 0.3s;
// }

// // .collapse-arrow.collapsed {
// //   transform: rotate(180deg);
// // }

// /* Optional: Add styles for the header */
// .accordion-header.add-new-course {
//   display: flex;
//   align-items: center;
// }

// .tinymce-invalid-cls.is-invalid {
//   border-radius: 4px !important;
// }

// //
// .upload-video-container label img {
//   width: auto !important;
//   margin-top: 13px;
// }

// .upload-video-container label {
//   border-radius: 50;
//   width: auto !important;
//   height: auto !important;
//   margin-top: 6px;
// }

// #userEditComponent .dashboard-content-container .moment-image-container img {
//   width: 100%;
//   height: 140px !important;
//   -o-object-fit: cover;
//   object-fit: cover;
// }

// .upload-img-container {
//   border: 1px dashed #07182F !important;
//   border-radius: 20px !important;
//   opacity: 1;
//   padding: 24px 40px !important;
//   height: 140px;
// }

// .form-check {
//   .custom-form-check {
//     padding: 12px;
//     width: 28px;
//   }
// }

// .site-button.save-button {
//   min-width: 120px;
//   text-transform: uppercase;
//   min-height: 55px;
// }

// #userEditComponent .dashboard-content-container .site-button {
//   min-width: 140px !important;
// }

// #userEditComponent .dashboard-content-container .delete-video-container i {
//   font-size: 25px;
//   z-index: 2;
//   display: grid;
// }

// .upload-video-container {
//   border-radius: 20px !important;
//   border: 0px;
//   opacity: 1;
//   height: 140px !important;
// }

// .video-wrapper {
//   width: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 140px !important;
// }

.unselected-button :hover {
  background-color: #1681ff;
  color: #fff;
}

.unselected-button {
  background-color: transparent;
  color: #000 !important;
}

.accordion-button {
  font-weight: bold;
}

.invalid-feedback {
  display: block;
}

.custom-switch:checked {
  background: transparent linear-gradient(234deg, #1681FF 0%, #FFF 100%) 0% 0% no-repeat padding-box;
}

/* Additional styling to ensure proper alignment */
.form-check.form-switch {
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-label {
  margin-bottom: 0;
  /* Remove default margin for alignment */
}

/* Remove background and shadow from the collapse */
.accordion-collapse {
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Customize the accordion button */
.accordion-button {
  background-color: transparent;
  border: none;
  box-shadow: none;
  outline: none;
}

/* Arrow customization */
.collapse-arrow {
  display: inline-block;
  margin-left: auto;
  transition: transform 0.3s;
}

/* Optional: Add styles for the header */
.accordion-header.add-new-course {
  display: flex;
  align-items: center;
}

.tinymce-invalid-cls.is-invalid {
  border-radius: 4px !important;
}

.upload-video-container label img {
  width: auto !important;
  margin-top: 13px;
}

.upload-video-container label {
  border-radius: 50;
  width: auto !important;
  height: auto !important;
  margin-top: 6px;
}

#userEditComponent .dashboard-content-container .moment-image-container img {
  width: 100%;
  height: 140px !important;
  -o-object-fit: cover;
  object-fit: cover;
}

.upload-img-container {
  border: 1px dashed #07182F !important;
  border-radius: 20px !important;
  opacity: 1;
  padding: 24px 40px !important;
  height: 140px;
}

.form-check {
  .custom-form-check {
    padding: 10px;
    width: 24px;
  }
}

.site-button.save-button {
  min-width: 120px;
  text-transform: uppercase;
  min-height: 55px;
}

#userEditComponent .dashboard-content-container .site-button {
  min-width: 140px !important;
}

#userEditComponent .dashboard-content-container .delete-video-container i {
  font-size: 25px;
  z-index: 2;
  display: grid;
}

.upload-video-container {
  border-radius: 20px !important;
  border: 0px;
  opacity: 1;
  height: 140px !important;
}

.video-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 140px !important;
}

.mh-0 {
  min-height: 0 !important;
}

/* CSS for drag preview */
.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  /* Add a shadow for depth */
  border: 1px solid #5a5757;
  /* Border to define edges */
  background: #3d3d3d;
  /* Background color */
  border-radius: 0px;
  /* Rounded corners */
  opacity: 1;
  /* Slightly transparent */
  padding: 10px;
  /* Add some padding */
  width: 100%;
  /* Ensure it takes the full width of the item */
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* Align items inside */
  box-sizing: border-box;
  /* Include padding and border in the element’s total width and height */
}

/* CSS for drag placeholder */
.cdk-drag-placeholder {
  border: 1px dashed #ccc;
  /* Dashed border */
  background: #000000;
  /* Light background */
  border-radius: 0px;
  /* Rounded corners */
  opacity: 0.5;
  /* Semi-transparent */
  height: 100%;
  /* Ensure placeholder fits the height */
}

/* Optional: CSS for dragging item while animating */
.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  /* Smooth transition */
}

.training-button.accordion-button:hover {
  cursor: move;
}

//hide arrow icon in Training
.training-button.accordion-button::after {
  display: none;
}

.training-btn.btn {
  border: none !important;
  /* Ensure no border in default state */
}

.training-btn.btn:hover,
.training-btn.btn:focus,
.training-btn.btn:active {
  border: none !important;
}


.typeahead-container {
  position: relative;
  // width: 300px;
}

.typeahead-input {
  // width: 100%;
  // padding: 8px;
  // box-sizing: border-box;
}

.typeahead-dropdown {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  list-style: none;
  margin: 0;
  padding: 0;
  z-index: 1000;
  /* Ensure the dropdown is above other elements */
  border-radius: 4px;
}

.typeahead-item {
  padding: 8px;
  cursor: pointer;

}

.typeahead-item:hover {
  background-color: #ebf4ff;
}

.form-check-label {
  font-size: 11px;
  margin-top: 7px;

  a {
    text-decoration: none;
    color: #007bff;

    &:hover {
      text-decoration: underline;
    }
  }

}


// ::ng-deep .ck-toolbar {
//   // display: flex;
//   // flex-wrap: nowrap;  /* Disable wrapping of the toolbar items */
//   overflow-x:hidden;
//   // overflow-y:visible;  /* Allow horizontal scrolling if the toolbar is too long */
// }

// .color-dark-grey{
//   color: #71828a;
// }

// ::ng-deep .ck-editor__editable_inline {
//   min-height: 180px;   /* Set minimum height */
//   width: 100%;         /* Ensure the width is responsive */
// }

// .ck-editor__editable {
//   max-height: 300px;  /* Adjust this as needed */
//   overflow-y: auto;
// }

::ng-deep .ck-editor__editable_inline {
  min-height: 180px;
  width: 100%;
}

::ng-deep .ck-editor__editable {
  /* Remove scroll settings */
  overflow-y: visible;
}


.ck-editor__editable {
  max-height: 300px;
  /* Adjust this if you want to control the editor's maximum height */
  overflow-y: auto;
  /* Keep vertical scrolling within the editable content */
}


::ng-deep .ck-dropdown__panel {
  z-index: 9999 !important;
  /* Ensure dropdown has a high z-index to appear above other elements */
  position: relative;
  /* Make sure it's positioned correctly */
}

.disabled {
  pointer-events: none;
  opacity: 0.7;
}