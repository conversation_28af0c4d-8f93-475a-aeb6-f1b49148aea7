import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Address } from './address';
export class Farm extends BaseModel {

	tenantId: number;
	slug: string;
	name: string;
	country: string;
	location: string;
	description: string;
	propertyNo: string;
	identifier: string;
	staffCount: number;
	farmAdminCount: number;
	farmStaffCount: number;
	farmingUrl: string;
	latitude: string;
	longitude: string;
	farmCode: string;
	displayLabel: string;
	userType: string;
	logoUrl: string;
	addressDetail: Address;
	activeFarmAdminCount: number;
	inactiveFarmAdminCount: number;
	invitedFarmAdminCount: number;

	constructor() {
		super();
		this.isDeleted = false;
		this.isActive = true;
		this.addressDetail = new Address();
	}

	static fromResponse(data: any): Farm {
		const obj = new Farm();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.name = data.name;
		obj.country = data.country;
		obj.description = data.description;
		obj.propertyNo = data.propertyNo;
		obj.location = data.location;
		obj.identifier = data.identifier;
		obj.staffCount = data.staffCount;
		obj.farmAdminCount = data.farmAdminCount;
		obj.farmStaffCount = data.farmStaffCount;
		obj.farmingUrl = data.farmingUrl;
		obj.latitude = data.latitude;
		obj.longitude = data.longitude;
		obj.farmCode = data.farmCode;
		obj.logoUrl = data.logoUrl;
		obj.addressDetail = Address.fromResponse(data.addressDetail);
		obj.activeFarmAdminCount = data.activeFarmAdminCount;
		obj.inactiveFarmAdminCount = data.inactiveFarmAdminCount;
		obj.invitedFarmAdminCount = data.invitedFarmAdminCount;

		return obj;
	}

	isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.name)) {
			form.controls.name.setErrors({ invalid: true });
			return false;
		}
		return true;
	}

	forRequest() {
		this.name = this.trimMe(this.name);
		this.country = this.trimMe(this.country);
		this.latitude = this.trimMe(this.latitude);
		this.longitude = this.trimMe(this.longitude);
		this.description = this.trimMe(this.description);
		this.propertyNo = this.trimMe(this.propertyNo);
		this.logoUrl = this.trimMe(this.logoUrl);
		return this;
	}
}
