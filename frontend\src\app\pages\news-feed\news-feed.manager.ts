import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { NewsFeedService } from './news-feed.service';

@Injectable({
    providedIn: 'root'
})
export class NewsFeedManager extends BaseManager {

    constructor(protected newsFeedService: NewsFeedService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(newsFeedService, loadingService, toastService);
    }
}