import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import { CoursePart } from './coursepart';
import { Users } from './users';
export class ManageUserCourseDetail extends BaseModel {

    id: string;
    tenantId: number;
    slug: string;
    title: string;
    description: string;
    isPublish: boolean;
    isDraft: boolean;
    isPrivate: boolean;
    url: string;
    code: string;
    email: string;
    status: string;
    isLock: false;
    userDetail: Users;
    courseDetail: CoursePart;
    percentage: string;
    user: string;
    course: string;
    userId: string;
    isApproved: string;
    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.isPublish = false;
        this.isDraft = false;
        this.isPrivate = false;
        this.courseDetail = new CoursePart();
        this.userDetail = new Users();

    }

    static fromResponse(data: any): ManageUserCourseDetail {
        const obj = new ManageUserCourseDetail();
        obj.id = data.id;
        obj.tenantId = data.tenantId;
        obj.slug = data.slug;
        obj.createdBy = data.createdBy;
        obj.updatedBy = data.updatedBy;
        obj.createdOn = data.createdOn;
        obj.updatedOn = data.updatedOn;
        obj.isDeleted = data.isDeleted;
        obj.isActive = data.isActive;
        obj.isPrivate = data.isPrivate;
        obj.isDraft = data.isDraft;
        obj.isPublish = data.isPublish;
        obj.title = data.title;
        obj.description = data.description;
        obj.url = data.url;
        obj.code = data.code;
        obj.email = data.email;
        obj.status = data.status;
        obj.isLock = data.isLock;
        obj.percentage = data.percentage;
        obj.user = data.user;
        obj.course = data.course;
        obj.userId = data.userId;
        obj.isApproved = data.isApproved;
        return obj;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.title = this.trimMe(this.title);
        this.description = this.trimMe(this.description);
        this.url = this.trimMe(this.url);
        return this;
    }
}
