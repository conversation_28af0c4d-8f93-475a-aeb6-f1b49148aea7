import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';

@Injectable({
    providedIn: 'root'
})
export class ManageContentService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/contenttype', '/api/ContentTypes');
    }

    assignProgram(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/assign-category', data);
    }
}

