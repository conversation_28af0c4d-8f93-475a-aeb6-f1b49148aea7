<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th>{{'USERS.FirstName' | translate}}</th>
                        <th>{{'USERS.LastName' | translate}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>
                            {{record?.userDetail.firstName}}
                        </td>
                        <td>
                            {{record?.userDetail.lastName}}
                        </td>
                    </tr>
                    <!-- <tr *ngIf="records.length===0">
                        <td class="text-center" colspan="5">
                            {{"COMMON.NORECORDS" | translate}}
                        </td>
                    </tr> -->
                </tbody>
            </table>
        </div>
    </div>
</div>