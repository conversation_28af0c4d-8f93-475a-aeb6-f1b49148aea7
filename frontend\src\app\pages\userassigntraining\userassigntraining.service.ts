import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';

@Injectable({
  providedIn: 'root'
})
export class UserAssignTrainingService extends BaseService {

  constructor(public http: HttpClient) {
    // super(http, '/api/userAssignTraining', '/api/userAssignTrainings');
    super(http, '/api/userAssignTraining', '/api/user/training');
  }

  unassignTraining(data: any, type: string): Promise<RestResponse> {
    return this.updateRecord('/api/delete/userAssignTrainings/' + type, data);
  }
}

