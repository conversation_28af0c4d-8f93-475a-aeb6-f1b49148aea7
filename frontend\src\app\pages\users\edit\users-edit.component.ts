import { ThrowStmt } from '@angular/compiler';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { FilterParam } from 'src/app/models/filterparam';
import { Users } from 'src/app/models/users';
import { UsersService } from 'src/app/services/users.service';
import { AuthService } from 'src/app/shared/auth.services';
import { environment } from 'src/environments/environment';
import { LoadingService } from '../../../services/loading.service';
import { RestResponse } from '../../../shared/auth.model';
import { CommonUtil } from '../../../shared/common.util';
import { ToastService } from '../../../shared/toast.service';
import { LanguageManager } from '../../language/language.manager';
import { Supplier<PERSON>hainManager } from '../../supplier-chain-manager/supplier-chain-manager.manager';
import { FarmManager } from '../../farm/farm.manager';
import { Farm } from 'src/app/models/farm';
import { FarmService } from '../../farm/farm.service';
import { UserFarmDetail } from 'src/app/models/userfarmdetail';

declare const $: any;
@Component({
  selector: 'app-users-edit',
  templateUrl: './users-edit.component.html',
  styleUrls: ['./users-edit.component.scss']
})
export class UsersEditComponent implements OnInit {
  @Input() userDetails?: boolean;
  @Input() userId?: string;
  @Input() isUserDetailsModal?: boolean;
  @Output() userDetailsModalClose = new EventEmitter<boolean>();

  user: Users;
  onClickValidation: boolean;
  request: any;
  selectedFarms: any = [];
  userDeletedFarms: any = [];
  filterParam: FilterParam;
  userFarms: any = [];
  languages: any = [];
  dropdownSettings = {};
  profileImageLoader: boolean = false;
  uploader: any;
  teams: Users[];
  farms!: Farm[];
  // roles : Array<any>;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly usersService: UsersService,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly router: Router,
    public commonUtil: CommonUtil,
    private readonly languageManager: LanguageManager,
    public authService: AuthService,
    private readonly supplierChainManager: SupplierChainManager,
    private readonly farmManager: FarmManager,
    protected farmService: FarmService
  ) {
    this.user = new Users();
    this.request = {} as any;
    this.request.recordId = this.route.snapshot.paramMap.get('id');
    this.request.isNewRecord = true;
    this.request.onClickValidation = false;
    this.filterParam = new FilterParam();
    this.teams = new Array<Users>();

  }

  ngOnInit() {
    (async () => {
      this.loadingService.show();
      this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService);
      this.setDropdownSettings();
      await this.fetchAssociatedData();
      if (this.request.recordId <= 0 && !this.userId) {
        this.loadingService.hide();
        return;
      }
      await this.fetchUserDetail();
      this.loadingService.hide();
    })();
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  async fetchAssociatedData() {
    if (this.authService.isSuperAdmin()) {
      this.teams = await this.supplierChainManager.fetchAllData(null);
    }
    // need to move to if check as we need farms detail for super admin
    this.filterParam = new FilterParam();
    const farms: RestResponse = await this.farmService.fetchAvailableFarms(this.filterParam);
    this.farms = farms.data;
  }

  async fetchUserDetail() {
    try {
      const response: RestResponse = await this.usersService.fetch(this.userId ? this.userId : this.request.recordId).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.request.isNewRecord = false;
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      if (this.user.userFarmDetail) {
        this.user.farmId = this.user.userFarmDetail[0]?.farmId;
      }
      this.setSelectedFarmsUserEdit(userFarmMapping);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setSelectedFarmsUserEdit(userFarms) {
    if (this.farms !== undefined && userFarms !== undefined) {
      const farms = this.farms.filter((data) => userFarms.some(data2 => data.id === data2.farmId))
      let newFarms = farms.reduce((responseData, { id, ...rest }) => ({ ...responseData, ...{ [id]: { id, ...rest } } }), {});

      userFarms.forEach(({ farmId, id }) => {
        newFarms[farmId].userFarmId = id
      });
      let data = Object.values(newFarms);
      this.userFarms = data;
      this.selectedFarms = this.userFarms;
      this.setSelectedFarmData(this.selectedFarms);
    }
  }

  setSelectedFarmData(farms: any) {
    this.user.userFarmMapping = farms.map(farm => {
      return {
        id: farm.userFarmId,
        farmId: farm.id
      }
    })
  }

  onItemSelect(item: any) {
    if (this.user.userFarmMapping && this.user.userFarmMapping.length > 0) {
      if (!this.user.userFarmMapping.some(el => el.farmId === item.id)) {
        this.setItemsData(item);
      }
    } else {
      this.setItemsData(item);
    }
  }

  onDeSelect(item: any) {
    let deletedUserFarms = []
    if (!this.request.isNewRecord) {
      this.addDeletedItemsOnUserEdit(item);
    }
    if (this.user.userFarmMapping && this.user.userFarmMapping.length > 0) {
      deletedUserFarms = this.user.userFarmMapping.filter(farmData => farmData.farmId !== item.id);

    }
    this.user.userFarmMapping = deletedUserFarms;
  }

  addDeletedItemsOnUserEdit(item) {
    let userFarmData = this.userFarms.find(farm => farm.id === item.id);
    if (userFarmData) {
      if (this.userDeletedFarms && this.userDeletedFarms.length > 0) {
        if (!this.userDeletedFarms.some(el => el.farmId === userFarmData.id)) {
          this.setDeletedItemsData(userFarmData);
        }
      } else {
        this.setDeletedItemsData(userFarmData);
      }
    }
  }

  setDeletedItemsData(item) {
    this.userDeletedFarms = [
      ...this.userDeletedFarms,
      {
        id: item.userFarmId,
        farmId: item.id,
        isDeleted: true
      },
    ];
  }


  setItemsData(item) {
    this.user.userFarmMapping = [
      ...this.user.userFarmMapping,
      {
        farmId: item.id,
      },
    ];
  }

  onFarmSelect(farmId: string) {
    let userFarmDetail: any[] = [];
    if (this.user.userFarmDetail && this.user.userFarmDetail.length > 0) {
      const prevFarm = this.user.userFarmDetail[0];
      if (prevFarm.farmId !== farmId) {
        userFarmDetail.push({ farmId: prevFarm.farmId, isDeleted: true });
      }
    }
    // Add the new farm selection
    userFarmDetail.push({ farmId });
    this.user.userFarmDetail = userFarmDetail;
  }

  async save(valid) {
    if (this.userDeletedFarms && this.userDeletedFarms.length > 0) {
      let updatedFarmsData = this.user.userFarmMapping.concat(this.userDeletedFarms);
      this.user.userFarmMapping = updatedFarmsData;
    }
    if (!valid) {
      this.request.onClickValidation = true;
      return;
    }
    this.loadingService.show();
    try {
      !this.userDetails ? this.request.isRequested = true : '';
      const method = this.request.isNewRecord ? 'save' : 'update';
      // this.user.roles = null;
      const response: RestResponse = await this.usersService[method](this.user);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        this.request.isRequested = false;
        return;
      }
      this.userDetails && this.setEmptyUserFarmData();
      this.isUserDetailsModal && this.userDetailsModalClose.emit(true);
      this.toastService.success(response.message);
      !this.userDetails && await this.router.navigateByUrl('/dashboard/users');
    } catch (e) {
      this.loadingService.hide();
      this.request.isRequested = false;
      this.toastService.error(e.message);
    }
  }

  setEmptyUserFarmData() {
    this.userDeletedFarms = [];
    this.user.userFarmMapping = this.user.userFarmMapping.filter(farm => farm.id !== undefined);
  }

  telInputObject(event: any) {
    if (this.request.recordId == 0) {
      event.setCountry('sg');
      return
    }
    if (this.user.countryCode && this.user.phoneNumber) {
      event.setNumber('+' + this.user.countryCode + this.user.phoneNumber);
      return
    } else {
      event.setCountry('sg')
    }

  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  hasError(event: any) {
  }

  uploadProfilePhoto(event: any) {
    if (event && event.target.files.length > 0) {
      this.profileImageLoader = true;
    }
  }

  fileValidationError(data: string, toastService: any) {
    this.profileImageLoader = false;
    toastService.error(data);
  }

  onUploadSuccess(data: any) {
    this.user.profileImageUrl = data.path;
    this.profileImageLoader = false;
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  initializeUploader(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationError("Image size to too large", this.toastService);
          }, 200);

          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationError("only jpg,png,jpeg files are allowed", this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const image = uploadResponse[0];
        image.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(image);
        setTimeout(() => {
          this.onUploadSuccess(image);
        }, 200);
      }
    };

    return uploader;
  }

  async fetchFarmOnCompanyCode(companyCode) {
    if (CommonUtil.isNullOrUndefined(companyCode) || companyCode == null || companyCode == "") {
      return;
    }
    this.fetchFarmsForSuperAdmin(companyCode);
  }

  async fetchFarmsForSuperAdmin(companyCode) {
    try {
      let param = new FilterParam();
      param.companyCode = companyCode;
      const response: RestResponse = await this.usersService.GetAllFarms(param);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.farms = response.data;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }
}

