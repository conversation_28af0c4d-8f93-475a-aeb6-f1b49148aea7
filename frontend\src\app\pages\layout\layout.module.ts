import { CommonModule } from '@angular/common';
import { NgModule, Pipe } from '@angular/core';
import { SwiperModule } from 'swiper/angular';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { EditorModule } from '@tinymce/tinymce-angular';
import { DataTablesModule } from 'angular-datatables';
import { MyDatePickerModule } from 'mydatepicker';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FileUploadModule } from 'ng2-file-upload';
import { ImageCropperModule } from 'ngx-image-cropper';
import { MaterialAppModule } from 'src/app/material.module';
import { FileCropperComponent } from '../../shared/file-cropper/file-cropper.component';
import { ImagePopUPModel } from '../../shared/image-popup-model/image-popup-model';
import { AccountSettingsComponent } from '../account-settings/account-settings.component';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { LandingComponent } from '../landing/landing.component';
import { LayoutComponent } from '../layout/layout.component';
import { UsersEditComponent } from '../users/edit/users-edit.component';
import { UsersComponent } from '../users/users.component';
import { LAYOUTROUTING } from './layout.routing';

import { HttpClient } from '@angular/common/http';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HttpLoaderFactory } from 'src/app/app.module';
import { SharedModuleModule } from 'src/app/shared/shared-module.module';
import { ManageContentComponent } from '../manage-content-type/manage-content.component';
import { ScrollTrackerDirective } from '../../directives/scroll-tracker.directive';
import { ManageContentDetailComponent } from '../manage-content-type/detail/manage-content-detail.component';
import { ManageContentEditComponent } from '../manage-content-type/edit/manage-content-edit.component';
import { FarmDetailComponent } from '../farm/detail/farm-detail.component';
import { FarmEditComponent } from '../farm/edit/farm-edit.component';
import { FarmComponent } from '../farm/farm.component';
import { FarmAssignTrainingDetailComponent } from '../farmassigntraining/detail/farmassigntraining-detail.component';
import { FarmAssignTrainingEditComponent } from '../farmassigntraining/edit/farmassigntraining-edit.component';
import { FarmAssignTrainingComponent } from '../farmassigntraining/farmassigntraining.component';
import { LanguageDetailComponent } from '../language/detail/language-detail.component';
import { LanguageEditComponent } from '../language/edit/language-edit.component';
import { LanguageComponent } from '../language/language.component';
import { MomentDetailComponent } from '../moment/detail/moment-detail.component';
import { MomentEditComponent } from '../moment/edit/moment-edit.component';
import { MomentComponent } from '../moment/moment.component';
import { SubCategoryDetailComponent } from '../subcategory/detail/subcategory-detail.component';
import { SubCategoryEditComponent } from '../subcategory/edit/subcategory-edit.component';
import { SubCategoryComponent } from '../subcategory/subcategory.component';
import { TrainingDetailComponent } from '../training/detail/training-detail.component';
import { TrainingEditComponent } from '../training/edit/training-edit.component';
import { TrainingComponent } from '../training/training.component';
import { UserAssignTrainingDetailComponent } from '../userassigntraining/detail/userassigntraining-detail.component';
import { UserAssignTrainingEditComponent } from '../userassigntraining/edit/userassigntraining-edit.component';
import { UserAssignTrainingComponent } from '../userassigntraining/userassigntraining.component';
import { UserFarmDetailComponent } from '../userfarm/detail/userfarm-detail.component';
import { UserFarmEditComponent } from '../userfarm/edit/userfarm-edit.component';
import { UserFarmComponent } from '../userfarm/userfarm.component';
import { UserTrainingStatusDetailComponent } from '../usertrainingstatus/detail/usertrainingstatus-detail.component';
import { UserTrainingStatusEditComponent } from '../usertrainingstatus/edit/usertrainingstatus-edit.component';
import { UserTrainingStatusComponent } from '../usertrainingstatus/usertrainingstatus.component';
import { FarmAssignedUsersComponent } from '../users/farm-assigned-users/farm-assigned-users.component';
import { DelayedInputModule } from 'src/app/shared/delayed-input.directive';
import { UserDetailsComponent } from '../user-details/user-details.component';
import { TrainingFeedComponent } from '../training-feed/training-feed.component';
import { StaffAssignedTrainingComponent } from '../staff-assigned-training/staff-assigned-training.component';
import { StaffmomentsComponent } from '../staffmoments/staffmoments.component';
import { StaffAddMomentComponent } from '../staff-add-moment/staff-add-moment.component';
import { StaffprofileComponent } from '../staffprofile/staffprofile.component';
import { StaffChangepasswordComponent } from '../staff-changepassword/staff-changepassword.component';
import { StaffAssignedSitesComponent } from '../staff-assigned-sites/staff-assigned-sites.component';
import { ViewStaffComponent } from '../view-staff/view-staff.component';
import { UserApprovedAndRejectedTrainingComponent } from '../user-approved-and-rejected-training/user-approved-and-rejected-training.component';
import { AssignedTrainingReelsVideoComponent } from '../staff-assigned-training/assigned-training-reels-video/assigned-training-reels-video.component';
import { UsersContactListComponent } from '../users-contact-list/users-contact-list.component';
import { Ng2TelInputModule } from 'ng2-tel-input';
import { TrainingAssignedUsersComponent } from '../training-assigned-users/training-assigned-users.component';
import { AdminSettingComponent } from '../admin-setting/admin-setting.component';
import { MomentRejectedLogsListComponent } from '../moment-rejected-logs-list/moment-rejected-logs-list.component';
import { RequestForSiteChangeComponent } from '../request-for-site-change/request-for-site-change.component';
import { DateRangeFilterComponent } from '../../shared/date-range-filter/date-range-filter.component';
import { ReelViewVideosComponent } from '../reel-view-videos/reel-view-videos.component';
import { RecurringMomentLogsComponent } from '../recurring-moment-logs/recurring-moment-logs.component';
import { SetNewPasswordComponent } from '../set-new-password/set-new-password.component';
import { TrainingAssignedFarmsComponent } from '../training/training-assigned-farms/training-assigned-farms.component';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { TimeAgoPipe } from 'time-ago-pipe';
import { NotificationComponent } from '../notification/notification.component';
import { CategoryAssignedUsersComponent } from '../category/category-assigned-users/category-assigned-users.component';
import { SubCategoryAssignedUsersComponent } from '../subcategory/subcategory-assigned-users/subcategory-assigned-users.component';
import { SupplierChainManagerComponent } from '../supplier-chain-manager/supplier-chain-manager.component';
import { SupplierChainManagerEditComponent } from '../supplier-chain-manager/edit/supplier-chain-manager-edit.component';
import { CategoryComponent } from '../category/category.component';
import { CategoryEditComponent } from '../category/edit/category-edit.component';
import { CategoryDetailComponent } from '../category/detail/category-detail.component';
import { ManageLearningSeriesComponent } from '../manage-learning-series/manage-learning-series.component';
import { ManageLearningSeriesEditComponent } from '../manage-learning-series/edit/manage-learning-series-edit.component';
import { TrainingHistoryComponent } from '../training-history/training-history.component';
import { NewsFeedComponent } from '../news-feed/news-feed.component';
import { NewsFeedEditComponent } from '../news-feed/edit/news-feed-edit.component';
import { RestrictedUsersListComponent } from '../restricted-users-list/restricted-users-list.component';
import { NewsFeedDetailComponent } from '../news-feed/detail/news-feed-detail.component';
import { TrainingRejectedLogsComponent } from '../training-rejected-logs/training-rejected-logs.component';
import { NgxSpinnerModule } from 'ngx-spinner';
import { MostWatchTrainingsComponent } from '../most-watch-trainings/most-watch-trainings.component';
import { MostWatchedNewsFeedComponent } from '../most-watched-news-feed/most-watched-news-feed.component';
import { MostCompletedTrainingsComponent } from '../most-completed-trainings/most-completed-trainings.component';
import { UsersVideoProgressComponent } from '../users-video-progress/users-video-progress.component';
import { NewsFeedClickLogsComponent } from '../news-feed-click-logs/news-feed-click-logs.component';
import { MostCompletedTrainingUsersListComponent } from '../most-completed-training-users-list/most-completed-training-users-list.component';
import { ManageCourseComponent } from '../manage-course/manage-course.component';
import { CourseEditComponent } from '../manage-course/edit/course-edit.component';
import { ProgramAdminDashboardComponent } from '../program-admin-dashboard/program-admin-dashboard.component';
import { CourseTrainingsComponent } from '../course-trainings/course-trainings.component';
import { CourseAllocatedUsersListComponent } from '../course-allocated-users-list/course-allocated-users-list.component';
import { CourseDetailComponent } from '../manage-course/detail/course-detail.component';
import { ManageCourseLearningSeriesComponent } from '../manage-course-learning-series/manage-course-learning-series.component';
import { CourseLearningSeriesEditComponent } from '../manage-course-learning-series/edit/course-learning-series-edit.component';
import { CourseTrainingEditComponent } from '../course-trainings/edit/course-training-edit.component';
import { ViewCourseTrainingComponent } from '../manage-course/view-trainings/view-course-training.component';
import { CourseTrainingDetailComponent } from '../course-trainings/course-training-detail/course-training-detail.component';
import { ProgramAdminSettingsComponent } from '../program-admin-settings/program-admin-settings.component';
import { ProgramChangePasswordComponent } from '../program-change-password/program-change-password.component';
import { ManageProgramUserComponent } from '../manage-program-user/manage-program-user.component';
import { UserMyCourseComponent } from '../user-my-course/user-my-course.component';
import { CourseUserDetailsComponent } from '../course-user-details/course-user-details.component';
import { TrainingLibraryComponent } from '../training-library/training-library.component';
import { TrainingCardComponent } from '../training-library/training-card/training-card.component';
import { TrainingCardDetailComponent } from '../training-library/training-card-detail/training-card-detail.component';
import { UserInvitedListingComponent } from '../user-invited-listing/user-invited-listing.component';
import { UserPendingListingComponent } from '../user-pending-listing/user-pending-listing.component';
import { UserEnrolledListComponent } from '../user-enrolled-list/user-enrolled-list.component';
import { ProgramTrainingHistoryComponent } from '../program-training-history/program-training-history.component';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { UserLoginProfileComponent } from '../user-login-profile/user-login-profile.component';
import { ManageAdminProgramUserComponent } from '../manage-admin-program-user/manage-admin-program-user.component';
import { ProgramAdminActiveUserComponent } from '../program-admin-active-user/program-admin-active-user.component';
import { ProgramAdminInactiveUserComponent } from '../program-admin-inactive-user/program-admin-inactive-user.component';
import { ProgramAdminInvitedUserComponent } from '../program-admin-invited-user/program-admin-invited-user.component';
import { UserInvitedEditComponent } from '../user-invited-listing/user-invited-edit/user-invited-edit.component';
import { AdminManageProgramAdminComponent } from '../admin-manage-program-admin/admin-manage-program-admin.component';
import { ProgramUserEditComponent } from '../program-user-edit/program-user-edit.component';
import { ManageAdminCourseComponent } from '../manage-admin-course/manage-admin-course.component';
import { ManageAdminTrainingComponent } from '../manage-admin-training/manage-admin-training.component';
import { ProgramUserTrainingDetailComponent } from '../program-user-training-detail/program-user-training-detail.component';
import { ManageAdminEditCourseComponent } from '../manage-admin-course/edit/manage-admin-edit-course.component';
import { ViewUserLogsComponent } from '../view-user-logs/view-user-logs.component';
import { ManageCourseAccessComponent } from '../manage-course-access/manage-course-access.component';
import{NgApexchartsModule} from 'ng-apexcharts';
import { CourseParticipantsComponent } from '../course-participants/course-participants.component';
import { FarmAdminComponent } from '../farm-admin/farm-admin.component';
import { FarmAdminEditComponent } from '../farm-admin/farm-admin-edit/farm-admin-edit.component';
import { GooglePlaceModule } from 'ngx-google-places-autocomplete';
import { NgxMaskModule } from 'ngx-mask';
import { PhoneInputComponent } from 'src/app/shared/phone-input/phone-input.component';
import { FarmAdminSettingComponent } from '../farm-admin-setting/farm-admin-setting.component';

@Pipe({
  name: 'timeAgo',
  pure: false
})
export class TimeAgoExtendsPipe extends TimeAgoPipe { }

@NgModule({
  declarations: [
    LandingComponent,
    ScrollTrackerDirective,
    LayoutComponent,
    ChangePasswordComponent,
    AccountSettingsComponent,
    UsersComponent,
    UsersEditComponent,
    ImagePopUPModel,
    FileCropperComponent,
    FarmComponent,
    FarmEditComponent,
    FarmDetailComponent,
    LanguageComponent,
    LanguageEditComponent,
    LanguageDetailComponent,
    ManageContentComponent,
    ManageContentEditComponent,
    ManageContentDetailComponent,
    ManageLearningSeriesComponent,
    ManageLearningSeriesEditComponent,
    CategoryComponent,
    CategoryEditComponent,
    CategoryDetailComponent,
    SubCategoryComponent,
    SubCategoryEditComponent,
    SubCategoryDetailComponent,
    UserFarmComponent,
    UserFarmEditComponent,
    UserFarmDetailComponent,
    TrainingComponent,
    TrainingEditComponent,
    TrainingDetailComponent,
    UserAssignTrainingComponent,
    UserAssignTrainingEditComponent,
    UserAssignTrainingDetailComponent,
    FarmAssignTrainingComponent,
    FarmAssignTrainingEditComponent,
    FarmAssignTrainingDetailComponent,
    MomentComponent,
    MomentEditComponent,
    MomentDetailComponent,
    UserTrainingStatusComponent,
    UserTrainingStatusEditComponent,
    UserTrainingStatusDetailComponent,
    FarmAssignedUsersComponent,
    UserDetailsComponent,
    TrainingFeedComponent,
    StaffAssignedTrainingComponent,
    StaffmomentsComponent,
    StaffAddMomentComponent,
    StaffprofileComponent,
    StaffChangepasswordComponent,
    StaffAssignedSitesComponent,
    ViewStaffComponent,
    UserApprovedAndRejectedTrainingComponent,
    AssignedTrainingReelsVideoComponent,
    UsersContactListComponent,
    TrainingAssignedUsersComponent,
    AdminSettingComponent,
    MomentRejectedLogsListComponent,
    RequestForSiteChangeComponent,
    DateRangeFilterComponent,
    ReelViewVideosComponent,
    RecurringMomentLogsComponent,
    SetNewPasswordComponent,
    TrainingAssignedFarmsComponent,
    TimeAgoExtendsPipe,
    NotificationComponent,
    CategoryAssignedUsersComponent,
    SubCategoryAssignedUsersComponent,
    SupplierChainManagerComponent,
    SupplierChainManagerEditComponent,
    TrainingHistoryComponent,
    NewsFeedComponent,
    NewsFeedEditComponent,
    RestrictedUsersListComponent,
    NewsFeedDetailComponent,
    TrainingRejectedLogsComponent,
    MostWatchTrainingsComponent,
    MostWatchedNewsFeedComponent,
    MostCompletedTrainingsComponent,
    UsersVideoProgressComponent,
    NewsFeedClickLogsComponent,
    MostCompletedTrainingUsersListComponent,
    ManageCourseComponent,
    CourseEditComponent,
    ProgramAdminDashboardComponent,
    CourseTrainingsComponent,
    CourseAllocatedUsersListComponent,
    CourseDetailComponent,
    ManageCourseLearningSeriesComponent,
    CourseLearningSeriesEditComponent,
    CourseTrainingEditComponent,
    ViewCourseTrainingComponent,
    CourseTrainingDetailComponent,
    ProgramAdminSettingsComponent,
    ProgramChangePasswordComponent,
    ManageProgramUserComponent,
    UserMyCourseComponent,
    CourseUserDetailsComponent,
    TrainingLibraryComponent,
    TrainingCardComponent,
    TrainingCardDetailComponent,
    UserInvitedListingComponent,
    UserPendingListingComponent,
    UserEnrolledListComponent,
    ProgramTrainingHistoryComponent,
    UserLoginProfileComponent,
    ManageAdminProgramUserComponent,
    ProgramAdminActiveUserComponent,
    ProgramAdminInactiveUserComponent,
    ProgramAdminInvitedUserComponent,
    UserInvitedEditComponent,
    AdminManageProgramAdminComponent,
    ProgramUserEditComponent,
    ManageAdminCourseComponent,
    ManageAdminTrainingComponent,
    ProgramUserTrainingDetailComponent,
    ManageAdminEditCourseComponent,
    ViewUserLogsComponent,
    ManageCourseAccessComponent,
    CourseParticipantsComponent,
    FarmAdminComponent,
    FarmAdminEditComponent,
    PhoneInputComponent,
    FarmAdminSettingComponent
  ],
  imports: [
    CommonModule,
    SwiperModule,
    FormsModule,
    EditorModule,
    CKEditorModule,
    FileUploadModule,
    MyDatePickerModule,
    ImageCropperModule,
    MaterialAppModule,
    SharedModuleModule,
    DelayedInputModule,
    Ng2TelInputModule,
    NgxSpinnerModule,
    NgApexchartsModule,
    GooglePlaceModule,
    NgxMaskModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    NgMultiSelectDropDownModule.forRoot(),
    RouterModule.forChild(LAYOUTROUTING),
    DataTablesModule,
    NgSelectModule,
    InfiniteScrollModule
  ]
})
export class LayoutModule {
}
