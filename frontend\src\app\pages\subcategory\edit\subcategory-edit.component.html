<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
	<div class="dashboard-content-container">
		<form #subcategoryForm="ngForm" novalidate="novalidate">
			<div class="row">
				<div class="mt-2 mb-3">
					<h4 class="fw-bold">{{request.recordId == 0 ? "New Course" : "Edit Course"}} </h4>
					<p class="user-edit-msg">Please make sure you fill all the fields before you click on save button
					</p>
				</div>
				<div *ngFor="let subCategory of subcategoryFormInputs;" class="col-12 col-md-6 col-lg-6 col-xl-4">
					<div class="category-language w-100 mb-4 p-3 bg-secondary d-flex justify-content-between">
						<h6 class="program-heading lh-lg text-light">{{subCategory.languageName}}</h6>
						<img [src]="getImageFetchByLanguageName(subCategory.languageName)" class="me-2" alt="">
					</div>
					<div class="form-floating">
						<div class="mb-4 form-control select-width ng-select-main-container"
							[ngClass]="{'is-invalid':categoryIds.invalid && onClickValidation}">
							<ng-multiselect-dropdown
								placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
								[name]="subCategory.languageName+'category'" [settings]="dropdownSettings"
								[data]="setCategoryDropdownByLanguageName(subCategory.languageName)"
								class="custom-multiselect form-control padding-bottom-8" name="categories"
								[(ngModel)]="subCategory.selectedCategories" #categoryIds="ngModel" 
								(onSelect)="subCategory.languageName == MY_CONSTANT.languages.English ? addCategoriesToRemainingLanguages($event) : null"
								(onDeSelect)="removeCategoriesToRemainingLanguages($event)" [ngClass]="{'disable-multi-select': subCategory.languageName !== MY_CONSTANT.languages.English}">
							</ng-multiselect-dropdown>
							<!-- <span class="input-group-btn" *ngIf="!isPlusButton">
								<button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('subCategoryCategoryIdPopup')"><span
										class="glyphicon glyphicon-plus"></span></button>
							</span> -->
						</div>
						<label for="language">{{"SubCategory.categoryId" | translate}}</label>
					</div>

					<div class="video-title">
						<div class="form-floating mb-4 category-language ">
							<input
								(ngModelChange)="subCategory.languageName == MY_CONSTANT.languages.English ? addSubCategoryTitleToRemainingLanguages($event) : null"
								[ngClass]="{'is-invalid':!title.valid && onClickValidation}" class="form-control"
								type="text" [name]="subCategory.languageName" #title="ngModel"
								[(ngModel)]="subCategory.title" required="required"
								placeholder="{{'SubCategory.title' | translate}}">
							<label for="floatingInput">{{"SubCategory.title" | translate}}</label>
						</div>
						<div class="form-floating form-floating-textarea mb-4 category-language">
							<textarea
								(ngModelChange)="subCategory.languageName == MY_CONSTANT.languages.English ? addDescriptionToRemainingLanguages($event) : null"
								[ngClass]="{'is-invalid':!Description.valid && onClickValidation}"
								class="form-control form-description" [name]="subCategory.languageName+'Description'"
								#Description="ngModel" [(ngModel)]="subCategory.description" required="required"
								placeholder="Description" id="floatingTextarea2"></textarea>
							<label for="floatingInput">{{"SubCategory.description" | translate}}</label>
						</div>
					</div>
				</div>

				<div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
					<button class="btn btn-secondary site-button btn-sm large-button save-button rounded-3"
						type="button" (click)="save(subcategoryForm.form)"
						*ngIf="authService.isAccessible('SUBCATEGORY','AddButton')"
						[disabled]="authService.isDisabled('SUBCATEGORY','AddButton')">
						{{"COMMON.SAVE" | translate}}
					</button>
				</div>
			</div>
		</form>
	</div>
</div>














<!--


<div class="breadcrumb-container" *ngIf="!isPlusButton">
	<div class="col-md-12 breadcrumb-detail-container">
		<a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
			<img src="/assets/images/menu.png" class="img-responsive">
		</a>
		<div class="project-name-container">
			<h3 class="project-name">SubCategory Administration</h3>
			<ol class="breadcrumb">
				<li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
				<li><a [routerLink]="['/dashboard/sub-category']">{{'SubCategory.objNames' | translate}}</a></li>
				<li class="active" *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'SubCategory.objName' |
					translate}}</li>
				<li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{subCategory.name}}
				</li>
			</ol>
		</div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
	<div class="site-card">
		<form #subcategoryForm="ngForm" novalidate="novalidate">
			<div class="row justify-content-start">
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"SubCategory.categoryId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="categories" bindLabel="title" bindValue="id"
								name="subCategoryCategoryId" #subCategoryCategoryId="ngModel"
								[(ngModel)]="subCategory.categoryId" required="required" #CategoryId="ngModel"
								[ngClass]="{'invalid-field':subCategoryCategoryId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} category">
							</ng-select> -->
<!-- <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('subCategoryCategoryIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span> -->
<!-- </div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"SubCategory.title" | translate}}
						</label>
						<div class="color-picker-input">
							<input class="form-control" type="text" minlength="0" maxlength="255"
								name="subCategoryTitle" required="required" [(ngModel)]="subCategory.title"
								#Title="ngModel">
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"SubCategory.description" | translate}}
						</label>
						<textarea class="form-control" rows="5" name="subCategoryDescription"
							[(ngModel)]="subCategory.description" #Description="ngModel"></textarea>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label">
							{{"SubCategory.languageId" | translate}}
						</label>
						<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
							<ng-select [items]="languages" bindLabel="name" bindValue="id" name="subCategoryLanguageId"
								#subCategoryLanguageId="ngModel" [(ngModel)]="subCategory.languageId"
								required="required" #LanguageId="ngModel"
								[ngClass]="{'invalid-field':subCategoryLanguageId.invalid && onClickValidation}"
								required="required" placeholder="{{'COMMON.SELECT_OPTION' | translate}} language">
							</ng-select>
							 <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('subCategoryLanguageIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span> -->
<!-- </div>
					</div>
				</div> -->
<!-- <div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.commonTitle" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="subCategoryCommonTitle"  [(ngModel)]="subCategory.commonTitle" #CommonTitle="ngModel">
									</div>
							</div>
						</div> -->
<!-- <div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.groupCode" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="subCategoryGroupCode"  [(ngModel)]="subCategory.groupCode" #GroupCode="ngModel">
									</div>
							</div>
						</div> -->
<!-- </div>
		</form>
		<div class="clearfix"></div>
		<div class="col-md-12 no-padding text-right">
			<button title="Save" class="btn btn-primary site-button" type="button" (click)="save(subcategoryForm.form)"
				*ngIf="authService.isAccessible('SUBCATEGORY','AddButton')"
				[disabled]="authService.isDisabled('SUBCATEGORY','AddButton')">
				{{"COMMON.SAVE" | translate}}
			</button>
			<button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button"
				(click)="navigate()">
				{{"COMMON.CANCEL" | translate}}
			</button>
			<div class="clearfix"></div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="clearfix"></div>
</div>
<div class="modal fade nav-scroll" id="subCategoryCategoryIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-category [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-category>
			</div>
		</div>
	</div>
</div>
<div class="modal fade nav-scroll" id="subCategoryLanguageIdPopup" role="dialog">
	<div class="modal-dialog associated-dialog">
		<div class="modal-content">
			<div class="modal-body" *ngIf="request.isShowAssociated">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<app-language [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-language>
			</div>
		</div>
	</div>
</div> -->