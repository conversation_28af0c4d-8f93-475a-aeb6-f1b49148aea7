import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminDashboardService extends BaseService {
    constructor(public http: HttpClient) {
        super(http, '/api/programadmin/dashboard', '/api/programadmin/dashboards');
    }

    getDashboardRecords(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/programadmin/dashboard', filterParam);
    }

    filterCourseData(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/programadmin/dashboard/course/filter', filterParam);
    }
    filterTrainingData(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/programadmin/dashboard/training/filter', filterParam);
    }
    getisPublish(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/ispublish/courses', filterParam);
    }

}

