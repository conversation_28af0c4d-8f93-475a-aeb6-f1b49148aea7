<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
	<div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
		<div class="row">
			<div class="col-12 col-sm-4 text-start">
				<div class="custom-input-group">
					<input class="form-control search-form-control" placeholder="" appDelayedInput
						(delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchTraining">
					<i class="bi bi-search pe-3"></i>
				</div>
			</div>
			<div class="col-12 col-sm-3">
      </div>
      <div class="col-12 col-sm-5 d-flex align-items-center justify-content-end mb-3">
        <button (click)="openFilterTrainingModal()" type="button"
          class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg height-54px">
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid width-22px" alt="">Filter
        </button>
      </div>
		</div>
		<div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
			<table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
				<thead>
					<tr>
						<!-- <th width="100">{{'SubCategory.languageId' | translate}}</th> -->
						<th width="250">{{'SubCategory.program' | translate}}</th>
						<th width="250">{{'SubCategory.course' | translate}}</th>
						<th width="200">{{'Training.videoTitle' | translate}}</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let record of records;">
						<!-- <td>{{record.languageIdDetail?.name}}</td> -->
						<td>
              <div *ngFor="let category of record.trainingCategoryMappingList; let last = last;">{{category.categoryName}}</div>
						</td>
						<td>
              <div *ngFor="let subCategory of record.trainingSubCategoryMappingList; let last = last;">{{subCategory.subCategoryName}}</div>
            </td>
						<td>
							<a class="text-decoration-underline" [routerLink]="['/dashboard/training/detail/'+record.id]">{{record.videoTitle}}</a>
						</td>

						<td class="text-center custom-action-button text-right">
							<div class="d-flex justify-content-end mb-3">
								<button
									*ngIf="authService.isAccessible('TRAININGFEED','EditButton') && !isDetailPage && !isPlusButton"
									[class.disabled]="authService.isDisabled('TRAININGFEED','EditButton')" title="Edit"
									class="btn btn-outline-light action-button" (click)="editRecord(record.id)">
									<i class="bi bi-pencil"></i> EDIT
								</button>
								<button *ngIf="authService.isAccessible('TRAININGFEED','DeleteButton') && !isPlusButton"
									[class.disabled]="authService.isDisabled('TRAININGFEED','DeleteButton')" title="Delete"
									class="btn btn-primary action-button" (click)="remove(record.id)">
									<i class="bi bi-trash"></i> DELETE
								</button>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
    <div class="modal fade" id="filterTrainingFeedModal" tabindex="-1" aria-labelledby="filterTrainingFeedModalLabel"
      aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="filterTrainingFeedModalLabel">Filter Training Feed</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div *ngIf="filterTrainingFeedModal && filterTrainingFeedModal._isShown" class="modal-body">
            <form #trainingFeedFilterForm="ngForm" novalidate="novalidate">
              <mat-form-field class="example-form-field w-100">
                <mat-label>Search Video Title...</mat-label>
                <input [(ngModel)]="filterParam.searchCommonTitle" name="searchVideoTitle" matInput type="text">
                <button mat-button *ngIf="filterParam.searchCommonTitle" matSuffix mat-icon-button aria-label="Clear"
                  (click)="filterParam.searchCommonTitle=''">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-form-field>
              <mat-form-field class="example-form-field w-100">
                <mat-label>Search Program...</mat-label>
                <input [(ngModel)]="filterParam.categorySearch" name="searchCategory" matInput type="text">
                <button mat-button *ngIf="filterParam.categorySearch" matSuffix mat-icon-button aria-label="Clear"
                  (click)="filterParam.categorySearch=''">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-form-field>
              <mat-form-field class="example-form-field w-100">
                <mat-label>Search Course...</mat-label>
                <input [(ngModel)]="filterParam.subCategorySearch" name="searchSubCategory" matInput type="text">
                <button mat-button *ngIf="filterParam.subCategorySearch" matSuffix mat-icon-button aria-label="Clear"
                  (click)="filterParam.subCategorySearch=''">
                  <mat-icon>close</mat-icon>
                </button>
              </mat-form-field>
              <!-- <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="fromDate" [toDateInput]="toDate"></app-date-range-filter> -->
              <div class="modal-footer">
                <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
                <button (click)="onClickTrainingFilter(trainingFeedFilterForm.form.valid)" type="button"
                  class="btn btn-primary">Filter</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
	</div>
</div>
