import { Injectable } from "@angular/core";
import { BaseManager } from "src/app/config/base.manager";
import { FilterParam } from "src/app/models/filterparam";
import { LoadingService } from "src/app/services/loading.service";
import { TrainingAssignedUsersService } from "src/app/services/training-assigned-users.service";
import { RestResponse } from "src/app/shared/auth.model";
import { ToastService } from "src/app/shared/toast.service";

@Injectable({
    providedIn: 'root'
})
export class TrainingAssignedFarmsManager extends BaseManager {

    constructor(protected trainingAssignedUsersService: TrainingAssignedUsersService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(trainingAssignedUsersService, loadingService, toastService);
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.trainingAssignedUsersService.fetchAllAssignFarmUsers(filterParam);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onFetchAllSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }
}