import { Component, ElementRef, HostListener, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import * as moment from 'moment';
import { AccountService } from 'src/app/services/account.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';
declare const $: any;
@Component({
  selector: 'app-staff-homepage',
  templateUrl: './staff-homepage.component.html',
  styleUrls: ['./staff-homepage.component.scss']
})
export class StaffHomepageComponent implements OnInit {
  public captchaResolved: boolean = false;
  submitButton: string = "Submit"
  public user: any = {
    firstName: null,
    lastName: null,
    email: null,
    phoneNumber: null,
    message: null,
    countryCode: null,
  }
  public onClickValidation: boolean = false;
  public currentYear: string;

  checkCaptcha(captchaResponse: string) {
    this.captchaResolved = (captchaResponse && captchaResponse.length > 0) ? true : false
  }
  constructor(private toastService: ToastService, private accountService: AccountService, private titleService: Title, private elementRef: ElementRef) {
    this.titleService.setTitle("Welcome to Integrax");
  }
  @HostListener('window:scroll', ['$event'])
  scrollHandler(event) {
    this.scrollFunction()
  }
  ngOnInit(): void {
    this.scrollFunction();
    this.currentYear = moment().format('YYYY');
  }
  scrollFunction() {
    let scrollTop = $(window).scrollTop();
    if (window.innerWidth > 992) {
      if (scrollTop > 160) {
        $('.fixed-menu').addClass('fix-nav-top').removeClass('d-none');
        return;
      }
      $('.fixed-menu').removeClass('fix-nav-top').addClass('d-none');
    }
  }

  telInputObject(event: any) {
    event.setCountry('au');
  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  async save(valid) {
    if (!valid) {
      this.onClickValidation = true;
      return;
    }
    if (!this.captchaResolved) {
      this.onClickValidation = true;
      this.toastService.error("Please click on captcha");
      return;
    }

    this.submitButton = "Please Wait..."
    try {
      const response: RestResponse = await this.accountService['contactUs'](this.user);
      if (!response.status) {
        this.toastService.error(response.message);
        this.submitButton = "Submit"
        return;
      }
      Object.keys(this.user).forEach(key => {
        this.user[key] = null;
      });
      this.toastService.success(response.message);
      this.submitButton = "Submit"
    } catch (e) {
      this.submitButton = "Submit"
      this.toastService.error(e.message);
    }
  }
  scrollToContact() {
    const contactElement = this.elementRef.nativeElement.querySelector('#Contact-Us');
    if (contactElement) {
      contactElement.scrollIntoView({ behavior: 'smooth' });
    }
  }
}
