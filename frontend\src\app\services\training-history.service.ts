import { Injectable } from '@angular/core';
import { HttpServiceRequests } from '../shared/http.service';
import { IResourceWithId, RestResponse } from '../shared/auth.model';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseService } from '../config/base.service';
import { FilterParam } from '../models/filterparam';

@Injectable({
  providedIn: 'root'
})
export class TrainingHistoryService extends BaseService {
  userId: string;
  constructor(public http: HttpClient) {
    super(http, '', '');
  }

  getUserId(id: string) {
    this.userId = id;
  }

  fetchAll(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/TrainingHistorys/' + this.userId, filterParam);
  }

  approveOrRejectTraining(data: any): Promise<RestResponse> {
    return this.updateRecord('/api/training/approved/rejected', data);
  }

}
