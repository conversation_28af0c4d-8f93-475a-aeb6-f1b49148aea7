import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminInactiveService extends BaseService {
    userId: string;

    constructor(public http: HttpClient) {
        super(http, '/api/account/inactive/program/admin', '/api/account/inactive/program/admins');
    }

    getEnrolledgCourse(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/account/inactive/program/admins', filterParam);
    }

    removeUserAccess(id: string): Promise<RestResponse> {
        return this.removeRecord('/api/account/admin/program/' + id)
    }

    activateDeactivateUser(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/account/user/activate-deactivate', data);
    }
}

