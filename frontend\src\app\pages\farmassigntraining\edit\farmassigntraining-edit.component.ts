import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMyDpOptions } from 'mydatepicker';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { FarmAssignTraining } from '../../../models/farmassigntraining';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { FarmAssignTrainingManager } from '../farmassigntraining.manager';

import { TrainingManager } from '../../training/training.manager';
import { Training } from '../../../models/training';
import { FarmManager } from '../../farm/farm.manager';
import { Farm } from '../../../models/farm';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
declare const $: any;

@Component({
  selector: 'app-farmassigntraining-edit',
  templateUrl: './farmassigntraining-edit.component.html',
  styleUrls: ['./farmassigntraining-edit.component.scss']
})

export class FarmAssignTrainingEditComponent extends BaseEditComponent implements OnInit {
   public myDatePickerOptions: IMyDpOptions;
  public farmAssignTraining: FarmAssignTraining;
	public trainings:Training[];
	public farms:Farm[];
  
  constructor(protected route: ActivatedRoute, protected farmassigntrainingManager: FarmAssignTrainingManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private trainingManager:TrainingManager, private farmManager:FarmManager
  			  ,public commonUtil:CommonUtil ) {
    	super(farmassigntrainingManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.farmAssignTraining = new FarmAssignTraining();
  	this.farmAssignTraining.isActive=true;   
    this.setRecord(this.farmAssignTraining);
	
     this.myDatePickerOptions = {
      dateFormat:Constant.MY_DATE_PICKER.DATE_TYPE
     } 
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.trainings = new Array<Training>();
  	this.farms = new Array<Farm>();
    this.init();
  }

  onFetchCompleted() {
  	this.farmAssignTraining = FarmAssignTraining.fromResponse(this.record);
    this.setRecord(this.farmAssignTraining);
  }

  
  
  async fetchAssociatedData() {
	this.trainings = await this.trainingManager.fetchAllData(null);       		
	this.farms = await this.farmManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const trainingIdId: string = this.route.snapshot.queryParamMap.get('Training');
		if (trainingIdId){
			this.onAssociatedValueSelected({"id":trainingIdId},'farmAssignTrainingTrainingIdSelect');
		}
    	const farmIdId: string = this.route.snapshot.queryParamMap.get('Farm');
		if (farmIdId){
			this.onAssociatedValueSelected({"id":farmIdId},'farmAssignTrainingFarmIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/farm-assign-training');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='farmAssignTrainingTrainingIdSelect') || this.request.popupId==='farmAssignTrainingTrainingIdPopup'){
			this.farmAssignTraining.trainingId = selectedRecord.id;
			this.checkConditionToReload(this.trainings, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='farmAssignTrainingFarmIdSelect') || this.request.popupId==='farmAssignTrainingFarmIdPopup'){
			this.farmAssignTraining.farmId = selectedRecord.id;
			this.checkConditionToReload(this.farms, selectedRecord);
			return;
	    }
  	
	 }
}
