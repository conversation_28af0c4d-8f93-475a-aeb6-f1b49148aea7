import {Injectable} from '@angular/core';
import {HttpServiceRequests} from '../shared/http.service';
import {IResourceWithId, RestResponse} from '../shared/auth.model';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {BaseService} from '../config/base.service';
import {FilterParam} from '../models/filterparam';
import { filter } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class DashboardService extends HttpServiceRequests<IResourceWithId> {

    constructor(public http: HttpClient) {
        super(http);
    }

    fetchAllDashboard(filterParam: FilterParam,url: string): Promise<RestResponse> {
      return this.getRecords(url,filterParam);
    }

}
