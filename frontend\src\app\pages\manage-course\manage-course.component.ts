import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ManageCourseService } from './manage-course-service';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { Router } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { ManageCourseManager } from './manage-course-manager';
import { Course } from 'src/app/models/course';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import * as moment from 'moment';
import { RestResponse } from 'src/app/shared/auth.model';
import AOS from 'aos';
import { Users } from 'src/app/models/users';
import { ManageCourseUserService } from '../program-users/manage-course-users-service';
import { ManageCourseUserManager } from '../program-users/manage-course-users-manager';
import { CourseAllocatedUsersListService } from '../course-allocated-users-list/course-allocated-users-list.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Constant } from 'src/app/config/constants';
import { Farm } from 'src/app/models/farm';
import { FarmService } from '../farm/farm.service';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-course',
  templateUrl: './manage-course.component.html',
  styleUrls: ['./manage-course.component.scss']
})
export class ManageCourseComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @ViewChild('userInviteForm') userInviteForm: NgForm;
  selectedFarmIds: string[] = [];
  moment: any = moment;
  courses: Course;
  publishCourseList: any[];
  programCourseList: any[];
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  optionalValidationMessage: string = "Please Select Site Or User Or Both";
  recordData: any;
  selectedCourseId: any;
  record: any = { isPublish: false };
  //modal
  // selectedInviteUserCourse: string;
  dropdownSettings = {};
  inviteUserManageCourseModal: any;
  insertedData: { username: string, email: string, id }[] = [];
  Courses: { username: string, email: string } = { username: '', email: '' };
  errorMessage: string = '';
  selectedCourseUserId: string;
  selectedCourseUser: any;
  selectedCourseProgramUserId: string;
  selectedCourseProgramUser: any;
  readonly MY_CONSTANT = Constant;
  selectedProgramAdmin: string | null = null; // Initialize with null or appropriate default value
  isSelfInvite: boolean = true; // Default to self-invite
  filterManageCourseModal: any;
  trainingFilterData: any;
  isFarmInvite: boolean = true;
  farms: Farm[];
  farmValidation = false;
  individualValidation = false;

  constructor(protected manageCourseManager: ManageCourseManager, protected toastService: ToastService, protected farmService: FarmService, protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService, protected router: Router, public commonUtil: CommonUtil, private manageCourseService: ManageCourseService, private manageCourseUserService: ManageCourseUserService) {
    super(manageCourseManager, commonService, toastService, loadingService, router);
  }

  ngOnInit(): void {
    this.setDropdownSettings();
    this.records = new Array<Course>();
    this.request.loadEditPage = false;
    this.courses = new Course();
    this.publishCourseList = new Array();
    this.programCourseList = new Array();
    this.isPlusButton = false
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.filterParam.strProgramAdmin = this.authService.getUser().id;
    this.fetchAuthorLearningSeriesData();
    this.fetchFiltereData();
    this.init();
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.selectedCourseProgramUserId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.inviteUserManageCourseModal = new bootstrap.Modal(
        document.getElementById('inviteUserManageCourseModal')
      );
    }, 0)
    setTimeout(() => {
      this.filterManageCourseModal = new bootstrap.Modal(
        document.getElementById('filterManageCourseModal')
      );
    }, 0)
  }

  async FetchFarms(courseId: string) {
    // need to move to if check as we need farms detail for super admin
    this.filterParam = new FilterParam();
    this.filterParam.relationId = courseId;
    this.filterParam.relationTable = 'COURSE';
    const farms: RestResponse = await this.farmService.fetchAvailableFarms(this.filterParam);
    this.farms = farms.data;
    this.farms.forEach(farm => {
      farm.displayLabel = farm.farmCode.trim() + ' - ' + farm.name;
    });
  }

  ngOnDestroy() {
    this.clean();
  }

  removeSuccess() {
    this.onCancel();
  }

  async fetchCourseUsersData() {

    const resp: RestResponse = await this.manageCourseService.getCourseUserRecords(this.filterParam);
    this.loadingService.hide();
    if (!resp.status) {
      return;
    }
    this.records = resp.data;
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/program-admin/course/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/program-admin/course/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/course/edit/' + id])
    this.selectedCourseId = id;
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  updateIspublish(data: any, recordData: any) {
    this.commonService.confirmation('Would like to change the status?', this.updateIspublishCallback.bind(this), { id: recordData.id, isPublish: data.currentTarget.checked }, null, null, this.cancelupdateIspublishCallback.bind(this));
  }

  cancelupdateIspublishCallback() {
    this.onCancel();
  }

  async updateIspublishCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseManager.updatePublish(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // invite user modal
  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  selectInviteUserCourse(event: any): void {
    if (event) {
      // this.selectedInviteUserCourse = event.title;
      this.selectedCourseUser = event.title;
      this.selectedCourseUserId = event ? event.id : null;
      this.FetchFarms(event.id);
    }

  }

  selectInviteProgramUserCourse(programCourse: any): void {
    let param = new FilterParam();
    param.strProgramAdmin = programCourse.id;
    this.fetchPublishRecords(param);
  }

  openInviteCourseUserModal() {
    // reset template-driven form and component state
    this.userInviteForm?.resetForm();
    this.onClickValidation = false;
    this.farmValidation = false;
    this.individualValidation = false;
    this.farms = new Array<Farm>();
    AOS.init({ disable: true });
    this.inviteUserManageCourseModal.show();
    this.courses = new Course();
    this.selectedCourseUserId = null;
    this.selectedCourseProgramUserId = null;
    this.insertedData = [];
    this.selectedFarmIds = [];
    this.errorMessage = '';
    this.isFarmInvite = true;
    this.isSelfInvite = true;
    this.filterParam.isSelf = true;
    this.fetchPublishRecords(this.filterParam);
    if (this.authService.getRoles().includes('ROLE_ADMIN')) {
      this.fetchProgramActiveUserRecords();
    }
  }

  closeInviteModal() {
    this.inviteUserManageCourseModal.hide();
    this.courses = new Course();
  }

  //program active user course
  async fetchProgramActiveUserRecords(filterParam?: FilterParam) {
    try {
      var course = new Course();
      course.id = this.selectedCourseProgramUserId;
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseService.getProgramCourse(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.programCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  //course user
  async fetchPublishRecords(param?: FilterParam) {
    try {
      // var course = new Course();
      // course.id = this.selectedCourseId;
      this.selectedCourseUserId = null;
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseService.getAvailablePublishedCourses(param);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.router.navigate(['/dashboard/program-admin/courses']);
  }

  async onClickInviteUserCourse(form: any) {
    this.onClickValidation = true;
    this.farmValidation = false;
    this.individualValidation = false;

    // Ensure course is selected before proceeding
    if (!form?.controls?.selectedCourseUser?.valid) {
      return;
    }

    if (this.isFarmInvite) {
      // Check if companies are selected when farm invite is chosen
      if (!this.selectedFarmIds || this.selectedFarmIds.length === 0) {
        this.farmValidation = true;
        return;
      }
      this.courses.courseEnrolled = this.selectedFarmIds.map(id => ({ farmId: id }));
    } else {
      const shouldValidateForm = this.insertedData.length === 0;

      if (shouldValidateForm && !form.valid) {
        this.individualValidation = true;
        return;
      }

      if (this.insertedData.length === 0) {
        this.toastService.error('Please add at least one user.');
        return;
      }

      // no error — set form data
      this.courses.courseEnrolled = this.insertedData;
    }

    // Passed all validation
    this.onClickValidation = false;

    try {
      this.loadingService.show();
      this.courses.id = this.selectedCourseUserId;

      if (this.authService.getRoles().includes('ROLE_ADMIN')) {
        this.courses.programAdmin = this.isSelfInvite
          ? this.authService.getUser().id
          : this.selectedCourseProgramUserId;
      }

      const response: RestResponse = (this.authService.getRoles().includes('ROLE_ADMIN'))
        ? await this.manageCourseUserService.sendAdminCourseInvite(this.courses)
        : await this.manageCourseUserService.sendCourseInvite(this.courses);

      this.loadingService.hide();

      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }

      // Reset form
      this.insertedData = [];
      this.courses = new Course();
      this.inviteUserManageCourseModal.hide();
      this.onSaveSuccess(response.message);
      this.fetchCourseUsersData();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  insertData(form: any, id: any) {
    console.log("form", form);
    this.individualValidation = !form.valid;

    if (!form.valid) {
      return;
    }

    const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$/;

    if (!emailPattern.test(this.courses.email)) {
      this.errorMessage = 'Invalid email format.';
      return;
    }

    const isDuplicate = this.insertedData.some(data => data.email === this.courses.email);

    if (isDuplicate) {
      this.errorMessage = 'This email is already entered.';
    } else {
      this.insertedData.push({
        username: this.courses.username,
        email: this.courses.email,
        id: this.courses.id
      });

      this.errorMessage = '';
      this.courses = new Course();
      this.individualValidation = false; // Reset error flag
    }
  }

  removeData(index: number) {
    this.insertedData.splice(index, 1);
    this.errorMessage = '';  // Clear error message
  }

  //cours detail page linking
  openCourseDetailPage(record: any, type: string): void {
    if (type === 'coursedetail') {
      this.router.navigate(['/dashboard/program-admin/course/detail/' + record.id],
      );
    }
    else {
      this.router.navigate(['/dashboard/program-admin/preview/course/detail/' + record.id],)

    }
  }

  clearErrorMessage() {
    this.errorMessage = '';
  }

  //radio button using in  invite user modal
  selectInviteUser(isSelf: boolean): void {
    this.isSelfInvite = isSelf;
    const param = new FilterParam();
    param.isSelf = isSelf;
    this.fetchPublishRecords(param);
  }

  openCourseParticipants(record: any) {
    this.router.navigate(
      ['/dashboard/program-admin/course/participants/' + record.id],
    );
  }

  //filter course
  openFilterCourseModal() {
    AOS.init({ disable: true });
    this.filterManageCourseModal.show();
  }

  onClickCourseFilter(valid) {
    this.filterManageCourseModal.hide()
    this.onCancel();
  }

  resetFilter() {
    delete this.filterParam.author;
    delete this.filterParam.courseTitle;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    delete this.filterParam.isPublish;
    this.filterManageCourseModal.hide();
    this.onCancel();
  }

  async fetchAuthorLearningSeriesData() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseService.fetchLearningSeriesAuthorBased(this.filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.record = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  async fetchFiltereData() {
    this.trainingFilterData = await this.manageCourseService.getTrainingFilterData(null);
  }

  onAuthorChange(author: string) {
    let param = new FilterParam();
    param.author = author;
    this.fetchAuthorLearningSeriesData();
  }

  //naivgate to course completeion users page
  navigateToCourseUsers(courseId: string): void {
    if (courseId) {
      this.router.navigate([`/dashboard/program-admin/course/users/${courseId}`]);
    } else {
      console.error('Course ID is undefined');
    }
  }

}


