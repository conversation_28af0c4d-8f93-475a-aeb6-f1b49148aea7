<div class="site-login-page">
    <div class="row g-0">
        <div data-aos="flip-up" data-aos-duration="1000"
            class="col-12 col-lg-5 col-xl-3 col-xxl-3 offset-lg-1 offset-xl-1 offset-xxl-2 login-left-side-section">
            <div class="register-form-section login-form-section">
                <div class="site-logo d-sm-block d-lg-none text-right">
                    <img src="/assets/images/banner.png">
                </div>
                <div [routerLink]="['/']" class="margin-bottom-20 cursor-pointer">
                    <img src="/assets/images/logo.svg" class="img-fluid" width="200">
                </div>
                <h4>Create Account</h4>
                <p>Please make sure fill all the filed before click on button</p>
                <div class="clearfix"></div>
                <form autocomplete="off" #registerForm="ngForm" novalidate="novalidate">
                    <div class="row">
                        <div class="col-12 col-lg-6 mb-4 ps-0">
                            <div class="form-floating">
                                <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text"
                                    name="firstName" #firstName="ngModel" [(ngModel)]="user.firstName"
                                    required="required" placeholder="First Name"
                                    [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
                                <label for="floatingInput">{{"USERS.FirstName" | translate}}</label>
                            </div>
                            <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation">
                            </app-validation-message>
                        </div>
                        <div class="col-12 col-lg-6 mb-4 pe-0">
                            <div class="form-floating">
                                <input pattern="[a-zA-Z][a-zA-Z ]+[a-zA-Z]$" class="form-control" type="text"
                                    name="lastname" #lastName="ngModel" [(ngModel)]="user.lastName" required="required"
                                    placeholder="Last Name"
                                    [ngClass]="{'is-invalid':!lastName.valid && onClickValidation}">
                                <label for="floatingInput">Last Name</label>
                            </div>
                            <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation">
                            </app-validation-message>
                        </div>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="form-floating">
                            <input autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
                                (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                                (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="user.phoneNumber"
                                [ngClass]="{'is-invalid':(phone.invalid || !phone.value) && onClickValidation && (email.invalid || !email.value)}"
                                [required]="!email.value || !email.valid" placeholder="Phone Number" value=""
                                minlength="7" maxlength="12" pattern="^[0-9]+$"
                                (countryChange)="onCountryChange($event)">
                            <!-- <label for="floatingInput" class="mobile_number_label">Mobile Number</label> -->
                        </div>
                        <app-validation-message [field]="phone" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="form-floating">
                            <input autocomplete="off" class="form-control" type="email" name="email"
                                (hasError)="hasError($event)" #email="ngModel" [(ngModel)]="user.email"
                                [ngClass]="{'is-invalid':(email.invalid || !email.value) && onClickValidation  && (phone.invalid || !phone.value)}"
                                placeholder="Email Address" [required]="!phone.value || !phone.valid" value=""
                                pattern="^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$">
                            <label for="floatingInput">Email Address</label>

                        </div>
                        <app-validation-message [field]="email" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <!-- <div class="col-12 col-md-12 col-lg-12 col-xl-12">
                <div class="form-floating">
                    <div class="mb-4 mt-2 form-control select-width ng-select-main-container"
                        [ngClass]="{'is-invalid':!programNameTypeId.valid && onClickValidation}">
                        <ng-select placeholder="Program Name" name="programNameTypeId"
                            [items]="filteredContentTypes" bindLabel="title" bindValue="id" (change)="selectContentType($event)"
                            required="required" class="custom-multiselect form-control padding-bottom-8"
                            [(ngModel)]="user.programName" #programNameTypeId="ngModel">
                        </ng-select>
                    </div>
                    <label for="programName">Program Name</label>
                </div>
            </div> -->
                    <div class="col-12 mb-4">
                        <div class="form-floating">
                            <input class="form-control" type="text" name="programName" #programName="ngModel"
                                [(ngModel)]="user.programName" required="required" placeholder="Program Name"
                                [ngClass]="{'is-invalid':!programName.valid && onClickValidation}">
                            <label for="floatingInput">Program Name</label>
                        </div>
                        <app-validation-message [field]="programName" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="form-floating">
                            <input [type]="passwordFieldType" class="form-control" name="password" id="floatingPassword"
                                placeholder="Password" #password="ngModel" [(ngModel)]="user.password"
                                required="required" autocomplete="new-password"
                                [ngClass]="{'is-invalid':!password.valid && onClickValidation}">
                            <label for="floatingPassword">Password</label>
                            <i (click)="eyePassword()" *ngIf="passwordFieldType == 'text'" class="bi bi-eye"></i>
                            <i (click)="eyePassword()" class="bi bi-eye-slash"
                                *ngIf="passwordFieldType == 'password'"></i>
                        </div>
                        <app-validation-message [field]="password" [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 mb-4">
                        <div class="form-floating">
                            <div class="form-floating">
                                <input [type]="confirmPasswordFieldType" [(ngModel)]="comparableField"
                                    #confirmPassword="ngModel"
                                    [ngClass]="{'is-invalid':(!confirmPassword.valid || password.value != confirmPassword.value) && onClickValidation}"
                                    required="required" name="confirmPassword" type="password" class="form-control"
                                    id="floatingConfirmPassword" placeholder="Password">
                                <label for="floatingConfirmPassword">Confirm Password</label>
                            </div>
                            <i (click)="eyeConfirmPassword()" *ngIf="confirmPasswordFieldType == 'text'" class="bi bi-eye"></i>
                            <i (click)="eyeConfirmPassword()" class="bi bi-eye-slash"
                                *ngIf="confirmPasswordFieldType == 'password'"></i>
                        </div>
                        <app-validation-message [field]="confirmPassword" [comparableField]="password"
                            [onClickValidation]="onClickValidation">
                        </app-validation-message>
                    </div>
                    <div class="col-12 col-lg-12 mb-4">
                        <button [disabled]="buttonName == 'PLEASE WAIT...' ? true : false"
                            (click)="save(registerForm.form.valid)"
                            class="btn bg-dark w-100 fs-6 p-3 btn-lg text-light">{{buttonName}}</button>
                    </div>
                </form>
                <p class="term-condition-text">
                    Existing user? <a [routerLink]="['/login']">Login</a>
                </p>
                <p class="term-condition-text">
                    By signing in, creating an account, you are agreeing to our <br><a
                        href="/terms-and-conditions">Terms of Use</a> and our
                    <a href="/privacy-policy">Privacy Policy</a>
                </p>
            </div>
        </div>
        <div data-aos="flip-up" data-aos-duration="1000"
            class="col-12 col-lg-6 col-xl-8 col-xxl-6 d-md-none d-lg-block login-right-side-section">
        </div>
    </div>
</div>