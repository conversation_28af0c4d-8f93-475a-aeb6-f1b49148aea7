import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class UserInvitedService extends BaseService {
    constructor(public http: HttpClient) {
        super(http, '/api/course/invited/user', '/api/course/invited/users');
    }

    strUserId: string;

    getUserId(id: string) {
        this.strUserId = id;
    }

    getInvitedCourse(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/course/invited/users', filterParam);
    }

    resendInviteData(id: string): Promise<RestResponse> {
        return this.updateRecord('/api/course/resend/invitation/' + id, null);
    }

    getisPublish(data: any): Promise<RestResponse> {
        return this.getRecords('/api/ispublish/courses', data);
    }

}

