import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageTrainingLibraryService } from './training-library.service';

@Injectable({
  providedIn: 'root'
})
export class ManageTrainingLibraryManager extends BaseManager {

  constructor(protected manageLearningSeriesService: ManageTrainingLibraryService, protected loadingService: LoadingService, protected toastService: ToastService) {
    super(manageLearningSeriesService, loadingService, toastService);
  }
}
