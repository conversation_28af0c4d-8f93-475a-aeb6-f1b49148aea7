import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { ToastService } from 'src/app/shared/toast.service';
import { TrainingHistoryManager } from './training-history.manager';
import { Users } from 'src/app/models/users';
import { TrainingHistoryService } from 'src/app/services/training-history.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Constant } from 'src/app/config/constants';
import { CommonUtil } from 'src/app/shared/common.util';
import AOS from 'aos';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { TrainingService } from '../training/training.service';
import { AuthService } from 'src/app/shared/auth.services';
import { Observable } from 'rxjs';
import { saveAs } from 'file-saver';
import { catchError, filter } from 'rxjs/operators';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment.prod';
import * as moment from 'moment';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-training-history',
  templateUrl: './training-history.component.html',
  styleUrls: ['./training-history.component.scss']
})
export class TrainingHistoryComponent extends BaseListServerSideComponent implements OnInit {
  @Input() assignVideoFromMoment: boolean | false;
  @Output() assignTrainingIdOutput = new EventEmitter<string>();

  readonly MY_CONSTANT = Constant;
  userId: string | undefined;
  loadingVideo: boolean;
  userTrainingVideoModal: any;
  moment: any = moment;
  searchTraining: any;
  isTrainingCsvExport: boolean = false
  filterTrainingModal: any;
  trainingFilterData: RestResponse;
  rejectedLogsCountsData: any;
  rejectedLogsCountsModal: any;
  username: any;
  constructor(private route: ActivatedRoute, protected trainingHistoryManager: TrainingHistoryManager, protected commonService: CommonService, protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private trainingHistoryService: TrainingHistoryService, private commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private trainingService: TrainingService, private authService: AuthService, private http: HttpClient) {
    super(trainingHistoryManager, commonService, toastService, loadingService, router);
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });
    this.records = [] as Users[];
    this.userId = this.route.snapshot.paramMap.get('id');
    this.trainingHistoryService.getUserId(this.userId);
    this.getTrainingFilterData();
    this.init();
  }
  async getTrainingFilterData() {
    this.trainingFilterData = await this.trainingService.getTrainingFilterData(null);
    this.trainingFilterData.data[0].trainingStatusDetail = this.trainingFilterData.data[0].trainingStatusDetail.map(data => {
      return {
        id: this.commonUtil.trainingFilterIdData[data.status],
        status: this.commonUtil.getTitleStatus(data.status)
      }
    })
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.userTrainingVideoModal = new bootstrap.Modal(
        document.getElementById('userTrainingVideoModal')
      );
    }, 0);
    setTimeout(() => {
      this.filterTrainingModal = new bootstrap.Modal(
        document.getElementById('filterTrainingModal')
      );
    }, 0);
    setTimeout(() => {
      this.rejectedLogsCountsModal = new bootstrap.Modal(
        document.getElementById('rejectedLogsCountsModal')
      );
    }, 0)
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  openRejectedLogsCount(record: any) {
    AOS.init({ disable: true });
    this.rejectedLogsCountsData = record;
    this.rejectedLogsCountsModal.show();
  }

  resetFilter() {
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.searchText;
    delete this.filterParam.status;
    this.filterTrainingModal.hide();
    this.onCancel();
  }

  onClickTrainingFilter() {
    this.filterTrainingModal.hide()
    this.onCancel();
  }

  approveOrRejectTraining(id: string, status: string) {
    let data = {
      trainingId: id,
      userId: this.userId,
      status: status
    }

    const statusText = status == "APPROVED" ? 'approve' : 'reject';
    const confirmatiomMessage = 'Would you like to ' + statusText + ' this training?';
    this.commonService.confirmation(confirmatiomMessage, this.approveOrRejectTrainingCallback.bind(this), data);
  }

  async approveOrRejectTrainingCallback(data: boolean) {
    try {
      const response: RestResponse = await this.trainingHistoryService.approveOrRejectTraining(data)
      if (!response.status) {
        this.onCancel();
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.onCancel();
      this.toastService.error(error.message);
    }
  }

  exportTrainingHistroyCsv() {
    this.isTrainingCsvExport = true
    try {
      this.loadingService.show();
      this.download().subscribe((response: any) => {
        this.loadingService.hide();
        const blob = new Blob([response], { type: 'application/vnd.ms.excel' });
        const file = new File([blob], this.username + '_Training Report_' + moment().format('DD-MM-YYYY') + '.xlsx', { type: 'application/vnd.ms.excel' });

        saveAs(file);
        this.toastService.success('File downloaded successfully');
        this.isTrainingCsvExport = false;
      }, (error: RestResponse) => {
        this.toastService.error(error.message);
        this.loadingService.hide();
        this.isTrainingCsvExport = false;
      });
    } catch (error) {
      this.toastService.error(error.message);
      this.isTrainingCsvExport = false;
      return;
    }
  }

  download(): Observable<Blob> {
    const userId = this.route.snapshot.paramMap.get('id');
    return this.http.post(environment.BaseApiUrl + '/api/traininghistory/' + userId + '/export',
      this.filterParam, { responseType: 'blob' }).pipe(catchError(this.parseErrorBlob));
  }

  parseErrorBlob(err: HttpErrorResponse): Observable<any> {
    const reader: FileReader = new FileReader();
    const obs = Observable.create((observer: any) => {
      reader.onloadend = (e) => {
        observer.error(JSON.parse(reader.result.toString()));
        observer.complete();
      };
    });
    this.isTrainingCsvExport = false;
    reader.readAsText(err.error);
    return obs;
  }

  watchVideo(record: any) {
    this.loadingVideo = true
    this.userTrainingVideoModal.show();
    AOS.init({ disable: true });
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.videoUrl);
      this.loadingVideo = false;
    }, 0)
  }

  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  openTrainingDetailPage(record: any) {
    if (this.assignVideoFromMoment) {
      this.assignTrainingIdOutput.emit(null);
    }
    window.open(
      '/dashboard/training/detail/' + record.trainingId,
      '_blank'
    );
  }

  assignTrainingVideoCallback(id: string) {
    this.assignTrainingIdOutput.emit(id);
  }
  assignTrainingVideo(id: string) {
    this.commonService.confirmation('Would you like to assign this training video?', this.assignTrainingVideoCallback.bind(this), id, null, null, null);
  }

}
