import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UserPendingService } from './user-pending.service';

@Injectable({
    providedIn: 'root'
})
export class UserPendingManager extends BaseManager {

    constructor(protected userPendingService: UserPendingService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userPendingService, loadingService, toastService);
    }
}
