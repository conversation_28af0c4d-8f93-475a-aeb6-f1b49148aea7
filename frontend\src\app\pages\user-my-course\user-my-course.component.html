<div class="site-customer-main-container px-0 pt-0" data-aos="fade-up" data-aos-duration="1000">
    <div class="allocated-users-list" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 d-flex justify-content-end mt-2 pe-0">
                <div class="manage-moment-button me-2">
                    <button [disabled]="isTrainingCsvExport ? true : false" (click)="exportTrainingHistroyCsv()"
                        type="button"
                        class="btn manage-filter-buttton export-btn btn-secondary text-light btn-lg d-flex height-51px font-15px">
                        <i class="icon-export bi bi-box-arrow-down me-2" alt=""></i><span
                            class="margin-top-2">{{isTrainingCsvExport
                            ?
                            'Please Wait...' : 'Export Report'}} </span>
                    </button>
                </div>
                <!-- <button (click)="openFilterTrainingModal()" type="button"
                    class="btn manage-filter-buttton bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button> -->
            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <tbody>
                    <!-- <tr style="vertical-align: middle; background-color: #fff;"> -->
                    <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                        <!-- <td class="pe-0">
                            <h5 class="code ms-2" title="Course Detail">
                                {{record.courseDetail?.code}}</h5>
                        </td> -->
                        <td class="pe-0" (click)="openCourseDetailPage(record.course)" class="pe-0">
                            <h5 class="code ms-2" title="Course Detail">
                                {{record.courseDetail?.code}}</h5>
                        </td>
                        <td>
                            <div class="vertical-line" style="border:1px solid #1681FF">
                            </div>
                        </td>
                        <td width="60" style="white-space: nowrap;">
                            <div class="title" [attr.title]="record?.title">{{record.courseDetail?.title}}</div>
                        </td>
                        <td width="100" style="white-space: nowrap;">
                            <div class="title">{{moment(record?.courseDetail.publishDate).format('DD-MM-YYYY')}}</div>
                        </td>
                        <td></td>
                        <!-- <td>
                            <img src="/assets/images/icons/menu/batch.svg" class="me-2 width-32px" alt="">
                        </td> -->
                        <td>
                            <!-- Percentage Not Complete -->
                            <div class="circular-progress" *ngIf="record.courseDetail?.percentage !== '100%'">
                                <span class="title-per timer">{{record.courseDetail?.percentage}}</span>
                                <div class="progress-bar"
                                    [ngStyle]="{'background': 'conic-gradient(#555 0% '+record.courseDetail?.percentage +', #f0f0f0 '+record.courseDetail?.percentage +')'}">
                                </div>
                            </div>
                            <!-- Percentage Complete -->
                            <div *ngIf="record.courseDetail?.percentage === '100%'">
                                <img src="/assets/images/icons/menu/batch.svg" class="me-2 img-fluid" alt=""
                                    style="vertical-align: sub;">
                            </div>
                        </td>
                        <td class="pe-0">
                            <h5 class="code" title="View User Profile" style="font-size:13px; white-space: nowrap;"
                                [routerLink]="['/dashboard/program-detail/' +record?.courseDetail?.userDetails?.id]"
                                [queryParams]="{ tab: 'profile', 'username': record?.courseDetail?.userDetails?.fullName }"
                                queryParamsHandling="merge">
                                {{record?.courseDetail?.userDetails?.fullName}}
                            </h5>
                        </td>
                        <td width="500"></td>
                        <td width="60" class="text-center custom-action-button text-right">
                            <div class="d-flex justify-content-end me-3">
                                <!-- <button type="button"
                                    class="btn manage-filter-buttton btn-lg font-15px filter-button-cls view-training me-2">
                                    View Trainings
                                </button> -->
                                <button (click)="openviewUserCourseModal(record.course)" type="button"
                                    class="btn manage-filter-buttton btn-lg font-15px filter-button-cls view-training me-2">
                                    View Training
                                </button>
                                <button
                                    class="btn manage-filter-buttton text-light btn-lg font-15px filter-button-cls revoke-access-btn"
                                    (click)="removeData(record.id)">
                                    Revoke Access
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- view user course training Modal -->
<div class="modal modal-xl fade" id="viewUserCourseModal" tabindex="-1" aria-labelledby="viewUserCourseModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <div>
                    <h5 class="modal-title" id="viewUserCourseModalLabel">Training</h5>
                </div>
                <div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div *ngIf="viewUserCourseModal && viewUserCourseModal._isShown" class="modal-body border-0">
                <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
                    <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions"
                        [dtTrigger]="dtTrigger">
                        <thead>
                            <tr>
                                <th style="width: 110px; text-wrap: nowrap;">
                                    {{'courseTraining.lastUpdatedDate' | translate}}
                                </th>
                                <!-- <th style="width:120px; text-wrap: nowrap;">
                                    {{"courseTraining.title" | translate}}
                                </th> -->
                                <th style="width:120px; text-wrap: nowrap;">
                                    {{"courseTraining.course" | translate}}
                                </th>
                                <th style="width:120px; text-wrap: nowrap;">
                                    {{"courseTraining.contentType" | translate}}
                                </th>
                                <th style="width:117px; text-wrap: nowrap;">
                                    {{"courseTraining.trainingHistory" | translate}}
                                </th>
                                <th style="width:200px; text-wrap: nowrap;">
                                    {{"courseTraining.trainingStatus" | translate}}
                                </th>
                                <th style="width:150px; text-wrap: nowrap;">
                                    {{"courseTraining.UserVideo" | translate}}
                                </th>
                                <th style="width: 50px; text-wrap: nowrap;">
                                    {{"courseTraining.action" | translate}}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="records-cls" *ngFor="let trainingRecord of trainingRecords;">
                                <td>
                                    <div>{{moment(trainingRecord?.updatedOn).format('DD/MM/YYYY')}}</div>
                                </td>
                                <!-- <td>
                                    {{trainingRecord?.trainingDetail?.learningSeriesDetail?.title}}
                                </td> -->
                                <td>
                                    {{trainingRecord?.courseDetail.title || '_'}}
                                </td>
                                <td>
                                    {{trainingRecord?.trainingDetail?.contentTypeDetail?.title || '-' }}
                                </td>
                                <td>
                                    <a (click)="openTrainingDetailPage(trainingRecord)"
                                        class="text-decoration-underline">{{trainingRecord?.trainingDetail?.title ||
                                        '-'}}</a>
                                </td>
                                <td>
                                    <div *ngIf="trainingRecord?.status else noStatus">
                                        <div [ngClass]="commonUtil.getTitleStatusColor(trainingRecord.status)"
                                            class="status-button semi-bold align-middle">
                                            <img class="me-1"
                                                [src]="commonUtil.getTitleStatusImg(trainingRecord.status)">
                                            {{commonUtil.getTitleStatus(trainingRecord.status)}}
                                        </div>
                                    </div>
                                    <ng-template #noStatus>-</ng-template>
                                </td>
                                <!-- <td>
                                    <button
                                        *ngIf="trainingRecord.videoUrl && trainingRecord?.status !== 'INPROGRESS'; else NoVideo"
                                        (click)="watchVideo(trainingRecord)" class="user-video-button bg-secondary">
                                        <img src="/assets/images/icons/menu/watch-video.svg"></button>
                                    <ng-template #NoVideo>-</ng-template>
                                </td> -->
                                <td>
                                    <ng-container
                                        *ngIf="trainingRecord.videoUrl && trainingRecord?.status !== 'INPROGRESS'; else NoVideo">
                                        <button (click)="watchVideo(trainingRecord)"
                                            class="user-video-button bg-secondary">
                                            <img src="/assets/images/icons/menu/watch-video.svg">
                                        </button>
                                    </ng-container>
                                    <ng-template #NoVideo>-</ng-template>
                                </td>
                                <!-- <td>
                                    <div class="d-flex align-items-center">
                                        <i *ngIf="trainingRecord && trainingRecord.status === 'WAITING_FOR_APPROVAL';"
                                            (click)="approveOrRejectTraining(trainingRecord.trainingId, 'APPROVED')"
                                            class="bi bi-check-circle fs-4 cursor-pointer"></i>
                                        <i *ngIf="trainingRecord && trainingRecord.status === 'WAITING_FOR_APPROVAL';"
                                            (click)="approveOrRejectTraining(trainingRecord.trainingId, 'REJECTED')"
                                            class="bi bi-x-circle ps-2 fs-4 cursor-pointer"></i>
                                        <ng-container
                                            *ngIf="!trainingRecord || trainingRecord.status !== 'WAITING_FOR_APPROVAL';">
                                            <span>-</span>
                                        </ng-container>
                                    </div>
                                </td> -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <ng-container
                                            *ngIf="trainingRecord?.status === 'WAITING_FOR_APPROVAL'; else noActions">
                                            <i (click)="approveOrRejectTraining(trainingRecord.id, 'APPROVED')"
                                                class="bi bi-check-circle fs-4 cursor-pointer"></i>
                                            <i (click)="approveOrRejectTraining(trainingRecord.id, 'REJECTED')"
                                                class="bi bi-x-circle ps-2 fs-4 cursor-pointer"></i>
                                        </ng-container>
                                        <ng-template #noActions><span>-</span></ng-template>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- watch video modal -->
<div class="modal fade" id="courseViewTrainingVideoModal" aria-hidden="true"
    aria-labelledby="courseViewTrainingVideoModalLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="courseViewTrainingVideoModalLabel"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" *ngIf="courseViewTrainingVideoModal && courseViewTrainingVideoModal._isShown">
                <div>
                    <div *ngIf="loadingVideo" class="loading-container-video-training">
                        <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
                        id="staff-video"></video>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- modal invite course user -->
<div class="modal fade" id="inviteUserManageCourseModal" tabindex="-1"
    aria-labelledby="inviteUserManageCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0" style="padding-top: 30px;
                      margin: auto 15px;">
                <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                    (click)="closeInviteModal()"></button>
            </div>
            <div *ngIf="inviteUserManageCourseModal && inviteUserManageCourseModal._isShown" class="modal-body"
                style="padding: 10px 50px;">
                <div class="modal-content-inside">
                    <h5 class="modal-title fw-bold" id="inviteUserManageCourseModalLabel">Invite User for Course</h5>
                    <p class="modal-heading pt-1" id="inviteUserManageCourseModalLabel">
                        Please make sure you fill in all the fields before you click on the Send Invite button
                    </p>
                </div>
                <form #userInviteForm="ngForm" novalidate="novalidate">
                    <!-- Radio button based on Self and Others -->
                    <div class="mb-4" *ngIf="!authService.getRoles().includes('ROLE_PROGRAM_ADMINISTRATOR')">
                        <label for="type" class="mb-2">
                            Select Your Preference
                        </label>
                        <div class="form-check">
                            <input (ngModelChange)="selectInviteUser(true)"
                                [ngClass]="{'is-invalid': !selfInviteUser.valid && onClickValidation}"
                                required="required" [(ngModel)]="isSelfInvite" #selfInviteUser="ngModel" [value]="true"
                                class="form-check-input radio-button-cls" type="radio" name="inviteUserType" id="self">
                            <label class="form-check-label ms-1" for="self">
                                Self Course Invite
                            </label>
                        </div>
                        <div class="form-check">
                            <input (ngModelChange)="selectInviteUser(false)"
                                [ngClass]="{'is-invalid': !otherInviteUser.valid && onClickValidation}"
                                required="required" [(ngModel)]="isSelfInvite" #otherInviteUser="ngModel"
                                [value]="false" class="form-check-input radio-button-cls" type="radio"
                                name="inviteUserType" id="other">
                            <label class="form-check-label ms-1" for="other">
                                Other Course Invite
                            </label>
                        </div>
                    </div>
                    <!-- Radio button based on Self and Others end-->
                    <div class="form-floating" *ngIf="!isSelfInvite && authService.getRoles().includes('ROLE_ADMIN')">
                        <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                            [ngClass]="{'is-invalid': !selectedCourseProgramUser.valid && onClickValidation}">
                            <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                name="selectedCourseProgramUser" clearable="false" [items]="programCourseList"
                                bindLabel="fullName" bindValue="id" (change)="selectInviteProgramUserCourse($event)"
                                class="custom-multiselect form-control padding-bottom-8"
                                [(ngModel)]="selectedCourseProgramUserId" #selectedCourseProgramUser="ngModel"
                                [searchable]="false">
                                <!-- [disabled]="selectedCourseProgramUserId" -->
                            </ng-select>
                        </div>
                        <label for="selectedCourseUser">{{"Course.programCourse" | translate}}</label>
                    </div>
                    <!-- Show this section only when selectedCourseProgramUserId is set -->
                    <div class="form-floating">
                        <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                            [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                            <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="selectedCourseUser"
                                clearable="false" [items]="publishCourseList" bindLabel="title" bindValue="id"
                                (change)="selectInviteUserCourse($event)"
                                class="custom-multiselect form-control padding-bottom-8"
                                [(ngModel)]="selectedCourseUserId" #selectedCourseUser="ngModel" [searchable]="false">
                                <!-- [disabled]="selectedCourseUserId" -->
                            </ng-select>
                        </div>
                        <label for="selectedCourseUser">
                            {{"Course.chooseCourse" | translate}}
                        </label>
                    </div>

                    <div class="form-floating mb-4">
                        <input maxlength="20" [ngClass]="{'is-invalid':!username.valid && onClickValidation}"
                            class="form-control b-r-8" type="text" name="username" #username="ngModel" required
                            [(ngModel)]="courses.username" placeholder="{{'Course.name' | translate}}">
                        <label for="floatingInput">{{"Course.name" | translate}}</label>
                    </div>

                    <div class="form-floating mb-2">
                        <input [ngClass]="{'is-invalid':!email.valid && onClickValidation}" class="form-control b-r-8"
                            type="email" name="email" #email="ngModel" [(ngModel)]="courses.email" required
                            placeholder="{{'Course.email' | translate}}"
                            pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                        <label for="floatingInput">{{"Course.email" | translate}}</label>
                    </div>
                    <div *ngIf="errorMessage" style="color: red;">
                        {{ errorMessage }}
                    </div>

                    <div class="text-end mb-2 mt-2">
                        <button class="btn" (click)="insertData(userInviteForm)"
                            style="border: 1px solid #000; border-style: dashed; font-size: 20px;"
                            title="Add username or email">
                            <i class="bi bi-plus-lg" style="color: #000;"></i>
                        </button>
                    </div>
                    <div class="mb-2" *ngFor="let insert of insertedData; let i = index">
                        <div class="d-flex justify-content-between align-middle"
                            style="border: 1px solid #1681FF; border-radius: 10px; padding: 10px;">
                            <h5 class="text-secondary mt-1">
                                {{insert.username}} ( {{insert.email}} )
                            </h5>
                            <div class="mt-1" (click)="removeInviteData()">
                                <img src="/assets/images/icons/menu/remove-user.svg" class="me-2 img-fluid text-end"
                                    alt="">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
                        <button (click)="onClickInviteUserCourse()" type="button"
                            [disabled]="insertedData.length===0 || selectedCourseUserId == null || selectedCourseUserId == undefined"
                            class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                            INVITE
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>