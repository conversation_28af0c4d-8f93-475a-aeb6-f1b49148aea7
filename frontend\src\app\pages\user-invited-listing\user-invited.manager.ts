import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UserInvitedService } from './user-invited.service';

@Injectable({
    providedIn: 'root'
})
export class UserInvitedManager extends BaseManager {

    constructor(protected userInvitedService: UserInvitedService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userInvitedService, loadingService, toastService);
    }
}
