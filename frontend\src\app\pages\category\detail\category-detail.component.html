<div class="breadcrumb-container">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" data-dismiss="modal" aria-label="Close" *ngIf="recordId">
	      <img src="/assets/images/back.png" class="img-responsive">
	    </a>
	    <a class="menu-icon-button" (click)="commonUtil.toggleMenu()" *ngIf="!recordId">
	      <img src="/assets/images/menu.png" class="img-responsive">
	    </a>
        <div class="project-name-container">
            <h3 class="project-name">Category Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a data-dismiss="modal" aria-label="Close">{{'Category.objNames' | translate}}</a></li>
                <li class="active">{{'COMMON.DETAIL' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right"
                title="{{'COMMON.EDIT' | translate}}" *ngIf="authService.isAccessible('CATEGORY','EditButton')"
                data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/category/edit/'+record.id]">
            <span><i class="fa fa-pencil" aria-hidden="true"></i></span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [ngClass]="{'no-padding':recordId}">
    <div class="site-card">
    	<div class="custom-tab-container">
    		<ul class="nav nav-pills nav-tabs">
		        <li [ngClass]="{'active':tabView == 'DETAIL'}"><a (click)="onChangeTab('DETAIL')">{{'Category.objName' | translate}}</a></li>
	      </ul>
      	</div>
        <div class="clearfix"></div>
        <div class="custom-tab-content-container">
		      <table class="table table-bordered table-striped" [hidden]="tabView !== 'DETAIL'" *ngIf="tabViewed['DETAIL'] || tabView == 'DETAIL'">
		    	<tbody>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.title" | translate}}
					          </td>
					            <td>{{record.title}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.description" | translate}}
					          </td>
					            <td>{{record.description}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.languageId" | translate}}
					          </td>
					    		<td>
					            	<a data-dismiss="modal" aria-label="Close" [routerLink]="['/dashboard/language/detail/'+record.languageIdDetail.id]"  *ngIf="record.languageIdDetail">
					    			{{record.languageIdDetail.name}}
					    			</a>
					    		</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.commonTitle" | translate}}
					          </td>
					            <td>{{record.commonTitle}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.groupCode" | translate}}
					          </td>
					            <td>{{record.groupCode}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.updatedOn" | translate}}
					          </td>
					             <td>{{record.updatedOn|date:'MM/dd/yyyy'}}</td>
					        </tr>
					        <tr>
					          <td class="detail-page-label" width="200">
					            {{"Category.isActive" | translate}}
					          </td>
					            <td>{{record.isActive}}</td>
					        </tr>
		    		</tbody>
		    	</table>
        </div>
     </div>
</div>
