import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { TrainingFeedService } from './trainingfeed.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class TrainingFeedManager extends BaseManager {

    constructor(protected trainingService: TrainingFeedService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(trainingService, loadingService, toastService);
    }
}
