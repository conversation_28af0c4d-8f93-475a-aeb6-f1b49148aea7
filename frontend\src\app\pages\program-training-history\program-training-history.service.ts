import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ProgramTrainingHistoryService extends BaseService {
    strUserId: string;

    constructor(public http: HttpClient) {
        super(http, '', '');
    }
    getUserId(id: string) {
        this.strUserId = id;
    }

    // fetchAll(filterParam: FilterParam): Promise<RestResponse> {
    //     return this.getRecords('/api/trainings/user/' + this.userId, filterParam);
    // }

    // GET COURSES BY USER ID
    // fetch(id: string): Observable<RestResponse> {
    //     return this.getRecord('/api/trainings/user/' + id);
    // }
    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/trainings/userid', filterParam);
      }
    
    approveOrRejectTraining(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/training/approve/reject', data);
    }
}

