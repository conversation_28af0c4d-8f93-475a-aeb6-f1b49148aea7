import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { LoginService } from 'src/app/services/login.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';

@Component({
  selector: 'app-set-new-password',
  templateUrl: './set-new-password.component.html',
  styleUrls: ['./set-new-password.component.scss']
})
export class SetNewPasswordComponent implements OnInit {
  changePasswordButtonDisabled: boolean;
  data: any = {};
  onClickValidation: boolean;
  validLinkMessage: string = "Please wait... we are validating the link"
  isUniqueCode: boolean = false

  constructor(private loadingService: LoadingService, private toastService: ToastService, private loginService: LoginService, private router: Router, private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.checkUniqueCode();
  }

  async checkUniqueCode() {
    let getParamValue = null;
    this.route.queryParams
      .subscribe(params => {
        if (params.p) {
          getParamValue = params.p;
        }
      });

    if (getParamValue) {
      try {
        const response: RestResponse = await this.loginService.getUniqueCode({uniqueCode: getParamValue});
        if (!response.status) {
          this.validLinkMessage = "Link is invalid redirect to login page"
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2000)
          return;
        }
        this.isUniqueCode = response.data;
        this.data.uniqueCode = getParamValue

      } catch (error) {
        this.toastService.error(error.message);
      }
    } else {
      this.validLinkMessage = "Unauthorized access redirect to login page"
      setTimeout(() => {
        this.router.navigate(['/login']);
      }, 2000)
    }
  }

  resetPassword(bol) {
    this.onClickValidation = !bol;
    if (!bol) {
      return false;
    }
    if (this.data.password !== this.data.confirmPassword) {
      this.onClickValidation = true;
      return;
    }
    this.changePasswordButtonDisabled = true;
    const { confirmPassword, ...rest } = this.data;
    this.loadingService.show();
    this.loginService.setNewPassword(rest)
      .then((data) => {
        this.loadingService.hide();
        if (!data.status) {
          this.changePasswordButtonDisabled = false;
          this.toastService.error(data.message);
          return;
        }
        this.toastService.success(data.message);
        this.router.navigate(['/login']);
      }, (error) => {
        this.loadingService.hide();
        this.changePasswordButtonDisabled = false;
        this.toastService.error(error.message);
      });
  }

}
