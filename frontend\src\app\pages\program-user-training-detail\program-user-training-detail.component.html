<div class="site-customer-main-container training-detail" id="userEditComponent">
    <div class="dashboard-content-container ">
        <div *ngIf="trainings?.id && !trainings?.trainingLibrary" class="row">
            <div class="col-12 col-lg-6">
                <div class="text-start">
                    <div class="d-flex">
                        <h2>{{trainings?.title}}</h2>
                    </div>
                    <hr class="mt-4 opacity-50">
                    <h6 class="mt-5 semi-bold mx-2">Description</h6>
                    <p class="mx-2" [innerHTML]="trainings?.description">
                    </p>
                    <hr class="mt-5">
                </div>
                <div class="row d-flex text-start mt-4 justify-content-between">
                    <div class="col-12 col-lg-6">
                        <h6 class="semi-bold">Content Type</h6>
                        <div>{{trainings.contentTypeDetail.title}}</div>
                    </div>
                    <div class="col-12 col-lg-6 pe-0 ps-5" *ngIf="trainings.isLock">
                        <h6 class="semi-bold">Prerequisites Training</h6>
                        <div class="text-start">{{trainings?.prerequisitesTrainingTitle}}</div>
                    </div>
                    <div class="col-12 col-lg-6 pe-0 ps-5">
                        <h6 class="semi-bold">Learning Series</h6>
                        <div class="text-start">{{trainings.learningSeriesDetail.title}}</div>
                    </div>
                    <hr class="mt-5">
                    <div class="row d-flex text-start mt-4 justify-content-between">
                        <div class="col-12 col-lg-6">
                            <h6 class="semi-bold">Private/Public</h6>
                            <div class="form-check form-switch">
                                <input data-bs-toggle="tooltip" data-bs-placement="top"
                                    class="form-check-input accessibility-switch toggle-width" type="checkbox"
                                    id="flexSwitchCheckCheckedAccssibility"
                                    [checked]="trainings?.accessibility === 'Public'"
                                    [ngModelOptions]="{standalone: true}" disabled>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 pe-0 ps-5">
                            <h6 class="semi-bold">Status</h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input toggle-width" type="checkbox" id="flexSwitchCheckChecked"
                                    [(ngModel)]="trainings.isPublish" [ngModelOptions]="{standalone: true}" disabled>
                                <label class="form-check-label" for="flexSwitchCheckChecked45545"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="ms-4">
                    <h6 class="mt-2 semi-bold">Video</h6>
                    {{trainings?.videoUrlTitle}}
                </div>
                <div class="video-wrapper mt-4">
                    <div class="video-container" id="video-container">
                        <div class="play-button-wrapper">
                            <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
                                id="circle-play-b">
                                <!-- SVG Play Button -->
                                <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                                    <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                                </svg>
                            </div>
                        </div>
                        <video playsinline [src]="trainings.url" class="mw-100" id="training_video"
                            controlslist="nodownload">
                            Your browser does not support HTML video.
                        </video>
                    </div>
                </div>
                <div class="ms-4">
                    <h6 class="mt-3 semi-bold">Thumbnail Image</h6>
                    {{trainings?.thumbnailImageUrlTitle}}
                </div>
                <div class="moment-image-container max-width-none mt-4">
                    <img [src]="trainings.thumbnailImageUrl" />
                </div>
            </div>
        </div>
        <div *ngIf="trainings?.trainingLibrary" class="row">
            <div class="col-12 col-lg-6">
                <div class="text-start">
                    <div class="d-flex">
                        <h2>{{trainings?.trainingLibraryDetail?.title}}</h2>
                    </div>
                    <hr class="mt-4 opacity-50">
                    <h6 class="mt-5 semi-bold mx-2">Description</h6>
                    <p class="mx-2" [innerHTML]="trainings?.trainingLibraryDetail?.description">
                    </p>
                    <hr class="mt-5">
                </div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="video-wrapper mt-5 mb-2">
                    <div class="video-container" id="video-container">
                        <div class="play-button-wrapper">
                            <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
                                id="circle-play-b">
                                <!-- SVG Play Button -->
                                <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                                    <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                                </svg>
                            </div>
                        </div>
                        <video playsinline [src]="trainings?.trainingLibraryDetail?.url" class="mw-100"
                            id="training_video" controlslist="nodownload">
                            Your browser does not support HTML video.
                        </video>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="row d-flex text-start mt-4 justify-content-between">
                    <div class="col-12 col-lg-6">
                        <h6 class="semi-bold">Content Type</h6>
                        <div>{{trainings?.trainingLibraryDetail?.contentTypeDetail.title}}</div>
                    </div>
                    <div class="col-12 col-lg-6 pe-0 ps-5" *ngIf="trainings?.trainingLibraryDetail?.isLock">
                        <h6 class="semi-bold">Prerequisites Training</h6>
                        <div class="text-start">{{trainings?.trainingLibraryDetail?.prerequisitesTrainingTitle}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="moment-image-container max-width-none mt-4">
                    <img [src]="trainings?.trainingLibraryDetail?.thumbnailImageUrl" />
                </div>
            </div>
        </div>
    </div>
</div>