<div class="loading-link-cls" *ngIf="!userName">
  <img src="/assets/images/loading-image.gif" /><span class="ms-2">{{validLinkMessage}}</span>
</div>
<div *ngIf="userName" class="offset-lg-3 offset-xl-3 col-xl-6 col-lg-6">
  <div class="verify-otp">
    <div class="verify-otp-logo">
      <img src="/assets/images/otp-logo.svg" alt="" class="img-fluid">
    </div>
    <div class="verify-otp-header">
      <h1 class="verify-otp-heading">Verify your Email</h1>
      <p class="verify-otp-sub-heading">You will receive 5 digit code to verify</p>
      <form #recordForm="ngForm" novalidate="novalidate">
        <div class="form-floating">
          <input name="password" required="required" [ngClass]="{'is-invalid':!verifyOtp.valid && onClickValidation}"
            #verifyOtp="ngModel" [(ngModel)]="otp" type="password" class="form-control" id="floatingPassword"
            placeholder="Password">
          <label for="floatingPassword">Password</label>
        </div>
        <div class="d-flex justify-content-between margin-top-30">
          <div class="mt-4">
            <p class="verify-otp-msg p-0">Didn`t receive code? <a (click)="resendEmailOtp()" class="fw-bold">Resend Verification Code</a>
            </p>
            <!-- <p class="verify-otp-msg p-0">Wrong phone number? <a (click)="openUpdateNumberModal()"
                class="fw-bold">Update Number</a></p> -->
          </div>
          <button (click)="verifyPhone(recordForm.form.valid)" class="verify-button bg-secondary">VERIFY</button>
        </div>
      </form>
    </div>
  </div>
</div>
<div class="modal fade" id="updatePhoneNumberModal" aria-hidden="true" aria-labelledby="updatePhoneNumberModal"
  tabindex="-1">
  <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content" *ngIf="updatePhoneNumberModal && updatePhoneNumberModal._isShown">
      <div class="modal-header">
        <h5 class="modal-title" id="updatePhoneNumberModalLabel">Update Phone Number</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form class="height-280-px" #updatePhoneNumberForm="ngForm" novalidate="novalidate">
          <div  class="col-12 col-lg-12 mb-12 height-200-px">
            <div class="form-floating">
              <input autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
                (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
                (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="phoneNumber"
                [ngClass]="{'is-invalid':!phone.valid && onClickValidationUpdateNumber}" required="required"
                placeholder="Phone Number" minlength="7" maxlength="12" pattern="^[0-9]+$"
                (countryChange)="onCountryChange($event)">
              <!-- <label for="floatingInput" class="mobile_number_label">Mobile Number</label> -->
            </div>
            <app-validation-message [field]="phone" [onClickValidation]="onClickValidationUpdateNumber">
            </app-validation-message>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            <button [disabled]="phoneNumberUpdating ? true : false" (click)="onClickUpdatePhoneNumber(updatePhoneNumberForm.form.valid)" type="button"
              class="btn btn-primary">{{phoneNumberUpdating ? 'Please Wait...' : 'Update'}}</button>
          </div>
          </form>
      </div>
    </div>
  </div>
</div>
