import { TranslateService } from "@ngx-translate/core";
import { BaseModel } from "../config/base.model";
import { ToastService } from "../shared/toast.service";
import { SafeHtml } from "@angular/platform-browser";

export class Notification extends BaseModel {

    messageHtml: SafeHtml;
    type: string;
    isArchive: boolean;
    targetId: string;
    targetType: string;
    userId: string;
    isSeen: boolean;
    userRole: string;
    archivedBy: string;
    archivedDate: string;
    message: string;

    
    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        throw new Error("Method not implemented.");
    }
    forRequest() {
        throw new Error("Method not implemented.");
    }

}