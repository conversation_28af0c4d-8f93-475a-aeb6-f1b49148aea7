import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';

@Component({
  selector: 'app-date-range-filter',
  templateUrl: './date-range-filter.component.html',
  styleUrls: ['./date-range-filter.component.scss']
})
export class DateRangeFilterComponent implements OnInit {
  @Input() fromDateInput: any | undefined;
  @Input() toDateInput: any | undefined;
  @Input() pageType: string;
  minFromDate: Date;
  maxFromDate: Date | null;
  minToDate: Date | null;
  maxToDate: Date;
  fromDate: any;
  toDate: any;
  @Output() fromDateOutput = new EventEmitter<any>();
  @Output() toDateOutput = new EventEmitter<any>();
  constructor() {
    this.minFromDate = new Date(1900, 0, 1);
    this.maxToDate = new Date();
  }

  ngOnInit() {
    this.maxFromDate = this.toDateInput ? this.toDateInput : new Date();
    this.minToDate = this.fromDateInput ? this.fromDateInput : null;
    this.fromDate = this.fromDateInput;
    this.toDate = this.toDateInput;
  }


  ngOnChanges(changes: SimpleChanges) {
    if (this.pageType === 'programAdminDashboard') {
      if (changes.fromDateInput && changes.fromDateInput.currentValue) {
        this.fromDate = changes.fromDateInput.currentValue;
      }
      if (changes.toDateInput && changes.toDateInput.currentValue) {
        this.toDate = changes.toDateInput.currentValue;
      }
    }
  }

  fromDateChange(type: string, event: MatDatepickerInputEvent<Date>) {
    this.minToDate = event.value;
    this.fromDateOutput.emit(this.fromDate);
    if (event.value !== null) {
      this.maxToDate = new Date();
    }
  }

  toDateChange(type: string, event: MatDatepickerInputEvent<Date>) {
    this.maxFromDate = event.value;
    this.toDateOutput.emit(this.toDate)
  }

  clearFromDate(event) {
    this.fromDate = null
    this.minToDate = null;
    this.fromDateOutput.emit(this.fromDate);
    event.stopPropagation();
  }

  clearToDate(event) {
    this.toDate = null;
    this.maxFromDate = new Date();
    this.toDateOutput.emit(this.toDate)
    event.stopPropagation();
  }

}
