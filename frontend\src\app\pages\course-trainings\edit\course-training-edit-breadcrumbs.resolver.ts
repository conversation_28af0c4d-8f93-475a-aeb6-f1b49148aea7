import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class CourseTrainingEditBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const trainingId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard/program-admin", active: false
            },
            {
                title: "Manage Trainings", link: "/dashboard/program-admin/trainings", active: false
            },
            {
                title: "Training", link: "/dashboard/program-admin/trainings/edit/" + trainingId, active: true
            }
        ])



    }
}
