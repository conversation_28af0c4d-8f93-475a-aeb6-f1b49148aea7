import { Component, OnDestroy, OnInit } from '@angular/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { ManageCourseLearningSeriesManager } from './manage-course-learning-series.manager';
import { ToastService } from 'src/app/shared/toast.service';
import { LoadingService } from 'src/app/services/loading.service';
import { CommonService } from 'src/app/shared/common.service';
import { AuthService } from 'src/app/shared/auth.services';
import { ManageCourseLearningSeriesService } from './manage-course-learning-series.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { Router } from '@angular/router';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import * as moment from 'moment';
declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-manage-course-learning-series',
  templateUrl: './manage-course-learning-series.component.html',
  styleUrls: ['./manage-course-learning-series.component.scss']
})
export class ManageCourseLearningSeriesComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  onClickValidation: boolean;
  moment: any = moment;
  fromDate: any;
  toDate: any;


  constructor(protected manageCourseLearningSeriesManager: ManageCourseLearningSeriesManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private manageCourseLearningSeriesService: ManageCourseLearningSeriesService,) {
    super(manageCourseLearningSeriesManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseLearningSeries>();
    this.init();
  }

  ngAfterViewInit() {
  }

  onItemSelection(record: any) {
    this.onAssociatedValueSelected(record);
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/program-admin/manage-course-learning-series/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/program-admin/manage-course-learning-series/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.refreshRecord();
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/manage-course-learning-series/edit/' + id]);
  }

  removeNullDataFromObject(obj: any) {
    for (var propName in obj) {
      if (obj[propName] === null || obj[propName] === undefined) {
        delete obj[propName];
      }
    }
    return obj
  }


}
