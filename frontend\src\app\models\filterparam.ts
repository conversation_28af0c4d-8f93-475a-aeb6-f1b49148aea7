export class FilterParam {
  relationTable: string;
  relationId: string;

  // Table Column for Filtering
  next: number;
  gumletId: string;
  offset: number;
  searchOrderBy: string;
  orderByColumn: string;
  searchText: string;
  isOnlyEnglishLanguage: boolean;
  userId: string;
  strUserId: string;
  status: string;
  farmId: string;
  MomentId: string;
  type: string | undefined;
  isPublish: boolean;
  StrFarmId: string;
  startDate: string;
  endDate: string;
  categorySearch: string;
  subCategorySearch: string;
  title: string;
  SearchCommonTitle: string;
  isRecurring: boolean;
  searchCommonTitle: string;
  strTrainingId: string;
  strCategoryId: string;
  strSubCategoryId: string;
  companyCode: string;
  contentType: string;
  accessibility: string;
  learningSeriesId: string;
  isNewsFeedPublic: boolean;
  trainingId: string;
  typeId: string;
  course: string;
  training: string;
  sortBy: boolean;
  coursePart: string;
  courseTitle: any;
  strProgramAdmin: string;
  learningSeries: string;
  author: string;
  programAdmin: string;
  intervalType: string;
  courseIds: string[];
  trainingIds: string[];
  isCompleted: boolean;
  farm: string;
  id: string;
  isSelf: boolean;
};
