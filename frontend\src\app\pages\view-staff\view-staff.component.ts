import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FilterParam } from 'src/app/models/filterparam';
import { AssignedUsersService } from 'src/app/services/assigned-users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { ToastService } from 'src/app/shared/toast.service';

@Component({
  selector: 'app-view-staff',
  templateUrl: './view-staff.component.html',
  styleUrls: ['./view-staff.component.scss'],
  providers: [FilterParam]
})
export class ViewStaffComponent implements OnInit {
  records: any;
  next: number = 10;
  offset: number = 1;
  loadMore: boolean = false;
  totalRecordsCount: any;
  totalPageNumber: number;
  constructor(private assignedUsersService: AssignedUsersService, private toastService: ToastService, private filterParam: FilterParam, private route: ActivatedRoute) { }

  ngOnInit(){
    this.filterParam.next = this.next;
    this.filterParam.offset = this.offset;
    this.filterParam.farmId = this.route.snapshot.paramMap.get('id');
    this.fetchRecords(false);
  }

  async fetchRecords(loadMore) {
    try {
      const response: RestResponse = await this.assignedUsersService.fetchAll(this.filterParam);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      if (loadMore) {
        this.loadMore = false;
        const newStaff = response.data;
        this.records = this.records.concat(newStaff);
      } else {
        this.records = response.data;
      }
      this.totalRecordsCount = response.data?.length ? response.data[0].totalCount : 0;
      this.totalPageNumber = Math.ceil(this.totalRecordsCount / this.filterParam.next);
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  onScrollingFinished() {
    if (this.records.length >= this.totalRecordsCount && this.filterParam.offset >= this.totalPageNumber) {
      return false;
    }
    this.filterParam.next = 10;
    this.filterParam.offset = this.filterParam.offset + 1;
    this.loadMore = true;
    this.fetchRecords(true);
  }

}
