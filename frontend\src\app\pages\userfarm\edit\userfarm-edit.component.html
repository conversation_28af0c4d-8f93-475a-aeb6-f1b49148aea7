<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserFarm Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/user-farm']">{{'UserFarm.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'UserFarm.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{userFarm.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #userfarmForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserFarm.userId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="users"
			                                       bindLabel="email"
			                                       bindValue="id"
			                                       name="userFarmUserId"
			                                       #userFarmUserId="ngModel"
					                            		[(ngModel)]="userFarm.userId" required="required" #UserId="ngModel"
			                                       [ngClass]="{'invalid-field':userFarmUserId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} users">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userFarmUserIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserFarm.farmId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="farms"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="userFarmFarmId"
			                                       #userFarmFarmId="ngModel"
					                            		[(ngModel)]="userFarm.farmId" required="required" #FarmId="ngModel"
			                                       [ngClass]="{'invalid-field':userFarmFarmId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} farm">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userFarmFarmIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(userfarmForm.form)"
	        *ngIf="authService.isAccessible('USERFARM','AddButton')" 
	        [disabled]="authService.isDisabled('USERFARM','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="userFarmUserIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-users [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-users>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="userFarmFarmIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-farm [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-farm>
		            </div>
		        </div>
		    </div>
		</div>

