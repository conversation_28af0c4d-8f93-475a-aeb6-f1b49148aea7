import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ManageCourseTrainingService extends BaseService {

  constructor(public http: HttpClient) {
    super(http, '/api/coursepart/training', '/api/coursepart/training');
  }

  // getTrainingLibrary(): Observable<RestResponse> {
  //   return this.getRecord('/api/traininglibrary/');
  // }

  getTrainingLibrary(data: any): Promise<RestResponse> {
    return this.getRecords('/api/course/leverage/trainings', data);
  }

  getLibrary(id: string): Observable<RestResponse> {
    return this.getRecord('/api/training/' + id);
  }

  getProgramAdminLibraryStatus(id: string): Observable<RestResponse> {
    return this.getRecord('/api/program-admin/training/' + id + '/status');
  }

  fetchPrerequisitesTrainings(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/trainings', filterParam);
  }


}

