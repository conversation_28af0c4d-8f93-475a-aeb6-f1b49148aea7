import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class FarmAdminEditTitle implements Resolve<string> {
  resolve(route: ActivatedRouteSnapshot): Observable<string> {
    const id = route.paramMap.get('id');
    if (id && id !== '0') {
      return of('Edit Farm');
    } else {
      return of('New Farm');
    }
  }
}
