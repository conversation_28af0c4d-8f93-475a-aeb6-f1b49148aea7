<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
	<div class="dashboard-content-container d-block text-center">
		<form #learningSeriesForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
			<div class="row">
				<div class="mt-2 mb-3">
					<h4 class="fw-bold">{{request.recordId == 0 ? "Add New Learning Series" : "Edit Learning Series"}}</h4>
					<p class="user-edit-msg">Please make sure you fill all the fields before you click on
						{{request.recordId ==
						0 ? 'save' : 'update'}} button
					</p>
				</div>
			</div>
			<div class="col-12 col-md-12 mb-4">
				<div class="form-floating mb-4 w-100">
					<input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
						class="form-control" type="text" name="title" #title="ngModel"
						[(ngModel)]="learningSeries.title" required="required"
						placeholder="{{'SubCategory.LEARNING_SERIES_TITLE' | translate}}">
					<label for="floatingInput">{{"SubCategory.LEARNING_SERIES_TITLE" | translate}}</label>
				</div>
			</div>
			<div class="col-12 col-md-12 mb-4">
				<div class="form-floating form-floating-textarea mb-4 w-100">
					<textarea maxlength="250" [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
						class="form-control form-description" name="description" #description="ngModel"
						[(ngModel)]="learningSeries.description" required="required" placeholder="Description"
						id="floatingTextarea2"></textarea>
					<label for="floatingInput">{{"SubCategory.description" | translate}}</label>
				</div>
			</div>
			<!-- <div class="form-floating">
				<div class="mb-4 form-control select-width ng-select-main-container"
					[ngClass]="{'is-invalid':learningSeries.learningSeriesContentTypeMappingDetail.length == 0 && onClickValidation}">
					<ng-multiselect-dropdown placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
						[settings]="dropdownSettingsForContentTypes" [data]="contentTypes"
						class="custom-multiselect form-control padding-bottom-8" name="contentTypes"
						[(ngModel)]="contentTypesSelected" #contentTypesIds="ngModel"
						(onSelect)="addContentType($event)" (onDeSelect)="removeContentType($event)">
					</ng-multiselect-dropdown>
				</div>
				<label for="language">{{"SubCategory.CONTENT_TYPES" | translate}}</label>
			</div> -->	
			<!-- <div class="col-12 mb-4 training-feed">
				<div>
					<label for="type" class="mb-2">{{"Training.Accessibility" | translate}}</label>
					<div class="form-check">
						<input (ngModelChange)="selectAccessibility(learningSeries.accessibility, $event)"
							[ngClass]="{'is-invalid':!accessibility.valid && onClickValidation}" required="required"
							[ngModel]="learningSeries.accessibility" #accessibility="ngModel" value="PUBLIC"
							class="form-check-input radio-button-cls" type="radio" name="accessibility" id="public">
						<label class="form-check-label" for="public">
							Public
						</label>
					</div>
					<div class="form-check">
						<input (ngModelChange)="selectAccessibility(learningSeries.accessibility, $event)"
							[ngClass]="{'is-invalid':!accessibility.valid && onClickValidation}" required="required"
							[ngModel]="learningSeries.accessibility" class="form-check-input radio-button-cls" type="radio"
							#accessibility="ngModel" name="accessibility" id="restricted" value="RESTRICTED">
						<label class="form-check-label" for="restricted">
							Restricted
						</label>
					</div>
				</div>
			</div> -->
			<!-- <div *ngIf="learningSeries.accessibility == 'RESTRICTED'" class="form-floating">
				<div class="mb-4 form-control select-width ng-select-main-container" [ngClass]="{'is-invalid':learningSeries.learningSeriesAssign.length == 0 && onClickValidation}">
					<ng-multiselect-dropdown placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
						[settings]="dropdownSettingsForUsers" [data]="users"
						class="custom-multiselect form-control padding-bottom-8" name="usersDropdown"
						[(ngModel)]="usersSelected" #userIds="ngModel" (onSelect)="addUser($event)"
						(onDeSelect)="removeUser($event)">
					</ng-multiselect-dropdown>
				</div>
				<label for="language">{{"Training.UsersList" | translate}}</label>
			</div> -->
			<div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
				<button (click)="save(learningSeriesForm.form)"
					class="btn btn-secondary site-button btn-sm large-button save-button rounded-3">{{request.recordId
					==
					0 ? 'SAVE' : 'UPDATE'}}</button>
			</div>
		</form>
	</div>
</div>