import { Component, OnDestroy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { UserAssignTrainingManager } from './userassigntraining.manager';
import { UserAssignTraining } from '../../models/userassigntraining';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import * as moment from 'moment';
import { RestResponse } from 'src/app/shared/auth.model';
import { UserAssignTrainingService } from './userassigntraining.service';
import AOS from 'aos';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { Constant } from 'src/app/config/constants';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-userassigntraining',
  templateUrl: './userassigntraining.component.html',
  styleUrls: ['./userassigntraining.component.scss']
})
export class UserAssignTrainingComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  trainingId: string = "";
  moment: any = moment;
  filterAssignedTrainingModal: any;
  readonly MY_CONSTANT = Constant;
  watchVideoModal: any;
  recordData: any;
  loadingVideo: boolean = false;
  fromDate: string;
  toDate: string;
  constructor(protected userAssignTrainingManager: UserAssignTrainingManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, protected route: ActivatedRoute, protected userAssignTrainingService: UserAssignTrainingService, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(userAssignTrainingManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<UserAssignTraining>();
    this.filterParam.strUserId = this.route.snapshot.paramMap.get('id');
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.filterAssignedTrainingModal = new bootstrap.Modal(
        document.getElementById('filterAssignedTrainingModal')
      );
      this.watchVideoModal = new bootstrap.Modal(
        document.getElementById('watchVideoModal')
      );
    }, 0)
  }

  onItemSelection(record: any) {
    this.onAssociatedValueSelected(record);
  }
  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  openFilterTrainingModal() {
    AOS.init({ disable: true });
    this.filterAssignedTrainingModal.show();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/user-assign-training/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/user-assign-training/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  onClickTrainingFilter(valid) {
    this.filterAssignedTrainingModal.hide()
    this.onCancel();
  }

  resetFilter() {
    delete this.filterParam.categorySearch;
    delete this.filterParam.subCategorySearch;
    delete this.filterParam.SearchCommonTitle;
    delete this.filterParam.status;
    this.fromDate = null;
    this.toDate = null;
    delete this.filterParam.startDate
    delete this.filterParam.endDate;
    this.filterAssignedTrainingModal.hide();
    this.onCancel();
  }

  assignedTrainingToUser(event: any, record: any) {
    if (event.currentTarget.checked) {
      this.trainingId = record.trainingId;
      $("#assignTrainingId" + record.trainingId).prop('checked', true);
      const data = {
        userId: this.route.snapshot.paramMap.get('id'),
        trainingId: record.trainingId,
        assignedDate: moment().format('LL'),
        isActive: true,
        isDeleted: false
      }
      const assignText = record.isApproved_count > 0 ? 'reassign' : 'assign';
      this.commonService.confirmation('Would you like to ' + assignText + ' this training to user?', this.assignedTrainingToUserCallback.bind(this), data, null, null, this.cancelAssignTrainingToUserCallback.bind(this));
    } else {
      $("#assignTrainingId" + record.trainingId).prop('checked', false);
    }
  }

  unassignTrainingToUser(event: any, record: any){
    if (!event.currentTarget.checked) {
      this.trainingId = record.trainingId;
      $("#assignTrainingId" + record.trainingId).prop('checked', false);
      const data = [
        record.id
      ]
      this.commonService.confirmation('Would you like to unassign this training to user?', this.unassignTrainingToUserCallback.bind(this), data, null, null, this.cancelUnassignTrainingToUserCallback.bind(this));
    }
  }

  async unassignTrainingToUserCallback(data: any){
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userAssignTrainingService.unassignTraining(data,'single');
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  cancelUnassignTrainingToUserCallback() {
    $("#assignTrainingId" + this.trainingId).prop('checked', true);
    this.trainingId = "";
  }

  approveTraining(record: any) {
    const data = {
      userId: this.route.snapshot.paramMap.get('id'),
      traningId: record.trainingId,
      userVideoUrl: record.videoUrl,
      isApproved: true,
      isActive: true,
      isDeleted: false
    }
    this.commonService.confirmation('Would you like to approve this training?', this.approveOrRejectTrainingCallback.bind(this), data, null, null, null);
  }

  async approveOrRejectTrainingCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userAssignTrainingService.saveRecord('/api/usertrainingstatus', data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  rejectTraining(record: any) {
    const data = {
      userId: this.route.snapshot.paramMap.get('id'),
      traningId: record.trainingId,
      userVideoUrl: record.videoUrl,
      isApproved: false,
      isActive: true,
      isDeleted: false
    }
    this.commonService.confirmation('Would you like to reject this training?', this.approveOrRejectTrainingCallback.bind(this), data, null, null, null);
  }
  async assignedTrainingToUserCallback(data: any) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.userAssignTrainingManager.save(data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  cancelAssignTrainingToUserCallback() {
    $("#assignTrainingId" + this.trainingId).prop('checked', false);
    this.trainingId = "";
  }

  removeSuccess() {
    this.onCancel();
  }

  ngDoCheck() {
    $("#watchVideoModal").on("hide.bs.modal", function () {
      var videoElement = document.getElementById('staff-video') as HTMLVideoElement;
      videoElement.pause();
      videoElement.removeAttribute('src'); // empty source
      videoElement.load();
    });

  }

  watchVideo(record: any) {
    this.recordData = record;
    this.loadingVideo = true
    this.watchVideoModal.show();
    AOS.init({ disable: true });
    this.loadVideoFromUrl.UrlToBlobUrl(record.videoUrl)
      .then(blobUrl => { // now it's loaded
        document.body.className = 'loaded';
        setTimeout(() => {
          let vid = document.getElementById('staff-video') as HTMLVideoElement;
          this.loadVideoFromUrl.setVideoUrl(vid, blobUrl)
          vid.addEventListener('canplaythrough', (event) => {
            this.loadingVideo = false;
          })
        }, 0);
      }).catch((err) => console.log(err));
  }

  fromDateOutput(event: any) {
    if (event) {
      this.fromDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.startDate = this.fromDate;
    } else {
      this.fromDate = null;
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.toDate = moment(event).format('YYYY-MM-DD');
      this.filterParam.endDate = this.toDate;
    } else {
      this.toDate = null;
      delete this.filterParam.endDate
    }
  }


  ngOnDestroy() {
    this.clean();
  }

  loadDetailPage(recordId) {
    this.selectedId = recordId;
    setTimeout(() => {
      $('#userAssignTrainingDetailPage').appendTo('body').modal('show');
      $('#userAssignTrainingDetailPage').on('hidden.bs.modal', () => {
        setTimeout(() => {
          this.selectedId = undefined;
        });
      });
    }, 500);
  }
}
