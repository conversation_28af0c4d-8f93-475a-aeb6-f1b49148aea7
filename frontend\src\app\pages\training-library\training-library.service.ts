import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { Observable } from 'rxjs';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
  providedIn: 'root'
})
export class ManageTrainingLibraryService extends BaseService {
  author: string;

  constructor(public http: HttpClient) {
    super(http, '/api/webTrainingLibraries', '/api/webTrainingLibraries');
  }

  getUserId(id: string) {
    this.author = id;
  }

  fetch(id: string): Observable<RestResponse> {
    return this.getRecord('/api/training' + '/' + id);
  }

  fetchLearningSeries(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/learningSeries/filter/data', filterParam);
  }

  fetchLearningSeriesAuthorBased(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/author/learningseries', filterParam);
  }

  getTrainingFilterData(filterParam: FilterParam): Promise<RestResponse> {
    return this.getRecords('/api/training/filter/data', null);
  }

}

