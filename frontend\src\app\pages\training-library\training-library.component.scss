.training-container {
  width: 100%;
  margin: 0 auto;
}

/* Tabs container with overflow handling */
.tabs-container {
  overflow-x: auto; /* Enables horizontal scrolling */
  white-space: nowrap; /* Prevents wrapping to the next line */
  padding: 10px 0;
  -webkit-overflow-scrolling: touch; /* Enables smooth scrolling for touch devices */
}

.tabs {
  display: inline-flex; /* Use inline-flex to keep all buttons in one line */
  gap: 10px; /* Gap between tabs */
  align-items: center; /* Align items in the center vertically */
}

.tab {
  background: #ffffff;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  border-radius: 8px;
  white-space: nowrap; /* Prevents the text from wrapping */
}

.tab.active {
  background: #000;
  color: #ffffff;
}

/* Cards Grid */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

@media (min-width: 1193px) {
  .cards-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}


.no-record-message {
  text-align: center;
  background-color: #f5f5f5; /* Light gray background */
  color: #000; /* Black text */
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Shadow effect */
  font-size: 18px;
  font-weight: bold;
}