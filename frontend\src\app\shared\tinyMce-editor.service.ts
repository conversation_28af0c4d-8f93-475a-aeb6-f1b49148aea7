import { Injectable } from '@angular/core';
import { finalize, last } from 'rxjs/operators';
import { ToastService } from 'src/app/shared/toast.service';
import {environment} from '../../environments/environment';
@Injectable({
  providedIn: 'root'
})
export class TinyMceEditorService {
  acceptType = ['image/png', 'image/jpg', 'image/jpeg'];
  uploader: any;
  constructor() {

  }

  getApiKey() {
    return "0sqtq4jzwc8anbpxtx3bnpntx9pn2qs6yuym8p6y5z0fz0m8"
  }

  settings() {
    return {
      menubar: true,
      plugins: [
        'advlist autolink lists link image charmap print preview anchor textcolor',
        'searchreplace visualblocks code fullscreen',
        'insertdatetime media table contextmenu paste code help wordcount link image'
      ],
      toolbar:
        'insert | undo redo |  formatselect | bold italic backcolor  | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help | link image',

      placeholder: 'Description *',
      content_style:
        `.mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
		  //color: rgba(34,47,62,.7);
		  color: red; }`,
      file_picker_types: 'image',
      image_advtab: false,
      image_description: false,
      image_dimensions: false,
      block_unsupported_drop: true,
      images_reuse_filename: true,
      paste_data_images: false,
      images_upload_handler: this.image_upload_handler
    };
  }

  image_upload_handler (blobInfo, success, failure, progress) {
    var xhr, formData;
  
    xhr = new XMLHttpRequest();
    xhr.withCredentials = false;
    xhr.open('POST', environment.BaseApiUrl + '/api/file/group/items/upload');
  
    xhr.upload.onprogress = function (e) {
      progress(e.loaded / e.total * 100);
    };
  
    xhr.onload = function() {
      var json;
  
      if (xhr.status === 403) {
        failure('HTTP Error: ' + xhr.status, { remove: true });
        return;
      }
  
      if (xhr.status < 200 || xhr.status >= 300) {
        failure('HTTP Error: ' + xhr.status);
        return;
      }
  
      json = JSON.parse(xhr.responseText);
      if (!json && json.length == 0 || typeof json[0].path != 'string') {
        failure('Something error occurred please try again later: ' + xhr.responseText);
        return;
      }
  
      success(json[0].path);
    };
  
    xhr.onerror = function () {
      failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
    };
  
    formData = new FormData();
    formData.append('file', blobInfo.blob(), blobInfo.filename());
  
    xhr.send(formData);
  };
}
