import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';

@Injectable({
    providedIn: 'root'
})
export class RejectedTrainingLogsService extends BaseService {
    trainingId: string;

    constructor(public http: HttpClient) {
        super(http, '/api/training', '/api/trainingrejectlogs');
    }

    getTrainingId(id: string) {
        this.trainingId = id;
    }

    fetchAll(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/trainingrejectlogs/' + this.trainingId, filterParam);
    }
}

