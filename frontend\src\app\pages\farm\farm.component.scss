.farm-site-cls {
    thead{
        th{
            background-color: #FBFBFB !important;
            border: unset !important
        }
    }
    tbody {
        tr {
            td {
                border-bottom: 1px solid #f3f1f1 !important;
                border-top: unset !important;
                border-right: unset !important;
                border-left: unset !important;
            }
            td:first-child{
                padding-left: 0px !important
            }
            td:last-child{
                padding-right: 0 !important;
            }
            
        }
    }
    .edit-button{
        color: black !important;
        background-color: white;
        font-size: 13px;
        padding: 7px 0px;
        max-width: 90px;
        border-radius: 10px;
        width: 100%;
        img{
            width: 15px;
        }
    }

    .delete-button{
        color: white !important;
        background-color: black;
        font-size: 13px;
        padding: 7px 0px;
        max-width: 90px;
        border-radius: 10px;
        width: 100%;
        img{
            width: 15px;
        }
    }
    .users-cls{
        background-color: #1681FF;
        padding: 7px 0px;
        border:none !important;
        border-radius: 10px;
        max-width: 40px;
        width: 100%;
        img{
            width: 20px;
        }
    }
    .admin-count-badge,
    .admin-count-badge:hover,
    .admin-count-badge:focus,
    .admin-count-badge:active {
      color: #fff !important;
      text-decoration: none;
    }
}

.farm-badge {
  font-size: 1.1rem;
  min-width: 2.2rem;
  display: inline-block;
  font-weight: 600;
  border-radius: 1rem;
  cursor: pointer;
  text-decoration: none;
}
