import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { ManageUserCourseDetail } from 'src/app/models/manageusercoursedetail';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminInvitedManager } from './program-admin-invited-user.manager';
import { ProgramAdminInvitedService } from './program-admin-invited-user.service';

@Component({
  selector: 'app-program-admin-invited-user',
  templateUrl: './program-admin-invited-user.component.html',
  styleUrls: ['./program-admin-invited-user.component.scss']
})
export class ProgramAdminInvitedUserComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  searchEnrolled: any;

  @Input() filterParam: FilterParam;

  constructor(protected programAdminInvitedManager: ProgramAdminInvitedManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil,
    private programAdminInvitedService: ProgramAdminInvitedService, protected route: ActivatedRoute) {
    super(programAdminInvitedManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<ManageUserCourseDetail>();
    this.fetchEnrolledCourseRecords();
    this.init();
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  ngOnDestroy() {
    this.clean();
  }

  removeSuccess() {
    this.onCancel();
  }

  async fetchEnrolledCourseRecords(filterParam?: FilterParam) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.programAdminInvitedService.getEnrolledgCourse(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.records = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  editRecord(id: any) {
    this.router.navigate(['/dashboard/program-admin/user/edit/' + id])
  }

  removeUserRecord(id: string) {
    this.commonService.confirmation('Would you like to delete?', this.DeleteUserCallback.bind(this), id, null, null, null);
  }

  async DeleteUserCallback(id: string) {
    try {
      // this.user.roles = null;
      const response: RestResponse = await this.programAdminInvitedService.removeUserAccess(id);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  async reloadData(id: string) :  Promise<RestResponse> {
    try {
      const response: RestResponse = await this.programAdminInvitedService.resendInviteData(id).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.toastService.success(response.message);
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

}
