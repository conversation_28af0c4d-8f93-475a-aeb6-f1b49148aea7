import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { UserAssignTrainingService } from './userassigntraining.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class UserAssignTrainingManager extends BaseManager {

    constructor(private userAssignTrainingService: UserAssignTrainingService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(userAssignTrainingService, loadingService, toastService);
    }
}
