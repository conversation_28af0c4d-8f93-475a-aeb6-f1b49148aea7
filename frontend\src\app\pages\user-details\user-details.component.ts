import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { Content } from 'src/app/models/content';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ProgramTrainingHistoryComponent } from '../program-training-history/program-training-history.component';
import { UserMyCourseComponent } from '../user-my-course/user-my-course.component';
import { LoadingService } from 'src/app/services/loading.service';
import { CourseUserDetailsService } from '../course-user-details/course-user-details.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { CommonService } from 'src/app/shared/common.service';
import { CourseUserDetailsManager } from '../course-user-details/course-user-details.manager';
import { ToastService } from 'src/app/shared/toast.service';
import AOS from 'aos';


declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-user-details',
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss']
})
export class UserDetailsComponent extends BaseListServerSideComponent implements OnInit {
  userDetails: boolean = true;
  momentsByUserId: boolean = true;
  trainingTabs: string = "training-assigned"
  userDetailsTabs: string = "profile";
  currentActiveTab: string;
  adminfilterCourseUserDetail: any;
  dropdownSettingsUsers = {};
  dropdownSettings = {};
  isTrainingCsvExport: boolean = false
  // filterCourseDetail: any;
  filterParam: FilterParam;
  courseFilterParam = new FilterParam;
  moment: any = moment;
  contentTypes: Content[];
  learningSeries: CourseLearningSeries[];
  trainingFilterData: RestResponse;
  selectedCourseId: any;
  publishCourseList: any[];
  filteredCourseStatus: any[] = [];
  courseFilterData: RestResponse;

  @ViewChild(ProgramTrainingHistoryComponent) programTrainingHistoryComponent: ProgramTrainingHistoryComponent;
  @ViewChild(UserMyCourseComponent) userMyCourseComponent: UserMyCourseComponent;

  constructor(private route: ActivatedRoute, public authService: AuthService, protected router: Router, private zone: NgZone, protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected courseUserDetailsService: CourseUserDetailsService, protected courseUserDetailsManager: CourseUserDetailsManager,
    protected commonService: CommonService, public commonUtil: CommonUtil) {
    super(courseUserDetailsManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.currentActiveTab = params.tab;
    });
    this.filterParam = new FilterParam();
    this.courseFilterParam = new FilterParam();
    this.setDropdownSettings();
    this.setDropdownSettingsUsers();
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseTrainings>();
    this.contentTypes = new Array<Content>();
    this.userDetailsTabs = this.currentActiveTab != null ? this.currentActiveTab : "profile";
    this.selectedCourseId = this.route.snapshot.paramMap.get('id');
    this.fetchAssociatedData();
    this.fetchCoursesForFilters();

  }

  updateStatus(event: Event): void {
    const selectedValue = (event.target as HTMLSelectElement).value;

    // Update `isCompleted` based on the selected value
    if (selectedValue === 'true') {
      this.courseFilterParam.isCompleted = true; // COMPLETED
    } else if (selectedValue === 'false') {
      this.courseFilterParam.isCompleted = false; // IN PROGRESS
    }
  }


  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  setDropdownSettingsUsers() {
    this.dropdownSettingsUsers = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  // filter common
  onClickCourseDetailFilter(valid) {
    this.adminfilterCourseUserDetail.hide();
    this.filterChildRecords();
  }

  resetFilter() {
    delete this.courseFilterParam.courseTitle;
    delete this.courseFilterParam.isCompleted;
    delete this.filterParam.courseTitle;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.status;
    this.adminfilterCourseUserDetail.hide();
    this.filterChildRecords();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('adminfilterCourseUserDetail');
      if (modalElement) {
        this.adminfilterCourseUserDetail = new bootstrap.Modal(modalElement);
      }
    }, 0);
  }

  openCourseDetailFilter() {
    this.fetchCoursesForFilters();
    AOS.init({ disable: true });
    this.adminfilterCourseUserDetail.show();
  }
  async fetchAssociatedData() {
    this.trainingFilterData = await this.courseUserDetailsService.getTrainingFilterData(null);
  }

  async fetchCoursesForFilters() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.courseUserDetailsService.getCourseFilter();
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  filterChildRecords() {
    switch (this.currentActiveTab) {
      case 'training':
        this.programTrainingHistoryComponent.refreshRecord();
        break;

      case 'course':
        this.userMyCourseComponent.refreshRecord();
        break;
    }
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    if (this.userDetailsTabs === "training") {
      this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    }
    // switch(this.currentActiveTab)
    this.filterChildRecords();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
      this.courseFilterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
      delete this.courseFilterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
      this.courseFilterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
      delete this.courseFilterParam.endDate
    }
  }

  onClickTrainingDetailsTab(name: string) {
    this.trainingTabs = name;
  }

  onClickUserDetailsTab(name: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          queryParams: { tab: name },
          queryParamsHandling: 'merge'
        });
    });
    this.userDetailsTabs = name;
  }

  openInviteCourseUserModal() {
    this.userDetailsTabs == 'course';
    this.userMyCourseComponent.openInviteCourseUserModal();
  }

}
