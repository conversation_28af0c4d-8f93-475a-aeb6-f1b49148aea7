<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">
    <div class="user-details-section">
        <div class="row">
            <div class="col-12 mb-4">
                <ul class="nav nav-pills">
                    <li class="nav-item bg-secondary user-details-btn width-180px"
                        [ngClass]="{'bg-secondary': userDetailsTabs == 'profile'}"
                        (click)="onClickUserDetailsTab('profile')">
                        <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'profile'}"
                            aria-current="page">Profile</a>
                    </li>
                    <li class="nav-item user-details-btn width-180px"
                        [ngClass]="{' bg-secondary': userDetailsTabs == 'training'}"
                        (click)="onClickUserDetailsTab('training')"
                        *ngIf="!authService.isSuperAdmin() && !authService.isProgramAdmin()">
                        <a class="btn nav-link"
                            [ngClass]="{' active bg-secondary': userDetailsTabs == 'training'}">Manage
                            Training
                        </a>
                    </li>
                    <li class="nav-item user-details-btn width-180px"
                        [ngClass]="{' bg-secondary': userDetailsTabs == 'course'}"
                        (click)="onClickUserDetailsTab('course')"
                        *ngIf="!authService.isSuperAdmin() && !authService.isProgramAdmin()">
                        <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'course'}">
                            Manage Course
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col-12 col-lg-4">
                <div class="custom-input-group" *ngIf="userDetailsTabs !== 'profile'">
                    <input class="form-control search-form-control" appDelayedInput (delayedInput)="search($event)"
                        [(ngModel)]="filterParam.searchText" [delayTime]="1000">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div class="col-12 col-lg-8">
                <div class="text-right">
                    <button *ngIf="userDetailsTabs !== 'profile'"
                        (click)="openProrgamDetailFilter()" type="button"
                        class="btn manage-filter-buttton bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
                        <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                            alt="">Filter
                    </button>
                    <button *ngIf="userDetailsTabs !== 'profile' && userDetailsTabs !== 'training'" (click)="openInviteCourseUserModal()" type="button"
                        class="btn add-button add-button-training bg-dark text-light btn-lg font-15px height-51px ms-3">
                        <img src="/assets/images/icons/menu/invite.svg" class="me-2 width-22px img-fluid" alt=""
                            style="vertical-align: sub; font-size: 12px;">Invite Now
                    </button>
                </div>
            </div>
        </div>
    </div>
    <app-program-user-edit *ngIf="userDetailsTabs == 'profile'" [userDetails]="userDetails">
    </app-program-user-edit>
    <!-- <app-user-login-profile *ngIf="userDetailsTabs == 'profile'" [userDetails]="userDetails">
    </app-user-login-profile> -->
    <app-manage-admin-training *ngIf="userDetailsTabs == 'training'" [filterParam]="filterParam">
    </app-manage-admin-training>
    <app-manage-admin-course *ngIf="userDetailsTabs == 'course'"
        [filterParam]="courseFilterParam"></app-manage-admin-course>
    <!--  -->
    <!-- Filter Training/Course Modal -->
    <div class="modal fade" id="adminFilterProgramDetail" tabindex="-1" aria-labelledby="adminFilterProgramDetailLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adminFilterProgramDetailLabel">
                        {{ userDetailsTabs === 'course' ? 'Filter Course' : (userDetailsTabs === 'training' ? 'Filter
                        Training' : '') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div *ngIf="adminFilterProgramDetail && adminFilterProgramDetail._isShown" class="modal-body">
                    <form #FilterForm="ngForm" novalidate="novalidate">
                        <div class="userMyCourseFilter" *ngIf="userDetailsTabs == 'course'">
                            <div class="form-floating mb-4 w-100">
                                <input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                                    class="form-control" type="text" name="title" #title="ngModel"
                                    [(ngModel)]="courseFilterParam.courseTitle" required="required"
                                    placeholder="{{'Course.title' | translate}}">
                                <label for="floatingInput">{{"Course.title" | translate}}</label>
                                <app-validation-message [field]="title" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                            <!-- <div class="form-floating">
                                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                    [ngClass]="{'is-invalid':!courseTrainingStatus.valid && onClickValidation}">
                                    <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                        name="courseTrainingStatus"
                                        [items]="trainingFilterData.data[0].trainingStatusDetail" bindLabel="status"
                                        bindValue="id" class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="courseFilterParam.status" #courseTrainingStatus="ngModel">
                                    </ng-select>
                                </div>
                                <label for="language">{{"Training.TrainingStatus" | translate}}</label>
                            </div> -->
                            <app-date-range-filter (fromDateOutput)="fromDateOutput($event)"
                                (toDateOutput)="toDateOutput($event)" [fromDateInput]="courseFilterParam.startDate"
                                [toDateInput]="courseFilterParam.endDate">
                            </app-date-range-filter>
                        </div>
                        <div class="trainingHistoryFilter" *ngIf="userDetailsTabs == 'training'">
                            <div class="form-floating">
                                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                    [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
                                    <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                        name="learningSeriesId"
                                        [items]="trainingFilterData.data[0].learningSeriesDetail" bindLabel="title"
                                        bindValue="id" class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="filterParam.learningSeries" #learningSeriesId="ngModel">
                                    </ng-select>
                                </div>
                                <label for="language">{{"Training.learningSeries" | translate}}</label>
                            </div>
                            <div class="form-floating">
                                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                    [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                                    <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                                        [items]="trainingFilterData.data[0].contentTypeDetail" bindLabel="title"
                                        bindValue="id" class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="filterParam.contentType" #contentTypeId="ngModel">
                                    </ng-select>
                                </div>
                                <label for="language">{{"Training.contentTypes" | translate}}</label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select form-control" name="status" aria-label="Please Select status"
                                    [(ngModel)]="filterParam.isPublish"
                                    [ngClass]="{'is-invalid':!status.valid && onClickValidation}" required="required"
                                    #status="ngModel">
                                    <option [ngValue]="undefined" selected disabled>Select Option</option>
                                    <option value="true">Publish</option>
                                    <option value="false">Draft</option>

                                </select>
                                <label for="type">{{"SubCategory.status" | translate}}</label>
                            </div>
                            <div class="form-floating">
                                <select class="form-select form-control" name="accessibility"
                                    aria-label="Please Select accessibility setup"
                                    [(ngModel)]="filterParam.accessibility"
                                    [ngClass]="{'is-invalid':!accessibility.valid && onClickValidation}"
                                    required="required" #accessibility="ngModel">
                                    <option [ngValue]="undefined" selected disabled>Select Option</option>
                                    <option value="PUBLIC">Public</option>
                                    <option value="PRIVATE">Private</option>
                                </select>
                                <label for="type">{{"SubCategory.ACCESSIBILITY_SETUP" | translate}}</label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button (click)="resetFilter()" type="button"
                                class="text-white btn btn-secondary me-2">Reset</button>
                            <button (click)="onClickCourseDetailFilter(FilterForm.form.valid)" type="button"
                                class="btn btn-primary">Filter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--  -->
</div>