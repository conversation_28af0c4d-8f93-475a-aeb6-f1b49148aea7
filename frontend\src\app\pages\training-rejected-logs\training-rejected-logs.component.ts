import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { FilterParam } from 'src/app/models/filterparam';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { ToastService } from 'src/app/shared/toast.service';
import { RejectedTrainingLogsManager } from './rejectedtraininglogs.manager';
import { RejectedTrainingLogs } from 'src/app/models/rejectedtraininglogs';
import AOS from 'aos';
import { RejectedTrainingLogsService } from './rejectedtraininglogs.service';
import * as moment from 'moment';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-training-rejected-logs',
  templateUrl: './training-rejected-logs.component.html',
  styleUrls: ['./training-rejected-logs.component.scss']
})
export class TrainingRejectedLogsComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  @Input() getRejectedLogsCountsData: any | undefined;
  moment: any = moment;
  restrictedUsersVideoOrImageModal: any;
  loadingVideo: boolean = false;
  mediaType: string;
  filterParam: FilterParam;
  rejectedTrainingLogsVideoModal: any;
  trainingId: any;
  constructor(protected rejectedTrainingLogsManager: RejectedTrainingLogsManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, private rejectedTrainingLogsService: RejectedTrainingLogsService) {
    super(rejectedTrainingLogsManager, commonService, toastService, loadingService, router);
  }
  ngOnDestroy(): void {

  }

  ngOnInit(): void {
    this.trainingId = this.getRejectedLogsCountsData.trainingId;
    this.records = new Array<RejectedTrainingLogs>();
    this.rejectedTrainingLogsService.getTrainingId(this.trainingId);
    this.init();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.rejectedTrainingLogsVideoModal = new bootstrap.Modal(
        document.getElementById('rejectedTrainingLogsVideoModal')
      );
    }, 0)
  }

  openImageOrVideo(record: any) {
    AOS.init({ disable: true });
    this.rejectedTrainingLogsVideoModal.show();
    this.loadingVideo = true
    setTimeout(() => {
      let vid = document.getElementById('staff-video') as HTMLVideoElement;
      this.loadVideoFromUrl.setVideoUrl(vid, record.userVideoUrl);
      this.loadingVideo = false;
    }, 0)
  }

}
