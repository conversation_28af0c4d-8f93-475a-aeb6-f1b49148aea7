import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { RestrictedUsersService } from './restricted-users.service';

@Injectable({
    providedIn: 'root'
})
export class RestrictedUsersManager extends BaseManager {

    constructor(protected restrictedUsersService: RestrictedUsersService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(restrictedUsersService, loadingService, toastService);
    }
}
