import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ProgramAdminActiveService } from './program-admin-active-user.service';

@Injectable({
    providedIn: 'root'
})
export class ProgramAdminActiveManager extends BaseManager {

    constructor(protected programAdminActiveService: ProgramAdminActiveService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(programAdminActiveService, loadingService, toastService);
    }
}
