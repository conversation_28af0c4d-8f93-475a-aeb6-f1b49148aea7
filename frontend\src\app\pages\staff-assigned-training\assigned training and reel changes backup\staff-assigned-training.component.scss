.staff-assigned-training {
  text-align: center;
  max-width: 350px !important;
  overflow: hidden;
  height: auto;

  .filter-buttton {
    width: 100%;
    border-radius: 10px !important;

    .moment-icon {
      width: 22px;
    }
  }

  .staff-training-buttons {
    width: 100%;
    border: 1px solid;
    border-radius: 10px !important;
    margin-top: 10px;
  }

  .staff-active-training-button {
    border: unset !important;
    color: white;
  }

  .staff-assigned-content {
    margin-top: 25px;
    background-color: #ffffff;
    padding: 20px 4px;
    max-width: 350px;
    text-align: start;

    .content-msg {
      font-size: 14px;
    }

    .f-s-10 {
      font-size: 10px;
    }

    .card {
      background-color: #061428;
      width: 100%;
      height: 170px;
      padding: 1rem;
      display: flex;
      justify-content: center;
      border-radius: 20px;

      span {
        text-align: right;
        margin-top: -6px;
        text-decoration: underline;
        cursor: pointer;
        font-size: 14px;
      }

      .card-content {
        color: #fff;
        font-size: 14px;
        text-align: start;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .video-icon {
        border-radius: 10px !important;
        padding: 6px 8px;
      }

      .watch-video-button {
        background-color: #71828a;
        color: #ffff;
        margin-top: 10px;
        padding: 12px 10px;
        width: 100%;
        border-radius: 10px !important;
      }
    }
  }
}

@media (max-width: 600px) {
  .videos-reel-cls {
    position: fixed;
    top: 0;
    max-width: 100% !important;
    bottom: 0;
    right: 0;
    left: 0;
    padding: 0 !important;
  }
}
