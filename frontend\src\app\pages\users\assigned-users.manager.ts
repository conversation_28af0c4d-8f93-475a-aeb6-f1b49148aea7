import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersService } from 'src/app/services/users.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';
import { AssignedUsersService } from 'src/app/services/assigned-users.service';

@Injectable({
    providedIn: 'root'
})
export class AssignedUsersManager extends BaseManager {

    constructor(protected assignedUsersService: AssignedUsersService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(assignedUsersService, loadingService, toastService);
    }
}
