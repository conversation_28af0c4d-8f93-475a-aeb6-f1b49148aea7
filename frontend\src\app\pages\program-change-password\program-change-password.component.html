<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
    <div class="dashboard-content-container">
        <div class="staff-changepassword container">
            <div class="staff-changepassword-form">
                <form #changePasswordForm="ngForm" novalidate="novalidate">
                    <div class="row">
                        <div class="col-12 col-md-6 mb-4">
                            <div class="staff-heading mt-4">
                                <h4 class="staff-heading-title">Change Password</h4>
                                <p class="staff-heading-sub-title">
                                    Please Make sure fill all the filed before click on save button
                                </p>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <div class="form-floating">
                                        <input type="password" name="oldpassword" class="form-control  rounded-3"
                                            id="floatingPassword" placeholder="Old Password" #oldPassword="ngModel"
                                            [ngClass]="{'is-invalid':!oldPassword.valid && onClickValidation}"
                                            [(ngModel)]="data.oldPassword" required="required" autofocus>
                                        <label for="floatingPassword">Old Password</label>
                                    </div>
                                    <app-validation-message [field]="oldPassword"
                                        [onClickValidation]="onClickValidation">
                                    </app-validation-message>
                                </div>
                                <div class="col-12 mb-4">
                                    <div class="form-floating">
                                        <div class="form-floating">
                                            <input type="password" name="newpassword" class="form-control rounded-3"
                                                id="floatingPassword1" placeholder="New Password" #newPassword="ngModel"
                                                [ngClass]="{'is-invalid': !newPassword.valid && onClickValidation}"
                                                [(ngModel)]="data.password" required="required"
                                                pattern="^(?=.*[!@#$%^&*])(?=.*[0-9])(?=.*[A-Z]).{8,}$" />
                                            <label for="floatingPassword1">New Password</label>
                                        </div>
                                        <app-validation-message [field]="newPassword"
                                            [onClickValidation]="onClickValidation">
                                        </app-validation-message>
                                    </div>
                                    <div *ngIf="newPassword.errors?.pattern && onClickValidation"
                                        class="text-danger mt-1">
                                        Password must include at least one special character, one number, one uppercase
                                        letter, and be at least 8 characters long.
                                    </div>
                                </div>
                                <div class="col-12 mb-4">
                                    <div class="form-floating">
                                        <div class="form-floating">
                                            <input type="password" name="confirmpassword" class="form-control rounded-3"
                                                id="floatingPassword2" placeholder="Confirm Password"
                                                #confirmPasswordRef="ngModel"
                                                [ngClass]="{'is-invalid':(!confirmPasswordRef.valid || newPassword.value != confirmPasswordRef.value) && onClickValidation}"
                                                [(ngModel)]="confirmPassword" required="required">
                                            <label for="floatingPassword2">Confirm Password</label>
                                        </div>
                                        <app-validation-message [field]="confirmPasswordRef"
                                            [comparableField]="newPassword" [onClickValidation]="onClickValidation">
                                        </app-validation-message>
                                    </div>
                                </div>
                                <div class="col-5">
                                    <button [disabled]="changePasswordButtonDisabled"
                                        (click)="changePassword(changePasswordForm.form.valid)"
                                        class="btn btn-md bg-dark change-password">Change Password</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-4">
                            <img src="/assets/images/icons\menu\Layer.svg" class="img-fluid change-password-logo ms-2"
                                alt="">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>