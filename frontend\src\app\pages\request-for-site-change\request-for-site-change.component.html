<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container">
  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-4 text-start">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="" appDelayedInput (delayedInput)="search($event)"
            [delayTime]="1000">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div class="col-12 col-sm-4">
      </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th width="160">{{'RequestSiteChangeList.fullName' | translate}}</th>
            <th width="">{{'RequestSiteChangeList.sitesRequest' | translate}}</th>
            <th width="">{{'RequestSiteChangeList.message' | translate}}</th>
            <th width="">{{'RequestSiteChangeList.status' | translate}}</th>
            <th width="170" class="text-center">{{'RequestSiteChangeList.action' | translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;">
            <td>{{record?.userIdDetail?.fullName}}</td>
            <td>
              <div *ngFor="let farm of record?.farms">
                <p class="mb-0">{{farm.name}}</p>
              </div>
            </td>
            <td>{{record?.message}}</td>
            <td>
              <div (click)="record?.status == 'PENDING' ? updateStatus(record) : null"
                class="text-center width-100-px status-button"
                [ngClass]="{'status-button-progress-color cursor-pointer': record?.status == 'PENDING', 'status-button-completed-color': record?.status == 'COMPLETED' }">
                {{record?.status}}</div>
            </td>
            <td class="text-center custom-action-button text-right">
              <div class="d-flex justify-content-end">
                <button (click)="openUserDetailsModal(record)" title="Update User Sites"
                  class="btn btn-outline-light action-button width-140-px">
                  UPDATE USER SITES
                </button>
                <button title="Delete" class="btn btn-primary action-button" (click)="remove(record.id)">
                  <i class="bi bi-trash"></i> DELETE
                </button>
              </div>
            </td>
          </tr>
          <!-- <tr *ngIf="records.length===0">
						<td class="text-center" colspan="5">
							{{"COMMON.NORECORDS" | translate}}
						</td>
					</tr> -->
        </tbody>
      </table>
      <div class="modal fade modal-xl" id="userDetailsModal" aria-hidden="true" aria-labelledby="userDetailsModal"
        tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="userDetailsModalLabel">{{recordData?.userIdDetail?.fullName}} User Details
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" *ngIf="userDetailsModal && userDetailsModal._isShown">
              <app-users-edit (userDetailsModalClose)="userDetailsModalClose($event)"
                [isUserDetailsModal]="isUserDetailsModal" [userId]="recordData?.userIdDetail?.id"
                [userDetails]="userDetails"></app-users-edit>
            </div>
            <div class="modal-footer">
              <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
