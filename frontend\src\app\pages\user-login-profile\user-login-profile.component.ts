import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FileLikeObject, FileUploader } from 'ng2-file-upload';
import { FilterParam } from 'src/app/models/filterparam';
import { Users } from 'src/app/models/users';
import { LoadingService } from 'src/app/services/loading.service';
import { UsersService } from 'src/app/services/users.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonUtil } from 'src/app/shared/common.util';
import { RouteDataService } from 'src/app/shared/title.service';
import { ToastService } from 'src/app/shared/toast.service';
import { environment } from 'src/environments/environment';
import { LayoutComponent } from '../layout/layout.component';
declare const $: any;

@Component({
  selector: 'app-user-login-profile',
  templateUrl: './user-login-profile.component.html',
  styleUrls: ['./user-login-profile.component.scss']
})
export class UserLoginProfileComponent implements OnInit {
  @Input() userDetails?: boolean;
  @Input() userId?: string;
  @Input() isUserDetailsModal?: boolean;
  @Output() userDetailsModalClose = new EventEmitter<boolean>();
  user: Users;
  onClickValidation: boolean;
  farms: any[];
  request: any;
  selectedFarms: any = [];
  userDeletedFarms: any = [];
  dropdownSettings = {};
  profileImageLoader: boolean = false;
  uploader: any;
  teams: Users[];
  filterParam: FilterParam;
  // roles : Array<any>;
  constructor(private route: ActivatedRoute, private usersService: UsersService, private toastService: ToastService,
    private loadingService: LoadingService, private router: Router, public commonUtil: CommonUtil, public routeDataService: RouteDataService, private layoutComponent: LayoutComponent,
    public authService: AuthService) {
    this.user = new Users();
    // this.user.roleName = '';
    this.request = {} as any;
    this.request.recordId = this.route.snapshot.paramMap.get('id');
    this.request.isNewRecord = true;
    this.request.onClickValidation = false;
    this.filterParam = new FilterParam();
    this.teams = new Array<Users>();
    // this.roles=[{name:'test',commonName:'commonTest'},{name:'test1',commonName:'commonTest2'}];
    // console.log(this.roles);

  }

  async ngOnInit() {
    this.loadingService.show();
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg', null, null, this.toastService)
    this.setDropdownSettings()
    // if (!this.authService.isSuperAdmin()) {
    //   await this.fetchFarms();
    // }
    // await this.fetchAssociatedData()
    if (this.request.recordId <= 0 && !this.userId) {
      this.loadingService.hide();
      return;
    }
    await this.fetchUserDetail();
    this.loadingService.hide();
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  async fetchUserDetail() {
    try {
      const response: RestResponse = await this.usersService.fetch(this.userId ? this.userId : this.request.recordId).toPromise();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.request.isNewRecord = false;
      const { userFarmMapping, ...restData } = response.data;
      this.user = { ...restData, userFarmMapping: [] };
      this.onFetchCompleted();
      // this.setSelectedFarmsUserEdit(userFarmMapping);
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  setDeletedItemsData(item) {
    this.userDeletedFarms = [
      ...this.userDeletedFarms,
      {
        id: item.userFarmId,
        farmId: item.id,
        isDeleted: true
      },
    ];
  }


  setItemsData(item) {
    this.user.userFarmMapping = [
      ...this.user.userFarmMapping,
      {
        farmId: item.id,
      },
    ];
  }

  async save(valid) {
    // if (this.userDeletedFarms && this.userDeletedFarms.length > 0) {
    //   let updatedFarmsData = this.user.userFarmMapping.concat(this.userDeletedFarms);
    //   this.user.userFarmMapping = updatedFarmsData;
    // }
    if (!valid) {
      this.request.onClickValidation = true;
      return;
    }
    this.loadingService.show();
    try {
      !this.userDetails ? this.request.isRequested = true : '';
      const method = this.request.isNewRecord ? 'save' : 'update';
      // this.user.roles = null;
      const response: RestResponse = await this.usersService[method](this.user);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        this.request.isRequested = false;
        return;
      }
      // this.userDetails && this.setEmptyUserFarmData();
      this.isUserDetailsModal && this.userDetailsModalClose.emit(true);
      this.toastService.success(response.message);
      !this.userDetails && await this.router.navigateByUrl('/dashboard/program-admin/users');
    } catch (e) {
      this.loadingService.hide();
      this.request.isRequested = false;
      this.toastService.error(e.message);
    }
  }

  telInputObject(event: any) {
    if (this.request.recordId == 0) {
      event.setCountry('sg');
      return
    }
    if (this.user.countryCode && this.user.phoneNumber) {
      event.setNumber('+' + this.user.countryCode + this.user.phoneNumber);
      return
    } else {
      event.setCountry('sg')
    }

  }

  onCountryChange(event) {
    this.user.countryCode = event.dialCode;
    this.user.countryCode = "+" + this.user.countryCode;
  }

  getNumber(event: any) {
  }

  hasError(event: any) {
  }

  uploadProfilePhoto(event: any) {
    if (event && event.target.files.length > 0) {
      this.profileImageLoader = true;
    }
  }

  fileValidationError(data: string, toastService: any) {
    this.profileImageLoader = false;
    toastService.error(data);
  }

  onUploadSuccess(data: any) {
    this.user.profileImageUrl = data.path;
    this.profileImageLoader = false;
  }

  isNullOrUndefined(value) {
    return value === undefined || value === null;
  }

  initializeUploader(files, allowedExtensions: string, maxFileSize: number, aspectRatio: number, toastService: ToastService) {
    const uploaderOptions = {
      url: environment.BaseApiUrl + '/api/file/group/items/upload',
      autoUpload: true,
      maxFileSize: maxFileSize * 1024,
      filters: []
    };
    if (allowedExtensions !== '') {
      uploaderOptions.filters.push({
        name: 'extension',
        fn: (item: any): boolean => {
          const fileExtension = item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase();
          return allowedExtensions.indexOf(fileExtension) !== -1;
        }
      });
    }
    const uploader = new FileUploader(uploaderOptions);
    uploader.onAfterAddingFile = (item => {
      item.withCredentials = false;
    });

    uploader.onWhenAddingFileFailed = (item: FileLikeObject, filter: any, options: any) => {
      switch (filter.name) {
        case 'fileSize':
          setTimeout(() => {
            this.fileValidationError("Image size to too large", this.toastService);
          }, 200);

          break;
        case 'extension':
          setTimeout(() => {
            this.fileValidationError("only jpg,png,jpeg files are allowed", this.toastService);
          }, 200);
          break;
        default:
          toastService.error('Unknown error');
      }
    };

    uploader.onSuccessItem = (fileItem, response) => {
      const uploadResponse = JSON.parse(response);
      if (uploadResponse.length > 0) {
        const image = uploadResponse[0];
        image.isDeleted = false;
        if (this.isNullOrUndefined(files)) {
          files = [] as any[];
        }
        files.push(image);
        setTimeout(() => {
          this.onUploadSuccess(image);
        }, 200);
      }
    };

    return uploader;
  }

  // onFetchCompleted() {
  //   this.routeDataService.setData(this.router.url, this.user.fullName);
  // }

  onFetchCompleted() {
    // this.layoutComponent.breadcrumbs.forEach((data, index) => {
    //   if (index == this.layoutComponent.breadcrumbs.length - 1) {
    //     data.title = this.user.fullName;
    //     data.active = false
    //   }
    // })
    // this.layoutComponent.breadcrumbs = this.layoutComponent.breadcrumbs
    this.routeDataService.setData(this.router.url, this.user.fullName);
  }

}
