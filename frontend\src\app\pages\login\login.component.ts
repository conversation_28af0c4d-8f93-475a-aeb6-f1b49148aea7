import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LocalStorageService } from 'angular-2-local-storage';
import { Login } from '../../models/login';
import { LoadingService } from '../../services/loading.service';
import { LoginService } from '../../services/login.service';
import { RestResponse } from '../../shared/auth.model';
import { ToastService } from '../../shared/toast.service';
import { Title } from '@angular/platform-browser';

declare const $: any;

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  onClickValidation: boolean;
  data: Login;
  passwordFieldType: string;
  loginInProcess: boolean = false;

  constructor(private toastService: ToastService, private loginService: LoginService, public router: Router,
    private localStorageService: LocalStorageService, private loadingService: LoadingService, private titleService: Title) {
    this.titleService.setTitle("Login to Integrax");
  }

  ngOnInit() {
    this.passwordFieldType = "password";
    this.onClickValidation = false;
    this.data = new Login();
    this.localStorageService.remove('userName');
  }

  async login(form) {
    if (form.valid == false) {
      this.onClickValidation = true;
      return false;
    }
    if (!this.data.isValidLoginRequest(form)) {
      return;
    }

    this.loginInProcess = true;
    try {
      this.loadingService.show();
      const data: RestResponse = await this.loginService.login(this.data);
      this.loadingService.hide();
      if (!data.status) {
        if (data.data?.uniqueCode) {
          this.router.navigate(['verify-otp'], { queryParams: { uc: data.data.uniqueCode } });
        } else {
          this.loginInProcess = false;
          this.toastService.error(data.message);
        }
        return;
      }
      const response = data.data;
      response.token.expires_at = new Date(response.token.expires).getTime();
      this.localStorageService.set('token', response.token);
      this.localStorageService.set('user', response.user);
      // Store isFirstLogin in localStorage for farm admin
      setTimeout(() => {
        if (response.user.roles.includes('ROLE_ADMIN') || response.user.roles.includes('ROLE_SUPER_ADMIN') || response.user.roles.includes('ROLE_PROGRAM_ADMINISTRATOR') || response.user.roles.includes('ROLE_FARM_ADMIN')) {
          if (response.user.roles.includes('ROLE_PROGRAM_ADMINISTRATOR')) {
            this.router.navigate(['dashboard/program-admin']);
          } else if (response.user.roles.includes('ROLE_FARM_ADMIN')) {
            this.router.navigate(['dashboard/users']);
          } else {
            this.router.navigate(['dashboard']);
          }
        } else {
          this.router.navigate(['staff-reel-trainings']);
        }
      }, 0);
    } catch (e) {
      this.loginInProcess = false;
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  async resetPassword(form) {
    if (form.valid == false) {
      this.onClickValidation = true;
      return false;
    }
    if (!this.data.isValidForgotPasswordRequest(form)) {
      return;
    }
    try {
      this.loadingService.show();
      delete this.data.password;
      const response: RestResponse = await this.loginService.resetPassword(this.data);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.data.userName = undefined;
      this.toastService.success(response.message);
      this.data.email = null;
    } catch (e) {
      this.loadingService.hide();
      this.toastService.error(e.message);
    }
  }

  gotoForgotPassword() {
    this.router.navigate(['/forgot-password']);
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }
  
}
