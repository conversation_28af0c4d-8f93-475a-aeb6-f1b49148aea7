import { Component, OnInit } from '@angular/core';
import { Training } from '../../../models/training';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { NewsFeedManager } from '../news-feed.manager';
import { event } from 'jquery';

@Component({
  selector: 'app-news-feed-detail',
  templateUrl: './news-feed-detail.component.html',
  styleUrls: ['./news-feed-detail.component.scss']
})
export class NewsFeedDetailComponent extends BaseEditComponent implements OnInit {
  public newsfeed: any;
  public videoPlaying: boolean = false;
  public loadingVideo: boolean = false;
  constructor(protected route: ActivatedRoute, protected newsFeedManager: NewsFeedManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
    protected translateService: TranslateService, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(newsFeedManager, commonService, toastService, loadingService, route, router, translateService);

  }

  ngOnInit() {
    this.init();
  }

  // playVideoFromPlayIcon() {
  //   var videoId = document.getElementById("video_id") as HTMLVideoElement | null;
  //   if (videoId != null) {
  //     if (videoId.paused) {
  //       videoId.play();
  //       videoId.controls = true
  //       this.videoPlaying = true;
  //     }
  //   }
  //   videoId.addEventListener("ended", (event) => {
  //     videoId.controls = false;
  //     this.videoPlaying = false;
  //   });
  // }

  playVideoFromPlayIcon() {
    var videoId = document.querySelector<HTMLVideoElement>("#video_id");
    if (videoId) {
      const thisUrl = this.router.url;
      videoId.addEventListener('leavepictureinpicture', () => {
        const currentUrl = this.router.url;
        if (currentUrl !== thisUrl) {
          this.router.navigate([thisUrl]);
        }
      });
      if (videoId.paused) {
        videoId.controls = true;
        this.videoPlaying = true;
      }
    }
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;
    })
  }


  async fetchExistingRecord() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.newsfeed = response.data;

    } catch (error) {
      this.toastService.error(error.message);
    }
  }


}
