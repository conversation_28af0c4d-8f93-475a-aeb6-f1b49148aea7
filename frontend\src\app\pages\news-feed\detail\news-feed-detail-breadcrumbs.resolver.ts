import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NewsFeedDetailBreadcrumbs implements Resolve<any> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    const newsfeed: string | null = route.paramMap.get("id");

    return of([
      {
        title: "Dashboard", link: "/dashboard", active: false
      },
      {
        title: "Manage News Feed", link: "/dashboard/news-feed", active: false
      },
      {
        title: "News Feed Detail", link: "/dashboard/news-feed/detail/" + newsfeed, active: true
      }
    ])
  }



}
