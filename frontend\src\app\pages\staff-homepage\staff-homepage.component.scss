.staff-homepage {
  .site-staff-logo {
    width: 170px;
  }

  .navbar {
    width: 90%;
    margin-left: 5%;
    text-align: start;

    .nav-link {
      color: #000000;
    }
  }

  .staff-login-button {
    border-radius: 10px !important;
    height: 60px;
    width: 150px;
    border: 0;
  }

  .staff-homepage-m {
    background-image: url(/assets/images/home-banner.png);
    background-repeat: no-repeat;
    width: 90%;
    margin-left: 5%;
    background-size: 100% 100%;
    border-radius: 25px;
    padding: 8rem 3rem;

    .content-heading {
      margin-top: 0;
      font-size: 10vw;
    }

    .content-sub-heading {
      font-size: 4vw;
    }

    .get-start-btn {
      border-radius: 10px !important;
      height: 60px;
      max-width: 200px;
      width: 100%;
      font-weight: 600;
      color: #000000;
      border: 0;
    }
  }

  .staff-about-us {
    padding-top: 50px;
    text-align: center;

    .about-heading {
      font-size: 16px;
      line-height: 20px;
    }

    .about-sub-heading {
      font-size: 28px;
    }

    .about-us-content {
      font-size: 14px;
    }

    .about-us-btn {
      width: 100%;
      border: 0;
      max-width: 200px;
      height: 65px;
      border-radius: 10px !important;
    }
  }

  .what-we-do-section {
    margin-top: 50px;
    background-color: #f5f5f5;
    padding-bottom: 40px;

    .what-we-do-heading {
      padding-top: 30px;
    }

    .what-we-do {
      font-size: 14px;
      line-height: 25px;
      font-weight: 600;
    }

    .what-we-do-msg {
      font-size: 26px;
      font-weight: 600;
    }

    .what-we-do-sub-msg {
      font-size: 20px;
    }

    .card {
      background-color: #fff;
      padding: 3rem;
      min-height: 350px;
      border: 0;
      border-radius: 25px;
      text-align: start;
      margin: 10px 20px;

      .card-logo img {
        background-color: #157cf4;
        border-radius: 15px;
        padding: 15px;
        width: 70px;
        height: 70px;
      }

      .card-content {
        text-align: left;
        margin-top: 15px;
        line-height: 26px;
        font-weight: 500;
      }

      .content-heading {
        font-size: 18px;
      }

      .content-heading-msg {
        font-size: 16px;
      }
    }
  }

  .staff-homepage-footer {
    margin-top: 80px;

    .footer-menu {
      .menu {
        flex-direction: column;

        li {
          margin: 0 0 20px 0;
        }
      }
    }

    .margin-top-40 {
      margin-top: 60px !important;
      margin-bottom: 40px;
    }

    .footer-copyright {
      .copyright-content {
        font-size: 26px;
      }

      .copyright-sub-content {
        font-size: 17px;
        margin-top: 10px;
      }

      .intergrax-sub-content {
        font-size: 17px;
        text-align: center;
        margin-top: 10px;
      }
    }

    .footer-icon {
      .icons {
        font-size: 40px;
      }

      .icons:hover {
        color: #157cf4 !important;
      }
    }
  }
}

.staff-homepage {
  position: relative;

  &.container-xxl {
    max-width: 1920px !important;
    padding-left: 0;
    padding-right: 0;
  }

  .contact-us-form {
    .contact-us {
      #floatingTextarea2 {
        height: 150px !important;
      }
    }
  }
}

.re-captcha.is-invalid>div {
  border: 1px solid #dc3545 !important;
  border-radius: 0.2rem;
}

@media (min-width: 1921px) {
  .staff-homepage {
    padding-top: 55px;

    .header {
      padding: 0 !important;
    }
  }

  .fixed-menu {
    .navbar {
      padding-left: 110px;
      padding-right: 110px;
    }
  }
}

.fixed-menu {
  .navbar {
    &.container-xxl {
      max-width: 1920px;
    }
  }
}

@media (min-width: 2000px) {
  .staff-homepage-nav {
    min-height: unset !important;
    height: 800px !important;
  }
}

// Small devices (landscape phones, 576px and up)
@media (min-width: 576px) {}

// Medium devices (tablets, 768px and up)
@media (min-width: 768px) {
  .staff-homepage {
    .staff-about-us {
      .about-heading {
        font-size: 24px;
      }

      .about-sub-heading {
        font-size: 38px;
        margin-top: 30px;
      }

      .about-us-content {
        margin-top: 45px;
      }
    }

    .what-we-do-section {
      .card {
        margin: 20px 0 0 0 !important;
      }
    }
  }

  .contact-us-form {
    padding: 5rem;
  }
}

@media (max-width: 992px) {
  .fixed-menu {
    display: block !important;
    background: #fff;
    position: fixed;
    z-index: 99999;
    overflow: hidden;
    box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.16);

    .nav-link {
      color: #000000 !important;
    }

    .site-staff-logo {
      width: 160px;
    }
  }

  .staff-homepage {
    margin-top: 95px !important;
    float: left;

    .image-menus {
      display: none !important;
    }
  }
}

// Large devices (desktops, 992px and up)
@media (min-width: 992px) {
  .fixed-menu {
    .nav-link {
      color: white !important;
    }
  }

  .staff-homepage {
    text-align: auto;

    .navbar {
      text-align: start;
      position: absolute;
      width: 100%;
      padding: 6em;
      z-index: 99;
      margin-left: 0;

      .navbar-nav {
        line-height: 44px;

        .nav-link {
          color: #ffffff;
        }

        .nav-link:hover {
          text-decoration: underline !important;
          text-underline-offset: 12px;
          text-decoration-color: #167df7 !important;
        }
      }
    }

    .header {
      padding: 3rem 3rem 0 3rem;

      .staff-homepage-nav {
        background-image: url(/assets/images/assign-farm.png);
        background-repeat: no-repeat;
        width: 100%;
        background-size: 100% 100%;
        padding: 8rem;
        min-height: 65vh;
        border-radius: 25px;

        .staff-login-button {
          border-radius: 10px !important;
          height: 60px;
          width: 150px;
          border: 0;
        }

        .staff-homepage-content {
          text-align: center;
          margin-top: 100px;

          .content-heading {
            margin-top: 140px;
            font-size: 78px;
          }

          .get-start-btn {
            border-radius: 10px !important;
            height: 60px;
            max-width: 200px;
            width: 100%;
            font-weight: 600;
            color: #000000;
            border: 0;
          }
        }
      }
    }

    .staff-about-us {
      padding-top: 120px;
      margin-bottom: 100px !important;
      text-align: start;

      .line {
        border-left: 6px solid #000000;
        height: 200px;
      }

      .about-heading {
        font-size: 26px;
        line-height: 20px;
      }

      .about-sub-heading {
        font-size: 50px;
      }

      .about-us-content {
        font-size: 18px;
        line-height: 40px;
      }

      .about-us-btn {
        margin-top: 30px;
        width: 100%;
        border: 0;
        max-width: 200px;
        height: 65px;
        border-radius: 10px !important;
      }
    }

    .what-we-do-section {
      margin-top: 80px;
      background-color: #f5f5f5;
      padding: 5rem;
      padding-bottom: 100px;

      .what-we-do-heading {
        margin-top: 30px;
        margin-bottom: 80px;
      }

      .what-we-do {
        font-size: 26px;
        line-height: 25px;
        font-weight: 600;
      }

      .what-we-do-msg {
        font-size: 40px;
        font-weight: 600;
      }

      .card {
        background-color: #fff;
        padding: 3rem;
        min-height: 378px;
        border: 0;
        border-radius: 25px;

        .card-logo img {
          background-color: #157cf4;
          border-radius: 25px;
          padding: 20px;
          width: 88px;
          height: 88px;
        }

        .card-content {
          text-align: left;
          margin-top: 24px;
          font-size: 19px;
        }
      }
    }

    .staff-homepage-footer {
      margin-top: 80px;

      .footer-menu {
        .menu {
          flex-direction: row;
          display: flex;
          justify-content: center;
        }
      }

      .margin-top-40 {
        margin-top: 60px !important;
        margin-bottom: 40px;
      }

      .footer-copyright {
        padding: 0 100px;

        .copyright-content {
          text-align: start;
        }

        .copyright-sub-content {
          text-align: end;
        }

        .intergrax-sub-content {
          font-size: 17px;
          text-align: center;
        }
      }
    }
  }

  .fix-nav-top {
    padding: 0 6rem !important;
    background: rgb(22, 129, 255, 0.7);
    position: fixed !important;
    z-index: 9999;
  }
}

// .adjust-div {
//     display: flex;
//     flex: 1
// }

// X-Large devices (large desktops, 1200px and up)
@media (min-width: 1200px) {}

// XX-Large devices (larger desktops, 1400px and up)
@media (min-width: 1400px) {
  .nav-link {
    font-size: 30px !important;
  }

  .staff-homepage-nav {
    .staff-homepage-content {
      text-align: center;
      margin-top: 100px;

      .content-heading {
        margin-top: 140px;
        font-size: 78px;
      }
    }
  }
}

.staff-login-button-fixed {
  border-radius: 10px !important;
  height: 60px;
  width: 150px;
  border: 0;
  margin: 0.6rem 1rem 0 1rem !important;
}

@media (max-width: 991px) {
  .staff-login-button-fixed {
    margin-left: 0px !important;
  }
}

// .form-floating {
//   position: relative;
// }

// .form-floating:before {
//   content: '';
//   position: absolute;
//   top: 1px;
//   /* border-width (default by BS) */
//   left: 1px;
//   /* border-width (default by BS) */
//   width: calc(100% - 14px);
//   /* to show scrollbar */
//   height: 32px;
//   border-radius: 4px;
//   /* (default by BS) */
//   background-color: #ffffff;
// }

// .form-floating textarea.form-control {
//   padding-top: 32px;
//   /* height of pseudo element */
//   min-height: 80px;
//   /* not relevant */
// }

@media (min-width: 1600px) {
  .copyright-sub-content {
    font-size: 19px !important;
  }

  .intergrax-sub-content {
    font-size: 19px !important;
  }
}

// 

.device-container {
  // background-color: #f5f7fb;
  padding: 60px 80px;
}

.device-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 50px;
  /* Space between buttons */
}

.download-app-header .h1,
p {
  margin-bottom: 15px;
}

.download-app-header h1 {
  font-size: 62px;
}

.download-button {
  display: flex;
  align-items: center;
  padding: 10px 30px;
  border-radius: 30px;
  /* Rounded corners */
  text-decoration: none;
  color: #000;
  background-color: transparent;
  transition: box-shadow 0.3s ease, transform 0.6s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* Shadow effect on hover */
  transform-style: preserve-3d;
  perspective: 1000px;
}

.download-button:hover {
  background-color: #1681ff;
  color: #fff;
  transform: scale(1.05);
  animation: flip-vertical 1s forwards;
}

.download-button .icon {
  width: 48px;
  /* Icon size */
  height: 48px;
  margin-right: 10px;
  transition: fill 0.3s ease;
}

.download-button:hover .icon {
  filter: invert(1);
  /* Invert colors to change to white */
}

.download-button div {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.download-button span {
  font-size: 12px;
  margin-bottom: 2px;
}

.download-button strong {
  font-size: 16px;
}

/* Adding the 3D flip animation */
@keyframes flip-vertical {
  0%,100% {
    transform: perspective(1000px) rotateX(0deg);
  }

  50% {
    transform: perspective(1000px) rotateX(180deg);
  }
}