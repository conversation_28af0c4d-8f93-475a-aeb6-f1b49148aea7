<div class="site-customer-main-container px-0 pt-0" data-aos="fade-up" data-aos-duration="1000">
    <div [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table allocated-users-list"
            [ngClass]="{'has-records': records.length > 0 }">
            <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th width="80"></th>
                        <th width="110" class="text-nowrap">{{'Course.fullName' | translate}}</th>
                        <th width="140" class="text-nowrap">{{'Course.email' | translate}}</th>
                        <th width="120" class="text-nowrap">{{'Course.phoneNumber' | translate}}</th>
                        <!-- <th width="80" *ngIf="!authService.isProgramAdmin()" class="text-nowrap">
                            {{'Course.programAdminName' |translate}}
                        </th> -->
                        <th width="80" class="text-nowrap">
                            {{'Course.action' |translate}}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                        <td style="text-align: center;">
                            <img src="/assets/images/icons/menu/user-icon.svg" height="38" width="38" alt="">
                        </td>
                        <td class="pe-0">
                            <h5 class="code ms-2" title="View User Profile"
                                style="font-size:24px; font-weight: 600; white-space: nowrap;"
                                [routerLink]="['/dashboard/program-admin/user-details/' +record.user]"
                              >
                                {{record?.usersDetail.fullName}}
                            </h5>
                        </td>
                        <td>
                            {{record?.usersDetail.email}}
                        </td>
                        <td>
                            {{record?.usersDetail.phoneNumber}}
                        </td>
                        <!-- <td *ngIf="!authService.isProgramAdmin()">
                            {{record?.usersDetail?.programAdminName}}
                        </td> -->
                        <td>
                            <div class="custom-action-button text-right d-sm-flex">
                                <img src="/assets/images/icons/menu/Profile.svg" title="Profile"
                                    class="width-25-px me-2 cursor-pointer"
                                    [routerLink]="['/dashboard/program-admin/user-details/'+record.user]" />
                                <img src="/assets/images/icons/menu/manage-traning-icon.svg" title="My Training"
                                    class="width-25-px me-2 cursor-pointer"
                                    [routerLink]="['/dashboard/program-admin/user-details/'+ record.user]"
                                    [queryParams]="{ tab: 'training', 'username': record?.usersDetail.fullName }"
                                    queryParamsHandling="merge" />
                                <img src="/assets/images/icons/menu/course-ims.svg" title="My Courses"
                                    [routerLink]="['/dashboard/program-admin/user-details/' +record.user]"
                                    class="width-25-px me-2 cursor-pointer"
                                    [queryParams]="{ tab: 'course', 'username': record?.usersDetail.fullName }"
                                    queryParamsHandling="merge" />
                                <img src="/assets/images/icons/menu/course-revoke.svg" title="Revoke All Course Access"
                                    class="width-25-px me-2 cursor-pointer" (click)="revokeCourse(record.user)"
                                    *ngIf="authService.isAccessible('MANAGE_USER','DeleteButton') && !isPlusButton"
                                    [class.disabled]="authService.isDisabled('MANAGE_USER','DeleteButton')" />
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</div>
