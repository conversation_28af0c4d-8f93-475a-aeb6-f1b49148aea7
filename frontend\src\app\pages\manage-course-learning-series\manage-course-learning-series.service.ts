import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';

@Injectable({
    providedIn: 'root'
})
export class ManageCourseLearningSeriesService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/learningserie', '/api/learningseries');
    }

}

