import { Component, OnInit } from '@angular/core';
import { Training } from '../../../models/training';
import { ActivatedRoute, Router } from '@angular/router';
import { TrainingManager } from '../training.manager';
import { ToastService } from '../../../shared/toast.service';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { AuthService } from '../../../shared/auth.services';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtil } from 'src/app/shared/common.util';
import { Language } from 'src/app/models/language';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { event } from 'jquery';
import { RouteDataService } from 'src/app/shared/title.service';

@Component({
  selector: 'app-training-detail',
  templateUrl: './training-detail.component.html',
  styleUrls: ['./training-detail.component.scss']
})
export class TrainingDetailComponent extends BaseEditComponent implements OnInit {
  public training: any;
  public videoPlaying: boolean = false;
  public loadingVideo: boolean = false;
  constructor(protected route: ActivatedRoute, protected trainingManager: TrainingManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
    protected translateService: TranslateService, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl, public routeDataService: RouteDataService) {
    super(trainingManager, commonService, toastService, loadingService, route, router, translateService);

  }

  ngOnInit() {
    this.init();
  }

  // playVideoFromPlayIcon() {
  //   var videoId = document.getElementById("training_video") as HTMLVideoElement | null;
  //   if (videoId != null) {
  //     if (videoId.paused) {
  //       videoId.play();
  //       videoId.controls = true
  //       this.videoPlaying = true;
  //     }
  //   }
  //   videoId.addEventListener("ended", (event) => {
  //     videoId.controls = false;
  //     this.videoPlaying = false;
  //   });
  // }

  playVideoFromPlayIcon() {
    var videoId = document.querySelector<HTMLVideoElement>("#training_video");
    if (videoId) {
      const thisUrl = this.router.url;
      videoId.addEventListener('leavepictureinpicture', () => {
        const currentUrl = this.router.url;
        if (currentUrl !== thisUrl) {
          this.router.navigate([thisUrl]);
        }
      });
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
        this.videoPlaying = true;
      }
    }
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;
    });
  }

  async fetchExistingRecord() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.training = response.data;
      // const fullUrl = this.router.url; // Includes the entire URL
      // console.log(fullUrl);
      // this.routeDataService.addBreadcrumb(fullUrl,this.training.title)
      this.onFetchCompleted();
      this.setBreadcrumbs()

    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  onFetchCompleted() {
    this.routeDataService.setData(this.router.url, this.training.title);
  }

  setBreadcrumbs() {
    //set the breadcrumb
    const fullUrl = this.router.url; // Current full URL
    const queryIndex = fullUrl.indexOf('?'); // Find query parameter start
    const cleanUrl = queryIndex > -1 ? fullUrl.slice(0, queryIndex) : fullUrl;
    this.routeDataService.addBreadcrumb(this.training.title ?? this.training.trainingLibraryDetail.title, cleanUrl,this.training.id ?? this.training.trainingLibraryDetail.id)
  }

}
