.verify-otp {
    .verify-otp-logo {
        margin-top: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        // padding-left: 15px;

        img {
            width: 166px;
        }
    }

    .verify-otp-header {
        padding: 3rem;

        .verify-otp-heading {
            font-size: 26px;
            font-weight: 600;
        }

        .verify-otp-msg {
            font-size: 12px;
            padding-top: 22px;
            line-height: 25px;
        }

        .verify-button {
            border: 0;
            width: 110px;
            margin-left: 5px;
            padding: 20px 30px;
            color: #ffff;
            max-height: 70px;
        }

        .margin-top-30 {
            margin-top: 30px;
        }
    }
}

@media (min-width: 576px) {}

// Medium devices (tablets, 768px and up)
@media (min-width: 768px) {
    .verify-otp {
        margin: 20px 80px;

        .verify-otp-logo {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-left: 15px;

            img {
                width: 260px;
            }
        }

        .verify-otp-header {
            padding: 1rem 4rem;

            .verify-otp-heading {
                font-size: 30px;
            }

            .verify-otp-sub-heading {
                font-size: 22px;
            }

            .verify-otp-msg {
                font-size: 20px;
            }

            .verify-button {
                width: 200px;
                font-size: 22px;
                
            }
        }
    }
}

// Large devices (desktops, 992px and up)
@media (min-width: 992px) {}

// X-Large devices (large desktops, 1200px and up)
@media (min-width: 1200px) {}

// XX-Large devices (larger desktops, 1400px and up)
@media (min-width: 1400px) {}