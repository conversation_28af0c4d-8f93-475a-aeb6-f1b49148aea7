<div class="training-detail-container">
  <video *ngIf="safeVideoUrl" class="training-detail-video" preload="metadata" autoplay muted controls [poster]="trainingData?.thumbnailImageUrl">
    <source [src]="safeVideoUrl" type="video/mp4">
    Your browser does not support the video tag.
  </video>
  <div class="training-detail-content">
    <div class="training-detail-card">
      <div class="training-detail-card-header">
        <div class="training-detail-card-author">
          <img [src]="trainingData?.userDetails?.programProfileImage || '/assets/images/icons/menu/author.svg'" alt="Author Icon" class="training-detail-card-author-icon">
          <span class="training-detail-card-author-name">{{ trainingData?.userDetails?.authorName || 'Unknown Author' }}</span>
        </div>
        <span class="training-detail-card-date">{{ trainingData?.publishDate | date: 'dd-MM-yyyy' }}</span>
      </div>
      <h2 class="training-detail-card-title">{{ trainingData?.title }}</h2>
      <hr class="training-detail-card-divider">
      <div class="training-detail-card-footer">
        <h3 class="training-detail-card-series-title">{{ trainingData?.learningSeriesDetail?.title || 'Learning Series Title' }}</h3>
        <p class="training-detail-card-description">{{ trainingData?.learningSeriesDetail?.description || 'No description available.' }}</p>
      </div>
    </div>
  </div>
</div>



