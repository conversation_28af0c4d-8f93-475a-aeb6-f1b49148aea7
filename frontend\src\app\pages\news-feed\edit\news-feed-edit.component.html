<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
    <div class="dashboard-content-container d-block text-center">
        <form #newsFeedForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
            <div class="row">
                <div class="mt-2 mb-3">
                    <h4 class="fw-bold">{{request.recordId == 0 ? "Add New News Feed" : "Edit News Feed"}}</h4>
                    <p class="user-edit-msg">Please make sure you fill all the fields before you click on
                        {{request.recordId ==
                        0 ? 'save' : 'update'}} button
                    </p>
                </div>
            </div>
            <div class="col-12 mb-4">
                <div class="form-floating">
                    <select (ngModelChange)="selectType(newsfeed.type, $event)" class="form-select form-control"
                        name="type" aria-label="Please Select Type" [ngModel]="newsfeed.type"
                        [ngClass]="{'is-invalid':!type.valid && onClickValidation}" required="required" #type="ngModel">
                        <option [ngValue]="undefined" selected disabled>Select Option</option>
                        <option value="SPONSORED">Sponsored</option>
                        <option value="BLOG">Blog</option>
                        <option value="TRAINING">Training</option>
                    </select>
                    <label for="type">{{"NewsFeed.type" | translate}}</label>
                </div>
            </div>
            <div *ngIf="newsfeed.type == 'TRAINING'">
                <div class="col-12 col-md-12 col-lg-12 col-xl-12">
                    <div class="form-floating">
                        <div class="mb-4 mt-2 form-control select-width ng-select-main-container"
                            [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
                            <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
                                required="required" [items]="learningSeries" bindLabel="title" bindValue="id"
                                (change)="selectLearningSeries($event)"
                                class="custom-multiselect form-control padding-bottom-8"
                                [(ngModel)]="newsfeed.learningSeries" #learningSeriesId="ngModel">
                            </ng-select>
                        </div>
                        <label for="language">{{"Training.learningSeries" | translate}}</label>
                    </div>
                </div>
                <div class="col-12 col-md-12 col-lg-12 col-xl-12">
                    <div class="form-floating">
                        <div class="mb-4 mt-2 form-control select-width ng-select-main-container"
                            [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                            <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                                [items]="contentTypes" bindLabel="title" bindValue="id"
                                (change)="selectContentType($event)" required="required"
                                class="custom-multiselect form-control padding-bottom-8"
                                [(ngModel)]="newsfeed.contentType" #contentTypeId="ngModel">
                            </ng-select>
                        </div>
                        <label for="language">{{"Training.contentType" | translate}}</label>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-12 mb-4">
                <div class="form-floating mb-4 w-100">
                    <input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}"
                        class="form-control" type="text" name="title" #title="ngModel" [(ngModel)]="newsfeed.title"
                        required="required" placeholder="{{'NewsFeed.title' | translate}}">
                    <label for="floatingInput">{{"NewsFeed.title" | translate}}</label>
                </div>
            </div>
            <div class="col-12 col-md-12 mb-4">
                <div class="form-floating form-floating-textarea mb-4 w-100">
                    <editor [ngClass]="{'is-invalid':!description.valid && onClickValidation}"
                        [apiKey]="tinyMceEditorService.getApiKey()" [init]="tinyMceEditorService.settings()"
                        class="tinymce-invalid-cls tinyMce-border-radius" name="description" #description="ngModel"
                        [(ngModel)]="newsfeed.description" required="required" placeholder="Description"
                        id="floatingTextarea2">
                    </editor>
                    <label for="floatingInput">{{"NewsFeed.description" | translate}}</label>
                </div>
            </div>
            <div class="col-12 mb-4">
                <div class="form-floating">
                    <select class="form-select form-control" name="mediaType" aria-label="Please Select Type"
                        [ngModel]="newsfeed.mediaType" (ngModelChange)="selectMediaType(newsfeed.mediaType, $event)"
                        [ngClass]="{'is-invalid':!mediaType.valid && onClickValidation}" required="required"
                        #mediaType="ngModel">
                        <option [ngValue]="undefined" selected disabled>Select Option</option>
                        <option value="VIDEO">Video</option>
                        <option *ngIf="newsfeed.type !== 'TRAINING'" value="IMAGE">Image</option>
                    </select>
                    <label for="type">{{"NewsFeed.mediaType" | translate}}</label>
                </div>
            </div>
            <div *ngIf="!newsfeed.url && newsfeed.mediaType && !newsfeed.thumbnailImageUrl"
                class="col-12 mb-4 form-control text-center border-2 p-3 border-dark"
                [ngClass]="{'is-invalid': !newsfeed.url && onClickValidation}">
                <label id="file-input" class="upload-img-button cursor-pointer"
                    [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': isLoader}"><img
                        src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">

                    {{!isLoader ? ( newsfeed.mediaType == "VIDEO" ? 'UPLOAD VIDEO' : newsfeed.mediaType == "IMAGE" &&
                    'UPLOAD IMAGE' |
                    translate) : fileUploadingMessage }}
                    <div *ngIf="isLoader" class="spinner-border ms-2" role="status"
                        style="width: 1.7rem; height: 1.7rem">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <input *ngIf="!isLoader" name="mediaUrl" class="d-none" (change)="uploadVideoOrImage($event)"
                        id="file-input" type="file" [accept]="acceptType" />
                </label>
            </div>
            <div *ngIf="!newsfeed.thumbnailImageUrl && newsfeed.mediaType && newsfeed.mediaType == MY_CONSTANT.MEDIA_TYPE.VIDEO"
                class="col-12 mb-4 form-control text-center border-2 p-3 border-dark"
                [ngClass]="{'is-invalid': !newsfeed.thumbnailImageUrl && onClickValidation}">
                <label id="file-input-thumbnail" class="upload-img-button cursor-pointer"
                    [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': uploadingThumbnail}"><img
                        src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">

                    {{!uploadingThumbnail ? ('UPLOAD VIDEO THUMBNAIL' | translate) : ('UPLOADING THUMBNAIL..' |
                    translate) }}
                    <div *ngIf="uploadingThumbnail" class="spinner-border ms-2" role="status"
                        style="width: 1.7rem; height: 1.7rem">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <input *ngIf="!uploadingThumbnail" name="thumbnailUrl" class="d-none"
                        (change)="uploadThumbnail($event)" id="file-input-thumbnail" type="file"
                        accept="image/png, image/jpg, image/jpeg" />
                </label>
            </div>
            <div *ngIf="newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.VIDEO && newsfeed.url"
                [ngStyle]="{'display': !loadingVideo ? 'block': 'none'}" class="video-wrapper moment-video-width mb-4">
                <div class="video-container" id="video-container">
                    <div class="play-button-wrapper">
                        <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
                            id="circle-play-b">
                            <!-- SVG Play Button -->
                            <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                                <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
                            </svg>
                        </div>
                    </div>
                    <div class="position-absolute delete-video-container" (click)="removeFile(newsfeed.url)">
                        <i class="bi bi-x"></i>
                    </div>
                    <video [src]="newsfeed.url" playsinline class="mw-100" id="videoId" controlslist="nodownload">
                    </video>
                </div>
            </div>
            <div *ngIf="newsfeed.mediaType == MY_CONSTANT.MEDIA_TYPE.IMAGE && newsfeed.url"
                class="moment-image-container mb-4">
                <img [src]="newsfeed.url" />
                <div class="position-absolute delete-video-container" (click)="removeFile(newsfeed.url)">
                    <i class="bi bi-x"></i>
                </div>
            </div>
            <div *ngIf="newsfeed.mediaType == this.MY_CONSTANT.MEDIA_TYPE.IMAGE && newsfeed.thumbnailImageUrl"
                class="moment-image-container mb-4">
                <img [src]="newsfeed.thumbnailImageUrl" />
                <div class="position-absolute delete-video-container" (click)="removeFile(newsfeed.thumbnailImageUrl)">
                    <i class="bi bi-x"></i>
                </div>
            </div>
            <!-- <div *ngIf="newsfeed.type == 'TRAINING'">
                <label for="type" class="mb-2">{{"Training.TrainingRequired" | translate}}</label>
                <div class="form-check">
                    <input
                        (ngModelChange)="selectTrainingRequired($event, MY_CONSTANT.TRAINING_REQUIRED.UPLOAD_REQUIRED)"
                        [ngClass]="{'is-invalid':!uploadRequired.valid && onClickValidation}" required="required"
                        [(ngModel)]="newsfeed.uploadRequired" #uploadRequired="ngModel" [value]="true"
                        class="form-check-input radio-button-cls" type="radio" name="uploadRequired"
                        [id]="'Training.uploadRequired' | translate">
                    <label class="form-check-label" [for]="'Training.uploadRequired' | translate">
                        {{"Training.uploadRequired" | translate}}
                    </label>
                </div>
                <div class="form-check">
                    <input
                        (ngModelChange)="selectTrainingRequired($event, MY_CONSTANT.TRAINING_REQUIRED.WATCH_REQUIRED)"
                        [ngClass]="{'is-invalid':!watchRequired.valid && onClickValidation}" required="required"
                        [ngModel]="newsfeed.watchRequired" [value]="true" class="form-check-input radio-button-cls"
                        type="radio" #watchRequired="ngModel" name="watchRequired"
                        [id]="'Training.watchRequired' | translate">
                    <label class="form-check-label" [for]="'Training.watchRequired' | translate">
                        {{"Training.watchRequired" | translate}}
                    </label>
                </div>
            </div> -->
            <div class="col-12 mb-4 training-feed">
                <div>
                    <label for="type" class="mb-2">{{"NewsFeed.Accessibility" | translate}}</label>
                    <div class="form-check">
                        <input (ngModelChange)="selectAccessibility(newsfeed.isNewsFeedPublic, $event)"
                            [ngClass]="{'is-invalid':!isNewsFeedPublic.valid && onClickValidation}" required="required"
                            [ngModel]="newsfeed.isNewsFeedPublic" #isNewsFeedPublic="ngModel" [value]="true"
                            class="form-check-input radio-button-cls" type="radio" name="isNewsFeedPublic" id="public">
                        <label class="form-check-label" for="public">
                            Public
                        </label>
                    </div>
                    <div class="form-check">
                        <input (ngModelChange)="selectAccessibility(newsfeed.isNewsFeedPublic, $event)"
                            [ngClass]="{'is-invalid':!isNewsFeedPublic.valid && onClickValidation}" required="required"
                            [ngModel]="newsfeed.isNewsFeedPublic" class="form-check-input radio-button-cls" type="radio"
                            #isNewsFeedPublic="ngModel" name="isNewsFeedPublic" id="private" [value]="false">
                        <label class="form-check-label" for="private">
                            Private
                        </label>
                    </div>
                </div>
            </div>
            <!-- <div *ngIf="newsfeed.accessibility == 'RESTRICTED'" class="form-floating">
                <div class="mb-4 form-control select-width ng-select-main-container">
                    <ng-multiselect-dropdown placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                        [settings]="dropdownSettings" [data]="users"
                        class="custom-multiselect form-control padding-bottom-8" name="usersDropdown"
                        [(ngModel)]="usersSelected" #userIds="ngModel" (onSelect)="addUser($event)"
                        (onDeSelect)="removeUser($event)">
                    </ng-multiselect-dropdown>
                </div>
                <label for="language">{{"Training.UsersList" | translate}}</label>
            </div> -->
            <div *ngIf="newsfeed.isNewsFeedPublic == true" class="col-12 col-md-12 col-lg-12 col-xl-12">
                <div class="training-feed">
                    <!-- <a href="" class="fw-bold"><img src="/assets/images/icons/menu/tick.svg" class="img-fluid me-2"
								alt="">{{"Training.publishedForTrainingFeed" | translate}}</a> -->
                    <!-- <div class="material-switch">
							<input id="trainingPublishedForTrainingFeedId" name="trainingPublishedForTrainingFeed"
								type="checkbox" [(ngModel)]="newsfeed.publishedForTrainingFeed" />
							<label for="trainingPublishedForTrainingFeedId" class="label-primary"></label>
						</div> -->
                    <div class="form-check publish-training-check-cls">
                        <input class="form-check-input" type="checkbox" name="isPublish" value="" id="flexCheckDefault"
                            [(ngModel)]="newsfeed.isPublish">
                        <label class="form-check-label" for="flexCheckDefault">
                            {{"NewsFeed.publishedForTrainingFeed" | translate}}
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
                <button (click)="save(newsFeedForm.form)"
                    class="btn btn-secondary site-button btn-sm large-button save-button rounded-3">{{request.recordId
                    ==
                    0 ? 'SAVE' : 'UPDATE'}}</button>
            </div>
        </form>
    </div>
</div>