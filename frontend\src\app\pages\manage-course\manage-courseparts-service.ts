import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RestResponse } from 'src/app/shared/auth.model';
import { BaseService } from '../../config/base.service';
import { FilterParam } from 'src/app/models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class ManageCoursePartsService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/coursepart', '/api/courseparts');
    }

    // saveCoursePart(data: any): Promise<RestResponse> {
    //     return this.saveRecord('/api/coursepart', data);
    // }
    // updateCoursePart(data: any): Promise<RestResponse> {
    //     return this.updateRecord('/api/coursepart', data);
    // }

}

