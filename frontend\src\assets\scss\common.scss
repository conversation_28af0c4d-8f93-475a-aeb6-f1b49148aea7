.margin-top-5 {
  margin-top: 5px !important;
}

.margin-top-2 {
  margin-top: 2px !important;
}

.top-3px {
  top: 3px !important;
}

.margin-top-8 {
  margin-top: 8px !important;
}

.margin-top-10 {
  margin-top: 10px !important;
}

.max-width-none {
  max-width: none !important;
}

.margin-top-15 {
  margin-top: 15px !important;
}

.margin-top-20 {
  margin-top: 20px !important;
}

.margin-top-25 {
  margin-top: 25px !important;
}

.margin-top-30 {
  margin-top: 30px !important;
}

.margin-top-35 {
  margin-top: 35px !important;
}

.margin-top-40 {
  margin-top: 40px !important;
}

.margin-top-45 {
  margin-top: 45px !important;
}

.margin-top-50 {
  margin-top: 50px !important;
}

.margin-top-55 {
  margin-top: 55px !important;
}

.margin-top-60 {
  margin-top: 60px !important;
}

.margin-top-70 {
  margin-top: 70px !important;
}

.margin-top-80 {
  margin-top: 80px !important;
}

/*------------*/

.margin-bottom-5 {
  margin-bottom: 5px !important;
}

.margin-bottom-10 {
  margin-bottom: 10px !important;
}

.margin-bottom-15 {
  margin-bottom: 15px !important;
}

.margin-bottom-20 {
  margin-bottom: 20px !important;
}

.margin-bottom-25 {
  margin-bottom: 25px !important;
}

.margin-bottom-30 {
  margin-bottom: 30px !important;
}

.margin-bottom-35 {
  margin-bottom: 35px !important;
}

.margin-bottom-40 {
  margin-bottom: 40px !important;
}

.margin-bottom-45 {
  margin-bottom: 45px !important;
}

.margin-bottom-50 {
  margin-bottom: 50px !important;
}

.margin-bottom-55 {
  margin-bottom: 55px !important;
}

.margin-bottom-60 {
  margin-bottom: 60px !important;
}

.margin-bottom-70 {
  margin-bottom: 70px !important;
}

.margin-bottom-80 {
  margin-bottom: 80px !important;
}

/*--------------*/

.margin-left-5 {
  margin-left: 5px !important;
}

.margin-left-10 {
  margin-left: 10px !important;
}

.margin-left-15 {
  margin-left: 15px !important;
}

.margin-left-20 {
  margin-left: 20px !important;
}

.margin-left-25 {
  margin-left: 25px !important;
}

.margin-left-30 {
  margin-left: 30px !important;
}

.margin-left-35 {
  margin-left: 35px !important;
}

.margin-left-40 {
  margin-left: 40px !important;
}

.margin-left-45 {
  margin-left: 45px !important;
}

.margin-left-50 {
  margin-left: 50px !important;
}

.margin-left-55 {
  margin-left: 55px !important;
}

.margin-left-60 {
  margin-left: 60px !important;
}

/*--------------*/

.margin-right-5 {
  margin-right: 5px !important;
}

.margin-right-10 {
  margin-right: 10px !important;
}

.margin-right-15 {
  margin-right: 15px !important;
}

.margin-right-20 {
  margin-right: 20px !important;
}

.margin-right-25 {
  margin-right: 25px !important;
}

.margin-right-30 {
  margin-right: 30px !important;
}

.margin-right-35 {
  margin-right: 35px !important;
}

.margin-right-40 {
  margin-right: 40px !important;
}

.margin-right-45 {
  margin-right: 45px !important;
}

.margin-right-50 {
  margin-right: 50px !important;
}

.margin-right-55 {
  margin-right: 55px !important;
}

.margin-right-60 {
  margin-right: 60px !important;
}

/*------------*/

.no-margin-left {
  margin-left: 0px !important;
}

.no-margin-bottom {
  margin-bottom: 0px !important;
}

.no-margin-top {
  margin-top: 0px !important;
}

.no-margin-right {
  margin-right: 0px !important;
}

.no-margin {
  margin: 0px !important;
}

/*End Margin Classes*/

/*Padding Classes*/

.no-padding {
  padding: 0px !important;
}

.no-padding-left {
  padding-left: 0px !important;
}

.no-padding-right {
  padding-right: 0px !important;
}

.no-padding-top {
  padding-top: 0px !important;
}

.no-padding-bottom {
  padding-bottom: 0px !important;
}

.padding-5 {
  padding: 5px;
}

.padding-10 {
  padding: 10px !important;
}

.padding-15 {
  padding: 15px;
}

.padding-20 {
  padding: 20px !important;
}

.padding-left-5 {
  padding-left: 5px;
}

.padding-left-10 {
  padding-left: 10px;
}

.padding-left-15 {
  padding-left: 15px;
}

.padding-left-20 {
  padding-left: 20px;
}

.padding-left-42 {
  padding-left: 42px !important;
}

.padding-right-5 {
  padding-right: 5px;
}

.padding-right-10 {
  padding-right: 10px;
}

.padding-right-15 {
  padding-right: 15px;
}

.padding-right-20 {
  padding-right: 20px;
}

.padding-top-5 {
  padding-top: 5px;
}

.padding-top-10 {
  padding-top: 10px;
}

.padding-top-15 {
  padding-top: 15px;
}

.padding-top-20 {
  padding-top: 20px;
}

.padding-bottom-5 {
  padding-bottom: 5px;
}

.padding-bottom-8 {
  padding-bottom: 8px !important;
}

.padding-bottom-10 {
  padding-bottom: 10px;
}

.padding-bottom-15 {
  padding-bottom: 15px;
}

.padding-bottom-20 {
  padding-bottom: 20px;
}

.font-23px {
  font-size: 23px !important;
}

.font-21px {
  font-size: 21px !important;
}

.cursor-auto {
  cursor: auto !important;
}


.disable {
  display: none;
}

html {
  min-height: 100%;
  position: relative;
  height: 100%;
}

body {
  font-family: "Blinker", sans-serif !important;
  min-height: 100%;
  background-color: $secondary;
}

.app-root {
  height: 100%;
}

a {
  cursor: pointer !important;
  text-decoration: none !important;
  color: $primary;
}

.btn {
  border-radius: 0px !important;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-danger {
  color: #b11b25 !important;
}

.primary-color-text {
  color: $primary !important;
  font-weight: 500;
}

.primary-color {
  color: $primary !important;
  font-weight: 500;
  text-transform: capitalize;
}

.integrax-black {
  background: #212529 0% 0% no-repeat padding-box !important;
}

.site-main-container {
  padding: 0px;

  .site-container {
    width: 80%;
    margin: 0 auto;
  }

  .site-common-section {
    min-height: 966px;
    width: 100%;

    .site-content-container {
      width: 75%;
      margin: 0 auto;
    }

    &.background-gray {
      background: #f9f9f9;
    }
  }
}

.row {
  margin-left: 0px !important;
  margin-right: 0px !important;
  width: 100% !important;
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.form-control,
.form-select {
  border-radius: 0px !important;
  color: #3f3f3f !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  border: 1px solid #f0f4f9;

  &:focus {
    box-shadow: 0 0 0 0.1rem rgb(30 0 109 / 25%);
  }
}

.form-floating>.form-control {
  height: calc(3.7rem + 2px) !important;
  padding-left: 1rem !important;
}

.form-floating>.form-select {
  height: calc(3.7rem + 2px) !important;
  padding-left: 1rem !important;
}

.form-floating>label {
  color: #71828a;
  font-size: 15px;
  padding: 1.3rem 1rem !important;
}

.form-floating>.form-control:focus~label,
.form-floating>.form-control:not(:placeholder-shown)~label,
.form-floating>.form-select.form-selected-has-value~label,
.form-floating>.form-select~label {
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem) !important;
}

.form-control.form-control-color {
  width: 100%;
  font-size: 13px;

  &.large {
    font-size: 24px;
    height: calc(4.5rem + 2px) !important;
  }
}

.form-control.large-form-control {
  width: 100%;
  font-size: 16px;
  height: calc(4.5rem + 2px) !important;
}

.form-floating>.form-control-color:focus,
.form-floating>.form-control-color:not(:placeholder-shown) {
  padding-top: 2rem;
}

.only-bottom-border {
  .form-control {
    border-radius: 0px !important;
    color: #3f3f3f !important;
    font-size: 13.5px;
    font-weight: 600;
    border: 0px !important;
    border-bottom: 1px solid $primary !important;
    box-shadow: none !important;
    padding-left: 0.25rem !important;
  }

  label {
    padding-left: 0rem !important;
  }
}

.semi-bold {
  font-weight: 600 !important;
}

.text-justify {
  text-align: justify !important;
}

.form-control {
  &.custom-detail-label {
    line-height: 27px !important;
  }
}

.height-25px {
  height: 25px;
}

.height-200-px {
  height: 200px !important;
}

.height-280-px {
  height: 280px !important;
}

.form-control:disabled {
  background:#fff !important;

}

.pre-line {
  white-space: pre-line;
}

.four-lines-text {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: normal;
}

.three-lines-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: normal;
}

.action-buttons {

  .action-edit-button,
  .action-delete-button,
  .action-login-button,
  .action-publish-button,
  .action-clone-button {
    height: 30px;
    width: 80px;
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
  }

  .action-edit-button {
    border: 1px solid #c9c5d8 !important;
    // margin-right: 5px;
  }

  .action-login-button {
    background-color: #faa1ba;
    border-color: #faa1ba !important;
    color: #fff;
  }

  .action-publish-button {
    background-color: #f15d57;
    border-color: #f15d57 !important;
    color: #fff;
  }

  .action-clone-button {
    background-color: #29bdb1;
    border-color: #29bdb1 !important;
    color: #fff;
  }
}

.uppercase-text {
  text-transform: uppercase !important;
}

.site-button {
  box-shadow: none !important;
  font-size: 13px;
  letter-spacing: 0.26px;
  font-weight: 500;
  position: relative;

  &.btn-secondary,
  &.btn-primary {
    color: #fff !important;
  }

  span {
    font-size: 13px;
    letter-spacing: 0.26px;
    font-weight: 500;
  }

  &.large-button {
    min-height: 58px;
  }

  &.full-width {
    width: 100%;
  }

  &.medium-button {
    min-height: 45px;
  }

  &.input-button {
    min-height: 56px;
  }

  &.save-button {
    min-width: 166px;
    text-transform: uppercase;

    i {
      font-size: 2rem !important;
      margin-right: 5px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  &.new-record-button {
    min-width: 150px;
    text-transform: uppercase;
    background-color: $blue-button-color !important;
    border-color: $blue-button-color !important;

    i {
      font-size: 1.4rem !important;
      display: inline-block;
      vertical-align: middle;
    }
  }

  &.save-cancel {
    min-width: 150px;

    &:hover,
    &:focus,
    &:active {
      background-color: #fff !important;
      color: $primary !important;
    }
  }

  &.filter-button {
    min-width: 80px;
  }

  &.small-font-size {
    font-size: 13px !important;
    font-weight: 500;
  }

  &.icon-only-button {
    min-width: 65px;
    color: #fff !important;
    background-color: $blue-button-color !important;
    border-color: $blue-button-color !important;

    i {
      font-size: 1.4rem !important;
      display: inline-block;
      vertical-align: middle;
    }
  }

  &.file-upload-preview {
    min-height: 56px;
    font-size: 13px !important;
    padding: 0.375rem 1.75rem !important;
  }

  .file-remove-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 14px;
  }
}

.file-input {
  display: none;
}

.cursor-pointer {
  cursor: pointer;
}


.file-upload-container {
  background: $secondary;
  border: 1px dashed $primary;
  max-width: 500px;
  height: 146px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
  position: relative;
  text-align: center;

  &.small {
    max-width: 250px !important;
  }

  &.large {
    max-width: 100% !important;
    height: 250px;
  }

  .file-upload-icon {
    width: 50px;
    height: 50px;
  }

  .file-upload-text {
    font-size: 12px;
    line-height: 15px;
    font-weight: 500;
    color: $primary;
    margin-top: 12px;
    max-width: 80%;
  }

  .file-upload-size {
    margin-top: 6px;
    font-size: 10px;
    line-height: 15px;
    color: $primary;
    margin-bottom: 0px;
  }
}

.file-preview-container {
  background: $secondary;
  border: 1px dashed $primary;
  max-width: 500px;
  height: 146px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;

  &.small {
    max-width: 250px !important;
  }

  .file-remove-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }

  .file-upload-text {
    font-size: 12px;
    line-height: 15px;
    font-weight: 500;
    color: $primary;
    margin-top: 12px;
    max-width: 80%;
  }
}

.margin-left-33n {
  margin-left: -33px !important;
}

.page-name-staff-cls {
  margin-top: 2px;
}

@media (max-width: 600px) {
  .page-name-staff-cls {
    margin-top: 7px;
  }
}

.selected-item {
  margin-bottom: 4px !important;
  margin-right: 7px !important;
}

.max-width-500px {
  max-width: 500px !important;
}

.height-150px {
  height: 150px !important;
}

.height-63px {
  height: 63px !important;
}

.height-61px {
  height: 61px !important;
}

.width-425px {
  width: 425px !important;
}

.width-190px {
  width: 190px !important;
}

.width-15px {
  width: 15px !important;
}

.width-180px {
  width: 180px !important;
}

.cursor-default {
  cursor: default !important;
}

.width-100-px {
  width: 100px !important;
}

.width-140-px {
  width: 140px !important;
}

.width-95-px {
  width: 95px;
}

.width-37-px {
  width: 37px !important;
}

.width-25-px {
  width: 25px !important;
}

.radio-button-cls {
  width: 20px !important;
  height: 20px !important;
}

.width-27px {
  width: 27px;
}

.width-29px {
  width: 29px;
}

.width-32px {
  width: 32px;
}

.width-22px {
  width: 22px !important;
}

.height-30px {
  height: 30px;
}

.height-44px {
  height: 44px !important;
}

.height-54px {
  height: 54px !important;
}

.height-51px {
  height: 51px !important;
}

.font-15px {
  font-size: 15px !important;
}

.font-17px {
  font-size: 17px !important;
}

.pad-9 {
  padding: 9px !important
}

.padding-right-30 {
  padding-right: 30px !important;
}

.tinymce-invalid-cls.is-invalid {
  border-color: #dc3545 !important;
  border-radius: 10px !important;
  border: 1px solid;

  .tox-tinymce {
    border: none !important;
  }
}

.tinyMce-border-radius {
  .tox-tinymce {
    border-radius: 10px !important;
  }
}

.ng-select .ng-select-container .ng-value-container .ng-placeholder {
  font-size: 14px !important;
  font-family: "Blinker", sans-serif !important;
  color: #71828a;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
  top: 50% !important;
  margin-top: -10.5px !important;
  font-size: 14px !important;
  font-family: "Blinker", sans-serif !important;
  color: #71828a;
}

.ng-select .ng-select-container {
  // border: 0px !important;
  height: 56px !important;
  border-radius: 0px;
  border: 1px solid #f0f4f9 !important;

  input {
    height: 44px !important;
  }

  .ng-value {
    .ng-value-label {
      color: #3f3f3f !important;
      font-size: 14px !important;
      font-weight: 600 !important;
    }
  }
}

.ng-dropdown-panel-items {
  .ng-option {
    //text-transform: capitalize !important;
  }
}

.form-control {
  &.is-invalid {
    position: relative;
  }
}

.is-invalid {
  &.form-control {
    border-color: #dc3545 !important;
  }

  .ng-select-container {
    border-color: #dc3545 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 1.3875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }
}

.customer-checkbox {
  width: 50%;
  display: inline-block !important;
  vertical-align: middle !important;
  margin-top: 5px;
}

.table-responsive {
  overflow-x: initial;
}

.multiple-file-preview {
  display: flex;
  width: 100%;
  overflow-x: scroll;
}

.toggle-width {
  width: 35px !important;
}

.mat-form-field-infix {
  font-family: "Blinker", sans-serif;
}

.integrax-font {
  font-family: "Blinker", sans-serif !important;
}

.integrax-tags {
  letter-spacing: 1px;
  font-weight: 600 !important;
  font-size: 12.5px !important;
  background: #1681ff 0% 0% no-repeat padding-box;
}

.mat-form-field-flex {
  border: 1px solid #ced4da;
  height: 61px;
  padding: 0.375rem 0.25rem 0.375rem 0.75rem;
}

.mat-form-field-appearance-legacy .mat-form-field-underline {
  background-color: unset;
}

.mat-form-field-appearance-legacy .mat-form-field-infix {
  padding: 0;
}

.mat-form-field-empty.mat-form-field-label {
  font-size: 14px;
}

.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label,
.mat-form-field-appearance-legacy.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label {
  top: 1.28125em;
  padding-top: 6px;
  font-size: 17px;
}

input.mat-input-element {
  padding-top: 10px;
  font-size: 14px;
  font-weight: 600;
}

.mat-icon {
  padding-top: 6px;
}

.custom_tags_cls {
  color: white;
  background: #1681ff 0% 0% no-repeat padding-box;
  padding: 3px 6px 1px 6px;
}

.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-datepicker-toggle-default-icon {
  margin-top: -3px;
}

.form-check {
  .form-check-input {
    width: 35px;
    height: 20px;
  }

  .form-check-input:checked {
    background-color: #4caf50;
    border: none;
  }
}

@media screen and (max-width: 991px) {
  .site-main-container {
    .site-container {
      width: 100%;
    }

    .site-content-container {
      width: 100% !important;
    }

    .site-common-section {
      min-height: auto !important;
    }
  }

  .action-buttons {

    .action-edit-button,
    .action-delete-button {
      margin-right: 0px;
      margin-left: 0px;
      margin-top: 5px;
    }
  }

  .customer-checkbox {
    width: 100%;
  }
}

@media screen and (max-width: 767px) {
  .site-button {
    &.save-button {
      font-size: 18px;
      min-width: 100%;
      text-transform: uppercase;

      i {
        font-size: 2rem !important;
        margin-right: 5px;
        display: inline-block;
        vertical-align: middle;
      }
    }

    &.save-cancel {
      font-size: 18px;
      min-width: 100%;
      margin-top: 10px;
    }
  }
}

.font-size-17px {
  font-size: 17px;
}

@media screen and (max-width: 480px) {
  .site-button {
    &.save-button {
      font-size: 13px !important;
    }

    &.save-cancel {
      font-size: 13px !important;
    }
  }
}

.iti {
  display: block !important;

  input {
    height: 61px;
  }
}

.swiper-container {
  background: black;
}

.swiper-slide {
  background: black !important;
}

.video-title-reel {
  padding-left: 15px;
  padding-right: 15px;
  text-align: left;

  p {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.video-credit-upload-icon {
  display: flex;
  margin-top: -15px;
  justify-content: space-between;
}

.unmute-button-reel-video {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;

  .mute-button-container {
    background: white;
    margin-left: 15px;
    margin-top: 15px;
    padding: 8px;
    width: 50px;

    img {
      width: 35px;
    }
  }
}

.upload-video-container {
  padding-right: 15px;

  label {
    background: #057dfd;
    border-radius: 50%;
    width: 45px;
    height: 45px;

    img {
      width: 25px;
      margin-top: 13px;
    }

    i {
      font-size: 23px;
    }
  }

  input {
    display: none;
  }

  p {
    font-size: 11px;
    padding-top: 6px;
  }
}

.video-credit-container {
  padding-left: 15px;

  p {
    font-size: 13px;
  }
}

.video-inner-content-container {
  position: absolute;
  z-index: 2;
  bottom: 0;
  width: 100%;
}

@media (min-width: 601px) {
  .swiper-container {
    height: 590px !important;
  }
}

table .dataTable {
  border-collapse: separate;
  border-spacing: 0 12px;
}

.video-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100% !important;

  .video-container {
    width: 100%;
    border-radius: 4px;
    margin: 0 auto;
    height: 100% !important;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    video {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.back-button-reel-video {
  top: 6px;
  left: 6px;
  padding: 8px;
  width: 50px;
  cursor: pointer;
  z-index: 4;

  img {
    width: 35px;
  }
}

.play-button-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  pointer-events: none;

  .circle-play-b-cls {
    cursor: pointer;
    pointer-events: auto;
    z-index: 1;

    svg {
      width: 55px;
      fill: #fff;
      stroke: #fff;
      cursor: pointer;
      background-color: rgba(black, 0.2);
      border-radius: 50%;
      opacity: 0.9;
    }
  }
}