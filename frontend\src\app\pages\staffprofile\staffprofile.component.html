<div data-aos="fade-up" data-aos-duration="1000" class="staff-profile container">
  <div *ngIf="user.id" class="staffprofile-section">
    <form #recordForm="ngForm" novalidate="novalidate">
      <div class="row">
        <div class="col-12">
          <div class="profile-image-container position-relative"
            [ngClass]="{'d-flex align-items-center justify-content-center': profileImageLoader == true}">
            <img *ngIf="!profileImageLoader"
              [src]="user.profileImageUrl ? user.profileImageUrl : '/assets/images/blank-profile-pic.jpg'" />
            <div *ngIf="profileImageLoader" class="spinner-border" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <div *ngIf="!profileImageLoader"
              class="position-absolute profile-image-edit-container d-flex align-items-center justify-content-center">
              <label for="file-input">
                <i class="bi bi-pencil-fill"></i>
                <input name="profile-photo" (change)="uploadProfilePhoto($event)" ng2FileSelect [uploader]="uploader"
                  id="file-input" type="file" accept="image/png, image/jpg, image/jpeg" />
              </label>
            </div>
          </div>
        </div>
        <div class="col-6 mb-4">
          <div class="form-floating">
            <input class="form-control" type="text" name="firstName" #firstName="ngModel" [(ngModel)]="user.firstName"
              required="required" placeholder="First Name"
              [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
            <label for="floatingInput">First Name</label>
          </div>
        </div>
        <div class="col-6 mb-4">
          <div class="form-floating">
            <input class="form-control" type="text" name="lastname" #lastname="ngModel" [(ngModel)]="user.lastName"
              required="required" placeholder="Last Name"
              [ngClass]="{'is-invalid':!lastname.valid && onClickValidation}">
            <label for="floatingInput">Last Name</label>
          </div>
        </div>
        <div class="col-12 mb-4">
          <div class="form-floating">
            <input readonly="readonly" autocomplete="off" class="form-control" type="text" name="phone" ng2TelInput
              (hasError)="hasError($event)" (intlTelInputObject)="telInputObject($event)"
              (ng2TelOutput)="getNumber($event)" #phone="ngModel" [(ngModel)]="user.phoneNumber"
              [ngClass]="{'is-invalid':!phone.valid && onClickValidation}" required="required"
              placeholder="Phone Number" minLength="7" maxlength="12" (countryChange)="onCountryChange($event)">
          </div>
        </div>
        <div class="col-12 mb-4">
          <div class="form-floating">
            <input class="form-control" type="text" name="location" #location="ngModel" [(ngModel)]="user.location"
              required="required" placeholder="Location"
              [ngClass]="{'is-invalid':!location.valid && onClickValidation}">
            <label for="floatingInput">{{"USERS.Location" | translate}}</label>
          </div>
        </div>
        <div class="col-12 mb-4">
          <div class="form-floating">
            <select class="form-select form-control" name="languageId" aria-label="Please Select Language"
              [(ngModel)]="user.languageId" [ngClass]="{'is-invalid':!language.valid && onClickValidation}"
              required="required" #language="ngModel">
              <option [ngValue]="undefined" selected disabled>Select Language</option>
              <option *ngFor="let language of languages" [value]="language.id">{{language.name}}
              </option>
            </select>
            <label for="floatingInput">{{"USERS.language" | translate}}</label>
          </div>

        </div>
        <div class="col-12 mb-4">
          <div class="form-floating staff-profile-sites">
            <ng-multiselect-dropdown disabled="true" [settings]="dropdownSettings" [data]="userFarms" required="required"
              class="form-control multi-select-dropdown-cls pb-5" name="sites" [(ngModel)]="selectedFarms"
              #farmIds="ngModel" (onSelect)="onItemSelect($event)" (onDeSelect)="onDeSelect($event)"
              [ngClass]="{'is-invalid':!farmIds.valid && onClickValidation}">
            </ng-multiselect-dropdown>
            <label for="sites">{{"USERS.site" | translate}}</label>
          </div>
        </div>
        <div class="col-12 mb-4">
          <a (click)="openRequestForSiteChangeModal()" class="text-decoration-underline">Request for Site change</a>
        </div>
        <div class="col-12 mb-4">
          <button [routerLink]="['/staff-change-password']" class="btn change-password">CHANGE PASSWORD</button>
        </div>
        <div class="col-12 mb-4">
          <button (click)="save(recordForm.form.valid)" class="btn btn-secondary save-button"
            type="submit">Save</button>
        </div>
      </div>
    </form>
  </div>
  <div class="modal fade" id="requestForSiteChangeModal" tabindex="-1" aria-labelledby="requestForSiteChangeModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="requestForSiteChangeModalLabel">Request For Site Change</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div *ngIf="requestForSiteChangeModal && requestForSiteChangeModal._isShown" class="modal-body">
          <form #requestForSiteChangeForm="ngForm" novalidate="novalidate">
            <div class="mb-3">
              <ng-multiselect-dropdown placeholder="Select Sites" [settings]="dropdownSettings" [data]="farms"
                required="required" class="multi-select-dropdown-cls form-control padding-bottom-8" name="request_for_sites"
                [(ngModel)]="selectedFarmsForRequest" #requestFarmIds="ngModel"
                (onSelect)="onItemSelectForRequest($event)" (onDeSelect)="onDeSelectForRequest($event)"
                [ngClass]="{'is-invalid':!requestFarmIds.valid && onClickValidationForRequestSiteChange}">
              </ng-multiselect-dropdown>
            </div>
            <div class="mb-3">
              <textarea maxlength="250" name="message" [ngClass]="{'is-invalid':!description.valid && onClickValidationForRequestSiteChange}" class="form-control height-150px" id="message-text" placeholder="Enter Your Message Here...." #description="ngModel"
              [(ngModel)]="messageForRequestSiteChange" required="required"></textarea>
            </div>
            <div class="modal-footer">
              <button type="button" class="text-white btn btn-secondary" data-bs-dismiss="modal"
                aria-label="Close">Close</button>
              <button [disabled]="sendingRequestForSiteChange" (click)="onClickRequestForSiteChange(requestForSiteChangeForm.form.valid)" type="button"
                class="btn btn-primary">{{sendingRequestForSiteChange ? 'Please Wait...' : 'Send Request'}}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
