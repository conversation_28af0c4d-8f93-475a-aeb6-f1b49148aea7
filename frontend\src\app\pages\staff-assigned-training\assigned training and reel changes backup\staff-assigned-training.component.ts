import { AfterViewInit, Component, Directive, Input, OnInit } from '@angular/core';
import { FilterParam } from 'src/app/models/filterparam';
import { StaffAssignedTrainingService } from 'src/app/services/staff-assigned-training.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';
import * as moment from 'moment';
import AOS from 'aos';

declare var bootstrap: any;

@Component({
  selector: 'app-staff-assigned-training',
  templateUrl: './staff-assigned-training.component.html',
  styleUrls: ['./staff-assigned-training.component.scss'],
  providers: [FilterParam]
})
export class StaffAssignedTrainingComponent implements OnInit, AfterViewInit {
  trainingTabs: any = "INPROGRESS"
  trainingData: any = [];
  currentTrainingData: any;
  recordData: any;
  moment: any = moment;
  baseUrl: any = '/api/user/' + this.authService.getUser().id;
  trainingDescriptionModal: any;
  constructor(private authService: AuthService, private staffassignedTrainingService: StaffAssignedTrainingService, private toastService: ToastService, private filterParam: FilterParam) { }

  ngOnInit() {
    this.fetchAllTrainings('/api/user/' + this.authService.getUser().id + '/assigned-trainings', "INPROGRESS");
  }

  changeTrainingData(data: string) {
    if (data !== this.trainingTabs) {
      let url = data == "INPROGRESS" ? '/assigned-trainings' : '/assigned-trainings-status'
      this.fetchAllTrainings(this.baseUrl + url, data);
    }
    this.trainingTabs = data;
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.trainingDescriptionModal = new bootstrap.Modal(
        document.getElementById('trainingDescriptionModal')
      );
    },0)
  }

  async fetchAllTrainings(url: string, status: string) {
    this.filterParam.status = status;
    try {
      const response: RestResponse = await this.staffassignedTrainingService.fetchAllTrainings(this.filterParam, url);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainingData = response.data.map(training => ({ videoLoaded: false, uploader: null, ...training }));
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  backToTrainingListCallback() {
    this.currentTrainingData = null;
    let url = this.trainingTabs == "INPROGRESS" ? '/assigned-trainings' : '/assigned-trainings-status'
    this.fetchAllTrainings(this.baseUrl + url, this.trainingTabs);

  }

  watchVideo(trainingData: any) {
    this.currentTrainingData = trainingData;
  }

  readMoreDescription(record: any) {
    AOS.init({ disable: true });
    this.recordData = {
      description: record.trainingIdDetail.description,
      name: record.trainingIdDetail.videoTitle,
    };
      this.trainingDescriptionModal.show();
  }

}
