import { Component, ElementRef, Input, OnInit, Renderer2, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { VideoTraining } from 'src/app/models/trainingLibrary';

@Component({
  selector: 'app-training-card',
  templateUrl: './training-card.component.html',
  styleUrls: ['./training-card.component.scss']
})
export class TrainingCardComponent implements OnInit {
  @Input() training: VideoTraining;
  username: any;
  
  constructor(public router: Router, private renderer: Renderer2, protected route: ActivatedRoute,) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.username = params.title;
    });

    // this.init();

  }

  @ViewChild('videoPlayer', { static: false }) videoPlayer: ElementRef;
  ngAfterViewInit() {
    const video = this.videoPlayer.nativeElement;
    const overlay = video.parentElement.querySelector('.video-overlay') as HTMLElement;

    video.parentElement.addEventListener('mouseenter', () => {
      this.pauseOtherVideos(video);
      video.play();
      overlay.style.opacity = '0';
      video.controls = true; // Show native video controls
    });

    video.parentElement.addEventListener('mouseleave', () => {
      video.pause();
      overlay.style.opacity = '1';
      video.controls = false; // Hide native video controls when not hovered
    });
  }

  onVideoLoad(videoElement: HTMLVideoElement) {
    const isLandscape = videoElement.videoWidth > videoElement.videoHeight;

    if (isLandscape) {
      this.renderer.addClass(videoElement, 'landscape-video');
      this.renderer.removeClass(videoElement, 'portrait-video');
    } else {
      this.renderer.addClass(videoElement, 'portrait-video');
      this.renderer.removeClass(videoElement, 'landscape-video');
    }
  }

  pauseOtherVideos(currentVideo: HTMLVideoElement) {
    const allVideos = document.querySelectorAll('video');
    allVideos.forEach(video => {
      if (video !== currentVideo && !video.paused) {
        video.pause();
        const overlay = video.parentElement.querySelector('.video-overlay') as HTMLElement;
        overlay.style.opacity = '1';
      }
    });
  }

  navigateToDetail(trainingId: string) {
    this.router.navigate(['/dashboard/program-admin/training/library/', trainingId]); // Navigate to the detail page with the training ID
  }

  // click to open the training detail page
  openTrainingDetails(record:any) {
    this.router.navigate(['/dashboard/program-admin/training/detail/' + record.id])
  }

  openCourseDetailPage(id: any) {
    this.router.navigate(['/dashboard/program-admin/course/detail/' + id]);
  }

  openAuthorDetails(id: any) {
    this.router.navigate(['/dashboard/program-admin/profile-setting/' + id]);

  }

}
