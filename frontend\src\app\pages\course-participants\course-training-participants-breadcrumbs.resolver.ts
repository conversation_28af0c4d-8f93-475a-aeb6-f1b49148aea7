import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class CourseTrainingParticipantsBreadcrumbs implements Resolve<any> {
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
        const userId: any | null = route.paramMap.get("id");

        return of([
            {
                title: "Dashboard", link: "/dashboard/program-admin", active: false
            },
            {
                title: "Manage Course", link: "/dashboard/program-admin/courses", active: false
            },
            // {
            //     title: "Manage Course", link: "/dashboard/program-admin/courses", active: false
            // },
            // {
            //     title: "Course Training Participant",
            //     link: "/dashboard/program-admin/course/training/participants/" + userId,
            //     active: false
            // }
        ])



    }
}
