<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
    <div class="allocated-users-list" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 col-sm-4 text-start">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="Search" appDelayedInput
                        (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchCourse">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th style="width: 110px; text-wrap: nowrap;">
                            {{'courseTraining.lastUpdatedDate' | translate}}
                        </th>
                        <!-- <th style="width: 110px; text-wrap: nowrap;">
                            Created By
                        </th>
                        <th style="width:120px; text-wrap: nowrap;">
                            {{"courseTraining.title" | translate}}
                        </th> -->
                        <th style="width:120px; text-wrap: nowrap;">
                            <!-- {{"courseTraining.course" | translate}} -->
                            Seeker's Name
                        </th>
                        <th style="width:120px; text-wrap: nowrap;">
                            {{"courseTraining.coursePart" | translate}}
                        </th>
                        <th style="width:120px; text-wrap: nowrap;">
                            {{"courseTraining.contentType" | translate}}
                        </th>
                        <th style="width:117px; text-wrap: nowrap;">
                            {{"courseTraining.trainingHistory" | translate}}
                        </th>
                        <th style="width:200px; text-wrap: nowrap;">
                            {{"courseTraining.trainingStatus" | translate}}
                        </th>
                        <th style="width:150px; text-wrap: nowrap;">
                            {{"courseTraining.UserVideo" | translate}}
                        </th>
                        <th style="width: 50px; text-wrap: nowrap;">
                            {{"courseTraining.action" | translate}}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="records-cls" *ngFor="let record of records;">
                        <td>
                            <div>{{moment(record?.updatedOn).format('DD/MM/YYYY')}}</div>
                        </td>
                        <!-- <td class="pe-0">
                            <h5 class="code" title="View User Profile" style="font-size:13px; white-space: nowrap;"
                                [routerLink]="['/dashboard/program-detail/' +record?.userDetails?.id]"
                                [queryParams]="{ tab: 'profile', 'username': record?.userDetails?.fullName }"
                                queryParamsHandling="merge">
                                {{record?.userDetails?.fullName}}
                            </h5>
                        </td>
                        <td>
                            <div
                                *ngIf="record?.trainingDetail?.learningSeriesDetail?.title; else noRecordLearningTitle">
                                {{ record.trainingDetail.learningSeriesDetail.title }}
                            </div>
                            <ng-template #noRecordLearningTitle>-</ng-template>
                        </td> -->
                        <!-- <td>
                            <div *ngIf="record?.courseDetail?.title; else noRecordCourseTitle">
                                <a (click)="openCourseDetailPage(record)" class="text-decoration-underline">
                                    {{record?.courseDetail?.title}}
                                </a>
                            </div>
                            <ng-template #noRecordCourseTitle>-</ng-template>
                        </td> -->
                        <td>
                            {{record?.userDetail?.fullName}}
                        </td>
                        <td>
                            <a (click)="openPart(record)" class="">{{record?.courseDetail?.coursePartCount}}
                            </a>
                        </td>
                        <td>
                            <div
                                *ngIf="record?.trainingDetail?.contentTypeDetail?.title; else noRecordContentTypeTitle">
                                {{record?.trainingDetail?.contentTypeDetail?.title}}
                            </div>
                            <ng-template #noRecordContentTypeTitle>-</ng-template>
                        </td>
                        <td>
                            <a (click)="openTrainingDetailPage(record)"
                                class="text-decoration-underline">{{record?.trainingDetail?.title}}
                            </a>
                        </td>
                        <td>
                            <div *ngIf="record?.status else noStatus">
                                <div [ngClass]="commonUtil.getTitleStatusColor(record.status)"
                                    class="status-button semi-bold">
                                    <img class="me-1" [src]="commonUtil.getTitleStatusImg(record.status)">
                                    {{commonUtil.getTitleStatus(record.status)}}
                                </div>
                            </div>
                            <ng-template #noStatus>-</ng-template>
                        </td>
                        <td>
                            <button *ngIf="record.videoUrl; else NoVideo" (click)="watchVideo(record)"
                                class="user-video-button bg-secondary">
                                <img src="/assets/images/icons/menu/watch-video.svg"></button>
                            <ng-template #NoVideo>-</ng-template>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i *ngIf="record && record.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                                    (click)="approveOrRejectTraining(record.id, 'APPROVED')"
                                    class="bi bi-check-circle fs-4 cursor-pointer" title="Approved"></i>
                                <i *ngIf="record && record.status === MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;"
                                    (click)="approveOrRejectTraining(record.id, 'REJECTED')"
                                    class="bi bi-x-circle ps-2 fs-4 cursor-pointer" title="Rejected"></i>
                                <ng-container
                                    *ngIf="!record || record.status !== MY_CONSTANT.TRAINING_STATUS.WAITING_FOR_APPROVAL;">
                                    <span>-</span>
                                </ng-container>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- watch video modal -->
<div class="modal fade" id="programUserTrainingVideoModal" aria-hidden="true"
    aria-labelledby="programUserTrainingVideoModalLabel" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="programUserTrainingVideoModalLabel"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" *ngIf="programUserTrainingVideoModal && programUserTrainingVideoModal._isShown">
                <div>
                    <div *ngIf="loadingVideo" class="loading-container-video-training">
                        <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
                        id="staff-video"></video>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>