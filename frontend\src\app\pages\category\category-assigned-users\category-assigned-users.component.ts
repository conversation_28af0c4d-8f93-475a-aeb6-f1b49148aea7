import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { BaseListServerSideComponent } from "src/app/config/base.list.server.side.component";
import { CategoryAssignedUsers } from "src/app/models/categoryassignedusers";
import { LoadingService } from "src/app/services/loading.service";
import { RestResponse } from "src/app/shared/auth.model";
import { CommonService } from "src/app/shared/common.service";
import { ToastService } from "src/app/shared/toast.service";
import { CategoryAssignedUsersManager } from "./category-assigned-users.mamager";
import { CategoryAssignedUsersService } from "./category-assigned-users.service";

declare const $: any;
@Component({
  selector: 'app-category-assigned-users',
  templateUrl: './category-assigned-users.component.html',
  styleUrls: ['./category-assigned-users.component.scss']
})

export class CategoryAssignedUsersComponent extends BaseListServerSideComponent implements OnInit {

  userSelected: boolean;
  type: string;

  constructor(private route: ActivatedRoute, protected categoryAssignedUsersManager: CategoryAssignedUsersManager, protected commonService: CommonService,
    protected toastService: ToastService, protected loadingService: LoadingService, protected router: Router, private categoryAssignedUsersService: CategoryAssignedUsersService) {
    super(categoryAssignedUsersManager, commonService, toastService, loadingService, router);
  }

  ngOnInit(): void {
    const categoryId = this.route.snapshot.paramMap.get('id');
    this.categoryAssignedUsersService.getCategoryId(categoryId);
    this.records = [] as CategoryAssignedUsers[];
    this.type = this.getUrlWithoutParams() === '/dashboard/category-assigned-users' ? "USER" : "FARM"
    this.categoryAssignedUsersService.setType(this.type);
    this.init();
  }

  getUrlWithoutParams() {
    let urlTree = this.router.parseUrl(this.router.url);
    urlTree.queryParams = {};
    urlTree.fragment = null; // optional
    let urlArry = urlTree.toString().split("/");
    let filterArr = urlArry.slice(0, urlArry.length - 1);
    return filterArr.join("/").toString();
  }

  onCancel() {
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  removeSuccess() {
    this.onCancel();
  }

  ngOnDestroy() {
    this.clean();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.userSelected = false;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  onChangeShowEntries(value: any) {
    this.dtOptions.pageLength = parseInt(value);
    this.userSelected = false;
    $(".selectAll").prop('checked', false)
    this.refreshRecord();
  }

  selectUnselectAll(event: any) {
    if (event.currentTarget.checked) {
      this.userSelected = true;
      $('tbody').find('input[type="checkbox"]').prop('checked', true);
    } else {
      this.userSelected = false;
      $('tbody').find('input[type="checkbox"]').prop('checked', false);
    }

  }

  selectUnselectCheck() {
    let rowsLength = $('tbody').find('.records-cls').length;
    let checkedCheckboxLength = $('tbody').find('input[type="checkbox"]:checked').length;
    if (rowsLength == checkedCheckboxLength)
      $(".selectAll").prop('checked', true)
    else
      $(".selectAll").prop('checked', false)

    if (checkedCheckboxLength > 0)
      this.userSelected = true;
    else
      this.userSelected = false;
  }

  unassignedUser() {
    let categoryIds = []
    $('input[name="categoryId"]:checked').each(function () {
      categoryIds.push(this.value);
    });
    this.commonService.confirmation(categoryIds.length == 1 ? "Would you like to unassigned staff?" : "Would you like to unassigned multiple staff?",
      this.unAssignedUserCallback.bind(this), categoryIds, categoryIds.length == 1 ? "single" : "multiple");
  }

  async unAssignedUserCallback(data: any, type: string) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.categoryAssignedUsersService.unassignCategoryUsers(data, type);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  unassignedFarm() {
    let categoryIds = []
    $('input[name="categoryId"]:checked').each(function () {
      categoryIds.push(this.value);
    });
    this.commonService.confirmation(categoryIds.length == 1 ? "Would you like to unassigned site?" : "Would you like to unassigned multiple site?",
      this.unAssignedFarmCallback.bind(this), categoryIds, categoryIds.length == 1 ? "single" : "multiple");
  }

  async unAssignedFarmCallback(data: any, type: string) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.categoryAssignedUsersService.unassignCategoryFarms(data, type);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onCancel();
      this.toastService.success(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

}