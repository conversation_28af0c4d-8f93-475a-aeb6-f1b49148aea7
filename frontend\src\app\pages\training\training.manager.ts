import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { TrainingService } from './training.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class TrainingManager extends BaseManager {

    constructor(protected trainingService: TrainingService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(trainingService, loadingService, toastService);
    }
}
