<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
    <div class="allocated-users-list" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="row">
            <div class="col-12 col-sm-4 text-start">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" placeholder="Search" appDelayedInput
                        (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchText">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div class="col-12 col-sm-3">
            </div>
            <div class="col-12 col-sm-5 d-flex align-items-center justify-content-end mb-2 pe-0">

            </div>
        </div>
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records?.length>0}">
            <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <tbody>
                    <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
                        <td width="30">
                            <span class="ms-3">
                                <img src="/assets/images/icons/menu/view-training-logo.svg" alt=""
                                    style="vertical-align: sub; width:38px;">
                            </span>
                        </td>
                        <td width="100">
                            <h5 class="code" (click)="openTrainingDetailPage(record.id)">{{record.title}}</h5>
                        </td>
                        <td width="40">
                            <div class="vertical-line" style="border:1px solid #1681FF">
                            </div>
                        </td>
                        <td style="white-space: nowrap;">
                            <div class="title">{{moment(record.updatedOn).format('DD-MM-YYYY')}}</div>
                        </td>
                        <td></td>
                        <td style="white-space: nowrap;">
                            <div class="d-flex">
                                <span class="semi-bold title" style="color: #8F9198;">Content Type <span
                                        class="ms-2 text-dark title">{{record.contentTypeDetail.title}}</span> </span>
                            </div>
                        </td>
                        <td width="380"></td>
                        <td width="60" class="text-center custom-action-button text-right">
                            <div class="d-flex justify-content-end">
                                <div class="d-flex">
                                    <button [routerLink]="['/dashboard/program-admin/view/user/logs/'+ record.id]"
                                        class="btn manage-filter-buttton bg-dark text-light btn-lg font-15px filter-button-cls check-user-logs">
                                        Check User Logs
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>