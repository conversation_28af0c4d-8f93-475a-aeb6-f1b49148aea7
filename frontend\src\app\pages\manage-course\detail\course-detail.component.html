<div *ngIf="course.courseParts && course.courseParts.length > 0; else noPartsTemplate">
  <!-- Loop through courseParts -->
  <div *ngFor="let part of courseParts">
    {{ part.name }} <!-- Display part details -->
  </div>
</div>

<!-- Template to show when courseParts is empty -->
<ng-template #noPartsTemplate>
  <div class="no-records-container">
    No Course Part Record Added
  </div>
</ng-template>

<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
  <div *ngIf="isCourseDetailVisible">
    <div class="dashboard-content-container" [ngClass]="{'no-padding':isDetailPage}">
      <div class="d-flex justify-content-between align-items-center" style="padding: 0px 20px;">
        <div class="d-flex justify-content-center align-items-center">
          <div>
            <h4 class="fw-bold">{{course?.code}}</h4>
          </div>
          <div class="vertical-line" style="border:1px solid #1681FF">
          </div>
          <div>
            <h6 class="title" style="font-weight: 600;">
              {{course?.title}}
            </h6>
          </div>
          <div class="width-27px"></div>
          <div>
            <h6 class="title">
              <div>{{moment(course?.updatedOn).format('DD-MM-YYYY')}}</div>
            </h6>
          </div>
        </div>
      </div>
      <div>
        <hr class="line">
      </div>
      <div class="mt-2 mb-2 course-description" [innerHTML]="course?.description">
      </div>
    </div>
    <div class="accordion mt-4" id="coursePartAccordion">
      <div *ngFor="let item of course?.courseParts; let i = index" class="accordion-item border-0 mb-2">
        <h2 class="accordion-header" [attr.id]="'heading' + i" style="position: relative;">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            [attr.data-bs-target]="'#collapse' + i" aria-expanded="false" [attr.aria-controls]="'collapse' + i">
            <span class="ps-3 title">
              Part {{ toRoman(i + 1) }} - {{ item.title }}
            </span>
          </button>
          <button (click)="openCoursePartModal($event,item)" class="btn info-part">
            <i class="bi bi-info-circle"></i>
          </button>
        </h2>
        <!-- modal using for course part description -->
        <div class="modal fade" id="coursePartModal" tabindex="-1" aria-labelledby="coursePartModalLabel"
          aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                  (click)="closeCoursePartModal()"></button>
              </div>
              <div class="modal-body">
                <div class="modal-content-inside">
                  <h5 class="modal-title" id="coursePartModalLabel">Description</h5>
                  <div class="mt-1" [innerHTML]="description">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- modal using for course part description end -->
        <div [attr.id]="'collapse' + i" class="accordion-collapse collapse show" [attr.aria-labelledby]="'heading' + i"
          data-bs-parent="#coursePartAccordion">
          <div class="accordion-body border-0 p-0">
            <div *ngIf="!item.trainings || item.trainings.length === 0"
              class="d-flex justify-content-center align-items-center"
              style="margin: 20px auto; padding: 10px; box-shadow: 0px 3px 8px #15223214;">
              <div class="fw-bold fs-4">No Training Found</div>
            </div>
            <div *ngFor="let training of item.trainings,let i = index"
              class="d-flex justify-content-between align-items-center" 
              style="padding: 10px; box-shadow: 0px 3px 8px #15223214; border: 0px;">
              <div class="ps-4 code" (click)="openTrainingDetails(training)" [ngClass]="{'cursor-auto':training?.accessibility !== 'Public'}">
                {{training.trainingLibraryDetail?.id ?
                training.trainingLibraryDetail.title:training.title}}
              </div>
              <div class="d-flex align-items-center justify-content-sm-between">
                <div class="complete-previous-step ms-4">
                  <img *ngIf="training.isLock" src="/assets/images/icons/menu/lock.svg" class="me-3" alt="">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="!isCourseDetailVisible" class="accordion mt-4" id="coursePartAccordion">
    <div *ngFor="let item of course?.courseParts; let i = index" class="accordion-item border-0 mb-2">
      <h2 class="accordion-header" [attr.id]="'heading' + i" style="position: relative;">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
          [attr.data-bs-target]="'#collapse' + i" aria-expanded="false" [attr.aria-controls]="'collapse' + i">
          <span class="ps-3 title">
            Part {{ toRoman(i + 1) }} - {{ item.title }}
          </span>
        </button>
        <button *ngIf="isCourseDetailVisible" (click)="openCoursePartModal($event,item)" class="btn info-part">
          <i class="bi bi-info-circle"></i>
        </button>
      </h2>
      <!-- modal using for course part description -->
      <div class="modal fade" id="coursePartModal" tabindex="-1" aria-labelledby="coursePartModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header border-0 pb-0">
              <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                (click)="closeCoursePartModal()"></button>
            </div>
            <div class="modal-body">
              <div class="modal-content-inside">
                <h5 class="modal-title" id="coursePartModalLabel">Description</h5>
                <div class="mt-1" [innerHTML]="description">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- modal using for course part description end -->
      <div [attr.id]="'collapse' + i" class="accordion-collapse collapse show" [attr.aria-labelledby]="'heading' + i"
        data-bs-parent="#coursePartAccordion">
        <div class="accordion-body border-0 p-0">
          <div *ngIf="!item.trainings || item.trainings.length === 0"
            class="d-flex justify-content-center align-items-center"
            style="margin: 20px auto; padding: 10px; box-shadow: 0px 3px 8px #15223214;">
            <div class="fw-bold fs-4">No Training Found</div>
          </div>
          <div *ngFor="let training of item.trainings,let i = index"
            class="d-flex justify-content-between align-items-center"
            style="padding: 10px; box-shadow: 0px 3px 8px #15223214; border: 0px;">
            <div class="ps-4 code" (click)="openTrainingDetails(training)">
              <!-- Training {{ toRoman(i + 1) }} - {{ training.title }} -->
              {{training.trainingLibraryDetail?.id ? training.trainingLibraryDetail.title:training.title}}
            </div>
            <div class="d-flex align-items-center justify-content-sm-between">
              <div class="d-flex text-end">
                <div *ngIf="!training?.trainingLibrary" class="courseRate" style="width: 100%;">
                  {{training?.contentTypeDetail?.title}}
                </div>
                <div *ngIf="training?.trainingLibrary" class="courseRate" style="width: 100%;">
                  {{training?.trainingLibraryDetail?.contentTypeDetail?.title}}
                </div>
                <div class="courseRate cursor-pointer ms-2" (click)="openCourseParticipants(training)">
                  {{training?.completionRate}}
                </div>
              </div>
              <div class="complete-previous-step ms-4">
                <img *ngIf="training.isLock" src="/assets/images/icons/menu/lock.svg" class="me-3" alt="">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- end -->
</div>