import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { CourseUserDetailsService } from './course-user-details.service';

@Injectable({
    providedIn: 'root'
})
export class CourseUserDetailsManager extends BaseManager {

    constructor(protected courseUserDetailsService: CourseUserDetailsService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(courseUserDetailsService, loadingService, toastService);
    }
}
