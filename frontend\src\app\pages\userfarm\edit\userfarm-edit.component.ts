import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { UserFarm } from '../../../models/userfarm';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { UserFarmManager } from '../userfarm.manager';

import { UsersManager } from '../../users/users.manager';
import { Users } from '../../../models/users';
import { FarmManager } from '../../farm/farm.manager';
import { Farm } from '../../../models/farm';
import { CommonEventService } from '../../../shared/common.event.service';
declare const $: any;

@Component({
  selector: 'app-userfarm-edit',
  templateUrl: './userfarm-edit.component.html',
  styleUrls: ['./userfarm-edit.component.scss']
})

export class UserFarmEditComponent extends BaseEditComponent implements OnInit {
  public userFarm: UserFarm;
	public users:Users[];
	public farms:Farm[];
  
  constructor(protected route: ActivatedRoute, protected userfarmManager: UserFarmManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private usersManager:UsersManager, private farmManager:FarmManager
  			  ,public commonUtil:CommonUtil ) {
    	super(userfarmManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.userFarm = new UserFarm();
  	this.userFarm.isActive=true;   
    this.setRecord(this.userFarm);
	
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.users = new Array<Users>();
  	this.farms = new Array<Farm>();
    this.init();
  }

  onFetchCompleted() {
  	this.userFarm = UserFarm.fromResponse(this.record);
    this.setRecord(this.userFarm);
  }

  
  
  async fetchAssociatedData() {
	this.users = await this.usersManager.fetchAllData(null);       		
	this.farms = await this.farmManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const userIdId: string = this.route.snapshot.queryParamMap.get('Users');
		if (userIdId){
			this.onAssociatedValueSelected({"id":userIdId},'userFarmUserIdSelect');
		}
    	const farmIdId: string = this.route.snapshot.queryParamMap.get('Farm');
		if (farmIdId){
			this.onAssociatedValueSelected({"id":farmIdId},'userFarmFarmIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/user-farm');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userFarmUserIdSelect') || this.request.popupId==='userFarmUserIdPopup'){
			this.userFarm.userId = selectedRecord.id;
			this.checkConditionToReload(this.users, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userFarmFarmIdSelect') || this.request.popupId==='userFarmFarmIdPopup'){
			this.userFarm.farmId = selectedRecord.id;
			this.checkConditionToReload(this.farms, selectedRecord);
			return;
	    }
  	
	 }
}
