<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">
    <div class="user-details-section">
        <div class="row">
            <div class="col-12 mb-3">
                <div class="user-details-section">
                    <ul class="nav nav-pills">
                        <li class="nav-item bg-secondary user-details-btn width-180px"
                            [ngClass]="{'bg-secondary': userDetailsTabs == 'enrolled'}"
                            (click)="onClickUserDetailsTab('enrolled')">
                            <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'enrolled'}"
                                aria-current="page">Enrolled</a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': userDetailsTabs == 'invited'}"
                             (click)="onClickUserDetailsTab('invited')">
                            <a class="btn nav-link"
                                [ngClass]="{' active bg-secondary': userDetailsTabs == 'invited'}">Invited
                            </a>
                        </li>
                        <li class="nav-item user-details-btn width-180px"
                            [ngClass]="{' bg-secondary': userDetailsTabs == 'pending'}"
                            (click)="onClickUserDetailsTab('pending')">
                            <a class="btn nav-link"
                                [ngClass]="{' active bg-secondary': userDetailsTabs == 'pending'}">Pending
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-12 col-sm-4 text-start mt-3">
                <div class="custom-input-group">
                    <input class="form-control search-form-control" appDelayedInput (delayedInput)="search($event)"
                        [(ngModel)]="filterParam.searchText" [delayTime]="1000" placeholder="Search">
                    <i class="bi bi-search pe-3"></i>
                </div>
            </div>
            <div class="col-12 col-sm-3 mt-3">
            </div>
            <div class="col-12 col-sm-5 mt-3 d-flex align-items-center justify-content-end pe-0">
                <button (click)="openInviteCourseUserModal()" type="button"
                    class="btn add-button add-button-training bg-dark text-light btn-lg font-15px height-51px">
                    <img src="/assets/images/icons/menu/invite.svg" class="me-2 width-22px img-fluid" alt=""
                        style="vertical-align: sub;">Invite Now
                </button>
                <button type="button" (click)="openFilterTrainingModal()"
                    class="btn manage-filter-buttton ms-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px pe-0">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button>
            </div>
        </div>
    </div>
    <!-- Manage User Tabs -->
    <app-user-enrolled-list *ngIf="userDetailsTabs == 'enrolled'" [filterParam]="filterParam"></app-user-enrolled-list>
    <app-user-invited-listing *ngIf="userDetailsTabs == 'invited'"
        [filterParam]="filterParam"></app-user-invited-listing>
    <app-user-pending-listing *ngIf="userDetailsTabs == 'pending'" [filterParam]="filterParam">
    </app-user-pending-listing>

    <!-- modal invite course user -->
    <div class="modal fade" id="inviteUserCourseModal" tabindex="-1" aria-labelledby="inviteUserCourseModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0" style="padding-top: 30px;
                      margin: auto 15px;">
                    <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
                        (click)="closeInviteModal()">
                    </button>
                </div>
                <div *ngIf="inviteUserCourseModal && inviteUserCourseModal._isShown" class="modal-body"
                    style="padding: 10px 50px;">
                    <div class="modal-content-inside">
                        <h5 class="modal-title fw-bold" id="inviteUserCourseModalLabel">Invite User for Course</h5>
                        <p class="modal-heading pt-1" id="inviteUserCourseModalLabel">
                            Please make sure you fill in all the fields before you click on the Send Invite button
                        </p>
                    </div>
                    <form #userInviteForm="ngForm" novalidate="novalidate">
                        <!-- Radio button based on Self and Others -->
                        <div class="mb-4" *ngIf="!authService.getRoles().includes('ROLE_PROGRAM_ADMINISTRATOR')">
                            <label for="type" class="mb-2">
                                Select Your Preference
                            </label>
                            <div class="form-check">
                                <input (ngModelChange)="selectInviteUser(true)"
                                    [ngClass]="{'is-invalid': !selfInviteUser.valid && onClickValidation}"
                                    required="required" [(ngModel)]="isSelfInvite" #selfInviteUser="ngModel"
                                    [value]="true" class="form-check-input radio-button-cls" type="radio"
                                    name="inviteUserType" id="self">
                                <label class="form-check-label ms-1" for="self">
                                    Self Course Invite
                                </label>
                            </div>
                            <div class="form-check">
                                <input (ngModelChange)="selectInviteUser(false)"
                                    [ngClass]="{'is-invalid': !otherInviteUser.valid && onClickValidation}"
                                    required="required" [(ngModel)]="isSelfInvite" #otherInviteUser="ngModel"
                                    [value]="false" class="form-check-input radio-button-cls" type="radio"
                                    name="inviteUserType" id="other">
                                <label class="form-check-label ms-1" for="other">
                                    Other Course Invite
                                </label>
                            </div>
                        </div>
                        <!-- Radio button based on Self and Others end-->
                        <div class="form-floating"
                            *ngIf="!isSelfInvite && authService.getRoles().includes('ROLE_ADMIN')">
                            <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                                [ngClass]="{'is-invalid': !selectedCourseProgramUser.valid && onClickValidation}">
                                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                    name="selectedCourseProgramUser" clearable="false" [items]="programCourseList"
                                    bindLabel="fullName" bindValue="id" (change)="selectInviteProgramUserCourse($event)"
                                    class="custom-multiselect form-control padding-bottom-8"
                                    [(ngModel)]="selectedCourseProgramUserId" #selectedCourseProgramUser="ngModel"
                                    [searchable]="false">
                                    <!-- [disabled]="selectedCourseProgramUserId" -->
                                </ng-select>
                            </div>
                            <label for="selectedCourseUser">{{"Course.programCourse" | translate}}</label>
                        </div>
                        <!-- Show this section only when selectedCourseProgramUserId is set -->
                        <div class="form-floating">
                            <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                                [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                    name="selectedCourseUser" clearable="false" [items]="publishCourseList"
                                    bindLabel="title" bindValue="id" (change)="selectInviteUserCourse($event)"
                                    class="custom-multiselect form-control padding-bottom-8"
                                    [(ngModel)]="selectedCourseUserId" #selectedCourseUser="ngModel"
                                    [searchable]="false" required="required">
                                    <!-- [disabled]="selectedCourseUserId" -->
                                </ng-select>
                            </div>
                            <label for="selectedCourseUser">
                                {{"Course.chooseCourse" | translate}}
                            </label>
                        </div>

                        <div class="form-floating mb-3">
                            <!-- Company/Individual selection block START -->
                            <div class="mb-3">
                              <label>
                                <input type="radio" name="inviteType" [(ngModel)]="isFarmInvite" [value]="true">
                                Company
                              </label>
                              <label class="ms-3">
                                <input type="radio" name="inviteType" [(ngModel)]="isFarmInvite" [value]="false">
                                Individual
                              </label>
                            </div>
                            <ng-container *ngIf="isFarmInvite">
                              <div class="col-12 mb-4">
                                <div class="form-floating">
                                  <div class="form-control select-width ng-select-main-container"
                                       [ngClass]="{'is-invalid': !farmIds?.valid && farmValidation}">
                                    <ng-select [items]="farms"
                                               bindLabel="displayLabel"
                                               bindValue="id"
                                               name="farmIds"
                                               #farmIds="ngModel"
                                               [required]="isFarmInvite"
                                               [(ngModel)]="selectedFarmIds"
                                               placeholder="Select Companies"
                                               [multiple]="true">
                                    </ng-select>
                                  </div>
                                  <label for="farmIds">Companies</label>
                                  <app-validation-message [field]="farmIds" [onClickValidation]="farmValidation"></app-validation-message>
                                </div>
                              </div>
                            </ng-container>
                            <ng-container *ngIf="!isFarmInvite">
                              <div class="form-floating mb-4">
                                <input maxlength="20"
                                       [ngClass]="{'is-invalid': !username.valid && individualValidation}"
                                       class="form-control b-r-8"
                                       type="text"
                                       name="username"
                                       #username="ngModel"
                                       required
                                       [(ngModel)]="courses.username"
                                       placeholder="Name" autocomplete="off">
                                <label for="floatingInput">Name</label>
                              </div>
                              <div class="form-floating mb-2">
                                <input [ngClass]="{'is-invalid': !email.valid && individualValidation}"
                                       class="form-control b-r-8"
                                       type="email"
                                       name="email"
                                       #email="ngModel"
                                       required
                                       [(ngModel)]="courses.email"
                                       placeholder="Email"
                                       pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                                <label for="floatingInput">Email</label>
                              </div>
                            </ng-container>
                            <!-- Company/Individual selection block END -->
                        </div>
                        <div *ngIf="errorMessage" style="color: red;">
                            {{ errorMessage }}
                        </div>

                        <div class="text-end mb-2 mt-2">
                            <button class="btn" (click)="insertData(userInviteForm, courses.id)"
                                style="border: 1px solid #000; border-style: dashed; font-size: 20px;"
                                title="Add username or email">
                                <i class="bi bi-plus-lg" style="color: #000;"></i>
                            </button>
                        </div>
                        <div class="mb-2" *ngFor="let insert of insertedData; let i = index">
                            <div class="d-flex justify-content-between align-middle"
                                style="border: 1px solid #1681FF; border-radius: 10px; padding: 10px;">
                                <h5 class="text-secondary mt-1">
                                    {{insert.username}} ( {{insert.email}} )
                                </h5>
                                <div class="mt-1" (click)="removeData(i)">
                                    <img src="/assets/images/icons/menu/remove-user.svg" class="me-2 img-fluid text-end"
                                        alt="">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
                            <button (click)="onClickInviteUserCourse(userInviteForm.form)" type="button"
                                class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                                INVITE
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal invite course user end-->

    <!-- filter training listing -->
    <div class="modal fade" id="filterTrainingModal" tabindex="-1" aria-labelledby="filterTrainingModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterTrainingModalLabel">Filter Manage Users</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div *ngIf="filterTrainingModal && filterTrainingModal._isShown" class="modal-body">
                    <form #trainingFilterForm="ngForm" novalidate="novalidate">
                        <div class="form-floating">
                            <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                                [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                    name="selectedCourseUser" clearable="false" [items]="publishCourseList"
                                    bindLabel="title" bindValue="id" (change)="selectInviteUserCourse($event)"
                                    class="custom-multiselect form-control padding-bottom-8"
                                    [(ngModel)]="filterParam.course" #selectedCourseUser="ngModel" [searchable]="false">
                                </ng-select>
                            </div>
                            <label for="selectedCourseUser">
                                {{"Course.chooseCourse" | translate}}
                            </label>
                        </div>
                        <app-date-range-filter (fromDateOutput)="fromDateOutput($event)"
                            (toDateOutput)="toDateOutput($event)" [fromDateInput]="filterParam.startDate"
                            [toDateInput]="filterParam.endDate">
                        </app-date-range-filter>
                        <div class="modal-footer">
                            <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">
                                Reset
                            </button>
                            <div>
                                <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
                                    class="btn btn-primary ms-2">
                                    Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>