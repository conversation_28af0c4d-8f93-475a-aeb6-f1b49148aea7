import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Users } from './users';
import { Farm } from './farm';
import { Training } from './training';
export class UserTrainingStatus extends BaseModel {
	
				tenantId: number;
			  	slug: string;
			  	userIdDetail: Users;
			  	userId: string;
			  	farmIdDetail: Farm;
			  	farmId: string;
			  	traningIdDetail: Training;
			  	traningId: string;
			  	rejectCompletionDate: Date;
			  	rejectCompletionDateCalendar: any;
			  	userVideoUrl: string;
			  	isApproved: boolean;
			  	assignedDate: Date;
			  	assignedDateCalendar: any;
	
    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
			this.isApproved=false;
    }
    
   static fromResponse(data: any) : UserTrainingStatus {
		const obj = new UserTrainingStatus();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
	  	obj.userIdDetail = data.userIdDetail;
	  	obj.userId = data.userId;
	  	obj.farmIdDetail = data.farmIdDetail;
	  	obj.farmId = data.farmId;
	  	obj.traningIdDetail = data.traningIdDetail;
	  	obj.traningId = data.traningId;
		obj.rejectCompletionDate = data.rejectCompletionDate;
		obj.rejectCompletionDateCalendar = { date: moment(data.rejectCompletionDate).format('DD/MM/YYYY') };
		obj.userVideoUrl = data.userVideoUrl;
		obj.isApproved = data.isApproved;
		obj.assignedDate = data.assignedDate;
		obj.assignedDateCalendar = { date: moment(data.assignedDate).format('DD/MM/YYYY') };
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
				this.rejectCompletionDate = this.convertCalToDate(this.rejectCompletionDateCalendar);
				this.userVideoUrl = this.trimMe(this.userVideoUrl);
				this.assignedDate = this.convertCalToDate(this.assignedDateCalendar);
        return this;
    }
}
