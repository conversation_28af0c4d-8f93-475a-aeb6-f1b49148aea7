<div class="site-customer-main-container" data-aos="fade-up" data-aos-duration="1000">
  <div class="allocated-users-list" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="row">
      <div class="col-12 col-sm-4 text-start">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="Search" appDelayedInput
            (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchCourse">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div class="col-12 col-sm-3">
      </div>
      <div class="col-12 col-sm-5 d-flex align-items-center justify-content-end mb-2 pe-0">
        <!-- <button (click)="openFilterTrainingModal()" type="button"
                    class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
                    <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px"
                        alt="">Filter
                </button> -->
        <button (click)="openInviteCourseUserModal()" type="button"
          class="btn add-button add-button-training bg-dark text-light btn-lg font-15px height-51px">
          <img src="/assets/images/icons/menu/invite.svg" class="me-2 width-22px img-fluid" alt=""
            style="vertical-align: sub;">Invite Now
        </button>
        <button (click)="openFilterCourseModal()" type="button"
          class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg mx-2 filter-button-cls font-15px height-51px">
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
        </button>
        <button *ngIf="authService.isAccessible('MANAGE_COURSE','AddButton')" type="button"
          class="btn add-button add-button-training btn-primary btn-lg font-15px" (click)="onNewRecord()">
          <img src="/assets/images/icons/menu/add_icon.svg" class="me-2 width-17px" alt="">Add Course
        </button>
      </div>
    </div>
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th class="text-nowrap">{{'Course.code' | translate}}</th>
            <th></th>
            <th class="text-nowrap">{{'Course.title' | translate}}</th>
            <th class="text-nowrap">{{'Course.lastUpdatedDate' | translate}}</th>
            <th class="text-nowrap">{{'Course.status' | translate}}</th>
            <th class="text-nowrap">{{'Course.createdBy' | translate}}</th>
            <th class="text-nowrap">{{'Course.completion' | translate}}</th>
            <th class="text-nowrap text-center">{{'Course.action' |
              translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;" style="vertical-align: middle; background-color: #fff;">
            <td (click)="openCourseDetailPage(record,'coursedetail')" class="pe-0">
              <h5 class="code ms-2" title="Course Detail">
                {{record?.code}}</h5>
            </td>
            <td>
              <div class="vertical-line" style="border:1px solid #1681FF">
              </div>
            </td>
            <td style="white-space: nowrap;">
              <div class="title ellipsis" [attr.title]="record.title">{{ record?.title }}</div>
            </td>
            <td style="white-space: nowrap;">
              <div class="title">{{moment(record?.updatedOn).format('DD-MM-YYYY')}}</div>
            </td>
            <td>
              <div class="text-capitalize">
                {{record?.userDetails?.programAdminName}}
              </div>
            </td>
            <td>
              <div class="form-check form-switch">
                <input (change)="updateIspublish($event,record)" class="form-check-input toggle-width" type="checkbox"
                  id="flexSwitchCheckChecked" [(ngModel)]="record.isPublish" [ngModelOptions]="{standalone: true}">
                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
              </div>
            </td>

            <!-- <td width="40" class="text-right">
              <div class="d-flex">
                <button [routerLink]="authService.getRoles().includes('ROLE_ADMIN')
                ?['/dashboard/training/program/admin/detail/' + record?.id,]
                : ['/dashboard/program-admin/course/trainings/'+ record?.id]"
                  class="btn manage-filter-buttton bg-dark text-light btn-lg font-15px filter-button-cls">
                  view trainings
                </button>
                <div class="vertical-line">
                </div>
              </div>
            </td> -->

            <!-- <td width="60" class="text-right">
              <div style="white-space: nowrap;" class="cursor-pointer title"
                [routerLink]="['/dashboard/program-admin/course/users/' + record?.id]">
                <i class="bi bi-person fw-bold"></i>
                <span class="fw-bold mx-1">{{record?.allocatedUsersCount}}</span>Users Allocated
              </div>
            </td> -->
            <td>
              <div class="courseRate cursor-pointer" (click)="navigateToCourseUsers(record?.id)">
                {{ record?.completionRate }}
              </div>

            </td>

            <td class="text-center custom-action-button text-right">
              <div class="d-flex justify-content-end">
                <img (click)="openCourseDetailPage(record,'preview')" title="Preview Course Detail"
                  src="/assets/images/icons/menu/preview-icon.svg" class="width-22px img-fluid cursor-pointer" alt=""
                  style="vertical-align: sub;">
                <img class="custom-action-icon" (click)="openCourseParticipants(record)" title="Course Participants"
                  src="/assets/images/icons/menu/course-participate-icon.svg"
                  class="mx-3 width-22px img-fluid cursor-pointer" alt="" style="vertical-align: sub;">
                <i *ngIf=" authService.isAccessible('MANAGE_COURSE','EditButton') && !isDetailPage && !isPlusButton"
                  [class.disabled]="authService.isDisabled('MANAGE_COURSE','EditButton')" title="Edit"
                  (click)="editRecord(record?.id)" class="bi bi-pencil font-21px me-3 cursor-pointer">
                </i>
                <i *ngIf="authService.isAccessible('MANAGE_COURSE','DeleteButton') && !isPlusButton"
                  [class.disabled]="authService.isDisabled('MANAGE_COURSE','DeleteButton')" title="Delete"
                  (click)="remove(record?.id)" class="bi bi-trash font-21px cursor-pointer">
                </i>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- modal invite course user -->
  <div class="modal fade" id="inviteUserManageCourseModal" tabindex="-1"
    aria-labelledby="inviteUserManageCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header border-0" style="padding-top: 30px;
                      margin: auto 15px;">
          <button type="button" class="btn-close btn-close-dark" data-bs-dismiss="modal" aria-label="Close"
            (click)="closeInviteModal()"></button>
        </div>
        <div *ngIf="inviteUserManageCourseModal && inviteUserManageCourseModal._isShown" class="modal-body"
          style="padding: 10px 50px;">
          <div class="modal-content-inside">
            <h5 class="modal-title fw-bold" id="inviteUserManageCourseModalLabel">Invite User for Course</h5>
            <p class="modal-heading pt-1" id="inviteUserManageCourseModalLabel">
              Please make sure you fill in all the fields before you click on the Send Invite button
            </p>
          </div>
          <form #userInviteForm="ngForm" novalidate="novalidate">
            <!-- Radio button based on Self and Others -->
            <div class="mb-4" *ngIf="!authService.getRoles().includes('ROLE_PROGRAM_ADMINISTRATOR')">
              <label for="type" class="mb-2">
                Select Your Preference
              </label>
              <div class="form-check">
                <input (ngModelChange)="selectInviteUser(true)"
                  [ngClass]="{'is-invalid': !selfInviteUser.valid && onClickValidation}" required="required"
                  [(ngModel)]="isSelfInvite" #selfInviteUser="ngModel" [value]="true"
                  class="form-check-input radio-button-cls" type="radio" name="inviteUserType" id="self">
                <label class="form-check-label ms-1" for="self">
                  Self Course Invite
                </label>
              </div>
              <div class="form-check">
                <input (ngModelChange)="selectInviteUser(false)"
                  [ngClass]="{'is-invalid': !otherInviteUser.valid && onClickValidation}" required="required"
                  [(ngModel)]="isSelfInvite" #otherInviteUser="ngModel" [value]="false"
                  class="form-check-input radio-button-cls" type="radio" name="inviteUserType" id="other">
                <label class="form-check-label ms-1" for="other">
                  Other Course Invite
                </label>
              </div>
            </div>
            <!-- Radio button based on Self and Others end-->
            <div class="form-floating" *ngIf="!isSelfInvite && authService.getRoles().includes('ROLE_ADMIN')">
              <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                [ngClass]="{'is-invalid': !selectedCourseProgramUser.valid && onClickValidation}">
                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="selectedCourseProgramUser"
                  clearable="false" [items]="programCourseList" bindLabel="fullName" bindValue="id"
                  (change)="selectInviteProgramUserCourse($event)"
                  class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="selectedCourseProgramUserId"
                  #selectedCourseProgramUser="ngModel" [searchable]="false">
                  <!-- [disabled]="selectedCourseProgramUserId" -->
                </ng-select>
              </div>
              <label for="selectedCourseProgramUser">{{"Course.programCourse" | translate}}</label>
            </div>
            <!-- Show this section only when selectedCourseProgramUserId is set -->

            <div class="form-floating">
              <div class="mb-4 form-control select-width ng-select-main-container b-r-8"
                [ngClass]="{'is-invalid': !selectedCourseUser.valid && onClickValidation}">
                <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="selectedCourseUser"
                  clearable="false" [items]="publishCourseList" bindLabel="title" bindValue="id"
                  (change)="selectInviteUserCourse($event)" class="custom-multiselect form-control padding-bottom-8"
                  [(ngModel)]="selectedCourseUserId" #selectedCourseUser="ngModel" [searchable]="false"
                  required="required">
                  <!-- [disabled]="selectedCourseUserId" -->
                </ng-select>
              </div>
              <label for="selectedCourseUser">
                {{"Course.chooseCourse" | translate}}
              </label>
            </div>

            <div class="mb-3">
              <label>
                <input type="radio" name="inviteType" [(ngModel)]="isFarmInvite" [value]="true">
                Company
              </label>
              <label class="ms-3">
                <input type="radio" name="inviteType" [(ngModel)]="isFarmInvite" [value]="false">
                Individual
              </label>
            </div>

            <ng-container *ngIf="!isFarmInvite">
              <div class="form-floating mb-4">
                <input maxlength="20" [ngClass]="{'is-invalid': !username.valid && individualValidation}"
                  class="form-control b-r-8" type="text" name="username" #username="ngModel" required
                  [(ngModel)]="courses.username" placeholder="{{'Course.name' | translate}}">
                <label for="floatingInput">{{"Course.name" | translate}}</label>
              </div>

              <div class="form-floating mb-2">
                <input [ngClass]="{'is-invalid': !email.valid && individualValidation}" class="form-control b-r-8"
                  type="email" name="email" #email="ngModel" [(ngModel)]="courses.email" required
                  placeholder="{{'Course.email' | translate}}" pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$">
                <label for="floatingInput">{{"Course.email" | translate}}</label>
              </div>
              <div *ngIf="errorMessage" style="color: red;">
                {{ errorMessage }}
              </div>

              <div class="text-end mb-2 mt-2">
                <button class="btn" (click)="insertData(userInviteForm, courses.id)"
                  style="border: 1px solid #000; border-style: dashed; font-size: 20px;" title="Add username or email">
                  <i class="bi bi-plus-lg" style="color: #000;"></i>
                </button>
              </div>
              <div class="mb-2" *ngFor="let insert of insertedData; let i = index">
                <div class="d-flex justify-content-between align-middle"
                  style="border: 1px solid #1681FF; border-radius: 10px; padding: 10px;">
                  <h5 class="text-secondary mt-1">
                    {{insert.username}} ( {{insert.email}} )
                  </h5>
                  <div class="mt-1" (click)="removeData()">
                    <img src="/assets/images/icons/menu/remove-user.svg" class="me-2 img-fluid text-end" alt="">
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="isFarmInvite">
              <div class="col-12 mb-4">
                <div class="form-floating">
                  <div class="form-control select-width ng-select-main-container"
                    [ngClass]="{'is-invalid': !farmIds?.valid && farmValidation}">
                    <ng-select [items]="farms" bindLabel="displayLabel" bindValue="id" name="farmIds" #farmIds="ngModel"
                      [required]="isFarmInvite" [(ngModel)]="selectedFarmIds" placeholder="Select Companies"
                      [multiple]="true">
                    </ng-select>
                  </div>
                  <label for="farmIds">Companies</label>
                  <app-validation-message [field]="farmIds"
                    [onClickValidation]="farmValidation"></app-validation-message>
                </div>
              </div>
            </ng-container>
            <div class="modal-footer border-0 mb-4 p-0 m-0 text-end">
              <button (click)="onClickInviteUserCourse(userInviteForm.form)" type="button"
                class="btn btn-secondary manage-filter-buttton btn-lg filter-button-cls font-15px height-51px text-light">SEND
                INVITE
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- filter training listing -->
  <div class="modal fade" id="filterManageCourseModal" tabindex="-1" aria-labelledby="filterManageCourseModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="filterManageCourseModalLabel">Filter Manage Course</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div *ngIf="filterManageCourseModal && filterManageCourseModal._isShown" class="modal-body">
          <form #courseFilterForm="ngForm" novalidate="novalidate">
            <div class="form-floating" *ngIf="authService.isAdmin()">
              <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                [ngClass]="{'is-invalid': !author.valid && onClickValidation}">
                <ng-select placeholder="{{ 'COMMON.SELECT_OPTION' | translate }}" name="author"
                  [items]="trainingFilterData.data[0].authorDetail" bindLabel="fullName" bindValue="id"
                  class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.author"
                  (ngModelChange)="onAuthorChange($event)" #author="ngModel">
                </ng-select>
              </div>
              <label for="author">{{ 'Training.author' | translate }}</label>
            </div>
            <div class="form-floating mb-4 w-100">
              <input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}" class="form-control"
                type="text" name="title" #title="ngModel" [(ngModel)]="filterParam.courseTitle" required="required"
                placeholder="{{'Course.title' | translate}}">
              <label for="floatingInput">{{"Course.title" | translate}}</label>
              <app-validation-message [field]="title" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>
            <div class="form-floating mb-3">
              <select class="form-select form-control" name="status" aria-label="Please Select status"
                [(ngModel)]="filterParam.isPublish" [ngClass]="{'is-invalid':!status.valid && onClickValidation}"
                required="required" #status="ngModel">
                <option [ngValue]="undefined" selected disabled>Select Option</option>
                <option value="true">Published</option>
                <option value="false">Draft</option>

              </select>
              <label for="type">{{"SubCategory.status" | translate}}</label>
            </div>
            <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
              [fromDateInput]="filterParam.startDate" [toDateInput]="filterParam.endDate">
            </app-date-range-filter>
            <div class="modal-footer">
              <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">
                Reset
              </button>
              <div>
                <button (click)="onClickCourseFilter(courseFilterForm.form.valid)" type="button"
                  class="btn btn-primary ms-2">
                  Filter
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <!--  -->

</div>