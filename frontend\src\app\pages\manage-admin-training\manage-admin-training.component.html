<div class="site-customer-main-container ps-1" data-aos="fade-up" data-aos-duration="1000">
    <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
        <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                    <tr>
                        <th style="text-wrap: nowrap;">{{'Training.videoTitle' | translate}}</th>
                        <th style="text-wrap: nowrap;">Created By</th>
                        <th style="text-wrap: nowrap;">{{'Training.contentType' | translate}}</th>
                        <th style="text-wrap: nowrap;">{{'Training.learningSeries' | translate}}</th>
                        <th style="width:80px; text-wrap: nowrap;">{{'Training.AccessibilityStatus' | translate}}</th>
                        <th></th>
                        <th style="width:50px; text-wrap: nowrap;">{{'Training.status' | translate}}</th>
                        <th></th>
                        <th style="width: 110px; text-wrap: nowrap;">{{'Training.lastUpdatedDate' | translate}}</th>
                        <th style="width: 50px; text-wrap: nowrap;" class="text-center">{{'Training.action' |
                            translate}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let record of records;">
                        <td>
                            <a (click)="openTrainingDetailPage(record)"
                                class="text-decoration-underline">{{record.title}}</a>
                        </td>
                        <td class="pe-0">
                            <h5 class="code" title="View User Profile" style="font-size:13px; white-space: nowrap;"
                                [routerLink]="['/dashboard/program-detail/' +record?.userDetails?.id]"
                                [queryParams]="{ tab: 'profile', 'username': record?.userDetails?.fullName }"
                                queryParamsHandling="merge">
                                {{record?.userDetails?.fullName}}
                            </h5>
                        </td>
                        <td>
                            <div>{{record.contentTypeDetail.title}}</div>
                        </td>
                        <td>
                            <div>{{record.learningSeriesDetail.title}}</div>
                        </td>
                        <td>
                            <div class="form-check form-switch">
                                <input (change)="updateStatusAccssibility(record)"
                                    [attr.title]="getTooltipText(record.accessibility)" data-bs-toggle="tooltip"
                                    data-bs-placement="top" class="form-check-input accessibility-switch toggle-width"
                                    type="checkbox" id="flexSwitchCheckCheckedAccssibility"
                                    [checked]="record.accessibility === 'Public'" [ngModelOptions]="{standalone: true}">
                            </div>
                        </td>
                        <td></td>
                        <td>
                            <div class="form-check form-switch">
                                <input (change)="updateStatus($event,record)" class="form-check-input toggle-width"
                                    type="checkbox" id="flexSwitchCheckChecked" [(ngModel)]="record.isPublish"
                                    [ngModelOptions]="{standalone: true}">
                                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                            </div>
                        </td>
                        <td></td>
                        <td>
                            <div>{{moment(record.updatedOn).format('DD/MM/YYYY')}}</div>
                        </td>
                        <td class="text-center custom-action-button text-right">
                            <div class="d-flex justify-content-end mb-3 me-2">
                                <i *ngIf="authService.isAccessible('TRAINING','EditButton') && !isDetailPage && !isPlusButton"
                                    [class.disabled]="authService.isDisabled('TRAINING','EditButton')" title="Edit"
                                    (click)="editRecord(record.id)"
                                    class="bi bi-pencil font-21px me-2 cursor-pointer"></i>
                                <i *ngIf="authService.isAccessible('MANAGE_ADMIN_COURSE_TRAINING','DeleteButton') && !isPlusButton"
                                    [class.disabled]="authService.isDisabled('MANAGE_ADMIN_COURSE_TRAINING','DeleteButton')"
                                    title="Delete" (click)="remove(record.id)"
                                    class="bi bi-trash font-21px cursor-pointer"></i>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- <div class="modal fade" id="reassignTrainingModal" tabindex="-1" aria-labelledby="reassignTrainingModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="reassignTrainingModalLabel">Assign Training</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div *ngIf="reassignTrainingModal && reassignTrainingModal._isShown" class="modal-body">
                        <form #reassignStaffForm="ngForm" novalidate="novalidate">
                            <div class="mb-3">
                                <ng-multiselect-dropdown required="required" placeholder="Select Sites"
                                    [settings]="dropdownSettings" [data]="farms"
                                    class="multi-select-dropdown-cls form-control padding-bottom-8" name="sites"
                                    [(ngModel)]="selectedFarms" #farmIds="ngModel"
                                    (onSelect)="onItemSelectForFarm($event)" (onDeSelect)="onDeSelectForFarm($event)"
                                    [disabled]="selectedUsers.length > 0">
                                </ng-multiselect-dropdown>
                            </div>
                            <div class="mb-3">
                                <ng-multiselect-dropdown required="required" placeholder="Select Users"
                                    [settings]="dropdownSettingsUsers" [data]="users"
                                    class="multi-select-dropdown-cls form-control padding-bottom-8" name="users"
                                    [(ngModel)]="selectedUsers" #userIds="ngModel"
                                    (onSelect)="onItemSelectForUser($event)" (onDeSelect)="onDeSelectForUser($event)"
                                    [disabled]="selectedFarms.length > 0">
                                </ng-multiselect-dropdown>
                            </div>
                            <div class="mb-3">
                                <app-validation-message [optionalCustomErrorMessage]="optionalValidationMessage"
                                    [field]="farmIds" [field1]="userIds" [onClickValidation]="onClickValidation">
                                </app-validation-message>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="text-white btn btn-secondary" data-bs-dismiss="modal"
                                    aria-label="Close">Close</button>
                                <button [disabled]="sendingRequestForSiteChange"
                                    (click)="onClickReassignStaffForm(reassignStaffForm.form.valid)" type="button"
                                    class="btn btn-primary">{{sendingRequestForSiteChange ? 'Please Wait...' :
                                    'Assign'}}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div> -->
        <!-- Training Filter -->
        <!-- <div class="modal fade" id="filterTrainingModal" tabindex="-1" aria-labelledby="filterTrainingModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="filterTrainingModalLabel">Filter Training</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div *ngIf="filterTrainingModal && filterTrainingModal._isShown" class="modal-body">
                        <form #trainingFilterForm="ngForm" novalidate="novalidate">
                            <div class="form-floating">
                                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                    [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
                                    <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}"
                                        name="learningSeriesId"
                                        [items]="trainingFilterData.data[0].learningSeriesDetail" bindLabel="title"
                                        bindValue="id" class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="filterParam.learningSeries" #learningSeriesId="ngModel">
                                    </ng-select>
                                </div>
                                <label for="language">{{"Training.learningSeries" | translate}}</label>
                            </div>
                            <div class="form-floating">
                                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                                    [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                                    <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                                        [items]="trainingFilterData.data[0].contentTypeDetail" bindLabel="title"
                                        bindValue="id" class="custom-multiselect form-control padding-bottom-8"
                                        [(ngModel)]="filterParam.contentType" #contentTypeId="ngModel">
                                    </ng-select>
                                </div>
                                <label for="language">{{"Training.contentTypes" | translate}}</label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select form-control" name="status" aria-label="Please Select status"
                                    [(ngModel)]="filterParam.isPublish"
                                    [ngClass]="{'is-invalid':!status.valid && onClickValidation}" required="required"
                                    #status="ngModel">
                                    <option [ngValue]="undefined" selected disabled>Select Option</option>
                                    <option value="true">Publish</option>
                                    <option value="false">Draft</option>

                                </select>
                                <label for="type">{{"SubCategory.status" | translate}}</label>
                            </div>
                            <div class="form-floating">
                                <select class="form-select form-control" name="accessibility"
                                    aria-label="Please Select accessibility setup"
                                    [(ngModel)]="filterParam.accessibility"
                                    [ngClass]="{'is-invalid':!accessibility.valid && onClickValidation}"
                                    required="required" #accessibility="ngModel">
                                    <option [ngValue]="undefined" selected disabled>Select Option</option>
                                    <option value="PUBLIC">Public</option>
                                    <option value="RESTRICTED">Restricted</option>
                                </select>
                                <label for="type">{{"SubCategory.ACCESSIBILITY_SETUP" | translate}}</label>
                            </div>
                            <div class="modal-footer">
                                <button (click)="resetFilter()" type="button"
                                    class="text-white btn btn-secondary">Reset</button>
                                <button (click)="onClickTrainingFilter(trainingFilterForm.form.valid)" type="button"
                                    class="btn btn-primary">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</div>

<!-- <div class="modal fade modal-xl" id="restrictedUsersModal" aria-hidden="true" aria-labelledby="restrictedUsersModal"
    tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="restrictedUsersModalLabel">User Restrcited List</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <app-restricted-users-list [getRestrictedUsersData]="restrictedUsersData && restrictedUsersData"
                    *ngIf="restrictedUsersModal && restrictedUsersModal._isShown"
                    [apiUrl]="'/api/training/assigned/users'"
                    [getFilterParam]="filterParam"></app-restricted-users-list>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div> -->