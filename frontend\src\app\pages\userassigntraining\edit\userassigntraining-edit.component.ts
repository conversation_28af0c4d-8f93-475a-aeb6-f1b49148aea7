import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMyDpOptions } from 'mydatepicker';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { UserAssignTraining } from '../../../models/userassigntraining';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { UserAssignTrainingManager } from '../userassigntraining.manager';

import { UsersManager } from '../../users/users.manager';
import { Users } from '../../../models/users';
import { TrainingManager } from '../../training/training.manager';
import { Training } from '../../../models/training';
import { FarmManager } from '../../farm/farm.manager';
import { Farm } from '../../../models/farm';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
declare const $: any;

@Component({
  selector: 'app-userassigntraining-edit',
  templateUrl: './userassigntraining-edit.component.html',
  styleUrls: ['./userassigntraining-edit.component.scss']
})

export class UserAssignTrainingEditComponent extends BaseEditComponent implements OnInit {
   public myDatePickerOptions: IMyDpOptions;
  public userAssignTraining: UserAssignTraining;
	public users:Users[];
	public trainings:Training[];
	public farms:Farm[];
  
  constructor(protected route: ActivatedRoute, protected userassigntrainingManager: UserAssignTrainingManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private usersManager:UsersManager, private trainingManager:TrainingManager, private farmManager:FarmManager
  			  ,public commonUtil:CommonUtil ) {
    	super(userassigntrainingManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.userAssignTraining = new UserAssignTraining();
  	this.userAssignTraining.isActive=true;   
    this.setRecord(this.userAssignTraining);
	
     this.myDatePickerOptions = {
      dateFormat:Constant.MY_DATE_PICKER.DATE_TYPE
     } 
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.users = new Array<Users>();
  	this.trainings = new Array<Training>();
  	this.farms = new Array<Farm>();
    this.init();
  }

  onFetchCompleted() {
  	this.userAssignTraining = UserAssignTraining.fromResponse(this.record);
    this.setRecord(this.userAssignTraining);
  }

  
  
  async fetchAssociatedData() {
	this.users = await this.usersManager.fetchAllData(null);       		
	this.trainings = await this.trainingManager.fetchAllData(null);       		
	this.farms = await this.farmManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const userIdId: string = this.route.snapshot.queryParamMap.get('Users');
		if (userIdId){
			this.onAssociatedValueSelected({"id":userIdId},'userAssignTrainingUserIdSelect');
		}
    	const trainingIdId: string = this.route.snapshot.queryParamMap.get('Training');
		if (trainingIdId){
			this.onAssociatedValueSelected({"id":trainingIdId},'userAssignTrainingTrainingIdSelect');
		}
    	const farmIdId: string = this.route.snapshot.queryParamMap.get('Farm');
		if (farmIdId){
			this.onAssociatedValueSelected({"id":farmIdId},'userAssignTrainingFarmIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/user-assign-training');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userAssignTrainingUserIdSelect') || this.request.popupId==='userAssignTrainingUserIdPopup'){
			this.userAssignTraining.userId = selectedRecord.id;
			this.checkConditionToReload(this.users, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userAssignTrainingTrainingIdSelect') || this.request.popupId==='userAssignTrainingTrainingIdPopup'){
			this.userAssignTraining.trainingId = selectedRecord.id;
			this.checkConditionToReload(this.trainings, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='userAssignTrainingFarmIdSelect') || this.request.popupId==='userAssignTrainingFarmIdPopup'){
			this.userAssignTraining.farmId = selectedRecord.id;
			this.checkConditionToReload(this.farms, selectedRecord);
			return;
	    }
  	
	 }
}
