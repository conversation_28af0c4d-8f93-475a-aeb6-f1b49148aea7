import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersVideoProgressManager } from './usersvideoprogress-users.manager';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { Router } from '@angular/router';
import { UsersVideoProgress } from 'src/app/models/usersvideoprogress';
import { FilterParam } from 'src/app/models/filterparam';

@Component({
  selector: 'app-users-video-progress',
  templateUrl: './users-video-progress.component.html',
  styleUrls: ['./users-video-progress.component.scss']
})
export class UsersVideoProgressComponent extends BaseListServerSideComponent implements OnInit, OnD<PERSON>roy {
  @Input() getUserVideoProgressData: any | undefined;
  filterParam: FilterParam | undefined;
  constructor(protected usersVideoProgressManager: UsersVideoProgressManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService,
    protected router: Router, public commonUtil: CommonUtil) {
    super(usersVideoProgressManager, commonService, toastService, loadingService, router);
  }
  
  ngOnInit(){
    this.filterParam.trainingId = this.getUserVideoProgressData.id;
    this.init();
    this.records = new Array<UsersVideoProgress>();
  }
  
  ngOnDestroy(): void {
  }

}
