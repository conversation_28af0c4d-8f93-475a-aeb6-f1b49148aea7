// .admin-dasboard {
//     background-color: #FBFBFB;
//     padding: 0rem 2rem;

//     .card {
//         height: 160px;
//         padding: 2rem;
//         box-shadow: 0px 3px 6px #00000029;
//         border-radius: 31px;
//         border: 0;
//         cursor: pointer;

//         .card-logo {
//             border-radius: 10px;
//             padding: 16px;
//             width: 56px;
//             background-color: #256bc1;

//             .cross-icon {
//                 position: absolute;
//                 top: 20px;
//                 right: 15px;
//                 width: 60px;
//             }
//         }

//         .numeric {
//             width: 115px;
//             height: 130px;
//             font: normal normal 600 35px/86px Blinker;
//             letter-spacing: 0px;
//             color: #000;
//             opacity: 1;
//             white-space: nowrap;
//         }
//     }

//     .card:hover {
//         background: transparent linear-gradient(360deg, #07182F 0%, #1681FF 100%) 0% 0% no-repeat padding-box;

//         p,
//         a,
//         .numeric {
//             color: #fff;
//         }

//         //
//     }


//     .bg-color {
//         background: #fff;
//         opacity: 1;
//         box-shadow: none;
//     }
// }


// @media (min-width: 768px) {
//     .admin-dasboard {
//         .card {
//             .numeric {
//                 font: normal normal 600 45px/86px Blinker;
//             }
//         }

//     }
// }

// @media (min-width: 1400px) {
//     .admin-dasboard {
//         .card {
//             .numeric {
//                 font: normal normal 600 34px / 60px Blinker;
//             }
//         }
//     }
// }

// @media (min-width: 2044px) {
//     .admin-dasboard {
//         .card {
//             .numeric {
//                 font: normal normal 600 58px/86px Blinker;
//             }
//         }
//     }
// }

// .filter {
//     cursor: pointer;
//     border: 0;

//     // float: inline-end;
//     .filter-icon {
//         font-size: 26px;
//         font-weight: 600;
//         vertical-align: middle;
//     }
// }

@media (min-width: 1400px) {
    .inner-container {
        padding: 26px;
    }
}

.inner-container {
    background-color: #fff;
    padding: 18px;
}

.icon-shape {
    img {
        width: 48px;
    }
}

button.filter {
    outline: none;
    /* Removes the focus outline */
    border: none;
    /* Removes the border */
    box-shadow: none;
    /* Removes any box shadow on focus */
    transition: background-color 0.3s ease;
    /* Optional: Smooth background color transition */
}

button.filter:hover,
button.filter:focus {
    outline: none;
    /* Ensures no outline on hover or focus */
    border: none;
    /* Ensures no border on hover or focus */
    box-shadow: none;
    /* Removes focus shadow */
}


// .box-title {
//     font-size: 12px;
// }

.card-box-main {
    display: flex;
    justify-content: space-between;
}

select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

select option:disabled {
    color: #71828a !important;
}

// // dashboard chart css
// // .fixed-bar-colors .apexcharts-series:nth-child(1) .apexcharts-bar {
// //     fill: #FF5733 !important;
// // }

// // .fixed-bar-colors .apexcharts-series:nth-child(2) .apexcharts-bar {
// //     fill: #33FF57 !important;
// // }

// // .fixed-bar-colors .apexcharts-series:nth-child(3) .apexcharts-bar {
// //     fill: #3357FF !important;
// // }

// // .fixed-bar-colors .apexcharts-series:nth-child(4) .apexcharts-bar {
// //     fill: #FF33A8 !important;
// // }

// // .fixed-bar-colors .apexcharts-series:nth-child(5) .apexcharts-bar {
// //     fill: #A833FF !important;
// // }

.custom-card {
    border-radius: 12px;
    border: 0px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
    /* Adds the box shadow */
    transition: background 0.3s ease;
    /* Smooth transition for hover effect */
}

.custom-card:hover {
    background: linear-gradient(360deg, #07182F 0%, #1681FF 100%);
    color: white;
    /* Optional: Change text color to white for better contrast */
    box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4);
    /* Intensify box shadow on hover */
}


.select-options {
    color: #71828a !important
}

.custom-multiselect {
    ::ng-deep {
        .ng-select-container {
            height: auto !important;
            min-height: 28px !important;
        }
    }
}