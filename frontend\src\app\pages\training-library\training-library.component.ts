import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Input, Output, ViewChild } from '@angular/core';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { ToastService } from '../../shared/toast.service';
import { ManageTrainingLibraryManager } from './training-library.manager';
import { Category } from '../../models/category';
import { Router } from '@angular/router';
import { CommonUtil } from '../../shared/common.util';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { RestResponse } from 'src/app/shared/auth.model';
import { FilterParam } from 'src/app/models/filterparam';
import { UsersService } from 'src/app/services/users.service';
import AOS from 'aos';
import { ManageTrainingLibraryService } from './training-library.service';
import { Content } from '@angular/compiler/src/render3/r3_ast';
import { LearningSeries } from 'src/app/models/learningseries';
import { VideoTraining } from 'src/app/models/trainingLibrary';
import * as moment from 'moment';

declare const $: any;
declare var bootstrap: any;

@Component({
  selector: 'app-manage-training-library',
  templateUrl: './training-library.component.html',
  styleUrls: ['./training-library.component.scss']
})
export class TrainingLibraryComponent extends BaseListServerSideComponent implements OnInit, OnDestroy {
  isNoRecord: boolean = false;
  recordData: LearningSeries;
  farmIds: any = [];
  userIds: any = [];
  moment: any = moment;

  onClickValidation: boolean;
  restrictedUsersModal: any;
  restrictedUsersData: string;
  learningSeriesData: any[] = [];
  searchTraining: any;
  selectedTab: string; // Default selected tab is 'All'
  filterTrainingModal: any;
  trainingFilterData: any;
  showLearningSeriesTabs: boolean = false; // Flag to control the tab visibility


  constructor(protected ManageTrainingLibraryManager: ManageTrainingLibraryManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    protected router: Router, public commonUtil: CommonUtil, private usersService: UsersService, private ManageTrainingLibraryService:
      ManageTrainingLibraryService) {
    super(ManageTrainingLibraryManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<LearningSeries>();
    this.fetchTrainingLibrary();
    this.fetchLearningSeriesData();
    // this.fetchAuthorLearningSeriesData();
    this.fetchFiltereData()
  }


  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('filterTrainingModal');
      if (modalElement) {
        this.filterTrainingModal = new bootstrap.Modal(modalElement);
      }

      // Initialize Bootstrap tooltips
      document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((tooltip: HTMLElement) => {
        new bootstrap.Tooltip(tooltip);
      });
    }, 0);
  }

  // Method to select a tab
  selectTab(tabId: string) {
    if (tabId != 'All') {
      this.selectedTab = tabId;
      this.filterParam.learningSeries = tabId;
    }
    this.fetchTrainingLibrary()
  }

  onItemSelection(record: any) {
    this.onAssociatedValueSelected(record);
  }

  onCancel() {
    this.request.loadEditPage = false;
    if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
        dtInstance.destroy();
      });
    }
    this.init();
  }

  onNewRecord() {
    if (!this.isPlusButton) {
      if (this.filterParam) {
        this.router.navigate(['/dashboard/manage-learning-series/edit/0'], { queryParams: { [this.filterParam.relationTable]: this.filterParam.relationId } });
      } else {
        this.router.navigate(['/dashboard/manage-learning-series/edit/0']);
      }
      return;
    }
    this.request.loadEditPage = true;
  }

  removeSuccess() {
    this.onCancel();
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.fetchTrainingLibrary();
  }


  ngOnDestroy() {
    this.clean();
  }

  libraryTraining: VideoTraining[] = [];


  // async fetchTrainingLibrary() {
  //   try {
  //     const records = [];
  //     this.loadingService.show();
  //     const response: RestResponse = await this.ManageTrainingLibraryService.fetchAll(this.filterParam);
  //     this.loadingService.hide();
  //     if (!response.status) {
  //       this.toastService.error(response.message);
  //       return;
  //     }
  //     this.isNoRecord = records.length === 0;
  //     this.libraryTraining = response.data.map(item => new VideoTraining(item));
  //   } catch (error) {
  //     this.loadingService.hide();
  //     this.toastService.error(error.message);
  //   }
  // }

  async fetchTrainingLibrary() {
    this.loadingService.show();
    try {
      const response: RestResponse = await this.ManageTrainingLibraryService.fetchAll(this.filterParam);
  
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
  
      // Check if there are records and set the libraryTraining property
      this.isNoRecord = response.data.length === 0;
      this.libraryTraining = response.data.map(item => new VideoTraining(item));
  
    } catch (error) {
      this.toastService.error(error.message);
    } finally {
      this.loadingService.hide();
    }
  }
  

  async fetchAuthorLearningSeriesData(param: FilterParam) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.ManageTrainingLibraryService.fetchLearningSeriesAuthorBased(param);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.learningSeriesData = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }
  async fetchLearningSeriesData() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.ManageTrainingLibraryService.fetchLearningSeries(null);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.learningSeriesData = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  // filter listing data

  openFilterTrainingModal() {
    if (this.searchTraining) {
      this.searchTraining = "";
      delete this.filterParam.searchCommonTitle;
      this.onCancel();
    }
    AOS.init({ disable: true });
    this.filterTrainingModal.show();
  }

  onClickTrainingFilter(valid) {
    if (valid) {
      this.showLearningSeriesTabs = true; // Show the learning series tabs after filter is applied
      this.fetchTrainingLibrary()
      this.filterTrainingModal.hide()
    }
  }

  resetFilter() {
    this.filterParam = new FilterParam();
    this.showLearningSeriesTabs = false; // Hide the learning series tabs when filter is reset
    this.selectedTab = null;
    this.fetchTrainingLibrary();
    this.filterTrainingModal.hide();
  }

  async fetchFiltereData() {
    this.trainingFilterData = await this.ManageTrainingLibraryService.getTrainingFilterData(null);
  }

  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
    }
  }

  onAuthorChange(author: string) {
    let param = new FilterParam();
    param.author = author;
    this.fetchAuthorLearningSeriesData(param);
  }

}
