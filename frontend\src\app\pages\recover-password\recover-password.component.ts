import { Component, OnInit } from '@angular/core';
import { LoginService } from '../../services/login.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from '../../shared/toast.service';
import { LoadingService } from '../../services/loading.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { LocalStorageService } from 'angular-2-local-storage';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

@Component({
  selector: 'app-recover-password',
  templateUrl: './recover-password.component.html',
  styleUrls: ['./recover-password.component.scss']
})
export class RecoverPasswordComponent implements OnInit {
  data: any;
  onClickValidation: boolean;
  forgotPasswordUsername: any;
  changePasswordButtonDisabled: boolean = false;
  passwordFieldType: any;

  constructor(private loginService: LoginService, private router: Router, private toastService: ToastService,
    private loadingService: LoadingService, private route: ActivatedRoute, private localStorageService: LocalStorageService) {
    this.onClickValidation = false;
    this.data = {} as any;
  }

  ngOnInit() {
    this.passwordFieldType = "password";
    this.forgotPasswordUsername = this.localStorageService.get('forgotPasswordUserName') as any;
    this.data = { userName: this.forgotPasswordUsername.userName }
  }

  resetPassword(bol) {
    this.onClickValidation = !bol;
    if (!bol) {
      return false;
    }
    if (this.data.password !== this.data.confirmPassword) {
      this.onClickValidation = true;
      return;
    }
    this.changePasswordButtonDisabled = true;
    const { confirmPassword, ...rest } = this.data;
    this.loadingService.show();
    this.loginService.recoverPassword(rest)
      .then((data) => {
        this.loadingService.hide();
        if (!data.status) {
          this.changePasswordButtonDisabled = false;
          this.toastService.error(data.message);
          return;
        }
        this.toastService.success(data.message);
        this.router.navigate(['/login']);
      }, (error) => {
        this.loadingService.hide();
        this.changePasswordButtonDisabled = false;
        this.toastService.error(error.message);
      });
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  validateFirstLetterCapitalInput(event: KeyboardEvent): void {
    const input = event.target as HTMLInputElement;
    const currentValue = input.value + event.key;

    // Only allow the first character to be uppercase
    if (currentValue.length === 1 && !/[A-Z]/.test(event.key)) {
      event.preventDefault();
    }
  }

  validateFirstLetterCapital(password: string): boolean {
    return /^[A-Z]/.test(password);
  }

  validateIncludesSymbol(password: string): boolean {
    return /[!@#$%^&*(),.?":{}|<>]/.test(password);
  }

  validateIncludesNumber(password: string): boolean {
    return /\d/.test(password);
  }

  validateMinLength(password: string): boolean {
    return password.length >= 8;
  }

  validateIncludesLowercase(password: string): boolean {
    return /[a-z]/.test(password);
  }

  validatePassword(password: string): boolean {
    return this.validateFirstLetterCapital(password) &&
      this.validateIncludesSymbol(password) &&
      this.validateIncludesNumber(password) &&
      this.validateMinLength(password) &&
      this.validateIncludesLowercase(password);
  }

}
