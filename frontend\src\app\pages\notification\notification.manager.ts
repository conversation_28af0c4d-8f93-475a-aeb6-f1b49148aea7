import { Injectable } from "@angular/core";
import { BaseManager } from "src/app/config/base.manager";
import { LoadingService } from "src/app/services/loading.service";
import { ToastService } from "src/app/shared/toast.service";
import { NotificationService } from "./notification.service";

@Injectable({
    providedIn: 'root'
})

export class NotificationManager extends BaseManager {
    constructor(protected notificationService: NotificationService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(notificationService, loadingService, toastService);
    }
    
}