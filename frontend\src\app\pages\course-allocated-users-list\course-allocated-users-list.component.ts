import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { CourseAllocatedUsersListService } from './course-allocated-users-list.service';
import { CourseAllocatedUsersList } from 'src/app/models/course-allocated-users-list';
import { CourseAllocatedUsersListManager } from './course-allocated-users-manager';
import { RestResponse } from 'src/app/shared/auth.model';
import AOS from 'aos';
import { ManageCourseUserService } from '../program-users/manage-course-users-service';
import { Course } from 'src/app/models/course';
import { ManageCourseService } from '../manage-course/manage-course-service';
import { BaseListServerSideComponent } from '../../config/base.list.server.side.component';
import { CourseTrainingsService } from '../course-trainings/course-trainings-service';
import { RouteDataService } from 'src/app/shared/title.service';
import { ManageCourseManager } from '../manage-course/manage-course-manager';
import { FilterParam } from 'src/app/models/filterparam';
import { FarmService } from '../farm/farm.service';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-course-allocated-users-list',
  templateUrl: './course-allocated-users-list.component.html',
  styleUrls: ['./course-allocated-users-list.component.scss']
})
export class CourseAllocatedUsersListComponent extends BaseListServerSideComponent implements OnInit {
  onClickValidation = false;
  selectedInviteUserCourse: string;
  selectedCourseId: any;
  dropdownSettings = {};
  publishCourseList: any[];
  inviteUserAllocatedCourseModal: any;
  insertedData: { username: string, email: string }[] = [];
  courses: Course;
  Courses: { username: string, email: string } = { username: '', email: '' };
  errorMessage: string = '';
  loginBaseUrl: boolean = true; // Set this based on your logic
  username: any;
  course: any;
  // Farm/Individual logic
  isFarmInvite: boolean = true;
  farms: any[] = []; // Populate this with your farm data (fetch in ngOnInit or as needed)
  selectedFarmIds: string[] = [];
  farmValidation = false;
  individualValidation = false;

  constructor(protected courseAllocatedUsersListManager: CourseAllocatedUsersListManager, protected toastService: ToastService,
    protected loadingService: LoadingService, protected commonService: CommonService, public authService: AuthService,
    public courseTrainingsService: CourseTrainingsService, protected router: Router, public commonUtil: CommonUtil,
    private courseAllocatedUsersListService: CourseAllocatedUsersListService, public routeDataService: RouteDataService,
    protected route: ActivatedRoute, private manageCourseUserService: ManageCourseUserService, private manageCourseService: ManageCourseService,
    protected manageCourseManager: ManageCourseManager, protected farmService: FarmService) {
    super(courseAllocatedUsersListManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    console.log("ngOnInit");
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
    });

    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseAllocatedUsersList>();
    this.courses = new Course();
    this.publishCourseList = new Array();
    this.filterParam.course = this.route.snapshot.paramMap.get('id');
    this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    console.log(this.selectedCourseId, "selectedCourseId");
    //this.fetchCourseUsersData();
    this.fetchCourseDetailsRecord(this.selectedCourseId);
    this.init()
    // this.loadCourseTitle();
  }


  ngAfterViewInit() {
    setTimeout(() => {
      this.inviteUserAllocatedCourseModal = new bootstrap.Modal(
        document.getElementById('inviteUserAllocatedCourseModal')
      );
    }, 0)
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    this.fetchCourseUsersData();
    this.refreshRecord();
  }

  async fetchCourseUsersData() {
    const resp: RestResponse = await this.courseAllocatedUsersListService.CourseAllocatedUsers(this.filterParam);
    this.loadingService.hide();
    this.onFetchCompleted();
    if (!resp.status) {
      return;
    }
    this.records = resp.data;
  }

  //Invite user modal

  async fetchPublishRecords() {
    try {
      this.loadingService.show();
      const filterParam = new FilterParam();
      filterParam.isSelf = false;

      const response: RestResponse = await this.manageCourseService.getAvailablePublishedCourses(filterParam);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
      // Only set selectedCourseId if it matches a course in the list
      if (this.selectedCourseId && this.publishCourseList.some(x => x.id == this.selectedCourseId)) {
        // keep it
      } else {
        this.selectedCourseId = null;
      }
      this.courses.id = this.selectedCourseId;

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  selectInviteUserCourse(event: any) {
    if (event) {
      this.selectedInviteUserCourse = event.title;
    }
    this.FetchFarms();
  }

  openInviteCourseUserModal() {
    this.fetchPublishRecords();
    AOS.init({ disable: true });
    this.inviteUserAllocatedCourseModal.show();
    this.courses = new Course();
  }

  async FetchFarms() {
    // need to move to if check as we need farms detail for super admin
    this.filterParam = new FilterParam();
    this.filterParam.relationId = this.selectedCourseId;
    this.filterParam.relationTable = 'COURSE';
    const farms: RestResponse = await this.farmService.fetchAvailableFarms(this.filterParam);
    this.farms = farms.data;
    this.farms.forEach(farm => {
      farm.displayLabel = farm.farmCode.trim() + ' - ' + farm.name;
    });
  }

  async onClickInviteUserCourse(form: any) {
    this.onClickValidation = true;
    this.farmValidation = false;
    this.individualValidation = false;

    // Ensure course is selected before proceeding
    if (!form?.controls?.chooseCourse?.valid) {
      return;
    }

    if (this.isFarmInvite) {
      if (!this.selectedFarmIds || this.selectedFarmIds.length === 0) {
        this.farmValidation = true;
        return;
      }
      this.courses.courseEnrolled = this.selectedFarmIds.map(id => ({ farmId: id }));
    } else {
      const shouldValidateForm = this.insertedData.length === 0;
      if (shouldValidateForm && !form.valid) {
        this.individualValidation = true;
        return;
      }
      if (this.insertedData.length === 0) {
        this.toastService.error('Please add at least one user.');
        return;
      }
      this.courses.courseEnrolled = this.insertedData;
    }
    this.onClickValidation = false;
    try {
      this.loadingService.show();
      this.courses.id = this.selectedCourseId;
      if (this.authService.getRoles().includes('ROLE_ADMIN')) {
        this.courses.programAdmin = this.authService.getUser().id;
      }
      const response: RestResponse = (this.authService.getRoles().includes('ROLE_ADMIN')) ? await this.manageCourseUserService.sendAdminCourseInvite(this.courses)
        : await this.manageCourseUserService.sendCourseInvite(this.courses);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.insertedData = [];
      this.courses = new Course();
      this.inviteUserAllocatedCourseModal.hide();
      this.onSaveSuccess(response.message);
      this.fetchCourseUsersData();
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.router.navigate(['/dashboard/program-admin/courses']);
  }

  insertData(form: any, id: any) {
    if (this.isFarmInvite) {
      this.farmValidation = !form.valid;
      if (!form.valid) {
        return;
      }
      // Farm-specific logic (if any) can go here
    } else {
      this.individualValidation = !form.valid;
      if (!form.valid) {
        return;
      }
      const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,4}$/;
      if (!emailPattern.test(this.courses.email)) {
        this.errorMessage = 'Invalid email format.';
        return;
      }
      const isDuplicate = this.insertedData.some(data => data.email === this.courses.email);
      if (isDuplicate) {
        this.errorMessage = 'This email is already entered.';
      } else {
        this.insertedData.push({ username: this.courses.username, email: this.courses.email });
        this.errorMessage = '';
        this.courses = new Course();
        this.individualValidation = false; // Reset error flag
      }
    }
  }

  closeInviteModal() {
    this.inviteUserAllocatedCourseModal.hide();
    this.courses = new Course();
  }

  removeData(index: number) {
    this.insertedData.splice(index, 1);
    this.errorMessage = '';  // Clear error message
  }

  removeSuccess() {
    this.fetchCourseUsersData();
    // this.onCancel();
  }

  clearErrorMessage() {
    this.errorMessage = '';
  }


  // loadCourseTitle(): void {
  //   const trainingId: string | null = this.route.snapshot.paramMap.get("id");
  //   if (trainingId) {
  //     this.courseTrainingsService.getcourseTitle(trainingId).subscribe(response => {
  //       const title = response?.data?.title || 'Allocated Users List';
  //       this.routeDataService.setData(this.router.url, title);
  //     });
  //   }
  // }

  //Breadcrumb set
  onFetchCompleted() {


  }
  async fetchCourseDetailsRecord(id) {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.manageCourseManager.getsCourseDetails(id);
      this.loadingService.hide();
      this.onFetchCompleted();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.course = response.data;
      this.setBreadcrumb();

      // this.layoutComponent.getBreadcrumb()

    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  setBreadcrumb() {
    //for course detail page
    const fullUrl = `/dashboard/program-admin/course/detail/${this.course.id}`
    this.routeDataService.addBreadcrumb(this.course.title, fullUrl, this.course.id)
    this.setBreadCrumbCourseCompletion();

  }

  //set breadcrumb for course completion page
  setBreadCrumbCourseCompletion() {
    const fullUrl = this.router.url; // Current full URL
    const queryIndex = fullUrl.indexOf('?'); // Find query parameter start
    const cleanUrl = queryIndex > -1 ? fullUrl.slice(0, queryIndex) : fullUrl;
    const title = 'Course Completion';
    this.routeDataService.addBreadcrumb(title, cleanUrl, this.course.id)
  }

}
