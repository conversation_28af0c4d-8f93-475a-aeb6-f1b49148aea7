import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
  selector: 'app-phone-input',
  templateUrl: './phone-input.component.html',
  styleUrls: ['./phone-input.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PhoneInputComponent),
      multi: true
    }
  ]
})
export class PhoneInputComponent implements ControlValueAccessor {
  @Input() required = false;
  @Input() disabled = false;
  @Input() placeholder = 'Phone Number';
  @Input() onClickValidation = false;
  @Input() ngModel: string = '';
  @Output() ngModelChange = new EventEmitter<string>();
  @Output() valueChange = new EventEmitter<string>();

  value: string = '';
  isTouched = false;
  isDisabled = false;
  showInvalid = false;

  onChange = (value: string) => {};
  onTouched = () => {};

  writeValue(value: string): void {
    this.value = value;
    this.ngModel = value;
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  handleInput(event: any) {
    this.value = event;
    this.ngModel = event;
    this.onChange(event);
    this.ngModelChange.emit(event);
    this.valueChange.emit(event);
    this.checkValidity();
  }

  handleError(event: boolean) {
    this.showInvalid = !event;
  }

  checkValidity() {
    const digits = (this.value || '').replace(/\D/g, '');
    this.showInvalid = digits.length > 15;
  }

  isFieldInvalid(): boolean {
    // Invalid if required and empty, or if showInvalid is set by ng2-tel-input
    return (
      (this.required && (!this.value || this.value.trim() === '')) ||
      this.showInvalid
    );
  }
} 