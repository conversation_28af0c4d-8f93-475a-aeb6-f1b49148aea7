<div class="fixed-menu d-none container-fluid p-0">
  <nav id="navbar" class="navbar navbar-expand-lg navbar-light container-xxl">
    <div class="container-fluid">
      <button class="navbar-toggler bg-light border border-dark" type="button" data-bs-toggle="collapse"
        data-bs-target="#navbarTogglerDemo02" aria-controls="navbarTogglerDemo02" aria-expanded="false"
        aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <a class="navbar-brand site-staff-logo d-sm-block d-lg-none" href="#">
        <img src="/assets/images/logo.png" class="img-fluid" alt="">
      </a>
      <div class="collapse navbar-collapse" id="navbarTogglerDemo02">
        <a class="navbar-brand" [routerLink]="['/']"><img src="/assets/images/staff-logo.png"
            class="img-fluid d-lg-block d-none" alt="">
        </a>
        <ul class="navbar-nav ms-auto mb-3 mb-lg-0 flex-nowrap">
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" aria-current="page" href="#home">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#aboutUs">About Us</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#service">Services</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#Contact-Us">Contact Us</a>
          </li>
          <li class="nav-item mt-1">
            <!-- <a class="nav-link m-lg-3 fs-5" href="#Contact-Us"> Pre-Register</a> -->
            <button class="btn bg-secondary text-light staff-login-button-fixed fs-5"
              (click)="scrollToContact()">Pre-Register
            </button>
          </li>
          <!-- <li class="nav-item">
            <a [routerLink]="['/register']" class="nav-link m-lg-3 fs-5" href="#/register">Sign
              Up</a>
          </li>
          <li class="nav-item">
            <button [routerLink]="['/login']"
              class="btn bg-secondary text-light staff-login-button-fixed fs-5">Login</button>
          </li> -->
        </ul>
      </div>
    </div>
  </nav>
</div>
<div class="staff-homepage container-xxl" id="home">
  <nav id="navbar" class="navbar navbar-expand-lg navbar-dark image-menus d-lg-block d-sm-none">
    <div class="container-fluid">
      <a class="navbar-brand site-staff-logo d-sm-block d-lg-none" [routerLink]="['/']">
        <img src="/assets/images/logo.png" class="img-fluid" alt="">
      </a>
      <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <a class="navbar-brand" href="#"><img src="/assets/images/staff-logo.png" class="img-fluid d-lg-block d-none"
            alt="">
        </a>
        <ul class="navbar-nav ms-auto mb-3 mb-lg-0 flex-nowrap">
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" aria-current="page" href="#home">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#aboutUs">About Us</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#service">Services</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#Contact-Us">Contact Us</a>
          </li>
          <li class="nav-item">
            <a class="nav-link m-lg-3 fs-5" href="#Contact-Us">Pre-Register</a>
          </li>
          <!-- <li class="nav-item">
            <a [routerLink]="['/register']" class="nav-link m-lg-3 fs-5" href="#/register">Sign
              Up</a>
          </li> -->
          <!-- <li class="nav-item">
            <button [routerLink]="['/login']"
              class="btn bg-secondary m-lg-3 text-light staff-login-button fs-5">Login</button>
          </li> -->
        </ul>
      </div>
    </div>
  </nav>
  <div data-aos="zoom-in" data-aos-duration="1000" class="header" id="slider_section">
    <div class="staff-homepage-nav">
      <div class="staff-homepage-content col-12 d-lg-block d-none">
        <h1 class="content-heading text-light">Agriculture's Skills Training Platform
        </h1>
        <p class="text-light pt-3 fs-4">Integrax is the world's first integrated learning platform dedicated to
          Agriculture.
        </p>
        <!-- <button class="btn get-start-btn bg-light">GET STARTED<i class="bi bi-arrow-right mx-2"></i></button> -->
      </div>
    </div>
  </div>
  <div class="staff-homepage-m d-sm-block text-center d-lg-none">
    <h1 class="content-heading text-light">Agriculture's Skills Training Platform</h1>
    <p class="text-light content-sub-heading pt-3">Integrax is the world's first integrated learning platform dedicated
      to
      Agriculture.
    </p>
    <!-- <button class="btn get-start-btn mt-3 bg-light">GET STARTED<i class="bi bi-arrow-right mx-2"></i></button> -->
  </div>
  <!-- about us -->
  <div class="offset-sm-1 col-sm-10" id="aboutUs">
    <div class="staff-about-us">
      <h6 class="text-secondary text-center pb-5 fs-4 fw-bold about-heading">ABOUT US</h6>
      <div class="row">
        <div data-aos="flip-left" data-aos-easing="ease-out-cubic" data-aos-duration="2000" class="col-12 col-lg-6">
          <h1 class="fw-bold about-sub-heading">Built by Ag Professionals, <br>
            for Ag Professionals.</h1>
        </div>
        <div class="col-12 col-lg-1">
          <div class="line d-lg-block d-none"></div>
        </div>
        <div data-aos="fade-right" data-aos-offset="300" data-aos-easing="ease-in-sine" class="col-12 col-lg-5">
          <p class="about-us-content">We're reinventing the Ag Learning landscape
            through <br> workflow focused, mobile first, video emulation.
          </p>
          <!-- <button class="btn about-us-btn bg-dark text-light">GET STARTED</button> -->
        </div>
      </div>
    </div>
  </div>
  <!-- what-we-do -->
  <div class="what-we-do-section" id="service">
    <div class="what-we-do-heading text-center">
      <h6 class="what-we-do fs-4 text-secondary">WHAT WE DO</h6>
      <h1 class="what-we-do-msg pt-3">Improving effectiveness of skills training programs and compliance</h1>
      <p class="what-we-do-sub-msg pt-3">Businesses and NGO programs need an effective and efficient mechanism for
        training. <br> Be that for onboarding or evidence for ESG, CSR, Compliance & Brand Commitment
      </p>
    </div>
    <div class="row">
      <div data-aos="zoom-in" data-aos-duration="1000" class="col-12 col-md-6 col-lg-6 col-xl-3 adjust-div">
        <div class="card">
          <div class="card-logo">
            <img src="/assets/images/icons/menu/icon_learning.svg" alt="" class="img-fluid">
          </div>
          <div class="card-content">
            <p class="fw-bold content-heading mb-0">Emulation Learning and Assessment</p>
            <p class="mb-0 content-heading-msg pt-3">We aren't an LMS or eLearning we are a video emulation
              platform. Think of it like Tik Tok for learning. "See-one, Do-one, Teach-one". Easy...</p>
          </div>
        </div>
      </div>
      <div data-aos="zoom-in" data-aos-duration="1000" class="col-12 col-md-6 col-lg-6 col-xl-3 adjust-div">
        <div class="card">
          <div class="card-logo">
            <img src="/assets/images/icons/menu/icon_reporting.svg" alt="" class="img-fluid">
          </div>
          <div class="card-content">
            <p class="fw-bold content-heading mb-0">Competence and
              Compliance Reporting</p>
            <p class="mb-0 content-heading-msg pt-3">Seamlessly track and record training
              compliance for your employees.
              members or clients.
            </p>
          </div>
        </div>
      </div>
      <div data-aos="zoom-in" data-aos-duration="1000" class="col-12 col-md-6 col-lg-6 col-xl-3 adjust-div">
        <div class="card">
          <div class="card-logo">
            <img src="/assets/images/icons/menu/teaching.svg" alt="" class="img-fluid">
          </div>
          <div class="card-content">
            <p class="fw-bold content-heading mb-0">Access to Ag Video Library</p>
            <p class="mb-0 content-heading-msg pt-3">Access a comprehensive library of
              publicly available Ag training videos for
              your training content.</p>
          </div>
        </div>
      </div>
      <div data-aos="zoom-in" data-aos-duration="1000" class="col-12 col-md-6 col-lg-6 col-xl-3 adjust-div">
        <div class="card">
          <div class="card-logo">
            <img src="/assets/images/icons/menu/icon_consulting.svg" alt="" class="img-fluid">
          </div>
          <div class="card-content">
            <p class="fw-bold content-heading mb-0">Content Creation or Editing</p>
            <p class="mb-0 content-heading-msg pt-3">If you want bespoke training content
              developed or edited then contact us
              and we can help.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- contact-us-form -->
  <div class="contact-us-form mt-5" id="Contact-Us">
    <h1 class="text-center text-secondary pb-5 fs-3 fw-bold">CONTACT US</h1>
    <form #captchaProtectedForm="ngForm" class="contact-us">
      <div class="offset-md-3 col-md-6">
        <div class="row">
          <div class="col-12 col-md-12 col-lg-6 mb-4">
            <div class="form-floating">
              <input type="text" class="form-control" id="floatingInput" #firstName="ngModel"
                [(ngModel)]="user.firstName" required="required" placeholder="First Name" name="firstName"
                [ngClass]="{'is-invalid':!firstName.valid && onClickValidation}">
              <label for="floatingInput">First Name</label>
            </div>
          </div>
          <div class="col-12 col-md-12 col-lg-6 mb-4">
            <div class="form-floating">
              <input type="text" class="form-control" id="floatingInput1" #lastName="ngModel"
                [(ngModel)]="user.lastName" required="required" placeholder="First Name" name="lastName"
                [ngClass]="{'is-invalid':!lastName.valid && onClickValidation}">
              <label for="floatingInput1">Last Name</label>
            </div>
          </div>
          <div class="col-12 col-md-12 col-lg-6 mb-4">
            <div class="form-floating">
              <input pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" type="email" class="form-control"
                id="floatingInput2" placeholder="<EMAIL>" #email="ngModel" [(ngModel)]="user.email"
                required="required" placeholder="First Name" name="email"
                [ngClass]="{'is-invalid':!email.valid && onClickValidation}">
              <label for="floatingInput2">Email Address</label>
            </div>
          </div>
          <div class="col-12 col-md-12 col-lg-6 mb-4">
            <div class="form-floating">
              <input autocomplete="off" class="form-control opacity-75" type="text" name="phone" ng2TelInput
                (intlTelInputObject)="telInputObject($event)" (ng2TelOutput)="getNumber($event)" #phone="ngModel"
                [(ngModel)]="user.phoneNumber" [ngClass]="{'is-invalid':!phone.valid && onClickValidation}"
                required="required" placeholder="Phone Number" minlength="7" maxlength="12" pattern="^[0-9]+$"
                (countryChange)="onCountryChange($event)">
            </div>
          </div>
          <div class="col-12 col-md-12 col-lg-12 mb-4">
            <div class="form-floating">
              <textarea class="form-control" placeholder="Leave a comment here" id="floatingTextarea2"
                #message="ngModel" [(ngModel)]="user.message" required="required" name="message"
                [ngClass]="{'is-invalid':!message.valid && onClickValidation}" maxlength="250"></textarea>
              <label for="floatingTextarea2">Message</label>
            </div>
          </div>
          <div class="col-12 col-md-12 col-lg-6 mb-5">
            <div class="form-group">
              <re-captcha (resolved)="checkCaptcha($event)" name="recaptchaReactive" ngModel
                siteKey="6LcqGPkhAAAAAFOEIRD3fnUPOVagA2_JtXVgmh5M">
              </re-captcha>
            </div>
          </div>
          <div class="col-12 col-md-12 col-lg-6 mb-5">
            <div class="text-end">
              <button [disabled]="submitButton == 'Please Wait...' ? true : false "
                (click)="save(captchaProtectedForm.form.valid)" type="button"
                class="btn btn-secondary site-button btn-sm fs-4 large-button save-button rounded-3">{{submitButton}}
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <!-- ios and android Device info section -->
  <div class="device-container">
    <div class="download-app-header text-center">
      <h1>Download Integrax Today</h1>
      <p>
        Ready to get informed, Training platform, and Learn smart? Download the app to get started.
      </p>
    </div>
    <div class="device-info">
      <a href="https://play.google.com/store/apps/details?id=co.integrax.app" target="_blank" class="download-button">
        <img src="/assets/images/icons\menu\android-logo.svg" alt="Google Store" class="icon">
        <div>
          <span>Download on</span>
          <strong>Google Store</strong>
        </div>
      </a>
      <a href="https://apps.apple.com/us/app/integrax/id6478096459" target="_blank" class="download-button">
        <img src="/assets/images/icons\menu\apple-logo.svg" alt="Apple Store" class="icon">
        <div>
          <span>Download on</span>
          <strong>Apple Store</strong>
        </div>
      </a>
    </div>
  </div>
  <!--  -->
  <!--footer-->
  <div class="staff-homepage-footer">
    <div class="row">
      <div class="col-12 text-center">
        <div>
          <img src="/assets/images/logo.png" class="img-fluid" alt="">
        </div>
      </div>
      <div class="col 12">
        <div class="footer-menu">
          <ul class="d-flex menu text-center list-unstyled mt-5">
            <li class="mx-4 fs-4">
              <a href="#home">HOME</a>
            </li>
            <li class="mx-4 fs-4">
              <a href="#aboutUs">ABOUT US</a>
            </li>
            <li class="mx-4 fs-4">
              <a href="#service">SERVICES</a>
            </li>
            <!-- <li class="mx-4 fs-4">
              <a href="/register">SIGN UP</a>
            </li> -->
            <li class="mx-4 fs-4">
              <a href="#Contact-Us">CONTACT US</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="col-12">
        <div class="footer-icon d-flex justify-content-center mt-5">
          <a href="https://www.instagram.com/invites/contact/?i=8a40mv8ut9dn&utm_content=pfwdjyg" class="icons mx-4"
            target="_blank"><i class="bi bi-instagram"></i></a>
          <a href="https://www.facebook.com/integrax.sg/" class="icons mx-4" target="_blank"><i
              class="bi bi-facebook"></i></a>
          <a href="https://www.linkedin.com/company/integrax-pte-ltd/" class="icons mx-4" target="_blank"><i
              class="bi bi-linkedin"></i></a>
        </div>
      </div>
      <div class="col-12">
        <div class="d-flex justify-content-center mt-5">
          <a class="font-17px" target="_blank" href="/terms-and-conditions">Terms of Service</a>
          <span class="mx-2">|</span>
          <a class="font-17px" target="_blank" href="/privacy-policy">Privacy Policy</a>
        </div>
      </div>
      <div class="footer-copyright">
        <div class=" row text-center margin-top-40">
          <div class="col-12 col-lg-3">
            <a target="_blank" href="https://www.iotasol.com/">
              <h4 class="copyright-content">Powered by<span class="text-secondary fw-bold">
                  Iotasol</span>
              </h4>
            </a>
          </div>
          <div class="col-12 col-lg-5">
            <h4 class="intergrax-sub-content">Integrax is owned by Alta Food and Agriculture Pte Ltd (Singapore)</h4>
          </div>
          <div class="col-12 col-lg-4">
            <h4 class="copyright-sub-content"><span class="fw-bold">Copyright © {{currentYear}}
                integrax.</span> All
              Rights reserved</h4>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>