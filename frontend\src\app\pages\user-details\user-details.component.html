<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">
  <div class="user-details-section">
    <div class="row">
      <div class="col-12 mb-3">
        <div class="user-details-section">
          <ul class="nav nav-pills">
            <li class="nav-item bg-secondary user-details-btn width-180px"
              [ngClass]="{'bg-secondary': userDetailsTabs == 'profile'}" (click)="onClickUserDetailsTab('profile')">
              <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'profile'}"
                aria-current="page">Profile</a>
            </li>
            <li class="nav-item user-details-btn width-180px"
              [ngClass]="{' bg-secondary': userDetailsTabs == 'training'}" (click)="onClickUserDetailsTab('training')">
              <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'training'}">My
                Training</a>
            </li>
            <li class="nav-item user-details-btn width-180px" [ngClass]="{' bg-secondary': userDetailsTabs == 'course'}"
              (click)="onClickUserDetailsTab('course')">
              <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'course'}">My Course</a>
            </li>
            <li class="nav-item user-details-btn width-180px" [ngClass]="{' bg-secondary': userDetailsTabs == 'moment'}"
              (click)="onClickUserDetailsTab('moment')">
              <a class="btn nav-link" [ngClass]="{' active bg-secondary': userDetailsTabs == 'moment'}">My Moment</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="row" *ngIf="userDetailsTabs !== 'profile' && userDetailsTabs !== 'moment'">
      <div class="col-12 col-sm-4 text-start">
        <div class="custom-input-group">
          <input class="form-control search-form-control" placeholder="Search" appDelayedInput
            (delayedInput)="search($event)" [(ngModel)]="filterParam.searchText" [delayTime]="1000">
          <i class="bi bi-search pe-3"></i>
        </div>
      </div>
      <div class="col-12 col-sm-4">
      </div>
      <div class="col-12 col-sm-4 text-end pe-0 mb-2">
        <button (click)="openCourseDetailFilter()" type="button"
          class="btn manage-filter-buttton bg-dark text-light btn-lg filter-button-cls font-15px height-51px">
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
        </button>
      </div>
    </div>
  </div>
  <app-users-edit *ngIf="userDetailsTabs == 'profile'" [userDetails]="userDetails"></app-users-edit>
  <app-program-training-history *ngIf="userDetailsTabs == 'training'" [filterParam]="filterParam">
  </app-program-training-history>

  <app-user-my-course *ngIf="userDetailsTabs == 'course'" [filterParam]="courseFilterParam"></app-user-my-course>

  <app-moment *ngIf="userDetailsTabs == 'moment'" [userDetails]="userDetails" [momentsByUserId]="true"></app-moment>
  <!-- Filter Training/Course Modal -->
  <div class="modal fade" id="adminfilterCourseUserDetail" tabindex="-1"
    aria-labelledby="adminfilterCourseUserDetailLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="adminfilterCourseUserDetailLabel">Filter Course</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div *ngIf="adminfilterCourseUserDetail && adminfilterCourseUserDetail._isShown" class="modal-body">
          <form #FilterForm="ngForm" novalidate="novalidate">
            <div class="userMyCourseFilter" *ngIf="userDetailsTabs == 'course'">
              <div class="form-floating mb-4 w-100">
                <input maxlength="80" [ngClass]="{'is-invalid':!title.valid && onClickValidation}" class="form-control"
                  type="text" name="title" #title="ngModel" [(ngModel)]="courseFilterParam.courseTitle"
                  required="required" placeholder="{{'Course.title' | translate}}">
                <label for="floatingInput">{{"Course.title" | translate}}</label>
                <app-validation-message [field]="title" [onClickValidation]="onClickValidation">
                </app-validation-message>
              </div>

              <div class="form-floating mb-3">
                <select class="form-select form-control" name="status" aria-label="Please Select status"
                  [(ngModel)]="courseFilterParam.isCompleted" (change)="updateStatus($event)"
                  [ngClass]="{'is-invalid': !status.valid && onClickValidation}" required="required" #status="ngModel">
                  <option [ngValue]="undefined" disabled>Select Option</option>
                  <option value="false">IN PROGRESS</option>
                  <option value="true">COMPLETED</option>
                </select>
                <label for="type">{{"SubCategory.status" | translate}}</label>
              </div>
              <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="courseFilterParam.startDate" [toDateInput]="courseFilterParam.endDate">
              </app-date-range-filter>
            </div>
            <div class="trainingHistoryFilter" *ngIf="userDetailsTabs == 'training'">
              <div class="form-floating mb-4 w-100">
                <input maxlength="80" [ngClass]="{'is-invalid':!courseTrainingTitle.valid && onClickValidation}"
                  class="form-control" type="text" name="courseTrainingTitle" #courseTrainingTitle="ngModel"
                  [(ngModel)]="filterParam.courseTitle" required="required"
                  placeholder="{{'Course.title' | translate}}">
                <label for="floatingInput">{{"Course.title" | translate}}</label>
                <app-validation-message [field]="title" [onClickValidation]="onClickValidation">
                </app-validation-message>
              </div>
              <div class="form-floating">
                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                  [ngClass]="{'is-invalid':learningSeriesId.invalid && onClickValidation}">
                  <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="learningSeriesId"
                    [items]="trainingFilterData.data[0].learningSeriesDetail" bindLabel="title" bindValue="id"
                    class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.learningSeries"
                    #learningSeriesId="ngModel">
                  </ng-select>
                </div>
                <label for="language">{{"Training.learningSeries" | translate}}</label>
              </div>
              <div class="form-floating">
                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                  [ngClass]="{'is-invalid':!contentTypeId.valid && onClickValidation}">
                  <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="contentTypeId"
                    [items]="trainingFilterData.data[0].contentTypeDetail" bindLabel="title" bindValue="id"
                    class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.contentType"
                    #contentTypeId="ngModel">
                  </ng-select>
                </div>
                <label for="language">{{"Training.contentTypes" | translate}}</label>
              </div>
              <div class="form-floating">
                <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                  [ngClass]="{'is-invalid':!trainingStatus.valid && onClickValidation}">
                  <ng-select placeholder="{{'COMMON.SELECT_OPTION' | translate}}" name="trainingStatus"
                    [items]="trainingFilterData.data[0].trainingStatusDetail" bindLabel="status" bindValue="id"
                    class="custom-multiselect form-control padding-bottom-8" [(ngModel)]="filterParam.status"
                    #trainingStatus="ngModel">
                  </ng-select>
                </div>
                <label for="language">{{"Training.TrainingStatus" | translate}}</label>
              </div>
              <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="filterParam.startDate" [toDateInput]="filterParam.endDate">
              </app-date-range-filter>
            </div>
            <div class="modal-footer">
              <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary me-2">Reset</button>
              <button (click)="onClickCourseDetailFilter(FilterForm.form.valid)" type="button"
                class="btn btn-primary">Filter</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>