import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DashboardService } from 'src/app/services/dashboard.service';
import { LoadingService } from 'src/app/services/loading.service';
import { LoginService } from 'src/app/services/login.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { AuthService } from 'src/app/shared/auth.services';
import { ToastService } from 'src/app/shared/toast.service';

declare const $: any;
@Component({
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss']
})
export class LandingComponent implements OnInit {
  dashboardData: any;
  showFarmAdminPasswordPopup: boolean = false;
  farmAdminNewPassword: string = '';
  farmAdminConfirmPassword: string = '';
  onClickValidation: boolean = false;
  newPasswordFieldType: string = 'password';
  confirmPasswordFieldType: string = 'password';
  user: any;

  constructor(
    private readonly dashboardService: DashboardService,
    private readonly toastService: ToastService,
    public authService: AuthService,
    protected router: Router,
    protected loginService: LoginService,
    protected loadingService: LoadingService
  ) { }

  ngOnInit() {
    if (!this.authService.hasRole(['ROLE_PROGRAM_ADMINISTRATOR'])) {
      this.fetchDashboardData('/api/dashboard');
    }

    this.user = this.authService.getUser();
    // Check for farm admin password popup flag
    if (this.user.isFirstLogin) {
      this.showFarmAdminPasswordPopup = true;
      this.farmAdminNewPassword = '';
      this.farmAdminConfirmPassword = '';
    }
  }

  async fetchDashboardData(url: string) {
    try {
      const response: RestResponse = await this.dashboardService.fetchAllDashboard(null, url);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.dashboardData = response.data;
    } catch (e) {
      this.toastService.error(e.message);
    }
  }

  get passwordsDoNotMatch(): boolean {
    return (
      this.farmAdminNewPassword &&
      this.farmAdminConfirmPassword &&
      this.farmAdminNewPassword !== this.farmAdminConfirmPassword
    );
  }

  onPasswordChangeSubmit(valid: boolean) {
    this.onClickValidation = !valid;
    if (!valid) {
      return false;
    }
    if (!this.farmAdminNewPassword || !this.farmAdminConfirmPassword) {
      this.onClickValidation = true;
      return;
    }
    // Do not show toast for password mismatch, let validation handle it in the template
    if (this.passwordsDoNotMatch) {
      this.onClickValidation = true;
      return;
    }
    const input: any = {
      userName: this.user?.userName,
      password: this.farmAdminNewPassword
    }
    this.loginService.recoverPassword(input)
      .then((data) => {
        this.loadingService.hide();
        this.toastService.success(data.message);
        this.showFarmAdminPasswordPopup = false;
        this.user.isFirstLogin = false;
        this.authService.setUer(this.user);
      }, (error) => {
        this.loadingService.hide();
        this.toastService.error(error.message);
      });
  }

  toggleNewPasswordVisibility() {
    this.newPasswordFieldType = this.newPasswordFieldType === 'password' ? 'text' : 'password';
  }

  toggleConfirmPasswordVisibility() {
    this.confirmPasswordFieldType = this.confirmPasswordFieldType === 'password' ? 'text' : 'password';
  }

  navigateToTrainingWithPublishedFilter() {
    this.router.navigate(['/dashboard/training'], { 
      queryParams: { 
        filter: 'published' 
      } 
    });
  }
}
