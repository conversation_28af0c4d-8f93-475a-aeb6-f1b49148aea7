import { Injectable } from "@angular/core";
import { BaseManager } from "src/app/config/base.manager";
import { CourseAllocatedUsersListService } from "./course-allocated-users-list.service";
import { LoadingService } from "src/app/services/loading.service";
import { ToastService } from "src/app/shared/toast.service";

@Injectable({
    providedIn: 'root'
})
export class CourseAllocatedUsersListManager extends BaseManager {

    constructor(protected courseAllocatedUsersListService: CourseAllocatedUsersListService,
        protected loadingService: LoadingService,
        protected toastService: ToastService) {
        super(courseAllocatedUsersListService, loadingService, toastService);
    }
}
