import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseEditComponent } from 'src/app/config/base.edit.component';
import { CourseTrainingsManager } from '../course-trainings-manager';
import { ToastService } from 'src/app/shared/toast.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { CommonUtil } from 'src/app/shared/common.util';
import { TranslateService } from '@ngx-translate/core';
import { LoadingService } from 'src/app/services/loading.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { RouteDataService } from 'src/app/shared/title.service';

@Component({
  selector: 'app-course-training-detail',
  templateUrl: './course-training-detail.component.html',
  styleUrls: ['./course-training-detail.component.scss']
})
export class CourseTrainingDetailComponent extends BaseEditComponent implements OnInit {
  public trainings: any;
  public videoPlaying: boolean = false;
  public loadingVideo: boolean = false;
  username: any;
  subTitle: string;

  constructor(protected route: ActivatedRoute, protected courseTrainingsManager: CourseTrainingsManager, protected toastService: ToastService, public routeDataService: RouteDataService,
    protected loadingService: LoadingService, protected router: Router, protected commonService: CommonService, public authService: AuthService,
    protected translateService: TranslateService, public commonUtil: CommonUtil, private loadVideoFromUrl: LoadVideoFromUrl) {
    super(courseTrainingsManager, commonService, toastService, loadingService, route, router, translateService);

  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.title;
      this.subTitle = params.username
    });
    this.init();
    // this.setBreadcrumbs();
  }

  setBreadcrumbs() {
    let obj: any = {};
    obj.type = "ADD_BREADCRUM_AND_LAST_UPDATE";
    obj.title = this.username;
    obj.subTitle = this.subTitle;
    // this.commonService.updateData(obj);
  }


  playVideoFromPlayIcon() {
    var videoId = document.querySelector<HTMLVideoElement>("#training_video");
    if (videoId) {
      const thisUrl = this.router.url;
      videoId.addEventListener('leavepictureinpicture', () => {
        const currentUrl = this.router.url;
        if (currentUrl !== thisUrl) {
          this.router.navigate([thisUrl]);
        }
      });
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
        this.videoPlaying = true;
      }
    }
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;
    });
  }

  async fetchExistingRecord() {
    try {
      const response: RestResponse = await this.manager.fetch(this.request.recordId);
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.trainings = response.data;

      // set the breadcrumbs
      const fullUrl = this.router.url; // Current full URL
      const queryIndex = fullUrl.indexOf('?'); // Find query parameter start
      const cleanUrl = queryIndex > -1 ? fullUrl.slice(0, queryIndex) : fullUrl;
      this.routeDataService.addBreadcrumb(this.trainings.title ?? this.trainings.trainingLibraryDetail.title, cleanUrl,this.trainings.id ?? this.trainings.trainingLibraryDetail.id)
      this.setPageTitle()
    } catch (error) {
      this.toastService.error(error.message);
    }
  }

  setPageTitle() {
    this.routeDataService.setData(this.router.url, this.trainings.title ?? this.trainings.trainingLibraryDetail.title);
  }

}
