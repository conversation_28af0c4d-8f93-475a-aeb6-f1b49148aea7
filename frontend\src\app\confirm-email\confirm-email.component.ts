import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountService } from '../services/account.service';
import { CommonService } from '../shared/common.service';
import { ToastService } from '../shared/toast.service';
import { RestResponse } from '../shared/auth.model';

@Component({
  selector: 'app-confirm-email',
  templateUrl: './confirm-email.component.html',
  styleUrls: ['./confirm-email.component.scss']
})
export class ConfirmEmailComponent implements OnInit {
  uniqueCode: any;
  validLinkMessage: string;
  code: any;
  error: boolean;
  unauthorizedAccess: boolean;
  invalidLink: boolean;
  success: boolean;

  constructor(private accountService: AccountService, private toastService: ToastService, private router: Router, private commonService: CommonService, private route: ActivatedRoute) {
  }

  ngOnInit(): void {
    this.confirmRegisterEmail();
  }

  async confirmRegisterEmail() {
    this.route.queryParams
      .subscribe(params => {
        if (params.p) {
          this.uniqueCode = encodeURIComponent(params.p);
          this.code = encodeURIComponent(params.code);
        }
      }
      );

    const data = {
      uniqueCode: this.uniqueCode,
      code: this.code,
    }
    if (this.uniqueCode && this.code) {
      try {
        const response: RestResponse = await this.accountService.confirmRegisterEmail(data);
        if (!response.status) {
          this.invalidLink = true;
          return;
        }
        this.success = true;

      } catch (error) {
        this.error = true;
        this.toastService.error(error.message);
      }
    } else {
      this.unauthorizedAccess = true;
    }
  }

}
