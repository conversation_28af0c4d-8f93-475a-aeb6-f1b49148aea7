import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { ManageProgramUserService } from './manage-program-user.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { Course } from 'src/app/models/course';

@Injectable({
    providedIn: 'root'
})
export class ManageProgramUserManager extends BaseManager {

    constructor(protected manageProgramUserService: ManageProgramUserService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(manageProgramUserService, loadingService, toastService);
    }

    getPublishUser(data: Course): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.manageProgramUserService.getCourseFilter(data);
                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onSaveSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }
}
