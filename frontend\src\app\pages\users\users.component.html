<div data-aos="fade-up" data-aos-duration="1000" class="site-customer-main-container manage-detail">

  <div class="row">
    <div class="col-12 pe-0 mt-3 d-flex align-items-center justify-content-between">
      <div class="custom-input-group">
        <input class="form-control search-form-control" placeholder="Search" appDelayedInput
          (delayedInput)="search($event)" [delayTime]="1000">
        <i class="bi bi-search pe-3"></i>
      </div>
      <div class="col-12 col-sm-4 text-end pe-0 mb-2">
        <button (click)="openfilterModal()" type="button"
          class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg filter-button-cls font-15px height-51px"
          *ngIf='authService.isAccessible("MANAGE_USER","FilterButton")'>
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
        </button>
        <button type="button" class="btn add-button btn-primary btn-lg" (click)="onNewRecord()"
          *ngIf="authService.isAccessible('STAFF','AddButton')">
          <img src="/assets/images/icons/menu/add_icon.svg" class="me-3 width-15px" alt="">Add New User
        </button>
      </div>
    </div>
  </div>
  <div class="clearfix"></div>

  <div class="dashboard-content-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
      <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
        <thead>
          <tr>
            <th style="width: 190px; text-wrap: nowrap;">{{"USERS.Name" | translate}}</th>
            <th style="width: 150px; text-wrap: nowrap;">{{"USERS.MobileNo" | translate}}</th>
            <th style="text-wrap: nowrap;">{{"USERS.Email" | translate}}</th>
            <th style="width: 190px; text-wrap: nowrap;">{{"USERS.farm" | translate}}</th>
            <th style="width:139px; text-wrap: nowrap;">{{"USERS.PhoneNumberConfirmed" | translate}}</th>
            <th style="width:107px; text-wrap: nowrap;">{{"USERS.EmailConfirmed" | translate}}</th>
            <th style="width:50px; text-wrap: nowrap;">{{"USERS.Status" | translate}}</th>
            <th style="min-width:190px; text-wrap: nowrap;" class="text-center">{{"USERS.Action" | translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let record of records;">
            <td class="text-capitalize">{{record.fullName}}</td>
            <td class="text-lowercase" style="width: 150px; text-wrap: nowrap;">
              {{record.phoneNumber}}</td>
            <td class="text-lowercase">{{record.email}}</td>
            <td>
              <ng-container *ngIf="record.userFarmDetail?.length; else noFarms">
                <ul class="list-inline mb-0 pb-0">
                  <li class="list-inline-item mb-1" *ngFor="let farm of record.userFarmDetail">
                    <span class="badge rounded-pill bg-light text-dark farm-badge" *ngIf="!farm.isDeleted"
                      [title]="farm.farmCode + ' - ' + farm.farmName">
                      {{ farm.farmCode }} - {{ farm.farmName }}
                    </span>
                  </li>
                </ul>
              </ng-container>
              <ng-template #noFarms>
                <span class="text-muted small">No Company</span>
              </ng-template>
            </td>

            <td class="text-uppercase">
              <img
                [src]="record.phoneNumberConfirmed == true ? '/assets/images/icons/menu/green-tick.svg' : '/assets/images/icons/menu/red-cross.svg'"
                class="width-25-px" alt="" />
            </td>
            <td class="text-uppercase">
              <img
                [src]="record.emailConfirmed == true ? '/assets/images/icons/menu/green-tick.svg' : '/assets/images/icons/menu/red-cross.svg'"
                class="width-25-px" alt="" />
            </td>
            <td>
              <div class="form-check form-switch">
                <input (change)="activateDeactiveUserConfirmation($event,record)" class="form-check-input toggle-width"
                  type="checkbox" id="flexSwitchCheckChecked" [(ngModel)]="record.isActive"
                  [ngModelOptions]="{standalone: true}">
                <label class="form-check-label" for="flexSwitchCheckChecked"></label>
              </div>
            </td>
            <td class=" custom-action-button text-right">
              <div>
                  <img src="/assets/images/icons/menu/calendar-icon.svg" title="My Moment"
                  [routerLink]="['/dashboard/user-details/' + record?.id]" class="width-25-px me-2 cursor-pointer"
                  [queryParams]="{ tab: 'moment', 'username': record?.fullName , 'id':record?.id}"
                  queryParamsHandling="merge" alt="" *ngIf="record.userFarmDetail.length > 0">
                <img src="/assets/images/icons/menu/manage-traning-icon.svg" title="My Training"
                  class="width-25-px me-2 cursor-pointer" [routerLink]="['/dashboard/user-details/' + record?.id]"
                  [queryParams]="{ tab: 'training', 'username': record?.fullName, 'id':record?.id }"
                  queryParamsHandling="merge" alt="" />
                <img src="/assets/images/icons/menu/course-ims.svg" title="My Course"
                  [routerLink]="['/dashboard/user-details/' + record?.id]" class="width-25-px me-2 cursor-pointer"
                  [queryParams]="{ tab: 'course', 'username': record?.fullName , 'id':record?.id}"
                  queryParamsHandling="merge" alt="" />
                <i title="Profile" [routerLink]="['/dashboard/user-details/'+record.id]"
                  class="bi bi-pencil font-21px me-2 cursor-pointer align-middle">
                </i>
                <i class="bi bi-trash font-21px cursor-pointer align-middle" title="Delete" (click)="remove(record.id)"
                  *ngIf="authService.isAccessible('STAFF','DeleteButton')"></i>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Filter Modal -->
  <div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="filterModalLabel">Filter Seekers</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div *ngIf="filterModal && filterModal._isShown" class="modal-body">
          <form #filterForm="ngForm" novalidate="novalidate">
            <div class="form-floating">
              <div class="mb-3 mt-2 form-control select-width ng-select-main-container"
                [ngClass]="{'is-invalid': !farm.valid && onClickValidation}">
                <ng-select [items]="farmsWithNoCompany" bindLabel="name" bindValue="id" name="farm"
                  [(ngModel)]="filterParam.farm" #farm="ngModel" placeholder="Select Company">
                  <ng-template ng-option-tmp let-item="item">
                    <ng-container *ngIf="item.id !== 'no-company'; else noCompanyOption">
                      {{ item.farmCode ? (item.farmCode + ' - ') : '' }}{{ item.name }}
                    </ng-container>
                    <ng-template #noCompanyOption>
                      No Company
                    </ng-template>
                  </ng-template>
                </ng-select>
              </div>
              <label for="farm">Company</label>
            </div>

            <div class="form-floating">
              <div class="mb-3 mt-2 form-control select-width ng-select-main-container">
                <ng-select [items]="statusOptions" bindLabel="label" bindValue="value" name="status"
                  [(ngModel)]="filterParam.status" placeholder="Select Status">
                </ng-select>
              </div>
              <label for="Status">Status</label>
            </div>

            <div class="modal-footer">
              <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
              <button (click)="applyFilter(filterForm.form.valid)" type="button" class="btn btn-primary">Filter</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>