import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import { Language } from './language';
import { Training } from './training';
export class CoursePart extends BaseModel {

    tenantId: number;
    slug: string;
    title: string;
    description: string='';
    isPublish: boolean;
    isDraft: boolean;
    isPrivate: boolean;
    url: string;
    code: string;
    status: string;
    percentage: string;
    showCourseMain: boolean = false
    showCoursePart: boolean = false;
    showTrainingPart: boolean = false;
    showAddTrainingPart: boolean = false;
    totalCount: number;
    course: string;
    trainings: Training[];
    constructor() {
        super();
        this.isDeleted = false;
        this.isActive = true;
        this.isPublish = false;
        this.isDraft = false;
        this.isPrivate = false;
        this.trainings = new Array<Training>();
    }

    static fromResponse(data: any): CoursePart {
        const obj = new CoursePart();
        obj.id = data.id;
        obj.tenantId = data.tenantId;
        obj.slug = data.slug;
        obj.createdBy = data.createdBy;
        obj.updatedBy = data.updatedBy;
        obj.createdOn = data.createdOn;
        obj.updatedOn = data.updatedOn;
        obj.isDeleted = data.isDeleted;
        obj.isActive = data.isActive;
        obj.isPrivate = data.isPrivate;
        obj.isDraft = data.isDraft;
        obj.isPublish = data.isPublish;
        obj.title = data.title;
        obj.description = data.description;
        obj.url = data.url;
        obj.code = data.code;
        obj.status = data.status;
        obj.percentage = data.percentage;
        obj.totalCount = data.totalCount;
        return obj;
    }

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        this.title = this.trimMe(this.title);
        this.description = this.trimMe(this.description);
        this.url = this.trimMe(this.url);
        return this;
    }
}
