import { Injectable } from '@angular/core';
import { constants } from 'buffer';
import { Constant } from '../config/constants';
import { OsAndBrowserDetect } from './os-and-browser-detect';

declare const $: any;

@Injectable({
  providedIn: 'root'
})
export class LoadVideoFromUrl {
  constructor(private osAndBrowserDetect: OsAndBrowserDetect) {
  }

  async UrlToBlobUrl(url: any) {
    if (this.osAndBrowserDetect.OsDetails() == Constant.OS_VERSION.IOS || (this.osAndBrowserDetect.OsDetails() == Constant.OS_VERSION.Mac_OS && this.osAndBrowserDetect.BrowserDetect() == Constant.BROWSER_DETECT.safari)) {
      return url;
    } else {
      return fetch(url)
        .then(resp => resp.blob())
        .then(blob => URL.createObjectURL(blob));
    }
  }

  setVideoUrl(vid: any, blobUrl: any) {
    if (this.osAndBrowserDetect.OsDetails() == Constant.OS_VERSION.IOS || (this.osAndBrowserDetect.OsDetails() == Constant.OS_VERSION.Mac_OS && this.osAndBrowserDetect.BrowserDetect() == Constant.BROWSER_DETECT.safari)) {
      var source = document.createElement('source');
      source.src = blobUrl;
      vid.appendChild(source);
    } else {
      vid.src = blobUrl; // we just set our mediaElement's src to this blobURL
      vid.onload = () => URL.revokeObjectURL(blobUrl);
    }
  }

}
