import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseListServerSideComponent } from 'src/app/config/base.list.server.side.component';
import { LoadingService } from 'src/app/services/loading.service';
import { AuthService } from 'src/app/shared/auth.services';
import { CommonService } from 'src/app/shared/common.service';
import { CommonUtil } from 'src/app/shared/common.util';
import { ToastService } from 'src/app/shared/toast.service';
import { CourseUserDetailsService } from './course-user-details.service';
import { CourseUserDetailsManager } from './course-user-details.manager';
import { ProgramTrainingHistoryComponent } from '../program-training-history/program-training-history.component';
import { UserMyCourseComponent } from '../user-my-course/user-my-course.component';
import AOS from 'aos';
import { RestResponse } from 'src/app/shared/auth.model';
import { FilterParam } from 'src/app/models/filterparam';
import * as moment from 'moment';
import { CourseTrainings } from 'src/app/models/coursetrainings';
import { Content } from 'src/app/models/content';
import { CourseLearningSeries } from 'src/app/models/courselearningseries';
import { RouteDataService } from 'src/app/shared/title.service';

declare const $: any;
declare var bootstrap: any;
@Component({
  selector: 'app-course-user-details',
  templateUrl: './course-user-details.component.html',
  styleUrls: ['./course-user-details.component.scss']
})
export class CourseUserDetailsComponent extends BaseListServerSideComponent implements OnInit {
  userDetails: boolean = true;
  userDetailsTabs: string = "profile";
  currentActiveTab: string;
  isTrainingCsvExport: boolean = false
  filterCourseDetail: any;
  filterParam: FilterParam;
  courseFilterParam = new FilterParam;
  selectedCourseId: any;
  moment: any = moment;
  selectedCourseUserId: string;
  selectedCourseUser: any;
  publishCourseList: any[];
  dropdownSettings = {};
  dropdownSettingsUsers = {};
  searchTraining: any;
  onClickValidation = false;
  fromDate: any;
  toDate: any;
  searchCourse: any;
  recordData: any;
  contentTypes: Content[];
  learningSeries: CourseLearningSeries[];
  trainingFilterData: RestResponse;
  videos: any[] = [];
  isViewAllTableVisible: boolean = false;
  records: CourseTrainings[];
  username: any;
  searchText: string;

  @ViewChild(ProgramTrainingHistoryComponent) programTrainingHistoryComponent: ProgramTrainingHistoryComponent;
  @ViewChild(UserMyCourseComponent) userMyCourseComponent: UserMyCourseComponent;

  constructor(private route: ActivatedRoute, public authService: AuthService, protected router: Router, private zone: NgZone, protected toastService: ToastService, public routeDataService: RouteDataService,
    protected loadingService: LoadingService,
    protected courseUserDetailsService: CourseUserDetailsService, protected courseUserDetailsManager: CourseUserDetailsManager,
    protected commonService: CommonService, public commonUtil: CommonUtil) {
    super(courseUserDetailsManager, commonService, toastService, loadingService, router);
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.username = params.username;
      this.currentActiveTab = params.tab;
    });
    this.filterParam = new FilterParam();
    this.courseFilterParam = new FilterParam();
    this.setDropdownSettings();
    this.setDropdownSettingsUsers();
    this.request.loadEditPage = false;
    this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
    this.records = new Array<CourseTrainings>();
    this.contentTypes = new Array<Content>();
    this.userDetailsTabs = this.currentActiveTab != null ? this.currentActiveTab : "profile";
    this.selectedCourseId = this.route.snapshot.paramMap.get('id');
    // this.selectedCourseId = this.router.url.substring(this.router.url.lastIndexOf('/') + 1);
    this.fetchAssociatedData();
    this.fetchCoursesForFilters();
    this.loadCourseTitle();
  }

  setDropdownSettings() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'name',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  setDropdownSettingsUsers() {
    this.dropdownSettingsUsers = {
      singleSelection: false,
      idField: 'id',
      enableCheckAll: false,
      textField: 'fullName',
      // itemsShowLimit: 3,
      allowSearchFilter: true
    };
  }

  updateStatus(event: Event): void {
    const selectedValue = (event.target as HTMLSelectElement).value;

    // Update `isCompleted` based on the selected value
    if (selectedValue === 'true') {
      this.courseFilterParam.isCompleted = true; // COMPLETED
    } else if (selectedValue === 'false') {
      this.courseFilterParam.isCompleted = false; // IN PROGRESS
    }
  }

  selectInviteUserCourse(event: any): void {
    if (event) {
      // this.selectedInviteUserCourse = event.title;
      this.selectedCourseUser = event.title;
      this.selectedCourseUserId = event ? event.id : null;
    }
  }

  onClickUserDetailsTab(name: string) {
    this.zone.run(() => {
      this.router.navigate(
        [],
        {
          relativeTo: this.route,
          // queryParams: { tab: name },
          queryParamsHandling: 'merge'
        });
    });
    this.userDetailsTabs = name;
  }

  // filter common
  onClickCourseDetailFilter(valid) {
    this.filterCourseDetail.hide();
    this.filterChildRecords();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      // Initialize Bootstrap modal
      const modalElement = document.getElementById('filterCourseDetail');
      if (modalElement) {
        this.filterCourseDetail = new bootstrap.Modal(modalElement);
      }
    }, 0);
  }

  resetFilter() {
    delete this.courseFilterParam.courseTitle;
    delete this.courseFilterParam.isCompleted;
    delete this.filterParam.courseTitle;
    delete this.filterParam.startDate;
    delete this.filterParam.endDate;
    delete this.filterParam.contentType;
    delete this.filterParam.learningSeries;
    delete this.filterParam.status;
    this.filterCourseDetail.hide();
    this.filterChildRecords();
  }

  openCourseDetailFilter() {
    this.fetchCoursesForFilters();
    AOS.init({ disable: true });
    this.filterCourseDetail.show();
  }
  async fetchAssociatedData() {
    this.trainingFilterData = await this.courseUserDetailsService.getTrainingFilterData(null);
  }

  async fetchCoursesForFilters() {
    try {
      this.loadingService.show();
      const response: RestResponse = await this.courseUserDetailsService.getCourseFilter();
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.publishCourseList = response.data;
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  filterChildRecords() {
    switch (this.currentActiveTab) {
      case 'training':
        this.programTrainingHistoryComponent.refreshRecord();
        break;

      case 'course':
        this.userMyCourseComponent.refreshRecord();
        break;
    }
  }

  exportTrainingHistroyCsv() {
    switch (this.currentActiveTab) {
      case 'training':
        this.programTrainingHistoryComponent.exportTrainingHistroyCsv();
        break;

      case 'course':
        this.userMyCourseComponent.exportTrainingHistroyCsv();
        break;
    }
  }

  search($event) {
    const value = ($event.target as HTMLInputElement).value;
    if (this.userDetailsTabs === "training") {
      this.filterParam.searchText = (value && value != '') ? value.trim() : null;
    }
    // switch(this.currentActiveTab)
    this.filterChildRecords();
  }

  fromDateOutput(event: any) {
    if (event) {
      this.filterParam.startDate = moment(event).format('YYYY-MM-DD');
      this.courseFilterParam.startDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.startDate
      delete this.courseFilterParam.startDate
    }
  }

  toDateOutput(event: any) {
    if (event) {
      this.filterParam.endDate = moment(event).format('YYYY-MM-DD');
      this.courseFilterParam.endDate = moment(event).format('YYYY-MM-DD');
    } else {
      delete this.filterParam.endDate
      delete this.courseFilterParam.endDate
    }
  }

  loadCourseTitle(): void {
    const trainingId: string | null = this.route.snapshot.paramMap.get("id");
    if (trainingId) {
      this.courseUserDetailsService.getcourseTitle(trainingId).subscribe(response => {
        const title = response?.data?.title || 'User Details';
        // this.routeDataService.setData(this.router.url, title);
      });
    }
  }

}
